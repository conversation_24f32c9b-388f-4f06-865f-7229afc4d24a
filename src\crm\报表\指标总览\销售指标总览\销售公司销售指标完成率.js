var data_arr = [
    {"complete_rate": 10, "month": "01", "year": 2024},
    {"complete_rate": 20, "month": "02", "year": 2024},
    {"complete_rate": 30, "month": "03", "year": 2024},
    {"complete_rate": 40, "month": "04", "year": 2024},
    {"complete_rate": 50, "month": "05", "year": 2024},
    {"complete_rate": 60, "month": "06", "year": 2024},
    {"complete_rate": 70, "month": "07", "year": 2024},
    {"complete_rate": 80, "month": "08", "year": 2024},
    {"complete_rate": 90, "month": "09", "year": 2024},
    {"complete_rate": 100, "month": "10", "year": 2024},
    {"complete_rate": 112, "month": "11", "year": 2024},
    {"complete_rate": 123, "month": "12", "year": 2024},
    {"complete_rate": 11, "month": "01", "year": 2025},
    {"complete_rate": 12, "month": "02", "year": 2025},
    {"complete_rate": 13, "month": "03", "year": 2025},
    {"complete_rate": 21, "month": "04", "year": 2025},
    {"complete_rate": 33, "month": "05", "year": 2025},
    {"complete_rate": 44, "month": "06", "year": 2025},
    {"complete_rate": 55, "month": "07", "year": 2025},
    {"complete_rate": 66, "month": "08", "year": 2025},
    {"complete_rate": 77, "month": "09", "year": 2025},
    {"complete_rate": 88, "month": "10", "year": 2025},
    {"complete_rate": 99, "month": "11", "year": 2025},
    {"complete_rate": 100, "month": "12", "year": 2025}
];

var years = data_arr.map(function(item) {
    return item.year;
}).filter(function(value, index, self) {
    return self.indexOf(value) === index;
}).sort();

var option = {
    title: {
        text: "销售公司销售指标完成率（当年度累计值）",
        left: "center"
    },
    tooltip: {
        trigger: "axis",
        axisPointer: {
            type: "line"
        }
    },
    legend: {
        data: years.reduce(function(acc, year) {
            acc.push(year + "年柱状图", year + "年趋势线");
            return acc;
        }, []),
        bottom: "5%",
        itemGap: 20,
        textStyle: {
            fontSize: 12,
            color: "#666"
        }
    },
    grid: {
        left: "3%",
        right: "4%",
        bottom: "12%",
        top: "10%",
        containLabel: true
    },
    xAxis: {
        type: "category",
        boundaryGap: true,
        data: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]
    },
    yAxis: [{
        type: "value",
        name: "完成率",
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
            formatter: "{value}%",
            margin: 20,
            align: "right"
        },
        nameGap: 40
    }],
    series: years.reduce(function(acc, year, index) {
        var yearData = data_arr.filter(function(item) {
            return item.year === year;
        }).map(function(item) {
            return item.complete_rate;
        });

        // 添加柱状图
        acc.push({
            name: year + "年柱状图",
            type: "bar",
            barWidth: "25%",
            barGap: "30%",
            data: yearData,
            itemStyle: {
                color: index === 0 ? "#5470c6" : "#91cc75",
                opacity: 0.8
            },
            label: {
                show: true,
                position: "top",
                formatter: "{c}%"
            }
        });

        // 添加趋势线
        acc.push({
            name: year + "年趋势线",
            type: "line",
            smooth: true,
            data: yearData,
            itemStyle: {
                color: index === 0 ? "#5470c6" : "#91cc75",
                opacity: 0.8
            },
            lineStyle: {
                width: 2,
                type: "dashed",
                opacity: 0.8
            },
            symbol: "circle",
            symbolSize: 6,
            showSymbol: false
        });

        return acc;
    }, [])
};

// 添加目标线
option.markLine = {
    data: [{
        name: "目标线",
        yAxis: 90,
        lineStyle: {
            color: "#fac858",
            type: "solid",
            width: 2,
            opacity: 0.8
        },
        label: {
            formatter: "目标线"
        }
    }]
};
console.log(JSON.stringify(option));
return option;
