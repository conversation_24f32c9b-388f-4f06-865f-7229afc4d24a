var custCodeArr =["C000019"]
var currentPage =1
var pageSize =10
var data =[
    {
        "main_class": "折叠纸盒类",
        "create_by_name": "李燕",
        "create_by": "1808689088825139201",
        "update_time": "2025-03-17 11:18:41",
        "quotation_code": "HJ251022669",
        "mnemonic_code": "ZDYY000632",
        "net_size": "181*206.25",
        "cust_name": "[山东博士伦福瑞达制药有限公司, 山东博士伦福瑞达制药有限公司]",
        "quotation_version": "3",
        "id": 38328,
        "update_by": "1808689088825139201",
        "material_name": "氯化钠滴眼液0.4ml:2.2mg10支小盒",
        "flag_deleted": 0,
        "product_version": "3.3",
        "update_by_name": "李燕",
        "create_time": "2025-03-17 11:17:09",
        "product_size": "73.0*25.0*110.0",
        "version": "2.0",
        "cust_code": "[C000019, C000019]",
        "reason_approval": "",
        "sub_class": "化药类",
        "out_of_book": "15",
        "standard_unit": "只",
        "status": "4",
        "material_code": "ZDHY0000807"
    },
    {
        "main_class": "折叠纸盒类",
        "create_by_name": "李燕",
        "create_by": "1808689088825139201",
        "update_time": "2025-03-17 10:03:30",
        "quotation_code": "HJ251022390",
        "mnemonic_code": "ZDYY000661",
        "net_size": "203*228.5",
        "cust_name": "[山东博士伦福瑞达制药有限公司, 山东博士伦福瑞达制药有限公司]",
        "quotation_version": "2",
        "id": 38317,
        "update_by": "1808689088825139201",
        "material_name": "氯化钠滴眼液0.4ml:2.2mg15支小盒",
        "flag_deleted": 0,
        "product_version": "2.2",
        "update_by_name": "李燕",
        "create_time": "2025-03-17 10:02:44",
        "product_size": "73.0*36.0*110.0",
        "version": "2.0",
        "cust_code": "[C000019, C000019]",
        "reason_approval": "",
        "sub_class": "化药类",
        "out_of_book": "15",
        "standard_unit": "只",
        "status": "4",
        "material_code": "ZDHY0000808"
    },
    {
        "main_class": "折叠纸盒类",
        "create_by_name": "李燕",
        "create_by": "1808689088825139201",
        "update_time": "2025-03-17 09:51:14",
        "quotation_code": "HJ251016793",
        "mnemonic_code": "ZDHY000080",
        "net_size": "142*160.5",
        "cust_name": "[山东博士伦福瑞达制药有限公司]",
        "quotation_version": "2",
        "id": 38314,
        "update_by": "1808689088825139201",
        "material_name": "托吡卡胺滴眼液6ml:30mg小盒",
        "flag_deleted": 0,
        "product_version": "1.2",
        "update_by_name": "李燕",
        "create_time": "2025-03-17 09:34:35",
        "product_size": "50.0*25.0*72.0",
        "version": "2.0",
        "cust_code": "[C000019]",
        "reason_approval": "与旧系统差异来源于：\n1、开排按实际一遍喷码的最大开排核算，较原开排减少4只。（原开排无法一遍喷印完成）\n2、新系统粘盒工序制造费用增加50元/h，较原来成本增加1.5%",
        "sub_class": "化药类",
        "out_of_book": "24",
        "standard_unit": "只",
        "status": "4",
        "material_code": "ZDHY0001331"
    },
    {
        "main_class": "折叠纸盒类",
        "create_by_name": "李燕",
        "create_by": "1808689088825139201",
        "update_time": "2025-02-26 11:58:39",
        "quotation_code": "HJ251023326",
        "mnemonic_code": "ZDYY001002",
        "net_size": "161*145.25",
        "cust_name": "[山东博士伦福瑞达制药有限公司]",
        "quotation_version": "2",
        "id": 37323,
        "update_by": "1808689088825139201",
        "material_name": "露达舒(氯替泼诺混悬滴眼液）5ml",
        "flag_deleted": 0,
        "product_version": "5.2",
        "update_by_name": "李燕",
        "create_time": "2025-02-26 11:51:04",
        "product_size": "41.0*27.0*86.0",
        "version": "2.0",
        "cust_code": "[C000019]",
        "reason_approval": "与旧系统差异源于去掉品检工序（天津批量时在品粘一体机上完成），对于固定成本、变动成本均有降低。",
        "sub_class": "化药类",
        "out_of_book": "24",
        "standard_unit": "只",
        "status": "4",
        "material_code": "ZDHY0000806"
    },
    {
        "main_class": "折叠纸盒类",
        "create_by_name": "赵玉善",
        "create_by": "1808689088829333505",
        "update_time": "2025-02-19 12:45:04",
        "quotation_code": "HJ251034077",
        "mnemonic_code": "ZDHY000631",
        "net_size": "165*104.5",
        "cust_name": "[山东博士伦福瑞达制药有限公司]",
        "quotation_version": "2",
        "id": 36923,
        "update_by": "1808689088829333505",
        "material_name": "卡波姆眼用凝胶小盒",
        "flag_deleted": 0,
        "product_version": "1.2",
        "update_by_name": "赵玉善",
        "create_time": "2025-02-19 12:43:57",
        "product_size": "27.0*20.0*107.0",
        "version": "2.0",
        "cust_code": "[C000019]",
        "reason_approval": "",
        "sub_class": "化药类",
        "out_of_book": "30",
        "standard_unit": "只",
        "status": "4",
        "material_code": "ZDHY0001660"
    }
]

function formatData(item) {  
    // 检查 cust_code 是否以 '[' 开头并以 ']' 结尾  
    if (item.cust_code.charAt(0) !== '[' || item.cust_code.charAt(item.cust_code.length - 1) !== ']') {  
        throw new Error('cust_code must be enclosed in square brackets');  
    }  
    // 移除 cust_code 两端的方括号  
    var custCodeStr = item.cust_code.slice(1, -1);  
    // 类似地检查 cust_name  
    if (item.cust_name.charAt(0) !== '[' || item.cust_name.charAt(item.cust_name.length - 1) !== ']') {  
        throw new Error('cust_name must be enclosed in square brackets');  
    }  
    var custNameStr = item.cust_name.slice(1, -1);  
    // 将 cust_code 和 cust_name 字符串分割成数组  
    var custCodeArray = custCodeStr.split(',');  
    var custNameArray = custNameStr.split(',');
    
    // 去除重复的客户代码
    var uniqueCustCodes = [];
    var uniqueCustNames = [];
    var uniqueIndexes = [];
    
    for (var i = 0; i < custCodeArray.length; i++) {
        var code = custCodeArray[i].trim();
        if (uniqueCustCodes.indexOf(code) === -1) {
            uniqueCustCodes.push(code);
            uniqueCustNames.push(custNameArray[i].trim());
            uniqueIndexes.push(i);
        }
    }
   
    // 为每个唯一的cust_code创建一个新对象  
    var expandedItems = uniqueCustCodes.map(function(custCode, index) {  
        var newObj = {};  
        for (var key in item) {  
            if (item.hasOwnProperty(key) && key !== 'cust_code' && key !== 'cust_name') {  
                newObj[key] = item[key]; // 复制其他属性  
            }  
        }  
        newObj.cust_code = custCode; // 设置单独的cust_code
        newObj.cust_name = uniqueCustNames[index]; // 设置对应的cust_name
        return newObj;  
    });  
  
    return expandedItems;  
}  
// 分页函数，使用ES5语法  
function paginate(data, pageSize, currentPage) {  
    var startIndex = (currentPage - 1) * pageSize;  
    var endIndex = Math.min(startIndex + pageSize, data.length);  
    return data.slice(startIndex, endIndex);  
}  
  
var expandedData = [];  
data.forEach(function(item) {  
    var expandedItems = formatData(item);  
    expandedData = expandedData.concat(expandedItems); // 合并扩展后的对象到总数组中  
});  
var arr = []
for (var x = 0; x < custCodeArr.length; x++) {
  for (var y = 0; y < expandedData.length; y++) {
    if (custCodeArr[x] == expandedData[y].cust_code) {
      arr.push(expandedData[y])
    }
  }
}
var paginatedData = paginate(arr, pageSize, currentPage);  
  
var obj = {};  
obj['records'] = paginatedData;  
obj['total'] = arr.length;  
console.log(JSON.stringify(obj))
return obj

