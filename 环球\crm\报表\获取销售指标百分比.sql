drop table dwd_cust_sale_percentage;
CREATE TABLE dwd_cust_sale_percentage
(
    id                INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    cust_code         VARCHAR(20)    NOT NULL COMMENT '客户编码',
    sales_code        VARCHAR(20)    NULL COMMENT '销售编码',
    sales_name        VARCHAR(100)   NULL COMMENT '销售名称',
    target_percentage DECIMAL(10, 5) NOT NULL COMMENT '目标百分比',
index cust_sale (cust_code,sales_code)
) COMMENT '客户销售指标占比';
select * from dwd_cust_sale_percentage where cust_code='C000225';

-- ETL:CRM客户销售指标占比->dwd_cust_sale_percentage
WITH deduplicated_sales AS (SELECT DISTINCT ccb.cust_code,
                                            ccb.cust_manager_code,
                                            ccb.cust_manager_name,
                                            ifnull(ccs.sales_code, ccb.cust_manager_code) sales_code,
                                            ifnull(ccs.sales_name, ccb.cust_manager_name) sales_name,
                                            NULLIF(
                                                    CAST(REPLACE(REPLACE(ccs.target_percentage, '%', ''), ' ', '') AS DECIMAL),
                                                    0) as                                 cleaned_percentage
                            FROM crm_cust_basic ccb
                                     LEFT JOIN crm_cust_sale ccs
                                               ON ccb.cust_mnemonic_code = ccs.cust_mnemonic_code
                                                   AND ccb.cust_version = ccs.cust_version
                                                   AND ccs.flag_deleted = 0
                            WHERE cust_status = 2
                              AND cust_type NOT IN (0, 1)
                              AND ccb.flag_deleted = 0)
SELECT cust_code,
       sales_code,
       sales_name,
       COALESCE(
               cleaned_percentage,
               CASE
                   WHEN SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END)
                            OVER (PARTITION BY cust_code)
                       = COUNT(*) OVER (PARTITION BY cust_code)
                       THEN 100.0 / COUNT(*) OVER (PARTITION BY cust_code)
                   ELSE (100 - SUM(COALESCE(cleaned_percentage, 0)) OVER (PARTITION BY cust_code)) /
                        NULLIF(SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END)
                                   OVER (PARTITION BY cust_code), 0)
                   END,
               0
       ) as target_percentage
FROM deduplicated_sales;


select cust_code,sum(target_percentage) from
                 (
                     WITH deduplicated_sales AS (
                         SELECT DISTINCT
                             ccb.cust_code,
                             ccs.sales_code,
                             NULLIF(CAST(REPLACE(REPLACE(ccs.target_percentage, '%', ''), ' ', '') AS DECIMAL), 0) as cleaned_percentage
                         FROM crm_cust_basic ccb
                                  LEFT JOIN crm_cust_sale ccs
                                            ON ccb.cust_mnemonic_code = ccs.cust_mnemonic_code
                                                AND ccb.cust_version = ccs.cust_version
                         WHERE cust_status = 2
                           AND cust_type NOT IN (0,1)
                           AND ccb.flag_deleted = 0
                           AND ccs.flag_deleted = 0
                     )
                     SELECT
                         cust_code,
                         sales_code,
                         COALESCE(
                                 cleaned_percentage,
                                 CASE
                                     WHEN SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END) OVER (PARTITION BY cust_code)
                                         = COUNT(*) OVER (PARTITION BY cust_code)
                                         THEN 100.0 / COUNT(*) OVER (PARTITION BY cust_code)
                                     ELSE (100 - SUM(COALESCE(cleaned_percentage, 0)) OVER (PARTITION BY cust_code)) /
                                          NULLIF(SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END) OVER (PARTITION BY cust_code), 0)
                                     END,
                                 0
                         ) as target_percentage
                     FROM deduplicated_sales

                 )temp group by cust_code;
