-- 查询纸张用料
SELECT DISTINCT
    pmmi.material_code,
    pmmi.material_name
FROM
    pm_material_module pmm
        LEFT JOIN pm_material_module_id pmmi ON
        pmm.document_number = pmmi.document_number
            AND pmmi.flag_deleted = 0
WHERE
    pmm.flag_deleted = 0
  AND pmm.material_class = 1
  AND pmm.pro_batch_no = 'DY25070092_01';

-- 领料数量
WITH tem AS (
    SELECT
        :pro_batch_no AS pro_batch_no,
        IFNULL(sum(pmmi.application_num), 0) AS getMaterial,
        0 AS repairMaterial
    FROM
        pm_material_module pmm
            LEFT JOIN pm_material_module_id pmmi ON
            pmm.document_number = pmmi.document_number
                AND pmmi.flag_deleted = 0
    WHERE
        pmm.flag_deleted = 0
      AND pmm.material_class = 1
      AND pmm.materials_type = 1
      AND pmm.pro_batch_no = :pro_batch_no
    UNION
    SELECT
        :pro_batch_no AS pro_batch_no,
        0 AS getMaterial,
        IFNULL(sum(pmmi.application_num), 0)AS repairMaterial
    FROM
        pm_material_module pmm
            LEFT JOIN pm_material_module_id pmmi ON
            pmm.document_number = pmmi.document_number
                AND pmmi.flag_deleted = 0
    WHERE
        pmm.flag_deleted = 0
      AND pmm.material_class = 1
      AND pmm.materials_type = 2
      AND pmm.pro_batch_no = :pro_batch_no
)
SELECT
    pro_batch_no,
    max(getMaterial) AS getMaterial,
    max(repairMaterial) AS repairMaterial
FROM
    tem
GROUP BY
    pro_batch_no;
