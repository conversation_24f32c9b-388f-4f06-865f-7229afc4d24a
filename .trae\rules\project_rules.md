---
description: ES5相关规范
globs: 
alwaysApply: false
---
---
description: ES5相关规范
globs: ["**/*.js", "**/*.ts"] 
alwaysApply: true
---

# ES5 代码规范

## 1. 命名规范

### 1.1 变量和函数命名
- 使用 snake_case 命名法（小写字母，单词间用下划线连接）
```javascript
// 正确
var first_name = '<PERSON>';
var calculate_total = function() { /* ... */ };

// 错误
var firstName = 'John';
var calculateTotal = function() { /* ... */ };
```

### 1.2 常量命名
- 使用全大写字母，单词间用下划线连接
```javascript
// 正确
var MAX_COUNT = 10;
var API_KEY = 'abc123';

// 错误
var maxCount = 10;
var apiKey = 'abc123';
```

### 1.3 构造函数命名
- 使用首字母大写的 PascalCase，这是唯一的例外
```javascript
// 正确
function UserAccount(name) {
    this.name = name;
}

// 错误
function userAccount(name) {
    this.name = name;
}
```

### 1.4 私有成员命名
- 以下划线开头，使用 snake_case
```javascript
function User() {
    this._private_data = {};
    this.public_data = {};
}
```

## 2. 语法规则

### 2.1 变量声明
- 始终使用 `var` 声明变量
- 一行声明一个变量，不使用逗号分隔的多变量声明
- 变量声明应位于函数或作用域的顶部

```javascript
// 正确
function foo() {
    var bar = 1;
    var baz = 2;
    
    // 函数体
}

// 错误
function foo() {
    var bar = 1, baz = 2;
    
    // 函数体
}
```

### 2.2 函数声明
- 使用函数声明而非函数表达式（除非必要）
- 匿名函数使用函数表达式

```javascript
// 函数声明（推荐）
function do_something() {
    // ...
}

// 函数表达式（仅在需要时使用）
var do_something = function() {
    // ...
};

// 匿名函数（回调等场景）
element.onclick = function() {
    // ...
};
```

### 2.3 严格模式
- 在函数级别使用严格模式，避免全局严格模式

```javascript
function foo() {
    'use strict';
    // 函数体
}
```

### 2.4 分号
- 每条语句后必须使用分号
- 不依赖自动分号插入（ASI）机制

```javascript
// 正确
var x = 1;
function foo() {
    return true;
}

// 错误
var x = 1
function foo() {
    return true
}
```

### 2.5 括号和空格
- 控制语句的条件表达式必须用括号括起来
- 括号内侧不要有空格
- 操作符两侧应有空格
- 逗号后应有空格，逗号前不应有空格

```javascript
// 正确
if (condition) {
    // ...
}

var x = 1 + 2;
var arr = [1, 2, 3];

// 错误
if(condition){
    // ...
}

var x=1+2;
var arr=[1,2,3];
```

### 2.6 代码块
- 所有代码块使用花括号，即使只有一行
- 左花括号与相关语句同行，右花括号独占一行

```javascript
// 正确
if (condition) {
    do_something();
}

// 错误
if (condition) do_something();
if (condition)
    do_something();
```

## 3. 对象和数组

### 3.1 对象声明
- 使用字面量创建对象
- 属性名不加引号（除非包含特殊字符）

```javascript
// 正确
var user = {
    name: 'John',
    age: 30,
    'special-key': 'value'
};

// 错误
var user = new Object();
user.name = 'John';
user.age = 30;
```

### 3.2 数组声明
- 使用字面量创建数组

```javascript
// 正确
var items = [1, 2, 3];

// 错误
var items = new Array(1, 2, 3);
```

## 4. 条件语句

### 4.1 相等比较
- 使用 `===` 和 `!==` 而非 `==` 和 `!=`
- 避免使用松散比较

```javascript
// 正确
if (value === 42) {
    // ...
}

// 错误
if (value == 42) {
    // ...
}
```

### 4.2 简化条件
- 使用简写进行布尔值检查

```javascript
// 正确
if (is_valid) {
    // ...
}

// 错误
if (is_valid === true) {
    // ...
}
```

### 4.3 嵌套三元运算符
- 避免使用嵌套的三元运算符
- 保持三元运算符的可读性

```javascript
// 正确
var result = condition ? value1 : value2;

// 错误
var result = condition1 ? value1 : condition2 ? value2 : value3;
```

## 5. 循环和迭代

### 5.1 for 循环
- 在 for 循环中缓存数组长度

```javascript
// 正确
var len = array.length;
for (var i = 0; i < len; i++) {
    // ...
}

// 错误
for (var i = 0; i < array.length; i++) {
    // ...
}
```

### 5.2 for...in 循环
- 只用于遍历对象属性，不用于数组
- 使用 hasOwnProperty 过滤原型链上的属性

```javascript
// 正确
for (var key in obj) {
    if (obj.hasOwnProperty(key)) {
        // ...
    }
}
```

## 6. 注释

### 6.1 单行注释
- 独占一行的注释，使用 `//` 后接一个空格
- 代码尾部的注释，使用 `//` 前后接一个空格

```javascript
// 这是一个独占一行的注释

var foo = 'bar'; // 这是代码尾部的注释
```

### 6.2 多行注释
- 使用 `/* ... */` 进行多行注释

```javascript
/*
 * 这是一个多行注释
 * 第二行
 */
```

### 6.3 文档注释
- 使用 JSDoc 风格的注释

```javascript
/**
 * 函数描述
 * @param {String} name - 参数描述
 * @returns {Boolean} 返回值描述
 */
function do_something(name) {
    // ...
    return true;
}
```

## 7. 错误处理

### 7.1 使用 try-catch
- 正确处理可能发生异常的代码
- 避免空的 catch 块

```javascript
try {
    // 可能出错的代码
} catch (error) {
    // 处理错误
    console.error('发生错误:', error.message);
}
```

## 8. 性能考虑

### 8.1 避免全局变量
- 最小化全局变量的使用
- 使用立即执行函数表达式(IIFE)创建私有作用域

```javascript
// 正确
(function() {
    var private_var = 'I am private';
    // ...
})();

// 错误
var global_var = 'I am global';
```

### 8.2 避免不必要的 DOM 操作
- 将多次 DOM 操作合并
- 使用文档片段(DocumentFragment)

### 8.3 避免过度使用闭包
- 注意内存泄漏风险
- 将不再需要的引用设置为 null

## 9. 常见模式

### 9.1 模块模式
```javascript
var my_module = (function() {
    // 私有变量和函数
    var private_var = 'private';
    
    function private_function() {
        // ...
    }
    
    // 返回公共 API
    return {
        public_function: function() {
            private_function();
            return private_var;
        }
    };
})();
```

### 9.2 构造函数模式
```javascript
function Person(name) {
    this.name = name;
    
    this.say_hello = function() {
        return 'Hello, my name is ' + this.name;
    };
}

var person = new Person('John');
```

### 9.3 原型模式
```javascript
function Person(name) {
    this.name = name;
}

Person.prototype.say_hello = function() {
    return 'Hello, my name is ' + this.name;
};

var person = new Person('John');
```

## 10. 结语

本规范旨在提高代码的可读性、可维护性和一致性。团队成员应当遵守这些规则，确保代码质量。在特殊情况下，如果需要违反规范，应当添加适当的注释说明原因。

# 数据库规范

## 代码风格
- 所有SQL关键字使用大写（如SELECT, INSERT, UPDATE, DELETE）
- SQL语句中的表名和字段名使用小写，并用下划线分隔（snake_case）

## 数据库连接
- 使用连接池管理数据库连接
- 确保所有数据库连接在操作完成后被正确关闭
- 连接参数（如用户名、密码等）应通过环境变量配置，不要硬编码

## 查询实践
- 避免在循环中执行查询，尽量使用批量操作
- 为复杂查询添加详细注释
- 所有查询必须使用参数化查询以防止SQL注入
- 限制SELECT查询返回的列数量，避免使用SELECT *

## 事务处理
- 确保数据一致性的操作必须使用事务
- 事务应尽可能短小，避免长时间锁定表
- 正确处理事务中的错误，确保在异常情况下回滚

## 索引使用
- 经常用于WHERE子句的列应建立索引
- 定期检查索引使用情况，移除未使用的索引
- 避免对频繁更新的列创建过多索引

## 错误处理
- 所有数据库操作必须包含适当的错误处理
- 记录数据库错误，但不将详细错误信息暴露给最终用户
- 实现重试机制处理临时性数据库连接问题

## 性能优化
- 大型查询应该分页处理
- 定期审查和优化慢查询
- 对于频繁访问的只读数据考虑使用缓存