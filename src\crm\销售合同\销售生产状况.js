var apiResL="{\"code\":200,\"msg\":\"success\",\"data\":{\"msg\":\"查询成功\",\"code\":200,\"data\":[{\"delivery_date\":\"2025-05-14\",\"factory_details_id\":\"1001A51000000121LRU6\",\"inkjet_code\":\"已完工\",\"stock_quantity\":0,\"die_cutting\":\"待生产\",\"production_batch_number\":\"L25040560A0101\",\"gluing_box\":\"待生产\",\"isSelectL\":\"true\",\"product_number\":\"ZDZY0000902\"},{\"delivery_date\":\"2025-05-14\",\"print\":\"已完工\",\"cutting\":\"已完工\",\"factory_details_id\":\"1001A51000000121LRU6\",\"finish\":\"2025-05-10 04:48:22\",\"stock_quantity\":0,\"production_batch_number\":\"L25040560A0201\",\"isSelectL\":\"true\",\"product_number\":\"ZDZY0000902\"},{\"delivery_date\":\"2025-05-14\",\"factory_details_id\":\"1001A51000000121LRU7\",\"inkjet_code\":\"已完工\",\"stock_quantity\":0,\"die_cutting\":\"待生产\",\"production_batch_number\":\"L25040560A0101\",\"gluing_box\":\"待生产\",\"isSelectL\":\"true\",\"product_number\":\"SMZY0000207\"},{\"delivery_date\":\"2025-05-14\",\"print\":\"已完工\",\"cutting\":\"已完工\",\"factory_details_id\":\"1001A51000000121LRU7\",\"finish\":\"2025-05-10 04:48:22\",\"stock_quantity\":208000,\"production_batch_number\":\"L25040560A0201\",\"isSelectL\":\"true\",\"product_number\":\"SMZY0000207\"}],\"error\":\"\"},\"errMsg\":null}"
var resDataL=[
    {
        "settlement_currency_amount_exclusive": "26601.80",
        "remark": "与上一批分开生产",
        "settlement_currency_price_exclusive": "0.133009",
        "main_class": "折叠纸盒类",
        "quantity_fluctuation": "+5",
        "tax_rate": "13",
        "quotation_product_id": "5686",
        "settlement_currency_amount_inclusive": "30060.00",
        "makeup_product": "",
        "contract_product_line_number": "1",
        "commission_print_number": "HBYKT250427",
        "stocked_product": "否",
        "id": 3062,
        "material_name": "小儿宝泰康颗粒4克x9袋",
        "product_version": "1.4",
        "amount_exclusive_tax": "26601.80",
        "exchange_rate": "1",
        "sales_plan_delivery_date": "2025-05-13",
        "amount_tax_inclusive": "30060.00",
        "unit_price": "0.1503",
        "total_order_quantity": "200000",
        "settlement_currency": "人民币",
        "sub_class": "中药类",
        "sales_order_code": "25040560A",
        "csaleorderbid": "1001A51000000121LRU6",
        "order_quantity_after_split": "200000",
        "standard_unit": "只",
        "grade_name": " 小儿宝泰康颗粒4克x9袋",
        "material_code": "ZDZY0000902",
        "unit_price_exclusive": "0.133009",
        "settlement_currency_price_inclusive": "0.1503"
    },
    {
        "settlement_currency_amount_exclusive": "2300.80",
        "remark": "与上一批分开生产",
        "settlement_currency_price_exclusive": "0.011504",
        "main_class": "说明书类",
        "quantity_fluctuation": "+5",
        "tax_rate": "13",
        "quotation_product_id": "5687",
        "settlement_currency_amount_inclusive": "2600.00",
        "makeup_product": "",
        "contract_product_line_number": "2",
        "commission_print_number": "HBYKT250427",
        "mnemonic_code": "",
        "stocked_product": "否",
        "id": 3063,
        "material_name": "小儿宝泰康颗粒4克x9袋说明书",
        "product_version": "1.3",
        "amount_exclusive_tax": "2300.80",
        "exchange_rate": "1",
        "sales_plan_delivery_date": "2025-05-13",
        "amount_tax_inclusive": "2600.00",
        "unit_price": "0.013",
        "total_order_quantity": "200000",
        "settlement_currency": "人民币",
        "sub_class": "中药类",
        "sales_order_code": "25040560A",
        "csaleorderbid": "1001A51000000121LRU7",
        "order_quantity_after_split": "200000",
        "standard_unit": "张",
        "grade_name": "小儿宝泰康颗粒4克x9袋说明书",
        "material_code": "SMZY0000207",
        "unit_price_exclusive": "0.011504",
        "settlement_currency_price_inclusive": "0.013"
    }
]
var apiResNewL = JSON.parse(apiResL);
if (!(apiResNewL.code == 200 && apiResNewL.data && apiResNewL.data.code == 200)) {
    return resDataL;
}

var apiDataL = apiResNewL.data.data;
var processPriority = ['inkjet_code', 'print', 'cutting', 'die_cutting', 'gluing_box', 'making_box', 'finish'];

// 预处理：按 factory_details_id 分组
var apiDataMap = {};
for (var i = 0; i < apiDataL.length; i++) {
    var key = apiDataL[i].factory_details_id;
    apiDataMap[key] = apiDataMap[key] || [];
    apiDataMap[key].push(apiDataL[i]);
}

// 主处理逻辑
for (var j = 0; j < resDataL.length; j++) {
    var currentId = resDataL[j].csaleorderbid;
    var matchedData = apiDataMap[currentId];
    if (!matchedData) continue;

    var finalData = {
        stock_quantity: 0,
        factory_plan_delivery_date: '',
        inkjet_code: '',
        print: '',
        cutting: '',
        die_cutting: '',
        gluing_box: '',
        making_box: '',
        finish: ''
    };

    // 单条数据优化路径
    if (matchedData.length === 1) {
        var dataItem = matchedData[0];
        // 直接全量赋值
        finalData.stock_quantity = dataItem.stock_quantity || 0;
        finalData.factory_plan_delivery_date = dataItem.delivery_date || '';
        finalData.production_batch_number = dataItem.production_batch_number || '';
        for (var p = 0; p < processPriority.length; p++) {
            var process = processPriority[p];
            finalData[process] = dataItem[process] || '';
        }
    } 
    // 多条数据处理
    else {
        var highestPriorityIndex = -1;
        var bestMatch = null;
        
        // 找出最高优先级记录
        for (var k = 0; k < matchedData.length; k++) {
            var dataItem = matchedData[k];
            for (var p = processPriority.length - 1; p >= 0; p--) {
                if (dataItem[processPriority[p]] && p > highestPriorityIndex) {
                    highestPriorityIndex = p;
                    bestMatch = dataItem;
                    break;
                }
            }
            if (highestPriorityIndex === processPriority.length - 1) break;
        }

        if (bestMatch) {
            // 按工序优先级赋值
            finalData.stock_quantity = bestMatch.stock_quantity || 0;
            finalData.factory_plan_delivery_date = bestMatch.delivery_date || '';
            finalData.production_batch_number = bestMatch.production_batch_number || '';
            if (finalData.production_batch_number != '')
                finalData.production_batch_number = getValue(finalData.production_batch_number);
            for (var p = 0; p <= highestPriorityIndex; p++) {
                var process = processPriority[p];
                finalData[process] = bestMatch[process] || '';
            }
        }
    }

    // 赋值
    resDataL[j].stock_quantity = finalData.stock_quantity;
    resDataL[j].factory_plan_delivery_date = finalData.factory_plan_delivery_date;
    resDataL[j].inkjet_code = finalData.inkjet_code;
    resDataL[j].print = finalData.print;
    resDataL[j].cutting = finalData.cutting;
    resDataL[j].die_cutting = finalData.die_cutting;
    resDataL[j].gluing_box = finalData.gluing_box;
    resDataL[j].making_box = finalData.making_box;
    resDataL[j].finish = finalData.finish;
}
console.log(JSON.stringify(resDataL))
return resDataL;

function getValue(str) {
  return str.replace(/.{2}$/, '');
}