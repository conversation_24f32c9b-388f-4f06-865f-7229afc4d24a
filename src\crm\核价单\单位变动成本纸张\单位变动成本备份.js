var materialResult = JSON.parse(JSON.stringify(obj.materialResult))
var paperlArr = []
var paperCodeArr = []
var craftArr = JSON.parse(JSON.stringify(obj.craftArr)) // 传进来的参数工艺路线
var inCarftArr = [] //需要存核价单的工艺路线
var bjArr = []
var parent_code = ""
for (var x = 0; x < craftArr.length ; x++) {
  if (craftArr[x].material_json_code) {
    var materialCode = ""
    var material_json_code = craftArr[x].material_json_code
    var flag = ""
    if (material_json_code.indexOf(",") != -1) {
      var jsonCode = material_json_code.split(",")
      for (var y = 0; y < jsonCode.length; y++) {
        var index = jsonCode[y].indexOf("编码：")
        var index2 = jsonCode[y].indexOf(" 名称：")
        materialCode = jsonCode[y].substring(index+3,index2)
        for (var z = 0; z < materialResult.length; z++) {
          if (materialCode == materialResult[z].material_code) {
            if (materialResult[z].categroy == 1) {
              if (flag == "") {
                flag = materialCode
              }
            }
          }
        }
      }
    }else{
      var index = material_json_code.indexOf("编码：")
      var index2 = material_json_code.indexOf(" 名称：")
       materialCode = material_json_code.substring(index+3,index2)
      for (var z = 0; z < materialResult.length; z++) {
          if (materialCode == materialResult[z].material_code) {
            if (materialResult[z].categroy == 1) {
              flag = materialCode
            }
          }
        }
    }
    if (flag != "") {
       paperCodeArr.push(flag)
       craftArr[x]['flag'] = true
       craftArr[x]['paperMaterialCode'] = flag
    }else{
      craftArr[x]['flag'] = false
      craftArr[x]['paperMaterialCode'] = ""
    }
  }
}
//数组去重
var uniqueArray = []
for (var i = 0; i < paperCodeArr.length; i++) {  
  var isDuplicate = false;  
  for (var j = 0; j < uniqueArray.length; j++) {  
    if (paperCodeArr[i] === uniqueArray[j]) {  
      isDuplicate = true;  
      break;  
    }  
  }  
  if (!isDuplicate) {  
    uniqueArray.push(paperCodeArr[i]);  
  }  
}  

paperCodeArr =  uniqueArray
for (var x = 0; x < materialResult.length; x++) {
  if(materialResult[x].categroy == 1){
    materialResult[x]['paper_specification'] = materialResult[x].specification
    materialResult[x]['paper_number'] = materialResult[x].material_code
    materialResult[x]['paper_cutting_number'] = materialResult[x].kaishu  //开数
    for (var z = 0; z < crm_price_manager.length; z++) {
      if (materialResult[x].material_code == crm_price_manager[z].material_code) {
        materialResult[x]['selling_price'] = (crm_price_manager[z].selling_price*1).toFixed(4)
        break
      }
    }
    if (!materialResult[x].selling_price) {
     materialResult[x]['selling_price'] = 0
    }
    for (var y = 0; y < paperCodeArr.length; y++) {
     if (materialResult[x].cut_size) {
     var cutArr = materialResult[x].cut_size.split("*")
     materialResult[x]['fqcc'] = cutArr[0]*1*cutArr[1]*1
    }else{
     materialResult[x]['fqcc'] = 0
    }
    if (materialResult[x].material_code == paperCodeArr[y]) {
      if (materialResult[x].pushing) {
      var pushing = materialResult[x].pushing
      var kaishu = null
      if (pushing.indexOf(",") != -1) {
        var arr = pushing.split(",")
        for (var y = 0; y < arr.length; y++) {
          var index = arr[y].indexOf("(")
          var index2 = arr[y].indexOf(")")
          kaishu += arr[y].substring(index+1,index2)*1
        }
      }else{
          var index = pushing.indexOf("(")
          var index2 = pushing.indexOf(")")
          kaishu = pushing.substring(index+1,index2)*1
      }
      materialResult[x]['kaishu'] = kaishu
    }else{
      materialResult[x]['kaishu'] = 0
    }
      paperlArr.push(materialResult[x])
    }
  }
  }
  
  if (materialResult[x].categroy == 3 || materialResult[x].categroy == 0 || materialResult[x].categroy == 7) {
    bjArr.push(materialResult[x])
  }
}
var max_out_of_book = 0
var multi_product = {};
for (var i = 0; i < craftArr.length; i++) {
  if(craftArr[i].sub_code){
    if (craftArr[i].process_type == "1") {
      if(!multi_product[craftArr[i].sub_code]){
        multi_product[craftArr[i].sub_code]=0
      }
    if (craftArr[i].out_of_book) {
      if (Number(craftArr[i].out_of_book) > multi_product[craftArr[i].sub_code]) {
        multi_product[craftArr[i].sub_code] = Number(craftArr[i].out_of_book)
      }
    }
  }
  }else{
    if (craftArr[i].process_type == "1") {
    if (craftArr[i].out_of_book) {
      if (Number(craftArr[i].out_of_book) > max_out_of_book) {
        max_out_of_book = Number(craftArr[i].out_of_book)
      }
    }
  }
  }
  
}
for (var x = 0; x < craftArr.length; x++) {
  if (craftArr[x].flag == true) {
    var obj = {}
    obj['categroy'] = "10"
    obj['parent_code'] = craftArr[x].parent_code
    obj['craft_name'] = craftArr[x].craft_name
    if (craftArr[x].process_type == "1") {   //大张工序
      if(craftArr[x].sub_code){
        obj['out_of_book'] = multi_product[craftArr[x].sub_code];
      }else{
        obj['out_of_book'] = max_out_of_book //出本
      }
    }else{
      obj['out_of_book'] = craftArr[x].out_of_book*1 //出本
    }
    
    for (var y = 0; y < paperlArr.length; y++) {
      if (craftArr[x].paperMaterialCode == paperlArr[y].material_code && craftArr[x].parent_code == paperlArr[y].parent_code) {
        obj['process_type'] = craftArr[x].process_type
        obj['paper_specification'] = paperlArr[y].paper_specification
        obj['paper_number'] = paperlArr[y].paper_number
        obj['paper_cutting_number'] = paperlArr[y].kaishu  //开数
        obj['main_class'] = paperlArr[y].main_class
        if (paperlArr[y].main_class == "卷筒") {
          obj['single_product_unit_usage'] =  1/Number(obj.out_of_book)/Number(obj.paper_cutting_number)*(Number(paperlArr[y].fqcc)/1000000)*Number(paperlArr[y].gram_weight)/1000*1  //单位用量单位
        }else if (paperlArr[y].main_class == "平张") {
          obj['single_product_unit_usage'] = 1/Number(obj.out_of_book)/Number(obj.paper_cutting_number)
        }
        if(count_obj[obj['parent_code']]){
          obj.single_product_unit_usage = (obj.single_product_unit_usage*eval(count_obj[obj['parent_code']])).toFixed(12)
        }else{
          obj.single_product_unit_usage = obj.single_product_unit_usage.toFixed(12)
        }
        obj['material_unit_price'] = paperlArr[y].selling_price
        obj['amount'] = (obj.single_product_unit_usage * obj.material_unit_price).toFixed(12)
        inCarftArr.push(obj)
        break
      }
    }
  }
}
var inArr = []
inArr = inArr.concat(paperlArr)
inArr = inArr.concat(inCarftArr)
inArr = inArr.concat(bjArr)
for(var k=0;k<inArr.length;k++){
  inArr[k].quotation_code = quotation_code;
  inArr[k].new_version = new_version;
  inArr[k].version = "1.0";
}
return inArr