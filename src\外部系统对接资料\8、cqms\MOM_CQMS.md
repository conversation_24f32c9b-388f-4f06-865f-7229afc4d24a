# CQMS_MES_API (1911612066490224640)  使用文档

## 概述

这份文档详细描述了如何使用API（CQMS_MES_API），包含了API的详细解释、调试示例、调用示例。

---

## API说明

#### API名称: CQMS_MES_API

**API ID:** 1911612066490224640

**API类型:** guide

**请求类型:** `POST`

**请求URL:** http://172.16.32.11/invoker_engine/query_with_params

**请求参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| 榨季 | string | false | "\"2023/24\"" | 对应榨季 |
| pageSize | int | false | -- | 分页个数 |
| pageNum | int | false | -- | 分页页码 |

```
{
    "ApiID": "1911612066490224640",
    "Option": [
        {
            "Id": 100,
            "Val": 0,
            "Val_": "eyJhbGciOiJBRVMiLCJ0eXAiOiJKV1QifQ.eyJhcHAiOiJsaW1zLnRvLm1lcyIsImlhdCI6MTgsInRpZCI6IjIiLCJ1bml4IjoxNzQyNTQ2MDU4fQ.Djlt6wprQPEua-XRQR-pCEAAC8H5LehpJ0Awu_ul8jbkWHqi70VjP8omQ66H1SQzu2cMNppRsx_f7-sHSlub6WPlE8EZ7cbrgeMPcDkSo4NiXazTM0UkFSjMyYfobNSlVdBjKnoiY-DBBeqkQ6R9Xnk"
        }
    ],
    "Params": {
    "榨季": "2024/25",
    "pageNum": 5,
    "pageSize": 10
}
}
```

**返回参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| 榨季 | string | false | -- | -- |
| 工厂代码 | string | false | -- | -- |
| 进厂时间 | date | false | -- | -- |
| 人工砍蔗甘蔗简纯度 | double | false | -- | -- |
| 机收蔗甘蔗简纯度 | double | false | -- | -- |
| 人工砍蔗甘蔗糖分 | double | false | -- | -- |
| 机收蔗甘蔗糖分 | double | false | -- | -- |
| 当日榨季累计人工砍蔗简纯度 | double | false | -- | -- |
| 当日榨季累计人工砍蔗甘蔗糖分 | double | false | -- | -- |
| 当日榨季累计机收蔗简纯度 | double | false | -- | -- |
| 当日榨季累计机收蔗甘蔗糖分 | double | false | -- | -- |
| 甘蔗品种名称 | string | false | -- | -- |
| 各甘蔗品种当日糖分 | double | false | -- | -- |
| 各甘蔗品种榨季累计糖分 | double | false | -- | -- |
| 甘蔗糖分 | double | false | -- | -- |


**正常响应示例:**

```json
{
	"Fields": [
		{
			"Name": "榨季",
			"Type": 3
		},
		{
			"Name": "工厂代码",
			"Type": 3
		},
		{
			"Name": "进厂时间",
			"Type": 4
		},
		{
			"Name": "人工砍蔗甘蔗简纯度",
			"Type": 2
		},
		{
			"Name": "机收蔗甘蔗简纯度",
			"Type": 2
		},
		{
			"Name": "人工砍蔗甘蔗糖分",
			"Type": 2
		},
		{
			"Name": "机收蔗甘蔗糖分",
			"Type": 2
		},
		{
			"Name": "当日榨季累计人工砍蔗简纯度",
			"Type": 2
		},
		{
			"Name": "当日榨季累计人工砍蔗甘蔗糖分",
			"Type": 2
		},
		{
			"Name": "当日榨季累计机收蔗简纯度",
			"Type": 2
		},
		{
			"Name": "当日榨季累计机收蔗甘蔗糖分",
			"Type": 2
		},
		{
			"Name": "甘蔗品种名称",
			"Type": 3
		},
		{
			"Name": "各甘蔗品种当日糖分",
			"Type": 2
		},
		{
			"Name": "各甘蔗品种榨季累计糖分",
			"Type": 2
		},
		{
			"Name": "甘蔗糖分",
			"Type": 2
		}
	],
	"Data": "[[\"2024/25\",\"CZ        \",1732723200,75908.60874899998,1325.337851,13136.079999999984,226.04000000000002,689539.6210399988,118776.23000000019,4732.351812,806.71,\"桂糖49号            \",13.89,13.89,13362.11999999999],[\"2024/25\",\"CZ        \",1732723200,75908.60874899998,1325.337851,13136.079999999984,226.04000000000002,689539.6210399988,118776.23000000019,4732.351812,806.71,\"桂糖55号            \",154.85,997.9100000000001,13362.11999999999],[\"2024/25\",\"CZ        \",1732809600,184313.65750900048,1578.7469649999994,31906.129999999874,271.22999999999996,873853.2785489993,150682.36000000004,6311.098776999999,1077.94,\"中蔗9号             \",39.879999999999995,146.89,32177.359999999873],[\"2024/25\",\"CZ        \",1732809600,184313.65750900048,1578.7469649999994,31906.129999999874,271.22999999999996,873853.2785489993,150682.36000000004,6311.098776999999,1077.94,\"桂柳05136           \",8264.819999999983,33718.25999999999,32177.359999999873],[\"2024/25\",\"CZ        \",1732809600,184313.65750900048,1578.7469649999994,31906.129999999874,271.22999999999996,873853.2785489993,150682.36000000004,6311.098776999999,1077.94,\"桂糖42号            \",23501.619999999963,115959.51000000002,32177.359999999873],[\"2024/25\",\"CZ        \",1732809600,184313.65750900048,1578.7469649999994,31906.129999999874,271.22999999999996,873853.2785489993,150682.36000000004,6311.098776999999,1077.94,\"桂糖44号            \",228.96,696.82,32177.359999999873],[\"2024/25\",\"CZ        \",1732809600,184313.65750900048,1578.7469649999994,31906.129999999874,271.22999999999996,873853.2785489993,150682.36000000004,6311.098776999999,1077.94,\"桂糖55号            \",142.08,1139.99,32177.359999999873],[\"2024/25\",\"CZ        \",1732896000,183831.87805200042,2078.0043110000006,31951.58000000008,353.03000000000003,1057685.1566009996,182633.94000000012,8389.103088,1430.97,\"中糖3号             \",73.4,73.4,32304.610000000077],[\"2024/25\",\"CZ        \",1732896000,183831.87805200042,2078.0043110000006,31951.58000000008,353.03000000000003,1057685.1566009996,182633.94000000012,8389.103088,1430.97,\"中蔗9号             \",39.59,186.48,32304.610000000077],[\"2024/25\",\"CZ        \",1732896000,183831.87805200042,2078.0043110000006,31951.58000000008,353.03000000000003,1057685.1566009996,182633.94000000012,8389.103088,1430.97,\"桂柳05136           \",8347.03,42065.289999999986,32304.610000000077]]",
	"TimeCost": 48,
	"DataCnt": 10,
	"ErrorCode": "",
	"RenderedSql": "",
	"Format": "JSONCompact",
	"BaseResp": {
		"StatusMessage": "",
		"StatusCode": 0,
		"Extra": {
			"data_size": "2365",
			"time_cost": "48"
		}
	}
}
```

**错误响应示例:**

```json
{
    "Fields": [],
    "Data": "",
    "TimeCost": 3,
    "DataCnt": 0,
    "ErrorCode": "",
    "BaseResp": {
        "StatusMessage": "[10001] api access denied ",
        "StatusCode": 4
    }
}
```

**错误代码:**

## ErrorCode 常见状态码相关：
|  |  |  |
| --- | --- | --- |
| 状态码 | 名称 | 含义 |
| 0 | QueryErrorType_OK | 查询成功 |
| 1 | QueryErrorType_PARSER_ERROR | 解析报错 |
| 2 | QueryErrorType_ILLGEAL_INPUT_ERROR | 非法参数报错 |
| 3 | QueryErrorType_RATE_LIMIT_ERROR | 限流报错 |
| 4 | QueryErrorType_AUTH_ERROR | 权限报错 |
| 5 | QueryErrorType_QUERY_TIMEOUT | 查询超时报错 |
| 6 | QueryErrorType_DS_TIMEOUT | 数据源超时报错 |
| 7 | QueryErrorType_INTERNAL_ERROR | 程序内部报错 |
| 8 | QueryErrorType_META_ERROR | 元信息报错 |
| 9 | QueryErrorType_DS_RATE_LIMIT_ERROR | 数据源限流报错 |
| 255 | QueryErrorType_UNKNOWN_ERROR | 未知错误 |
## One Service 常见错误码相关：
|  |  |  |
| --- | --- | --- |
| 错误码 | 名称 | 含义 |
| 10000 | UnknownQueryEngine | 未知查询引擎 |
| 10001 | NoPermission | 没有权限 |
| 10002 | MetaErr | 元信息错误 |
| 10003 | ParamsParseErr | 参数解析错误 |
| 10004 | ApiRateLimitExceed | Api的QPS超限额 |
| 10005 | ParseErr | 解析错误 |
| 10006 | ExecuteErr | 执行错误 |
| 10007 | UnknownQueryType | 未知查询类型 |
| 10008 | QueryRequestError | 查询请求错误 |
| 10009 | QueryEngineMismatchError | 查询引擎不匹配错误 |


**调用说明:**

###### HTTP调用示例
```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  http://172.16.32.11/invoker_engine/query_with_params \
  -d '{"ApiID": "1911612066490224640","Option": [{"Id":100,"Val":0,"Val_":"$DPS_TOKEN"}],"Params": {"榨季":"\"2023/24\"","pageSize":0,"pageNum":0}}'
```

###### 如果使用动态密钥功能,可以使用如下接口动态获取token信息

```
curl -X POST \
  -H 'user:yushumeng' \
  --header 'Content-Type: application/json' \
  http://172.16.32.11/data_service/api/v2/api_service/token \
  -d '{"AppKey": "you_app_key","AppSecret": "you_app_secret"}'
```
###### 返回样例：
```
{
    "code": 0,
    "message": "Success",
    "data": {
        "AppKey": "you_app_key",
        "Token": "token_str"
    },
    "meta": {
        "text": "",
        "redirect": ""
    }
}
```
###### 解析其中的Token 调用的时候将$DPS_TOKEN替换为“该应用密钥”，动态token有一定有效期且会动态变化，请不要缓存动态token或者对动态token有任何假设，每次请求获取即可

**开启分页调用说明:**

向导式API分页调用说明
1、在API页面开启高级配置的分页，在API页面测试时系统会自动添加pageNum和pageSize参数。
2、调用API的时候需要将pageNum和pageSize填入请求参数中。
例如：{
  "pageNum": 1,
  "pageSize": 100
}

脚本式API分页调用说明
1、用SQL来进行分页操作，例如：SELECT * FROM table ORDER BY id LIMIT 10 OFFSET 0;
2、如果想要获取本次查询的total总数，请参考：https://bytedance.larkoffice.com/docx/HJKudzKHVoEAejxAmUncZ1LanIc

**API调用常见问题:**

1、api xxx meta not exists
答：一般是因为API没有在对应的环境发布，可在API页面版本信息中查看是否已发布到对应环境。

2、10001 api access denied
答：API调用时未输入DpsToken或者DpsToken与已对API授权的应用不一致。

---

## 使用 Postman 调试API说明

Postman是一个强大的HTTP API测试工具，可以帮助开发者快速开发和测试API。

### POST 请求示例:

1. 打开Postman应用程序。
2. 在请求方式空间中选择 `POST`。
3. 在请求URL空间中输入 http://172.16.32.11/invoker_engine/query_with_params，然后点击 `Body` 的选项卡，然后选择 `x-www-form-urlencoded`。
4. 在 `Key` 中输入参数名称，`Value` 中输入参数值，然后点击 `Send` 按钮。
5. 如果一切正常，则会在下方的 `Body` 中看到响应结果。

---

## 使用 Java 调用API说明

如果你在Java编程语言调用API，可以使用HttpClient等库。

### HttpClient库为例子，POST请求的代码示例

```java

      
// 导入所需的库
package org.example; // 你的包名，根据实际情况替换

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import java.io.IOException;

public class HTTPClientPOSTRequest {

    // 主函数
    public static void main(String[] args) throws IOException {
        // 定义请求的URL
        String baseUrl = "http://172.16.32.11/invoker_engine/query_with_params";

        // 创建JSON对象来表示Option
        JSONObject option = new JSONObject();
        option.put("Id", 100); //定义option键值对
        option.put("Val", 0);
        option.put("Val_", "$DPS_TOKEN");

        // 创建JSON对象来表示整个API请求
        JSONObject apiRequest = new JSONObject();
        apiRequest.put("ApiID", "1911612066490224640"); //设置ApiID
        apiRequest.put("Option", new JSONObject[]{option}); //将Option整体放入ApiRequest
        apiRequest.put("Params", "{\"榨季\":\"\\"2023/24\\"\",\"pageSize\":0,\"pageNum\":0}"); //将Params整体放入ApiRequest

        // 执行HTTP POST 请求
        performPostRequest(baseUrl, apiRequest.toString());
    }

    // 定义一个函数执行HTTP POST请求
    public static void performPostRequest(String url, String jsonInputString) throws IOException {
        // 创建一个HTTP客户端
        CloseableHttpClient client = HttpClients.createDefault();

        // 创建一个HTTP POST请求
        HttpPost httpPost = new HttpPost(url);

        // 创建一个实体协助发送POST数据，并设置实体
        StringEntity entity = new StringEntity(jsonInputString);
        httpPost.setEntity(entity);

        // 执行POST请求，并获取响应
        CloseableHttpResponse response = client.execute(httpPost);

        // 从响应中获取实体
        HttpEntity responseEntity = response.getEntity();

        // 如果响应实体存在，输出其内容
        if (responseEntity != null) {
            System.out.println(EntityUtils.toString(responseEntity));
        }

        // 关闭HTTP客户端
        client.close();
    }
}
/*
需要在pom.xml中添加的dependency如下
<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpclient</artifactId>
    <version>4.5.13</version>
</dependency>

<!-- https://mvnrepository.com/artifact/org.json/json -->
<dependency>
    <groupId>org.json</groupId>
    <artifactId>json</artifactId>
    <version>20210307</version>
</dependency>
 */
```