WITH deduplicated_sales AS (
    SELECT DISTINCT 
        ccb.cust_code,
        ccs.sales_code,
        NULLIF(CAST(REPLACE(REPLACE(ccs.target_percentage, '%', ''), ' ', '') AS DECIMAL), 0) as cleaned_percentage
    FROM crm_cust_basic ccb
    LEFT JOIN crm_cust_sale ccs 
        ON ccb.cust_mnemonic_code = ccs.cust_mnemonic_code 
        AND ccb.cust_version = ccs.cust_version
    WHERE cust_status = 2 
        AND cust_type NOT IN (0,1)
        AND ccb.flag_deleted = 0
        AND ccs.flag_deleted = 0
)
SELECT 
    cust_code,
    sales_code,
    COALESCE(
        cleaned_percentage,
        CASE 
            WHEN SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END) OVER (PARTITION BY cust_code) 
                 = COUNT(*) OVER (PARTITION BY cust_code)
            THEN 100.0 / COUNT(*) OVER (PARTITION BY cust_code)
            ELSE (100 - SUM(COALESCE(cleaned_percentage, 0)) OVER (PARTITION BY cust_code)) /
                 NULLIF(SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END) OVER (PARTITION BY cust_code), 0)
        END,
        0
    ) as target_percentage
FROM deduplicated_sales