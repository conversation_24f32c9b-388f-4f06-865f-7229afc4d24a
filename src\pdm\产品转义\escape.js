function escapeHtmlEntities(str) {
    if(!str) return '';
str = str
     .replace("\n", "") // 转义换行
     .replace(/&/g, "&amp;")
     .replace(/</g, "&lt;")
     .replace(/>/g, "&gt;")
     .replace(/"/g, "&quot;")
     .replace(/'/g, "&apos;");

 // 转义正则表达式中的其他特殊字符
 str = str.replace(/[+?^${}|[\]\\]/g, "\\$&");
 str = str.replace(/\u0002/g, '\\u0002');
 return str;
}
var content=''
console.log(escapeHtmlEntities(content))