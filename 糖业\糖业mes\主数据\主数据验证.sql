-- 客户信息
select * from mdm_information
where type=1;
SELECT
    code_srm,
    COUNT(*) as duplicate_count
FROM
    mdm_information
WHERE
    type = 2  -- 只查询 type = 2 的记录
GROUP BY
    code_sap
HAVING
    COUNT(*) > 1  -- 只显示重复的 code_sap
ORDER BY
    duplicate_count DESC;
-- 供应商信息
select * from mdm_information
where type=2;
SELECT
    code_sap,
    COUNT(*) as duplicate_count
FROM
    mdm_information
WHERE
    type = 2  -- 只查询 type = 2 的记录
GROUP BY
    code_srm
HAVING
    COUNT(*) > 1  -- 只显示重复的 code_sap
ORDER BY
    duplicate_count DESC;
-- 产品信息
select * from mdm_material_information
where material_product_type=0;

# delete from mdm_material_information
# where material_product_type=0;

-- 物料信息
select * from mdm_material_information
where
 code in (
    'N120104190021',
        'N120112821212',
'N120200020149'

    )
;


select code from  mdm_material_information
where material_product_type=1 group by code having count(*)>1;
