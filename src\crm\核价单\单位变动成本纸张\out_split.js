var obj = { "craftArr": [{ "craft_name": "原料分切", "work_name": "GPFQZX", "indexs": "ZDQX00014810000002", "technological_parameter": "[]", "operation_unit": "张", "product_code": "ZDQX0001481", "minimum_consumption": 20, "create_by": "2", "update_time": "2025-02-14 18:04:08", "rate": 0, "parent_code": "ZDQX0001481", "ban_json_code": "版编号：ZDQX0001481_1_0_01类型：印版类别：CTP版版名称：测试色次CTP版出本：10,版编号：ZDQX0001481_1_2_01类型：模切版类别：模切版版名称：测试色次模切版出本：7", "r_craft_code": "D0035", "process_type": "1", "ys_fixed_time": 0, "id": 0, "update_by": "2", "material_name": "编码：1010101010000010 名称：787 400g 宁波金丽白卡 index：ZDQX00014810000001", "source_name": "GP分切中心", "r_work_code": "GPFQZX", "flag_deleted": 0, "ys_standing_time": 0, "product_version": "1.7", "work_code": "GPFQZX", "create_time": "2025-02-14 18:04:08", "craft_code": "D0035", "count": "1", "index": "ZDQX00014810000002", "material_json_code": "编码：1010101010000010 名称：787 400g 宁波金丽白卡", "version": "1.0", "ys_fixed_rate": 0, "out_of_book": 10, "fixed_consumption": 0, "ys_manual_speed": 0, "source_code": "GPFQZX" }, { "craft_name": "印刷", "work_name": "GPYSKBSJYSZX", "indexs": "ZDQX00014810000003", "technological_parameter": "[]", "operation_unit": "张", "product_code": "ZDQX0001481", "minimum_consumption": 150, "create_by": "2", "update_time": "2025-02-14 18:04:08", "rate": 0, "parent_code": "ZDQX0001481", "ban_json_code": "版编号：ZDQX0001481_1_2_01类型：模切版类别：模切版版名称：测试色次模切版出本：7", "r_craft_code": "D0102", "process_type": "1", "ys_fixed_time": 0, "id": 1, "update_by": "2", "material_name": "编码：1010201010000008 名称：新金冠 蓝（高光） index：ZDQX00014810000004,编码：1010101010000010 名称：787 400g 宁波金丽白卡 index：ZDQX00014810000001", "source_name": "GP可变数据印刷中心", "r_work_code": "GPYSKBSJYSZX", "flag_deleted": 0, "ys_standing_time": 0, "product_version": "1.7", "work_code": "GPYSKBSJYSZX", "create_time": "2025-02-14 18:04:08", "craft_code": "D0102", "count": "1", "index": "ZDQX00014810000003", "material_json_code": "编码：1010201010000008 名称：新金冠 蓝（高光）,编码：1010101010000010 名称：787 400g 宁波金丽白卡", "version": "1.0", "ys_fixed_rate": 0, "out_of_book": "7", "fixed_consumption": 0, "ys_manual_speed": 0, "source_code": "GPYSKBSJYSZX" }, { "craft_name": "覆膜", "work_name": "GPZHRGXTZX", "technological_parameter": "[]", "operation_unit": "张", "product_code": "ZDQX0001481", "ban_id": "", "minimum_consumption": 35, "rate": 0, "parent_code": "ZDQX0001481_C1", "ban_json_code": "版编号：ZDQX0001481_1_0_03类型：印版类别：CTP版版名称：测试色次CTP版出本：18", "process_type": "1", "r_craft_code": "D0103", "ys_fixed_time": 0, "id": 2, "material_name": "编码：1010101010000007 名称：770 380g 瑞典伊格森德Invercote G白卡 index：ZDQX0001481_C10000011", "r_work_code": "GPZHRGXTZX", "ys_standing_time": 0, "product_version": "1.7", "work_code": "GPZHRGXTZX", "craft_code": "D0103", "count": 1, "index": "ZDQX0001481_C10000024", "material_json_code": "编码：1010101010000007 名称：770 380g 瑞典伊格森德Invercote G白卡", "ys_fixed_rate": 0, "out_of_book": "18", "fixed_consumption": 0, "ys_manual_speed": 0 }, { "craft_name": "粘盒", "work_name": "GPZHRGXTZX", "technological_parameter": "[]", "operation_unit": "张,只", "product_code": "ZDQX0001481", "ban_id": "", "minimum_consumption": 500, "rate": 0, "parent_code": "ZDQX0001481_C1", "ban_json_code": "版编号：ZDQX0001481_1_0_03类型：印版类别：CTP版版名称：测试色次CTP版出本：18", "process_type": "2", "r_craft_code": "D0036", "ys_fixed_time": 0, "id": 3, "material_name": "编码：1010101010000007 名称：770 380g 瑞典伊格森德Invercote G白卡 index：ZDQX0001481_C10000011", "r_work_code": "GPZHRGXTZX", "ys_standing_time": 0, "product_version": "1.7", "work_code": "GPZHRGXTZX", "craft_code": "D0036", "count": 1, "index": "ZDQX0001481_C10000025", "material_json_code": "编码：1010101010000007 名称：770 380g 瑞典伊格森德Invercote G白卡", "ys_fixed_rate": 0, "out_of_book": 1, "fixed_consumption": 0, "ys_manual_speed": 0 }], "banArr": [{ "flag": false, "edition_name": "测试色次模切版", "product_code": "ZDQX0001481", "edition_type_id": "554", "edition_categroy": "832", "create_by": "2", "edition_cleanliness": "", "update_time": "2025-02-14 18:04:06", "edition_code": "ZDQX0001481_1_2_01", "relevance_material_code": "1010201010000010", "parent_code": "ZDQX0001481", "share": 0, "id": 0, "update_by": "2", "edition_categroy_cn": "模切版", "flag_deleted": 0, "product_version": "1.6", "create_time": "2025-02-14 18:04:06", "color_sequence": "1", "version": "1.0", "product_name": "ZDQX0001481", "edition_categroy_id": "832", "relevance_material_name": "新金冠 蓝（高光）", "perimeter": 0, "out_of_book": "7", "print_rate": "700000", "edition_type": "554", "edition_type_cn": "模切版", "chromatic_degree": "1/1", "material_code": "ZDQX0001481" }, { "flag": false, "edition_name": "测试色次CTP版", "product_code": "ZDQX0001481", "edition_type_id": "553", "edition_categroy": "829", "create_by": "2", "update_time": "2025-02-14 18:04:06", "edition_code": "ZDQX0001481_1_0_03", "relevance_material_code": "1010201010000006", "parent_code": "ZDQX0001481", "share": 0, "id": 1, "update_by": "2", "edition_categroy_cn": "CTP版", "flag_deleted": 0, "product_version": "1.6", "create_time": "2025-02-14 18:04:06", "color_sequence": "1", "version": "1.0", "product_name": "ZDQX0001481_C1", "edition_categroy_id": "829", "relevance_material_name": "新金冠 红（高光）", "perimeter": 0, "out_of_book": "18", "print_rate": "40000", "edition_type": "553", "edition_type_cn": "印版", "chromatic_degree": "1/1", "material_code": "ZDQX0001481_C1" }, { "flag": false, "edition_name": "测试色次CTP版", "product_code": "ZDQX0001481", "edition_type_id": "553", "edition_categroy": "829", "create_by": "2", "update_time": "2025-02-14 18:04:06", "edition_code": "ZDQX0001481_1_0_01", "relevance_material_code": "1010201010000009", "parent_code": "ZDQX0001481", "share": 0, "id": 2, "update_by": "2", "edition_categroy_cn": "CTP版", "flag_deleted": 0, "product_version": "1.6", "create_time": "2025-02-14 18:04:06", "color_sequence": "1", "version": "1.0", "product_name": "ZDQX0001481", "edition_categroy_id": "829", "relevance_material_name": "新金冠 黄（高光）", "perimeter": 0, "out_of_book": "10", "print_rate": "40000", "edition_type": "553", "edition_type_cn": "印版", "chromatic_degree": "1/1", "material_code": "ZDQX0001481" }], "materialResult": [{ "standard_unit_cn": "mm", "procure_unit": "9077", "remark": "", "product_code": "ZDQX0001481", "create_by": "2", "update_time": "2025-02-14 18:04:06", "id": 950949, "component_count": "1", "update_by": "2", "material_name": "测试色次", "flag_deleted": 0, "bom_type": "产品", "product_version": "1.7", "procure_unit_cn": "mm", "create_time": "2025-02-14 18:04:06", "categroy": 3, "color_sequence": "1", "product_size": "1.0*1.0*1.0", "version": "1.0", "component_size": "1.0*1.0*1.0", "spread_size": "1*1", "standard_unit": "9077", "chromatic_degree": "1/1", "material_code": "ZDQX0001481" }, { "standard_unit_cn": "测试api-2", "procure_unit": "6566", "remark": "", "product_code": "ZDQX0001481", "color_order_quotation": "1(1)", "create_by": "2", "update_time": "2025-02-14 18:04:06", "makeup_product": 0, "parent_code": "ZDQX0001481", "id": 950950, "component_count": "1/1", "update_by": "2", "material_name": "测试色次1", "flag_deleted": 0, "bom_type": "部件", "product_version": "1.7", "procure_unit_cn": "测试api-2", "create_time": "2025-02-14 18:04:06", "categroy": 0, "color_sequence": "1", "product_size": "1*1*1", "version": "1.0", "component_size": "1*1*1", "spread_size": "1*1", "part_information": "", "standard_unit": "6566", "chromatic_degree": "1/1", "material_code": "ZDQX0001481_C1" }, { "standard_unit_cn": "米", "indexs": "ZDQX0001481_C10000011", "product_code": "ZDQX0001481", "main_class": "卷筒", "create_by": "2", "is_off_side": 0, "update_time": "2025-02-14 18:04:06", "parent_code": "ZDQX0001481_C1", "mnemonic_code": "J07670470050006", "id": 950951, "component_count": "1", "update_by": "2", "material_name": "770 380g 瑞典伊格森德Invercote G白卡", "flag_deleted": 0, "fixed_amount": "0", "product_version": "1.7", "create_time": "2025-02-14 18:04:06", "dosage_unit": "1", "categroy": 1, "gram_weight": "380", "index": "ZDQX0001481_C10000011", "specification": "770", "version": "1.0", "component_size": "1*1", "cut_size": "1*1", "pushing": "1*2(1)", "direct_material": 0, "sub_class": "白卡纸", "consumption_rate": "0", "part_information": "", "standard_unit": "米", "slitting": 0, "material_code": "1010101010000007" }, { "standard_unit_cn": "米", "indexs": "ZDQX00014810000001", "product_code": "ZDQX0001481", "main_class": "卷筒", "create_by": "2", "is_off_side": 0, "update_time": "2025-02-14 18:04:06", "parent_code": "ZDQX0001481", "mnemonic_code": "J07700220610006", "id": 950952, "component_count": "1", "update_by": "2", "material_name": "787 400g 宁波金丽白卡", "flag_deleted": 0, "fixed_amount": "0", "product_version": "1.7", "create_time": "2025-02-14 18:04:06", "dosage_unit": "1", "categroy": 1, "gram_weight": "400", "index": "ZDQX00014810000001", "specification": "787", "version": "1.0", "component_size": "1*1", "cut_size": "1*1", "pushing": "1*1(2)", "direct_material": 0, "sub_class": "白卡纸", "consumption_rate": "0", "part_information": "", "standard_unit": "米", "slitting": 0, "material_code": "1010101010000010" }, { "consume_unit": "6588", "standard_unit_cn": "kg", "indexs": "ZDQX00014810000004", "product_code": "ZDQX0001481", "main_class": "油墨类", "dosage_unit_unit_cn": "kg/万", "create_by": "2", "update_time": "2025-02-14 18:04:06", "parent_code": "ZDQX0001481", "mnemonic_code": "010100070", "id": 950953, "component_count": "1", "update_by": "2", "material_name": "新金冠 蓝（高光）", "flag_deleted": 0, "fixed_amount": "0", "product_version": "1.7", "create_time": "2025-02-14 18:04:06", "dosage_unit": "0.000000", "categroy": 2, "consume_unit_cn": "kg", "index": "ZDQX00014810000004", "specification": "200kg/桶", "version": "1.0", "component_size": "200kg/桶", "consume_round_up": 0, "direct_material": 0, "sub_class": "普通油墨", "consumption_rate": "0", "part_information": "", "standard_unit": "kg", "material_code": "1010201010000008" }, { "consume_unit": "6588", "standard_unit_cn": "kg", "indexs": "ZDQX0001481_C10000014", "product_code": "ZDQX0001481", "main_class": "油墨类", "dosage_unit_unit_cn": "kg/万", "create_by": "2", "update_time": "2025-02-14 18:04:06", "parent_code": "ZDQX0001481_C1", "mnemonic_code": "010100070", "id": 950954, "component_count": "1", "update_by": "2", "material_name": "新金冠 蓝（高光）", "flag_deleted": 0, "fixed_amount": "0", "product_version": "1.7", "create_time": "2025-02-14 18:04:06", "dosage_unit": "0.000000", "categroy": 2, "consume_unit_cn": "kg", "index": "ZDQX0001481_C10000014", "specification": "200kg/桶", "version": "1.0", "component_size": "200kg/桶", "consume_round_up": 0, "direct_material": 0, "sub_class": "普通油墨", "consumption_rate": "0", "part_information": "", "standard_unit": "kg", "material_code": "1010201010000008" }, { "packing_unit": "6577", "product_count": "1", "product_code": "ZDQX0001481", "create_by": "2", "update_time": "2025-02-14 18:04:06", "pack_other_texture": "", "parent_code": "", "id": 950955, "pack_other_sizes": "", "component_count": "1", "pack_paste_method": "", "update_by": "2", "material_name": "纸箱", "flag_deleted": 0, "rest": "尺寸：,背景颜色：,材质：,粘贴方法：", "product_version": "1.7", "packing_unit_cn": "套", "create_time": "2025-02-14 18:04:06", "categroy": 4, "weight": "1.0000", "version": "1.0", "pack_background_color": "", "unit_weight": 1, "material_code": "ZDQX0001481Pk_01" }] }
// 根据ban_json_code获取出本值
function getOutOfBook(ban_json_code) {
    var outOfBooks = [];
    
    if (ban_json_code) {
        // 将ban_json_code按逗号分割，处理多个版编号的情况
        var banInfos = ban_json_code.split(',');
        
        banInfos.forEach(function(banInfo) {
            // 提取出本值
            var outOfBookStart = banInfo.indexOf('出本：') + 3;
            var outOfBookEnd = banInfo.indexOf(',', outOfBookStart);
            var outOfBookStr = outOfBookEnd > outOfBookStart ? 
                banInfo.substring(outOfBookStart, outOfBookEnd) : 
                banInfo.substring(outOfBookStart);
            var outOfBook = parseInt(outOfBookStr);

            // 验证数据有效性并添加到数组
            if (!isNaN(outOfBook)) {
                outOfBooks.push(outOfBook);
            }
        });
    }

    return outOfBooks;
}

// 测试函数
var test_ban_json_code = "版编号：ZDQX0001481_1_0_01类型：印版类别：CTP版版名称：测试色次CTP版出本：10,版编号：ZDQX0001481_1_2_01类型：模切版类别：模切版版名称：测试色次模切版出本：7";
var test_result = getOutOfBook(test_ban_json_code);
console.log(test_result);

// 处理craftArr数据，获取所有出本值
function getAllOutOfBooks(craftArr) {
    var allOutOfBooks = [];

    // 遍历craftArr
    craftArr.forEach(function(item) {
        if (item.ban_json_code) {
            var itemOutOfBooks = getOutOfBook(item.ban_json_code);
            allOutOfBooks = allOutOfBooks.concat(itemOutOfBooks);
        }
    });

    return allOutOfBooks;
}

var all_out_of_books = getAllOutOfBooks(obj.craftArr);
console.log(all_out_of_books);