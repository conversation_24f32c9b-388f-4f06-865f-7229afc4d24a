let menuHuanArr = [{ "code": "P-LGPPYBMGX", "name": "喷印表面工序", "machine_id": "151", "machine_name": "LGP喷码04机台", "machine_code": "M-LGPPM04", "device_code": "LGPSCUVJY0003" },
{ "code": "P-LGPPYBMGX2", "name": "喷印表面工序", "machine_id": "149", "machine_name": "LGP覆膜02机台", "machine_code": "M-LGPFM02", "device_code": "LGPSCHH20008" },
];
var defaultBindingsArr = [
  {
      menuCode: 'P-LGPPYBMGX',
      menuName: '喷印',
      machine_id: '87'
  },
  {
      menuCode: "P-LGPPNGX",
      menuName: '品粘',
      machine_id: '80'
  },
  {
      menuCode: "P-LGPSMSBZ",
      menuName: '说明书包装工序',
      machine_id: '99'
  },
  {
      menuCode: "P-LGPMTGX",
      menuName: '模烫工序',
      machine_id: '79'
  },
  {
      menuCode: "P-LGPSMSCJ",
      menuName: '说明书车间工序',
      machine_id: '73'
  },
  {
      menuCode: "P-LGPYSGX",
      menuName: '印刷工序',
      machine_id: '71'
  }
]
/**
 *  将menuHuanArr转换为以下格式
 * [{"code":"","name":"","machine_id":"","machine_name":"","url":"","machines":[{"machine_id":"","machine_name":"","machine_code":""}]},
 * code (string)：工序代码。
name (string)：工序名称。
machine_id (integer)：默认选中的机器ID。
machine_name (string)：默认选中的机器名称。
url (string)：与该工序相关的资源链接
machines (array)：与该工序相关的机器列表，每个机器对象包含：
    machine_id (integer)：机器ID。
    machine_name (string)：机器名称。
    machine_code (string)：机器代码。
 * 。
 */

menuHuanArr.forEach((item) => {
    let machines = [];
    menuHuanArr.forEach((item2) => {
        if (item.code === item2.code) {
            machines.push({
                machine_id: item2.machine_id,
                machine_name: item2.machine_name,
                machine_code: item2.machine_code
            })
        }
        //将machines按machine_name排序
        machines.sort((a, b) => {
            return a.machine_name.localeCompare(b.machine_name)
        })
    })
    item.machines = machines;
})

//code 过滤数据
menuHuanArr = menuHuanArr.filter((item, index) => {
    return menuHuanArr.findIndex((item2) => {
        return item.code === item2.code
    }) === index
})
//menuHuanArr根据defaultBindingsArr menuCode过滤数据
menuHuanArr = menuHuanArr.filter((item) => {
    return defaultBindingsArr.find((item2) => {
        return item.code === item2.menuCode
    })
})
//遍历添加url
menuHuanArr.forEach((item) => {
    item.url = "http://www.baidu.com"
})
//getUrl(e.name)
console.log(menuHuanArr);