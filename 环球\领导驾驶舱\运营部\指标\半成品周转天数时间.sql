-- ETL:运营部周转天数统计数据->dwd_turnover_month_time
SELECT
    md.update_month,
    md.product_big_category,
    md.product_big_category_name,
    COALESCE(rd.batch_num, 0) batch_num,
    COALESCE(rd.turnover_time, 0) turnover_time
FROM
    (
        SELECT
            `year_month` update_month,
            dpbc.code product_big_category,
            dpbc.name product_big_category_name
        FROM
            dim_month dm,
            dim_product_big_category dpbc
        WHERE
            dm.`year` = YEAR(CURDATE())
        ORDER BY
            dm.`year_month`
    ) md
        LEFT JOIN
    (
        SELECT
            DATE_FORMAT(subquery.min_time, '%Y-%m') AS update_month,
            code product_big_category,
            name product_big_category_name,
            COUNT(*) batch_num,
            SUM(time_difference) turnover_time
        FROM
            (
                SELECT
                    dpbc.code,
                    dpbc.name,
                    DATEDIFF(ww.min_time, w.actual_start_time_operation) AS time_difference,
                    c.production_batch_number,
                    ww.min_time
                FROM
                    dim_product_big_category dpbc
                        LEFT JOIN ods_pm_job_order c ON
                        c.large_category = dpbc.code
                        JOIN ods_pm_job_order_management d ON
                        c.production_batch_number = d.production_batch_number
                        JOIN ods_pm_job_detail i ON
                        d.task_code = i.task_code
                        JOIN ods_pm_final_batch_job_audit w ON
                        i.id = w.job_strip_number
                        JOIN
                    (
                        SELECT
                            pro_batch,
                            MIN(create_time) AS min_time
                        FROM
                            ods_pm_finsih_project_stock
                        WHERE
                            YEAR(create_time) = YEAR(CURDATE())
                        GROUP BY
                            pro_batch
                    ) ww ON
                        c.production_batch_number = ww.pro_batch
                WHERE
                    c.is_collage = 1
                  AND c.flag_deleted = 0
                  AND (i.before_process_code IS NULL
                    OR i.before_process_code = '')
                  AND i.special_type IS NULL
                  AND YEAR(ww.min_time) = YEAR(CURDATE())
                UNION ALL
                SELECT
                    dpbc.code,
                    dpbc.name,
                    DATEDIFF(ww.min_time, w.actual_start_time_operation ) AS time_difference,
                    c.production_batch_number,
                    ww.min_time
                FROM
                    dim_product_big_category dpbc
                        LEFT JOIN ods_pm_job_order c ON
                        c.large_category = dpbc.code
                        LEFT JOIN ods_pm_job_order_middle f ON
                        c.production_batch_number = f.production_batch_number
                        JOIN ods_pm_job_order_management d ON
                        f.task_code = d.task_code
                        JOIN ods_pm_job_detail i ON
                        d.task_code = i.task_code
                        JOIN ods_pm_final_batch_job_audit w ON
                        i.id = w.job_strip_number
                        JOIN
                    (
                        SELECT
                            pro_batch,
                            min(create_time) AS min_time
                        FROM
                            ods_pm_finsih_project_stock
                        WHERE
                            YEAR(create_time) = YEAR(CURDATE())
                        GROUP BY
                            pro_batch
                    ) ww ON
                        c.production_batch_number = ww.pro_batch
                WHERE
                    c.is_collage = 0
                  AND f.flag_deleted = 0
                  AND c.flag_deleted = 0
                  AND (i.before_process_code IS NULL
                    OR i.before_process_code = '')
                  AND i.special_type IS NULL
                  AND YEAR(ww.min_time) = YEAR(CURDATE())
            ) AS subquery
        GROUP BY
            product_big_category,
            product_big_category_name,
            update_month
    ) rd ON
        rd.update_month = md.update_month
            AND rd.product_big_category = md.product_big_category
;
select * from dwd_turnover_month_time dtmt;
