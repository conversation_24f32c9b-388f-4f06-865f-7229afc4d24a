-- 1.首先根据条件查询缺失产品数据,符合条件的数据进入下一步
select id, product_version, is_group_product,standard_unit,standard_unit_cn, ppv.*
from pdm_product_version ppv
where is_group_product is null or standard_unit = 9077;
-- 2.按material_code更新组产品
update pdm_product_version set is_group_product=0 where material_code = 'ZDYY0010931' and is_group_product is null;
-- 3.更新单位
update pdm_product_version set standard_unit=6591,standard_unit_cn='只' 
where standard_unit = 9077 ;

-- 4.根据material_code,product_version查询产品BOM是否存在
select * from pdm_product_bom pm where material_code='ZDYY0010931' and product_version='1.1';

-- 5.不存在则新增,material_code,product_version,product_version对应
INSERT INTO pdm_product_bom (dosage_unit_unit_cn, packing_unit_cn, consume_unit_cn, standard_unit_cn, product_count,
                             bom_type, direct_material, indexs, pack_paste_method, pack_other_texture,
                             pack_background_color, pack_other_sizes, box_mark_content, packing_parameter, weight,
                             packing_unit, ink_formulation, consume_round_up, consume_unit, dosage_unit_unit, cut_size,
                             notes_on_cutting, slitting, cut_remark, pushing, is_off_side, consumption_rate,
                             dosage_unit, fixed_amount, product_version, product_code, parent_code, makeup_product,
                             remark, part_information, color_order_quotation, color_sequence, chromatic_degree,
                             standard_unit, spread_size, component_size, component_count, material_name, mnemonic_code,
                             material_code, categroy, flag_deleted, version, create_by, create_time, update_by,
                             update_time, domain_id, gram_weight_cn, mine_version, children_version, children_product)
VALUES (null, null, null, null, '1', null,
        null, null, null, null, null, null,
        null, null, null, null, null, null,
        null,null, null, null, null, null, null,
        null,null, null, null, '1.2', 'ZDYY0010931',
        '',null, null, null, null,null,
        '1/1', 6591, '1*1', null, '1',null,
        null, 'ZDYY0010931', '3', 0, null, null,null,
        null, null, null,null, null, null, null);

