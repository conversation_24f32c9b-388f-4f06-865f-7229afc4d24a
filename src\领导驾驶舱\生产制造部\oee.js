var api ={"msg":"success","code":200,"data":{"msg":"成功","code":200,"data":[{"QIS_quantity":0,"scrap_quantity":540303,"work_hour":14954,"job_downtime":2680,"machine_id":"113","scheduled_down_time":4261,"E1":0,"work_center_name":"LGP2加2色可翻转说明书印刷中心","machine_name":"LGP说明书大张品检01机台","cycle":"2025-05","actual_time":11573,"work_center_code":"R-LGP2J2SKFZSMSYSZX","actual_production_time":8509,"output":29,"job_status":"全部完工","total":0,"A3":0,"rated_speed":0,"process_code":"P-LGPYSGX","process_name":"印刷工序","yields":0}]}}
var dataList=api.data.data
var team_file=[{"id":1,"squad_name":"万能测试机台A组"},{"id":2,"squad_name":"UV印刷A02"},{"id":3,"squad_name":"印刷B02"},{"id":4,"squad_name":"说明书印刷A01"},{"id":5,"squad_name":"喷码A01"},{"id":6,"squad_name":"卡纸分切A01"},{"id":7,"squad_name":"卡纸裁切A01"},{"id":8,"squad_name":"烫印A02"},{"id":9,"squad_name":"模切A07"},{"id":10,"squad_name":"模切B05"},{"id":11,"squad_name":"粘盒B03"},{"id":13,"squad_name":"折页A01"},{"id":14,"squad_name":"印刷A02"},{"id":15,"squad_name":"流程测试班组"},{"id":16,"squad_name":"万能班组"},{"id":17,"squad_name":"喷码B02"},{"id":18,"squad_name":"UV印刷B02"},{"id":19,"squad_name":"模切B07"},{"id":20,"squad_name":"粘盒A07"},{"id":21,"squad_name":"模切A05"},{"id":22,"squad_name":"清废A04"},{"id":23,"squad_name":"粘盒A04"},{"id":25,"squad_name":"制版B班"},{"id":26,"squad_name":"制版A班"},{"id":27,"squad_name":"粘盒B07"},{"id":28,"squad_name":"制版A班"},{"id":29,"squad_name":"模切B04"},{"id":30,"squad_name":"粘盒A03"},{"id":31,"squad_name":"说明书印刷B01"},{"id":32,"squad_name":"清废B03"},{"id":33,"squad_name":"模切A04"},{"id":34,"squad_name":"模切A13"},{"id":35,"squad_name":"说明书印刷A02"},{"id":36,"squad_name":"折页A02"},{"id":37,"squad_name":"折页A03"},{"id":38,"squad_name":"万能测试机台001B"},{"id":39,"squad_name":"成品裁切B02"},{"id":40,"squad_name":"粘盒A08"},{"id":41,"squad_name":"品检B04"},{"id":42,"squad_name":"卡纸分切B01"},{"id":43,"squad_name":"品检B02"},{"id":44,"squad_name":"喷码A02"},{"id":47,"squad_name":"说明书人工选剔A01"},{"id":48,"squad_name":"烫印B02"},{"id":49,"squad_name":"说明书印刷A03"},{"id":51,"squad_name":"覆膜-01A"},{"id":52,"squad_name":"覆膜-01B"},{"id":53,"squad_name":"卡纸分切B02"},{"id":54,"squad_name":"模切B03"},{"id":55,"squad_name":"模切A03"},{"id":56,"squad_name":"粘盒B04"},{"id":57,"squad_name":"说明书人工选剔A02"},{"id":58,"squad_name":"纸盒人工选剔B01"},{"id":59,"squad_name":"精品制盒"},{"id":60,"squad_name":"精品选剔"},{"id":61,"squad_name":"灰板开槽"},{"id":62,"squad_name":"物料A01"},{"id":63,"squad_name":"清废B02"},{"id":64,"squad_name":"清废B01"},{"id":65,"squad_name":"说明书分切A04"},{"id":66,"squad_name":"说明书分切B04"},{"id":67,"squad_name":"粘盒A09"},{"id":68,"squad_name":"粘盒B09"},{"id":69,"squad_name":"印刷A06"},{"id":70,"squad_name":"印刷B06"},{"id":71,"squad_name":"粘盒A06"},{"id":72,"squad_name":"粘盒B06"},{"id":73,"squad_name":"喷码B01"},{"id":74,"squad_name":"模切A02"},{"id":75,"squad_name":"模切B02"},{"id":76,"squad_name":"喷码A03"},{"id":77,"squad_name":"成品裁切A02"},{"id":78,"squad_name":"成品裁切B03"},{"id":79,"squad_name":"纸盒人工选剔A01"},{"id":80,"squad_name":"模切A01"},{"id":81,"squad_name":"粘盒A10"},{"id":82,"squad_name":"UV印刷B01"},{"id":83,"squad_name":"UV印刷A01"},{"id":84,"squad_name":"烫印A03"},{"id":85,"squad_name":"烫印B03"},{"id":86,"squad_name":"粘盒B10"},{"id":87,"squad_name":"清废A01"},{"id":88,"squad_name":"粘盒A05"},{"id":89,"squad_name":"粘盒B05"},{"id":90,"squad_name":"清废A03"},{"id":91,"squad_name":"模切B08"},{"id":92,"squad_name":"喷码B03"},{"id":93,"squad_name":"卡纸分切A02"},{"id":94,"squad_name":"粘盒B02"},{"id":95,"squad_name":"模切A08"},{"id":96,"squad_name":"纸盒人工选剔A02"},{"id":97,"squad_name":"纸盒人工选剔B02"},{"id":98,"squad_name":"说明书印刷B02"},{"id":99,"squad_name":"模切B09"},{"id":100,"squad_name":"模切A09"},{"id":101,"squad_name":"覆膜-02B"},{"id":102,"squad_name":"品检A04"},{"id":103,"squad_name":"覆膜-02A"},{"id":104,"squad_name":"烫印A01"},{"id":107,"squad_name":"喷码A04"},{"id":108,"squad_name":"喷码B04"},{"id":109,"squad_name":"喷码A05"},{"id":110,"squad_name":"卡纸裁切A03"},{"id":111,"squad_name":"卡纸分切A03"},{"id":112,"squad_name":"卡纸分切B03"},{"id":113,"squad_name":"卡纸裁切B03"},{"id":114,"squad_name":"说明书印刷B03"},{"id":115,"squad_name":"UV印刷A04"},{"id":117,"squad_name":"成品裁切A03"},{"id":118,"squad_name":"折页B02"},{"id":119,"squad_name":"粘盒B08"},{"id":120,"squad_name":"粘合A02"},{"id":121,"squad_name":"模切A10"},{"id":122,"squad_name":"模切B10"},{"id":123,"squad_name":"印刷A05"},{"id":124,"squad_name":"印刷B05"},{"id":126,"squad_name":"制版A班"},{"id":127,"squad_name":"印刷B07"},{"id":128,"squad_name":"印刷A07"},{"id":130,"squad_name":"说明书印刷A04"},{"id":131,"squad_name":"喷码B05"},{"id":132,"squad_name":"卡纸裁切B01"},{"id":133,"squad_name":"清废A02"},{"id":134,"squad_name":"制版B班"},{"id":135,"squad_name":"大张检品A01"},{"id":136,"squad_name":"成品裁切A04"},{"id":137,"squad_name":"冷转印B01"},{"id":138,"squad_name":"印刷A03"},{"id":139,"squad_name":"标样01"},{"id":140,"squad_name":"模切B01"},{"id":141,"squad_name":"模切B13"},{"id":142,"squad_name":"清废B04"},{"id":143,"squad_name":"印刷B08"}]
for (var i = 0; i < dataList.length; i++) {
    for (var j = 0; j < team_file.length; j++) {
      if(dataList[i].team_or_group_id == team_file[j].id){
        dataList[i].team_name = team_file[j].squad_name
      }
    }
    // 理论时间(报工数量（工序报工+集中托报工+废品量）/机台理论速度)
    if(dataList[i].yields){
      dataList[i].theoretical_times = (dataList[i].yields).toFixed(2)
    }else{
      dataList[i].theoretical_times = 0
    }
  
    //合格品率(实际产量/（实际产量+废品数量+品检剔废量）)(%)
    if(dataList[i].scrap_quantity && (dataList[i].scrap_quantity || dataList[i].QIS_quantity || dataList[i].output) ){
      dataList[i].pass_rate = (Number((dataList[i].scrap_quantity/(dataList[i].scrap_quantity + dataList[i].output + dataList[i].QIS_quantity))*100)).toFixed(2)
    }else{
      dataList[i].pass_rate = 0
    }
  
    //设备性能率 (理论时间（H）/实际生产时间（H）)(%)
    if(dataList[i].theoretical_times && dataList[i].actual_production_time){
      dataList[i].equipment_performance_rate = (Number((dataList[i].theoretical_times/(dataList[i].actual_production_time/60))*100)).toFixed(2)
    }else{
      dataList[i].equipment_performance_rate = 0
    }
  
    // 时间利用率 (实际生产时间（H）/SUM（班组日报实际负荷时间）)(%)
    if(dataList[i].actual_production_time && dataList[i].actual_time){
      dataList[i].time_availability = (Number((dataList[i].actual_production_time/dataList[i].actual_time)*100)).toFixed(2)
    }else{
      dataList[i].time_availability = 0
    }
    
    //设备利用率	(SUM（班组日报实际负荷时间）/ 上班时间)(%)
    if(dataList[i].actual_time && dataList[i].work_hour){
      dataList[i].equipment_utilization_rate = (Number((dataList[i].actual_time/dataList[i].work_hour)*100)).toFixed(2)
    }else{
      dataList[i].equipment_utilization_rate = 0
    }
    
    //oee	(时间利用率（%）*设备性能率（%）*合格率（%）)(%)
    if(dataList[i].time_availability && dataList[i].equipment_performance_rate && dataList[i].pass_rate){
      dataList[i].oee = (Number((dataList[i].time_availability * dataList[i].equipment_performance_rate * dataList[i].pass_rate) / 10000)).toFixed(2)
    }else{
      dataList[i].oee = 0
    }
    
    //teep (OEE(%)*设备利用率（%)	(%)
    if(dataList[i].oee && dataList[i].equipment_utilization_rate){
      dataList[i].teep = (Number((dataList[i].oee * dataList[i].equipment_utilization_rate) / 100)).toFixed(2)
    }else{
      dataList[i].teep = 0
    }
    // 上班时间
    if(dataList[i].work_hour){
      dataList[i].work_hour = (Number(dataList[i].work_hour/60)).toFixed(2)
    }else{
      dataList[i].work_hour = 0
    }
  
    //计划停机时间
    if(dataList[i].scheduled_down_time){
      dataList[i].scheduled_down_time = (Number(dataList[i].scheduled_down_time/60)).toFixed(2)
    }else{
      dataList[i].scheduled_down_time = 0
    }
  
    //作业停机时间
    if(dataList[i].job_downtime){
      dataList[i].job_downtime = (Number(dataList[i].job_downtime/60)).toFixed(2)
    }else{
      dataList[i].job_downtime = 0
    }
  
    //实际生产时间
    if(dataList[i].actual_production_time){
      dataList[i].actual_production_time = (Number(dataList[i].actual_production_time/60)).toFixed(2)
    }else{
      dataList[i].actual_production_time = 0
    }
  
    //MTTR平均修复时间(SUM（故障维修时间E1+计划维修时间A3）/SUM(机台故障次数))
    if((dataList[i].A3 || dataList[i].E1) && dataList[i].total){
      dataList[i].mttr_avg = (Number((dataList[i].A3 + dataList[i].E1)/dataList[i].total)).toFixed(2)
    }else{
      dataList[i].mttr_avg = 0
    }
  
    //MTBF平均无故障时间(SUM(上班时间-计划停机时间-故障维修时间E1）/SUM(机台故障次数))
    if((dataList[i].work_hour || dataList[i].scheduled_down_time || dataList[i].E1) && dataList[i].total){
      dataList[i].mtbf_avg = (Number((dataList[i].work_hour - dataList[i].scheduled_down_time - dataList[i].E1)/dataList[i].total)).toFixed(2)
    }else{
      dataList[i].mtbf_avg = 0
    }
  }
  console.log(dataList)
  return dataList
  