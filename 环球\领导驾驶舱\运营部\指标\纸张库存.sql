-- ETL 运营部-纸张库存 ->dwd_yunying_shuomingshu
select sum(
               case
                   when qty2Unit in ('kg', 'kilogram') then qty2 / 1000
                   when qty2Unit in ('TNE') then qty2
                   when qty2Unit in ('fix') then SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', 1), ' ', -1) *
                                                 SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', -1), 'm', 1) / 1000000 *
                                                 t3.data_value / 1000000 * qty2
                   end) value,
    date_format(now(),'%Y-%m-%d %H:%m:%s') time
from ods_wms_inventory_collect t1
    join ods_mes_material_file t2 on t1.itemCode = t2.material_code
    join ods_mes_parameter_data t3 on t2.gram_weight = t3.id
where t2.category = 1
  and t2.flag_deleted = 0

