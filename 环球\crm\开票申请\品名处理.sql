select * from invoice_application_detail csid where product_name like '%渐变蓝%';

select distinct t1.sales_order_code '拆分单号',
                t1.status,
                t3.grade_name 'pdm品名',
                t4.grade_name 'bip品名',
                t1.create_time '拆分单创建时间'
from crm_sales_order t1
    join crm_sales_order_product t2
    join h3chq_pdmbusiness1704287270935.pdm_product_client t3
    on t3.product_code = t2.material_code
#         and t3.product_version=t2.product_version
#         and t3.product_version like concat(SUBSTRING_INDEX(t2.product_version, '.', 1), '.%')
        and t3.product_version = t2.product_version
        and (t3.is_used = '1' or is_used is null)
        and t3.client_code =t1.cust_code
   join (
    SELECT
        document_Number sales_order_code,
        so_saleorder_b.cmaterialvid material_code,
        so_saleorder_b.vbdef35 grade_name
    FROM hq_mdm_b.mdm_mom_outgoing mmo,
         JSON_TABLE(
                 mmo.data,
                 '$[*].so_saleorder_b[*]' COLUMNS(
                     cmaterialvid VARCHAR(50) PATH '$.cmaterialvid',
                     vbdef35 VARCHAR(200) PATH '$.vbdef35'
                     )
         ) so_saleorder_b
    WHERE mmo.data_Sources = '销售订单新增'
)t4 on t1.sales_order_code=t4.sales_order_code and t2.material_code=t4.material_code
where t1.flag_deleted=0
  and t2.flag_deleted=0
  and t3.flag_deleted=0
  and t3.grade_name!=t4.grade_name
;


select data from hq_mdm_b.mdm_mom_outgoing mmo
where  data_Sources='销售订单新增'
  and document_Number ='25040057A'
order by update_time desc;

-- 获取主数据品名
SELECT
    document_Number sales_order_code,
    so_saleorder_b.cmaterialvid material_code,
    so_saleorder_b.vbdef35 grade_name
FROM hq_mdm_b.mdm_mom_outgoing mmo,
     JSON_TABLE(
             mmo.data,
             '$[*].so_saleorder_b[*]' COLUMNS(
                 cmaterialvid VARCHAR(50) PATH '$.cmaterialvid',
                 vbdef35 VARCHAR(200) PATH '$.vbdef35'
                 )
     ) so_saleorder_b
WHERE mmo.data_Sources = '销售订单新增'
ORDER BY mmo.update_time DESC;
