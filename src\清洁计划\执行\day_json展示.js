var arr=[{"create_time":"2025-05-27 17:19:02","responsible_position":"33","responsible_person":"33","task_code":"Clean0008","distribution_code":"Clean2025052711","exection_json":"{\"day_1\":\"--\",\"day_2\":\"计划\",\"day_3\":\"--\",\"day_4\":\"--\",\"day_5\":\"--\",\"day_6\":\"--\",\"day_7\":\"--\",\"day_8\":\"--\",\"day_9\":\"--\",\"day_10\":\"--\",\"day_11\":\"--\",\"day_12\":\"--\",\"day_13\":\"--\",\"day_14\":\"--\",\"day_15\":\"--\",\"day_16\":\"--\",\"day_17\":\"--\",\"day_18\":\"--\",\"day_19\":\"--\",\"day_20\":\"--\",\"day_21\":\"--\",\"day_22\":\"--\",\"day_23\":\"--\",\"day_24\":\"--\",\"day_25\":\"--\",\"day_26\":\"--\",\"day_27\":\"--\",\"day_28\":\"--\",\"day_29\":\"--\",\"day_30\":\"--\",\"day_31\":\"--\"}","task_desc":"13","frequency_value":"2号","version":"1.0","create_by":"顾俊翔","update_time":"2025-05-27 17:19:02","plan_json":"{\"day_1\":\"--\",\"day_2\":\"计划\",\"day_3\":\"--\",\"day_4\":\"--\",\"day_5\":\"--\",\"day_6\":\"--\",\"day_7\":\"--\",\"day_8\":\"--\",\"day_9\":\"--\",\"day_10\":\"--\",\"day_11\":\"--\",\"day_12\":\"--\",\"day_13\":\"--\",\"day_14\":\"--\",\"day_15\":\"--\",\"day_16\":\"--\",\"day_17\":\"--\",\"day_18\":\"--\",\"day_19\":\"--\",\"day_20\":\"--\",\"day_21\":\"--\",\"day_22\":\"--\",\"day_23\":\"--\",\"day_24\":\"--\",\"day_25\":\"--\",\"day_26\":\"--\",\"day_27\":\"--\",\"day_28\":\"--\",\"day_29\":\"--\",\"day_30\":\"--\",\"day_31\":\"--\"}","id":3,"update_by":"顾俊翔","flag_deleted":0},{"create_time":"2025-05-27 17:19:02","responsible_position":"12","responsible_person":"12","task_code":"Clean0009","distribution_code":"Clean2025052711","exection_json":"{\"day_1\":\"计划\",\"day_2\":\"--\",\"day_3\":\"--\",\"day_4\":\"--\",\"day_5\":\"--\",\"day_6\":\"--\",\"day_7\":\"--\",\"day_8\":\"--\",\"day_9\":\"--\",\"day_10\":\"--\",\"day_11\":\"--\",\"day_12\":\"--\",\"day_13\":\"--\",\"day_14\":\"--\",\"day_15\":\"--\",\"day_16\":\"--\",\"day_17\":\"--\",\"day_18\":\"--\",\"day_19\":\"--\",\"day_20\":\"--\",\"day_21\":\"--\",\"day_22\":\"--\",\"day_23\":\"--\",\"day_24\":\"--\",\"day_25\":\"--\",\"day_26\":\"--\",\"day_27\":\"--\",\"day_28\":\"--\",\"day_29\":\"--\",\"day_30\":\"--\",\"day_31\":\"--\"}","task_desc":"12","frequency_value":"1号","version":"1.0","create_by":"顾俊翔","update_time":"2025-05-27 17:19:02","plan_json":"{\"day_1\":\"计划\",\"day_2\":\"--\",\"day_3\":\"--\",\"day_4\":\"--\",\"day_5\":\"--\",\"day_6\":\"--\",\"day_7\":\"--\",\"day_8\":\"--\",\"day_9\":\"--\",\"day_10\":\"--\",\"day_11\":\"--\",\"day_12\":\"--\",\"day_13\":\"--\",\"day_14\":\"--\",\"day_15\":\"--\",\"day_16\":\"--\",\"day_17\":\"--\",\"day_18\":\"--\",\"day_19\":\"--\",\"day_20\":\"--\",\"day_21\":\"--\",\"day_22\":\"--\",\"day_23\":\"--\",\"day_24\":\"--\",\"day_25\":\"--\",\"day_26\":\"--\",\"day_27\":\"--\",\"day_28\":\"--\",\"day_29\":\"--\",\"day_30\":\"--\",\"day_31\":\"--\"}","id":4,"update_by":"顾俊翔","flag_deleted":0}]
//将plan_json,和execution_json转化为json对象,并将day_1~day_31字段插到原来对象中
//如果字段不同,则取execution_json的字段
for(var i=0;i<arr.length;i++){
    var plan_json=JSON.parse(arr[i].plan_json);
    var exection_json=JSON.parse(arr[i].exection_json);
    for(var j=1;j<=31;j++){
        arr[i]['day_'+j]=plan_json['day_'+j];
        if(exection_json['day_'+j]!='--'){
            arr[i]['day_'+j]=exection_json['day_'+j];
        }
    }
}
console.log(JSON.stringify(arr))