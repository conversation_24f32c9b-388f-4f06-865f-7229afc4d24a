var crm_variable_cost_accounting = {
    "productPath": [
        "****************",
        "ZDQX0001493_C2"
    ],
    "craftArr": [
        "ZDQX0001493_C2"
    ],
    "productCode": "ZDQX0001493_C2",
    "paperCode": "****************",
    "records": [
        {
            "create_time": "2025-02-19 15:56:26",
            "categroy": "1",
            "paper_number": "****************",
            "paper_specification": "787",
            "version": "1.0",
            "create_by": "2",
            "update_time": "2025-02-19 15:56:26",
            "quotation_code": "HJ252000220",
            "children": [
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "3",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [
                        {
                            "amount": "0.000004789440",
                            "craft_name": "原料分切",
                            "create_time": "2025-02-19 15:56:26",
                            "categroy": "10",
                            "single_product_unit_usage": "0.000001200000",
                            "paper_number": "****************",
                            "paper_specification": "787",
                            "version": "1.0",
                            "paper_cutting_number": "4",
                            "create_by": "2",
                            "update_time": "2025-02-19 15:56:26",
                            "quotation_code": "HJ252000220",
                            "out_of_book": "12",
                            "parent_code": "ZDQX0001493",
                            "material_unit_price": "3.9912",
                            "process_type": "1",
                            "quotation_version": "2",
                            "id": 98888,
                            "update_by": "2",
                            "flag_deleted": 0
                        }
                    ],
                    "quotation_version": "2",
                    "id": 98890,
                    "update_by": "2",
                    "material_name": "123",
                    "material_code": "ZDQX0001493",
                    "flag_deleted": 0
                },
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "0",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [],
                    "parent_code": "ZDQX0001493",
                    "quotation_version": "2",
                    "id": 98891,
                    "update_by": "2",
                    "material_name": "123的部件",
                    "material_code": "ZDQX0001493_C1",
                    "flag_deleted": 0
                },
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "0",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [
                        {
                            "amount": "0.000019157760",
                            "craft_name": "原料分切",
                            "create_time": "2025-02-19 15:56:26",
                            "categroy": "10",
                            "single_product_unit_usage": "0.000004800000",
                            "paper_number": "****************",
                            "paper_specification": "787",
                            "version": "1.0",
                            "paper_cutting_number": "12",
                            "create_by": "2",
                            "update_time": "2025-02-19 15:56:26",
                            "quotation_code": "HJ252000220",
                            "out_of_book": "1",
                            "parent_code": "ZDQX0001493_C2",
                            "material_unit_price": "3.9912",
                            "process_type": "1",
                            "quotation_version": "2",
                            "id": 98889,
                            "update_by": "2",
                            "flag_deleted": 0
                        }
                    ],
                    "parent_code": "ZDQX0001493",
                    "quotation_version": "2",
                    "id": 98892,
                    "update_by": "2",
                    "material_name": "123的部件2",
                    "material_code": "ZDQX0001493_C2",
                    "flag_deleted": 0
                }
            ],
            "parent_code": "ZDQX0001493",
            "quotation_version": "2",
            "id": 98885,
            "update_by": "2",
            "material_name": "787 400g 宁波金丽白卡",
            "material_code": "****************",
            "flag_deleted": 0
        },
        {
            "create_time": "2025-02-19 15:56:26",
            "categroy": "1",
            "paper_number": "****************",
            "paper_specification": "787",
            "version": "1.0",
            "create_by": "2",
            "update_time": "2025-02-19 15:56:26",
            "quotation_code": "HJ252000220",
            "children": [
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "3",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [
                        {
                            "amount": "0.000004789440",
                            "craft_name": "原料分切",
                            "create_time": "2025-02-19 15:56:26",
                            "categroy": "10",
                            "single_product_unit_usage": "0.000001200000",
                            "paper_number": "****************",
                            "paper_specification": "787",
                            "version": "1.0",
                            "paper_cutting_number": "4",
                            "create_by": "2",
                            "update_time": "2025-02-19 15:56:26",
                            "quotation_code": "HJ252000220",
                            "out_of_book": "12",
                            "parent_code": "ZDQX0001493",
                            "material_unit_price": "3.9912",
                            "process_type": "1",
                            "quotation_version": "2",
                            "id": 98888,
                            "update_by": "2",
                            "flag_deleted": 0
                        }
                    ],
                    "quotation_version": "2",
                    "id": 98890,
                    "update_by": "2",
                    "material_name": "123",
                    "material_code": "ZDQX0001493",
                    "flag_deleted": 0
                },
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "0",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [],
                    "parent_code": "ZDQX0001493",
                    "quotation_version": "2",
                    "id": 98891,
                    "update_by": "2",
                    "material_name": "123的部件",
                    "material_code": "ZDQX0001493_C1",
                    "flag_deleted": 0
                },
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "0",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [
                        {
                            "amount": "0.000019157760",
                            "craft_name": "原料分切",
                            "create_time": "2025-02-19 15:56:26",
                            "categroy": "10",
                            "single_product_unit_usage": "0.000004800000",
                            "paper_number": "****************",
                            "paper_specification": "787",
                            "version": "1.0",
                            "paper_cutting_number": "12",
                            "create_by": "2",
                            "update_time": "2025-02-19 15:56:26",
                            "quotation_code": "HJ252000220",
                            "out_of_book": "1",
                            "parent_code": "ZDQX0001493_C2",
                            "material_unit_price": "3.9912",
                            "process_type": "1",
                            "quotation_version": "2",
                            "id": 98889,
                            "update_by": "2",
                            "flag_deleted": 0
                        }
                    ],
                    "parent_code": "ZDQX0001493",
                    "quotation_version": "2",
                    "id": 98892,
                    "update_by": "2",
                    "material_name": "123的部件2",
                    "material_code": "ZDQX0001493_C2",
                    "flag_deleted": 0
                }
            ],
            "parent_code": "ZDQX0001493",
            "quotation_version": "2",
            "id": 98885,
            "update_by": "2",
            "material_name": "787 400g 宁波金丽白卡",
            "material_code": "****************",
            "flag_deleted": 0
        },
        {
            "create_time": "2025-02-19 15:56:26",
            "categroy": "1",
            "paper_number": "****************",
            "paper_specification": "787",
            "version": "1.0",
            "create_by": "2",
            "update_time": "2025-02-19 15:56:26",
            "quotation_code": "HJ252000220",
            "children": [
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "3",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [
                        {
                            "amount": "0.000004789440",
                            "craft_name": "原料分切",
                            "create_time": "2025-02-19 15:56:26",
                            "categroy": "10",
                            "single_product_unit_usage": "0.000001200000",
                            "paper_number": "****************",
                            "paper_specification": "787",
                            "version": "1.0",
                            "paper_cutting_number": "4",
                            "create_by": "2",
                            "update_time": "2025-02-19 15:56:26",
                            "quotation_code": "HJ252000220",
                            "out_of_book": "12",
                            "parent_code": "ZDQX0001493",
                            "material_unit_price": "3.9912",
                            "process_type": "1",
                            "quotation_version": "2",
                            "id": 98888,
                            "update_by": "2",
                            "flag_deleted": 0
                        }
                    ],
                    "quotation_version": "2",
                    "id": 98890,
                    "update_by": "2",
                    "material_name": "123",
                    "material_code": "ZDQX0001493",
                    "flag_deleted": 0
                },
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "0",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [],
                    "parent_code": "ZDQX0001493",
                    "quotation_version": "2",
                    "id": 98891,
                    "update_by": "2",
                    "material_name": "123的部件",
                    "material_code": "ZDQX0001493_C1",
                    "flag_deleted": 0
                },
                {
                    "create_time": "2025-02-19 15:56:26",
                    "categroy": "0",
                    "version": "1.0",
                    "create_by": "2",
                    "update_time": "2025-02-19 15:56:26",
                    "quotation_code": "HJ252000220",
                    "children": [
                        {
                            "amount": "0.000019157760",
                            "craft_name": "原料分切",
                            "create_time": "2025-02-19 15:56:26",
                            "categroy": "10",
                            "single_product_unit_usage": "0.000004800000",
                            "paper_number": "****************",
                            "paper_specification": "787",
                            "version": "1.0",
                            "paper_cutting_number": "12",
                            "create_by": "2",
                            "update_time": "2025-02-19 15:56:26",
                            "quotation_code": "HJ252000220",
                            "out_of_book": "1",
                            "parent_code": "ZDQX0001493_C2",
                            "material_unit_price": "3.9912",
                            "process_type": "1",
                            "quotation_version": "2",
                            "id": 98889,
                            "update_by": "2",
                            "flag_deleted": 0
                        }
                    ],
                    "parent_code": "ZDQX0001493",
                    "quotation_version": "2",
                    "id": 98892,
                    "update_by": "2",
                    "material_name": "123的部件2",
                    "material_code": "ZDQX0001493_C2",
                    "flag_deleted": 0
                }
            ],
            "parent_code": "ZDQX0001493",
            "quotation_version": "2",
            "id": 98885,
            "update_by": "2",
            "material_name": "787 400g 宁波金丽白卡",
            "material_code": "****************",
            "flag_deleted": 0
        },
    ],
    "parent_code": "ZDQX0001493_C2",
    "sumAmount": "0.000023947200"
}
// 去除records中重复的material_code数据
function removeDuplicateRecords(records) {
    // 添加参数有效性检查
    if (!records || !Array.isArray(records)) {
        return [];
    }
    
    var result = [];
    var materialCodes = {};  // 用于记录已存在的material_code
    
    // 遍历records数组
    for (var i = 0; i < records.length; i++) {
        var record = records[i];
        // 检查record对象是否有效
        if (!record || typeof record !== 'object') {
            continue;
        }
        var materialCode = record.material_code;
        
        // 检查materialCode是否有效
        if (!materialCode) {
            continue;
        }
        
        // 如果该material_code还未被记录，则加入结果数组
        if (!materialCodes[materialCode]) {
            materialCodes[materialCode] = true;
            result.push(record);
        }
    }
    
    return result;
}

// 使用示例
var crm_variable_cost_accounting = crm_variable_cost_accounting || {};
crm_variable_cost_accounting.records = removeDuplicateRecords(crm_variable_cost_accounting.records);

// 输出结果
console.log(JSON.stringify(crm_variable_cost_accounting.records, null, 2));