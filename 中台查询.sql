-- 查询未配置cron
select t1.model_name, t2.cron, t1.gmt_create, t1.tag_name
from masa.masa_model t1
         left join masa.masa_etl_job t2 on t1.id = t2.id
where t1.model_type = 'etl'
#     and model_name like '%同步%'
#  and t2.cron='0 0,30 0 1 1 ? *'
 and t2.cron like '%1 1%'
  -- and t1.gmt_create > '2024-08-25 00:00:00'
  and t1.delete_flag = 0
  and cron is not null;

-- 查询时间段
select t1.model_name, t2.cron, t1.gmt_create
from masa.masa_model t1
         left join masa_etl_job t2 on t1.id = t2.id
where t1.model_type = 'etl'
  and t1.gmt_create < '2024-08-25 00:00:00'
  and t1.delete_flag = 0
  and cron = '0 0 0,12 * * ? *';

-- 查询url
select t1.model_name, t2.cron, JSON_UNQUOTE(JSON_EXTRACT(parse_content, '$.ETL_SOURCE_API[0].url')) url
from masa.masa_model t1
         left join masa.masa_etl_job t2 on t1.id = t2.id
where parse_content like '%/call/1916309686307823616%'
  and t1.delete_flag = 0
  and cron is not null
order by model_name asc
;

-- 根据表名查询etl
select *
from masa.masa_model
where parse_content like '%http://*************/openapi/mom/getSeasonValue/SteamBoiler/gas%'
#   and model_name like '%同步%'
  and delete_flag = 0
;

select * from ods_material_weighing omw order by update_time desc;
select :year-1;
SELECT LEFT(:select_month, 4) - 1 AS previous_year;
select * from dwd_paper_forecast_detail;
select week('2025-07-30',1);



SELECT
    YEAR(ld.create_time) update_year,
    WEEK(ld.create_time,1) week_of_year,
    SUM(psd.piece_reality) actual_shipment_boxes,
    SUM(if(psd.num_reality=0,psd.num,psd.num_reality)) actual_shipment_quantity
FROM
    (
        SELECT
            pfl.flow_id,
            MAX(pfl.create_time) create_time
        FROM
            ods_pm_flow_log pfl
        WHERE
            pfl.flag_deleted = 0
          AND pfl.flow_name = '配载单管理'
          AND pfl.next_node_name = '已出库'
          AND YEAR(pfl.create_time) = YEAR(CURDATE())
        GROUP BY
            pfl.flow_id
    ) ld
        LEFT JOIN ods_pm_stowage_detail psd ON
        psd.parent_id = ld.flow_id
WHERE
    psd.flag_deleted = 0
  AND psd.batch_no NOT LIKE 'DY%'
GROUP BY
    YEAR(ld.create_time),
    WEEK(ld.create_time,1);
select  * from selefgas_season;
