---
description: "数据库开发规范与最佳实践"
globs: ["**/*.sql", "**/*.prisma", "**/models/*.js", "**/models/*.ts", "**/migrations/*", "**/database/*"]
alwaysApply: true
---

# 数据库开发规范

## 通用规范

- 所有数据库对象名称必须使用小写字母和下划线
- 表名使用名词复数形式，例如：users, orders, products
- 字段名使用单数形式，例如：user_id, order_date, product_name
- 主键默认命名为 id，外键格式为 table_name_id
- 所有表都必须包含 created_at 和 updated_at 字段
- 所有删除操作使用软删除方式，增加 deleted_at 字段
- 所有字段必须添加注释说明其用途和约束
- 避免使用特定数据库的专有特性，保持兼容性

## 数据库设计原则

- 遵循第三范式(3NF)设计，减少数据冗余
- 表分区考虑因素：数据量、访问频率、历史数据管理
- 大型列表使用分页查询，每页不超过100条记录
- 敏感数据必须加密存储，使用行级权限控制访问
- 不使用外键关联作为强制约束，在应用层控制
- 数据库命名统一使用小写，使用下划线分隔单词
- 索引命名规则：idx_表名_字段名

## SQL编写规范

- SQL关键字统一使用大写，如SELECT, INSERT, UPDATE等
- 查询语句格式化，每个子句另起一行
- 复杂查询必须添加注释说明用途和实现思路
- 禁止使用SELECT *，必须明确指定查询字段
- 禁止使用OFFSET分页，使用基于主键的游标分页
- 表连接必须指定连接条件，避免笛卡尔积
- 增删改操作必须在事务中执行
- 条件过滤必须考虑NULL值情况

## 性能优化规则

- 所有查询必须有支持索引，避免全表扫描
- 索引字段选择：高基数、高频查询、低更新频率
- 合理使用索引类型：B-Tree、Hash、全文等
- 定期分析执行计划，优化慢查询
- 大批量操作必须分批执行，避免锁表时间过长
- 避免循环调用数据库，使用批量操作
- 合理设置连接池参数，避免连接泄漏
- 定期维护数据库统计信息和索引碎片

## 安全规范

- 所有SQL语句必须使用参数化查询，防止SQL注入
- 生产环境禁止使用SA/Root账户直接操作数据库
- 最小权限原则：应用账户只授予必要的操作权限
- 定期审计数据库访问日志，监控异常操作
- 生产环境数据库禁止直接暴露在公网
- 数据库密码强度要求：12位以上，包含大小写字母、数字和特殊字符
- 定期更换数据库密码，至少每季度一次

## ORM和数据访问层规范

- 统一使用ORM框架访问数据库，避免直接写SQL
- DAO层与业务逻辑层严格分离
- 复杂查询使用原生SQL，但必须通过ORM的原生查询接口
- 数据库连接信息通过环境变量或配置文件管理，禁止硬编码
- 查询结果必须明确类型转换，避免隐式转换
- 批量操作使用事务和批处理API
- 实体类与数据表结构一一对应，保持同步

## 数据迁移和变更管理

- 所有数据库结构变更必须使用迁移脚本
- 迁移脚本必须包含向前和向后迁移的逻辑
- 大表结构变更必须评估性能影响，采用低峰期执行
- 字段删除采用分步走：1.添加新字段 2.双写阶段 3.迁移数据 4.删除旧字段
- 数据迁移必须有完整的测试和回滚方案

## 数据库运维

- 主从复制延迟监控阈值：5秒
- 数据库备份策略：每日全量+增量备份，保留30天
- 定期演练数据恢复流程，验证备份有效性
- CPU使用率警戒线：70%
- 磁盘使用率警戒线：80%
- 连接数使用率警戒线：75%
- 缓存命中率最低要求：85%
