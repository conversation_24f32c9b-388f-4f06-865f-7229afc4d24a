//大版横向=（插舌+B-2+H+间距）*列+B-2+插舌-4
var arr4="12.00"
var B="24.00"
var H="12"
var arr5="4.00"
var arr3="12"
//  arr3=列,arr4=插舌,arr5=间距;
if ( isNaN(arr3) || isNaN(arr4) || isNaN(arr5) ) {
    console.log("存在空值、非法值或开排值为零，请检查输入！");
    var result = 0;
} else{
  // 计算公式
    var result = (arr4 + B - 2 + H + arr5) * arr3 + B - 2 + arr4 - 4;
}
console.log("大版横向",result)
console.log(arr4,B,-2,H,arr5,arr3,B,-2,arr4,-4)
// 输出结果
return result;