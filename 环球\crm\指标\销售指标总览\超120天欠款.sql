select date_format(create_time, '%Y') year, date_format(create_time, '%m') month, count(*) cust_size
from crm_account_receivable_age csrp
group by date_format(create_time, '%Y'), date_format(create_time, '%m');
-- 获取去年 1月到今年12月的数据,如果缺月份,进行补全,cust_size 为 0

WITH months AS (SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 12 MONTH) + INTERVAL n MONTH, '%Y') AS year,
                       DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 12 MONTH) + INTERVAL n MONTH, '%m') AS month
                FROM (SELECT 0 AS n
                      UNION ALL
                      SELECT 1
                      UNION ALL
                      SELECT 2
                      UNION ALL
                      SELECT 3
                      UNION ALL
                      SELECT 4
                      UNION ALL
                      SELECT 5
                      UNION ALL
                      SELECT 6
                      UNION ALL
                      SELECT 7
                      UNION ALL
                      SELECT 8
                      UNION ALL
                      SELECT 9
                      UNION ALL
                      SELECT 10
                      UNION ALL
                      SELECT 11
                      UNION ALL
                      SELECT 12
                      UNION ALL
                      SELECT 13
                      UNION ALL
                      SELECT 14
                      UNION ALL
                      SELECT 15
                      UNION ALL
                      SELECT 16
                      UNION ALL
                      SELECT 17
                      UNION ALL
                      SELECT 18
                      UNION ALL
                      SELECT 19
                      UNION ALL
                      SELECT 20
                      UNION ALL
                      SELECT 21
                      UNION ALL
                      SELECT 22
                      UNION ALL
                      SELECT 23) numbers)
SELECT m.year,
       m.month,
       COALESCE(c.cust_size, 0) AS cust_size
FROM months m
         LEFT JOIN
     (SELECT DATE_FORMAT(create_time, '%Y') AS year,
             DATE_FORMAT(create_time, '%m') AS month,
             COUNT(*)                       AS cust_size
      FROM crm_account_receivable_age
      GROUP BY DATE_FORMAT(create_time, '%Y'),
               DATE_FORMAT(create_time, '%m')) c ON m.year = c.year AND m.month = c.month
ORDER BY m.year, m.month;
;
-- 获取今年和去年
select temp1.*, ifnull(temp2.cust_size,0) cust_size
from (WITH RECURSIVE
          months AS (SELECT 1 AS month
                     UNION ALL
                     SELECT month + 1
                     FROM months
                     WHERE month < 12),
          years AS (SELECT YEAR(NOW()) AS year
                    UNION ALL
                    SELECT YEAR(NOW()) - 1 AS year)
      SELECT y.year  `year`,
             m.month `month`
      FROM years y
               CROSS JOIN
           months m
      ORDER BY y.year, m.month) temp1
         left join
     (select date_format(create_time, '%Y') `year`, date_format(create_time, '%c') `month`, count(*) cust_size
      from crm_account_receivable_age
      where qkts > 120
      group by date_format(create_time, '%Y'), date_format(create_time, '%c')) temp2
     on temp1.`year` = temp2.`year` and temp1.`month` = temp2.`month`
order by temp1.`year`, temp1.`month`
;
