-- ETL建表
drop table if exists dws_cust_profit_month;
create table dws_cust_profit_month
(
    id                int auto_increment primary key comment '自增主键'
        primary key,
    update_year       varchar(10)    null comment '指标年度',
    update_month      varchar(10)    null comment '指标月份',
    update_time       varchar(10)    null comment '指标时间',
    cumulative_amount decimal(20, 2) null comment '累计金额',
    cust_code         varchar(10)    null comment '客户编码'
) comment '客户毛利累计';
select * from dws_company_profit_month;

-- ETL:CRM客户毛利累计->dws_cust_profit_month
select year(now()) update_year, '01'  AS update_month,concat(year(now()), '-01') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount,cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m')='01'
group by cust_code
UNION ALL
select year(now()) update_year, '02'  AS update_month,concat(year(now()), '-02') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02')
group by cust_code
UNION ALL
select year(now()) update_year, '03'  AS update_month,concat(year(now()), '-03') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03')
group by cust_code
UNION ALL
select year(now()) update_year, '04'  AS update_month,concat(year(now()), '-04') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04')
group by cust_code
UNION ALL
select year(now()) update_year, '05'  AS update_month,concat(year(now()), '-05') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05')
group by cust_code
UNION ALL
select year(now()) update_year, '06'  AS update_month,concat(year(now()), '-06') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06')
group by cust_code
UNION ALL
select year(now()) update_year, '07'  AS update_month,concat(year(now()), '-07') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07')
group by cust_code
UNION ALL
select year(now()) update_year, '08'  AS update_month,concat(year(now()), '-08') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08')
group by cust_code
UNION ALL
select year(now()) update_year, '09'  AS update_month,concat(year(now()), '-09') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09')
group by cust_code
UNION ALL
select year(now()) update_year, '10'  AS update_month,concat(year(now()), '-10') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10')
group by cust_code
UNION ALL
select year(now()) update_year, '11'  AS update_month,concat(year(now()), '-11') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10','11')
group by cust_code
UNION ALL
select year(now()) update_year, '12'  AS update_month,concat(year(now()), '-12') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10','11','12')
group by cust_code
ORDER BY update_month, cust_code;
;


select t1.update_time,round( ifnull(100 * cumulative_amount / (t2.profit_target * 10000)*t3.target_percentage/100,0) ,2) as complete_rate
from cockpit.dws_cust_profit_month t1
         left join cockpit.ods_metric_person t2 on t1.update_year = t2.metric_year and t2.flag_deleted=0
         left join cockpit.dwd_cust_sale_percentage t3 on t1.cust_code = t3.cust_code and t2.cust_manager_code=t3.sales_code
where t2.cust_manager_code=:cust_manager_code
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t1.cust_code in (:cust_code_arr), 1)
      )
group by t1.update_time
;

select temp1.`year`, temp1.`month`, ifnull(temp2.complete_rate, 0) complete_rate
from (select `year_month`, `year`, `month`
      from cockpit.dim_month dm
      where dm.year in (year(now()), year(now()) - 1)) temp1
         left join (select t1.update_time,
                           round(ifnull(100 * cumulative_amount / (t2.profit_target * 10000) * t3.target_percentage /
                                        100, 0), 2) as complete_rate
                    from cockpit.dws_cust_profit_month t1
                             left join cockpit.ods_metric_person t2
                                       on t1.update_year = t2.metric_year and t2.flag_deleted = 0
                             left join cockpit.dwd_cust_sale_percentage t3
                                       on t1.cust_code = t3.cust_code and t2.cust_manager_code = t3.sales_code
                    where t2.cust_manager_code = :cust_manager_code
                      AND if(:admin,
                             1,
                             if(:cust_code_size > 0, t1.cust_code in (:cust_code_arr), 1)
                          )
                    group by t1.update_time) temp2
                   on temp1.`year_month` = temp2.update_time
order by temp1.`year`, temp1.`month`
;


