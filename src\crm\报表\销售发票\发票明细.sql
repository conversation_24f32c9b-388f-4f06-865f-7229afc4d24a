CREATE TABLE `crm_sales_invoice_details` (
  `user_code` varchar(255) COMMENT '开票人编码',
  `user_name` varchar(255) COMMENT '开票人名称',
  `dbilldate` varchar(255) COMMENT '开票日期',
  `fstatusflag` char(1) COMMENT '发票状态',
  `norigtaxmny` varchar(255) COMMENT '税价总计',
  `ntax` double(20, 2) COMMENT '税额',
  `ntaxrate` double(20, 2) COMMENT '税率',
  `norigmny` double(22, 4) COMMENT '开票金额',
  `norignetprice` double(22, 4) COMMENT '开票单价',
  `nnum` int COMMENT '开票数量',
  `vdef20` varchar(255) COMMENT '发票编号',
  `name` varchar(255) COMMENT '产品名称',
  `materialmnecode` varchar(255) COMMENT '助记码',
  `code` varchar(255) COMMENT '产品编码',
  `csaleorderbid` varchar(255) COMMENT '销售订单行',
  `id` int NOT NULL  AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `flag_deleted` int DEFAULT 0 COMMENT '是否删除',
  `version` varchar(20) COMMENT '版本',
  `create_by` varchar(50) COMMENT '创建人',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(50) COMMENT '修改人',
  `update_time` datetime COMMENT '修改时间',
  `csaleorderid` varchar(255) COMMENT 'CRM拆分订单编号',
  `handler_status` char(1) COMMENT '处理状态0未处理1已处理',
  `csaleinvoicebid` varchar(32) COMMENT 'bip销售发票行主键',
  `csaleinvoiceid` varchar(32) COMMENT 'bip销售发票主表主键',
  `dsfbtzj` varchar(32) COMMENT 'CRM开票申请ID',
  `dsfbtzj_b` varchar(32) COMMENT 'CRM开票申请行ID',
  `fopposeflag` varchar(255) COMMENT '红冲/开票标识',
  `vsrcrowno` varchar(255) COMMENT 'BIP出库单行号(来源单据表体行号)',
  `vsrccode` varchar(255) COMMENT 'BIP出库单号(来源单据号)',
  `dsfbtzjorder_b` varchar(255) COMMENT 'CRM拆分订单行(销售订单表体主键)',
  `dsfbtzjorder` varchar(255) COMMENT 'CRM拆分订单编号(销售订单表头主键)',
  `csrcbid` varchar(255) COMMENT 'BIP出库单行ID(来源单据表体主键)',
  `csrcid` varchar(255) COMMENT 'BIP表头ID(来源单据主键)',
  `ntaxmny` varchar(255) COMMENT '本币价税合计'
) ENGINE=InnoDB COMMENT='销售开票明细';

