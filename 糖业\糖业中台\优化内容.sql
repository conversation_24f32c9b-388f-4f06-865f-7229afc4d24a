-- ETL: lims-桔水各班产量 -> orange_team_product
select current_date,
       ifnull(sum(case when t2.BANBIEDM = '01' then t1.chanliang end), 0) '01',
       ifnull(sum(case when t2.BANBIEDM = '02' then t1.chanliang end), 0) '02',
       ifnull(sum(case when t2.BANBIEDM = '03' then t1.chanliang end), 0) '03'
from bi_ytbanbao_5 t1
         left join bi_ytbanbao_tb t2 on t1.id = t2.id
where t2.rq = current_date
;
select *
from orange_team_product;

-- ETL:lims当日实时桔水产量 ->orange_water_product_card
select 1                                                                                         id,
       t1.product_value_today,
       t2.product_value_total,
       ifnull(100 * t2.product_value_total / (select max(curve) from current_curve_lims ccl), 0) product_yield_total
from (select ifnull(sum(t1.chanliang), 0) product_value_today
      from bi_ytbanbao_5 t1
               join bi_ytbanbao_tb t2 on t1.id = t2.id
      where t2.rq = current_date) t1
         join
     -- 榨季总计
         (select ifnull(sum(t1.chanliang), 0) product_value_total
          from bi_ytbanbao_5 t1
                   join bi_ytbanbao_tb t2 on t1.id = t2.id
                   JOIN (SELECT start_time, end_time
                         FROM current_squeeze_info
                         WHERE current_date BETWEEN start_time AND end_time) squeeze_info
          where t2.rq >= squeeze_info.start_time
            and t2.rq <= squeeze_info.end_time) t2
;
select *
from orange_water_product_card;

-- ETL:lims当日榨季累计榨蔗量->current_curve_lims
select *
from current_curve_lims ccl;

-- ETL: lims-原糖各班产量 -> raw_sugar_team_product
select current_date,
       ifnull(sum(case when t2.BANBIEDM = '01' then t1.chanliang end), 0) '01',
       ifnull(sum(case when t2.BANBIEDM = '02' then t1.chanliang end), 0) '02',
       ifnull(sum(case when t2.BANBIEDM = '03' then t1.chanliang end), 0) '03'
from bi_ytbanbao_6 t1
         left join bi_ytbanbao_tb t2 on t1.id = t2.id
where name = '原糖'
  and t2.rq = current_date
;
select *
from raw_sugar_team_product;

-- ETL:lims-原糖各班累计产量->team_product_total
select ifnull(sum(case when t2.BANBIEDM = '01' then t1.chanliang end), 0) '01',
       ifnull(sum(case when t2.BANBIEDM = '02' then t1.chanliang end), 0) '02',
       ifnull(sum(case when t2.BANBIEDM = '03' then t1.chanliang end), 0) '03'
from bi_ytbanbao_6 t1
         left join bi_ytbanbao_tb t2 on t1.id = t2.id
         JOIN (SELECT start_time, end_time
               FROM current_squeeze_info
               WHERE current_date BETWEEN start_time AND end_time) squeeze_info
where name = '原糖'
  and t2.rq >= squeeze_info.start_time
  and t2.rq <= squeeze_info.end_time
;
select *
from team_product_total;

-- ETL:lims-原糖日产量、产率->sugar_product_today,raw_sugar_product_total,raw_sugar_product
select current_date,
       ifnull(max(CL_D), 0) product_today,
       ifnull(max(CL_Y), 0) product_total
from bi_ytribao_fac_6
where update_time = current_date
  and leibiedm = '原糖';
select *
from sugar_product_today;
select *
from raw_sugar_product_total;
select *
from raw_sugar_product;

-- ETL:lims-当日平均日洄溶糖量->material_to_sugar_rate
select current_date, RRTL_PJ, 3 type
from bi_jtribao_fac_3
where update_time = current_date;
select *
from material_to_sugar_rate;

-- ETL:lims-桔水日产量、产率->orange_water_product TODO 接口对方还未提供
    select * from group_report_day grd;

-- ETL:lims-糖浆锤度->brix_of_syrup
select current_date,
       (select ifnull(max(cd), 0)
        from bi_jtribaocn_hx_2
        where lb = '糖浆'
          and sample_name = '洄溶糖浆'
          and update_time = current_date) '洄溶糖浆',
       (select ifnull(max(cd), 0)
        from bi_jtribaocn_hx_2
        where lb = '糖浆'
          and sample_name = '浓缩糖浆'
          and update_time = current_date) '浓缩糖浆';
select *
from brix_of_syrup;

-- ETL:lims-膏蜜纯度差->honey_purity
select 'A膏蜜'                                                                                name,
       current_date,
       ifnull(t1.JGJYCDC, 0)                                                                  poor_purity,
       case t2.BANBIEDM when '01' then '甲班' when '02' then '乙班' when '03' then '丙班' end team
from bi_ytbanbao_7 t1
         join bi_ytbanbao_tb t2 on t1.id = t2.id
where t2.RQ = current_date
union all
select 'B膏蜜'                                                                                name,
       current_date,
       ifnull(t1.YGYYCDC, 0)                                                                  poor_purity,
       case t2.BANBIEDM when '01' then '甲班' when '02' then '乙班' when '03' then '丙班' end team
from bi_ytbanbao_7 t1
         join bi_ytbanbao_tb t2 on t1.id = t2.id
where t2.RQ = current_date
;
select *
from honey_purity;

-- ETL:lims上榨季同期可售蔗渣数量->sale_bagasse TODO字段不全
select (select max(zz_duifangliang) from bi_ytribao_fac_3 where update_time = current_date) current_quantity,
-- 去年同日
       (select max(zz_duifangliang)
        from bi_ytribao_fac_3
        where update_time = DATE_SUB(CURDATE(), INTERVAL 1 year))                           last_quantity;

select *
from sale_bagasse;

-- ETL:lims上榨季同期榨蔗量->capacity_last
select max(zhazheliang)
from bi_ytribao_fac_1
where update_time = DATE_SUB(CURDATE(), INTERVAL 1 year)
;
select *
from capacity_last;

-- ETL:lims各班组抽出率->extraction_rate_curve
select date_format(current_date, '%Y-%m-%d')                              date,
       ifnull(sum(case when t2.BANBIEDM = '01' then t1.CHOUCHULV end), 0) '01',
       ifnull(sum(case when t2.BANBIEDM = '02' then t1.CHOUCHULV end), 0) '02',
       ifnull(sum(case when t2.BANBIEDM = '03' then t1.CHOUCHULV end), 0) '03'
from bi_ytbanbao_1 t1
         left join bi_ytbanbao_tb t2 on t1.id = t2.id
where t2.rq = current_date;
select *
from extraction_rate_curve;

-- ETL:lims各班组蒸汽产量->steam_product_value
select current_date,
       ifnull(t1.ZHENGQICHANLIANG, 0)                                                         ZHENGQICHANLIANG,
       case t2.BANBIEDM when '01' then '甲班' when '02' then '乙班' when '03' then '丙班' end team
from bi_ytbanbao_1 t1
         join bi_ytbanbao_tb t2 on t1.id = t2.id
where t2.RQ = current_date;
select *
from steam_product_value;

-- ETL:lims各班组蔗渣燃烧量->bagasse_burned_value
select current_date,
       ifnull(t1.ZHEZHA_RSL, 0)                                                               ZHEZHA_RSL,
       case t2.BANBIEDM when '01' then '甲班' when '02' then '乙班' when '03' then '丙班' end team
from bi_ytbanbao_1 t1
         join bi_ytbanbao_tb t2 on t1.id = t2.id
where t2.RQ = current_date;
select *
from bagasse_burned_value;

-- ETL:lims各班组初混汁纯度差->team_purity_diff_middle
select current_date,
       ifnull(t1.AP, 0)                                                              '初压汁',
       case t2.BANBIEDM when '01' then '1' when '02' then '2' when '03' then '3' end team
from bi_ytbanbao_3 t1
         join bi_ytbanbao_tb t2 on t1.id = t2.id
where t1.LEIBIEDM = '初压汁'
  and t2.RQ = current_date
;
select current_date,
       ifnull(t1.AP, 0)                                                              '混合汁',
       case t2.BANBIEDM when '01' then '1' when '02' then '2' when '03' then '3' end team
from bi_ytbanbao_3 t1
         join bi_ytbanbao_tb t2 on t1.id = t2.id
where t1.LEIBIEDM = '混合汁'
  and t2.RQ = current_date
;
select *
from team_purity_diff_middle;

-- ETL:lims各班组设备生产安全率->mechanical_efficiency_curve
select date_format(current_date, '%Y-%m-%d')                                                     date,
       ifnull(sum(case when t2.BANBIEDM = '01' then t1.SHEBEISHENGCHANANQUANLV end), 0)          '01',
       ifnull(sum(case when t2.BANBIEDM = '02' then t1.SHEBEISHENGCHANANQUANLV end), 0)          '02',
       ifnull(sum(case when t2.BANBIEDM = '03' then t1.SHEBEISHENGCHANANQUANLV end), 0)          '03',
       ifnull((select SHEBEISHENGCHANANQUANLV from bi_ytribao_fac_3 where rq = current_date), 0) year_value
from bi_ytbanbao_1 t1
         left join bi_ytbanbao_tb t2 on t1.id = t2.id
where t2.rq = current_date;
select *
from mechanical_efficiency_curve;

-- ETL:lims产糖率->sugar_yield
select *
from sugar_yield
;

-- ETL:lims充电桩当日榨季累计售电量->charge_power_season
select ifnull(gongwaidianliang, 0) + ifnull(gongqitadianliang, 0)
from bi_ytribao_fac_3
where update_time = current_date;
select *
from charge_power_season;

-- ETL:lims外售电量->TODO 怎么是从IOT获取
-- ETL:lims吨溶糖用食品级氧化钙量->TODO 需要确定取值
-- ETL:lims发电->electricity_generation
select (select ifnull(zongfadianliang, 0) -- 总发电量
        from bi_ytribao_fac_3
        where update_time = current_date)                            '当日榨季累计总发电量',
       (select ifnull(zongfadianliang, 0) -- 总发电量
        from bi_ytribao_fac_3
        where update_time = DATE_SUB(current_date, INTERVAL 1 year)) '上榨季同期总发电量',
       (select ifnull(zongfadianliang, 0) - ifnull(gongqitadianliang, 0) - ifnull(gongwaidianliang, 0) +
               ifnull(haowaidianliang, 0) + ifnull(haoqitadianliang, 0) -- 总发电量-供其它电量-供外电量+耗外电量+耗其它电量
        from bi_ytribao_fac_3
        where update_time = current_date)                            '当日榨季累计生产总用电量',
       (select ifnull(zongfadianliang, 0) - ifnull(gongqitadianliang, 0) - ifnull(gongwaidianliang, 0) +
               ifnull(haowaidianliang, 0) + ifnull(haoqitadianliang, 0) -- 总发电量-供其它电量-供外电量+耗外电量+耗其它电量
        from bi_ytribao_fac_3
        where update_time = DATE_SUB(current_date, INTERVAL 1 year)) '上榨季同期生产总用电量',
       (select DIANBICHANPINTANG
        from bi_jtribao_fac_3
        where update_time = current_date)                            '当日榨季累计吨白糖用电量',
       (select DIANBICHANPINTANG
        from bi_jtribao_fac_3
        where update_time = DATE_SUB(current_date, INTERVAL 1 year)) '上榨季同期吨白糖用电量',
       (select DUNZHEHAODIANLIANG
        from bi_ytribao_fac_3
        where update_time = current_date)                            '当日榨季累计吨蔗用电量',
       (select DUNZHEHAODIANLIANG
        from bi_ytribao_fac_3
        where update_time = DATE_SUB(current_date, INTERVAL 1 year)) '上榨季同期吨蔗用电量'
;
select *
from electricity_generation;

-- ETL:lims吨蔗用电、用汽-折线图->cane_use_electricity
select current_date,
       ZHENGQIYUZHEBI     '吨蔗综合用汽',
       DUNZHEHAODIANLIANG '吨蔗用电'
from bi_ytribao_fac_1
where update_time = current_date;
select *
from cane_use_electricity;

-- ETL:lims当日人均日洄溶糖量->daily_sugar_content_pp
select RRTL_PJ / (select max(employee_number) from employee_number) '当日人均日洄溶糖量'
from bi_jtribao_fac_3
where update_time = current_date;

select *
from daily_sugar_content_pp;
;

-- ETL:lims当日原糖（含在制品）产率桔水产率->ads_lims_metric TODO:接口未提供
select *
from ads_lims_metric;

-- ETL:lims当日售电量->power_allocation,actual_electricity_sold
select ifnull(GONGWAIDIANLIANG, 0) +
       ifnull(GONGQITADIANLIANG, 0) '当日售电量'
from bi_ytribao_fac_1
where update_time = current_date;
select *
from actual_electricity_sold;
select *
from power_allocation;

-- ETL:lims蔗渣剩余率->bagasse_residual_rate,metric_statistics
select current_date,
       (select ZZ_SHENGYUYZB from bi_ytribao_fac_1 where update_time = current_date) '当日蔗渣剩余率',
       (select ZZ_SHENGYUYZB from bi_ytribao_fac_3 where update_time = current_date) '当日榨季累计蔗渣剩余率';
select *
from bagasse_residual_rate;
select *
from metric_statistics;

-- ETL:lims蒸汽使用->steam_used TODO:当日榨季累计吨蔗综合用汽量 暂未提供
select (select HaoDianChangZhengQi '当日电厂供汽量'
        from bi_ytribao_fac_1
        where update_time = current_date) '当日电厂供汽量',
       (select HaoDianChangZhengQi '当日榨季累计电厂供汽量'
        from bi_ytribao_fac_3
        where update_time = current_date) '当日榨季累计电厂供汽量',
       (select ZHENGQIBICHANPINTANG '当日单位产品用汽量'
        from bi_jtribao_fac_1
        where update_time = current_date) '当日单位产品用汽量',
       (select ZHENGQIBICHANPINTANG '当日榨季累计单位产品用汽量'
        from bi_jtribao_fac_3
        where update_time = current_date) '当日榨季累计单位产品用汽量',
       (select ZHENGQIYUZHEBI '当日吨蔗综合用汽量'
        from bi_ytribao_fac_1
        where update_time = current_date) '当日吨蔗综合用汽量',
       null                               '当日榨季累计吨蔗综合用汽量'
;
select *
from steam_used;

-- ETL:lims糖浆色值->syrup_color_num
SELECT date_format(current_date, '%Y-%m-%d')                         `current_date`,
       MAX(CASE WHEN sample_name = '浓缩糖浆' THEN sz ELSE 0 END) AS '浓缩糖浆',
       MAX(CASE WHEN sample_name = '脱色糖浆' THEN sz ELSE 0 END) AS '脱色糖浆',
       MAX(CASE WHEN sample_name = '滤清糖浆' THEN sz ELSE 0 END) AS '滤清糖浆',
       MAX(CASE WHEN sample_name = '洄溶糖浆' THEN sz ELSE 0 END) AS '洄溶糖浆'
FROM bi_jtribaocn_hx_2
WHERE lb = '糖浆'
  AND update_time = current_date;
select *
from syrup_color_num;

-- ETL:lims燃料分配->fuel_allocation,sale_bagasse  TODO:后面处理
select *
from fuel_allocation;

-- ETL:lims溶糖量 TODO: 当日榨季累计洄溶糖量 未找到
select current_date, RRTL_PJ '平均日溶糖量', 3 type
from bi_jtribao_fac_3
where update_time = current_date;

-- ETL:lims日能耗->chartographed_sugar_energy
select date_format(current_date, '%Y-%m-%d') `current_date`,
       DIANBIHUIRONGTANG                     '吨洄溶糖耗电量',
       ZHENGQIBIHUIRONGTANG                  '吨洄溶糖耗蒸汽量'
from bi_jtribao_fac_1
where update_time = current_date;
select *
from chartographed_sugar_energy;

-- ETL:lims当日设备生产安全率->equipment_safety_rate_daily
select max(SHEBEISHENGCHANANQUANLV) '设备生产安全率'
from bi_ytribao_fac_3
where update_time = current_date;
select *
from equipment_safety_rate_daily;

-- ETL:lims当日榨季累计设备生产安全率->raw_suger_daliy,mechanical_efficiency
select max(SHEBEISHENGCHANANQUANLV) '设备生产安全率'
from bi_ytribao_fac_3
where update_time = current_date;
select *
from raw_suger_daliy;
select *
from mechanical_efficiency;

-- ETL:lims当日榨蔗量->curve_of_tcd
select current_date,
       max(ZHAZHELIANG) '榨蔗量'
from bi_ytribao_fac_1
where update_time = current_date;
select *
from curve_of_tcd;

-- ETL:lims当日桔水产率->final_molasses
select max(ftm_chanliang) '当日桔水产率'
from group_report_day
where update_time = current_date;
select *
from final_molasses;

-- ETL:lims当日榨季累计桔水产率->final_molasses
select max(waste_molasses_yield) '当日榨季累计桔水产率'
from group_report_year
where update_time = current_date;

-- ETL:lims当日榨季累计桔水产量
select max(waste_molasses_production) '当日榨季累计桔水产量'
from group_report_year
where update_time = current_date;

-- ETL:lims当日蔗渣水分->moisture_content_bagasse
select max(ZZ_SHUIFEN) '当日蔗渣水分'
from bi_ytribao_fac_1
where update_time = current_date;
select *
from moisture_content_bagasse;

-- ETL:lims混合糖（含在制品）榨季累计产率->refined_sugar_daily
select max(HUNHETCHANLV) '混合糖（含在制品）榨季累计产率'
from bi_ytribao_fac_3
where update_time = current_date;
select *
from refined_sugar_daily;

-- ETL:lims混合糖（含在制品）当日产率->mixed_yield
select current_date,
       (select max(HUNHETCHANLV) from bi_ytribao_fac_1 where update_time = current_date) '混合糖（含在制品）当日产率',
       (select max(HUNHETCHANLV) from bi_ytribao_fac_3 where update_time = current_date) '混合糖（含在制品）榨季累计产率'
select *
from mixed_yield;

-- lims榨季累计能耗->tons_chartograph_steam,tons_chartograph_electricity
select current_date, t1.*, t2.*
from (select max(DIANBIHUIRONGTANG)    '当日榨季吨洄溶糖耗电量',
             max(ZHENGQIBIHUIRONGTANG) '当日榨季吨洄溶糖耗蒸汽量'
      from bi_jtribao_fac_3
      where update_time = current_date) t1
         join
     (select max(DIANBIHUIRONGTANG)    '上榨季吨洄溶糖耗电量',
             max(ZHENGQIBIHUIRONGTANG) '上榨季吨洄溶糖耗蒸汽量'
      from bi_jtribao_fac_3
      where update_time = DATE_SUB(CURDATE(), INTERVAL 1 year)) t2;

-- ETL:lims清混汁纯度差->
-- 70%*澄清汁视纯度+30%滤清汁视纯度-混合汁视纯度 case t2.BANBIEDM when '01' then '甲班' when '02' then '乙班' when '03' then '丙班' end as '班别'
select t1.ap '澄清汁',
       t2.ap '滤清汁',
       t3.ap '混合汁',
       case t2.BANBIEDM when '01' then '1' when '02' then '2' when '03' then '3' end as '班别'
from (select ifnull(t1.ap,0) ap ,
             t2.BANBIEDM
      from bi_ytbanbao_3 t1
               join bi_ytbanbao_tb t2 on t1.id = t2.id
      where update_time = '2025-02-01'
        and t1.leibiedm = '澄清汁') t1
         join
     (select ifnull(t1.ap,0) ap  ,
             t2.BANBIEDM
      from bi_ytbanbao_3 t1
               join bi_ytbanbao_tb t2 on t1.id = t2.id
      where update_time = '2025-02-01'
        and t1.leibiedm = '滤清汁') t2 on t1.BANBIEDM = t2.BANBIEDM
         join
     (select ifnull(t1.ap,0) ap ,
             t2.BANBIEDM
      from bi_ytbanbao_3 t1
               join bi_ytbanbao_tb t2 on t1.id = t2.id
      where update_time = '2025-02-01'
        and t1.leibiedm = '混合汁') t3 on t3.BANBIEDM = t2.BANBIEDM;
select *
from team_puritymix_diff_middle;
