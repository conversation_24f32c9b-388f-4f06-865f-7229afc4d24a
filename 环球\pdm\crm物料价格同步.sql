select sub_class,main_class,mnemonic_code,material_name,brand,material_code,specification,specification_cn
from pdm_material where material_code='1010201010000130' ;
select id,detail_value from pdm_param_detail ppd where id in ('6759','6922','0');

-- crm同步接口数据:物料价格管理_同步  0 纸张 1辅料 2包材
select
       (select detail_value from pdm_param_detail where id = main_class) main_class_name,
       (select detail_value from pdm_param_detail where id = sub_class) sub_class_name,
       mnemonic_code,
       material_name,
       (select detail_value from pdm_param_detail where id = brand) brand_name,
       material_code
from pdm_material where material_code='1010101010000386' ;

select id,status,specification,pm.* from pdm_material pm where material_code='1010102080000117';
select * from pdm_param_detail where id=27634;
select * from pdm_material where
    buy_unit = :id or default_factory = :id or procure_unit = :id or standard_unit = :id or sub_class = :id or main_class = :id or gram_weight = :id
                              or specification = :id or brand = :id and flag_deleted = 0;

select specification,id from pdm_material where specification not in (
    select id from pdm_param_detail ppd
    );
;
select material_code,pm.* from pdm_material pm  where material_code='1010301010000459' ;


select a.*,b.detail_value as big_code,c.detail_value as small_code from pdm_material a
                                                                            left join pdm_param_detail b on a.main_class = b.id
                                                                            left join pdm_param_detail c on a.sub_class = c.id
where (category = 2 or category = 3) and a.flag_deleted = 0 and a.status = 1
#   and a.main_class in ('6899')
#   and a.sub_class in ('6758')
#   and a.specification in (:specification)
  and ((:material_code is null or :material_code = "") or (a.material_code like concat('%',:material_code,'%')))
  and ((:material_name is null or :material_name = "") or (a.material_name like concat('%',:material_name,'%')))
  and a.material_code not in (:arr)
order by a.material_code
;
