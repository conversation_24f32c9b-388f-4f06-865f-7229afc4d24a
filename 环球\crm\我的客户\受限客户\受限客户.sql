-- 默认策略
select *
from crm_customer_strategy ccs;
-- 客户策略查询
select *
from crm_restricted_strategy;
-- 策略子表
select *
from crm_restricted_strategy_list;
-- 关联执行逻辑
select t2.customer_parameters, conditions, t2.days, and_or
from crm_restricted_strategy t1
         join crm_restricted_strategy_list t2 on t1.restricted_strategy_task_code = t2.restricted_strategy_task_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
order by t2.id asc
;0 10 1 * * ? *

-- 查找客户
select *
from crm_cust_basic
where flag_deleted = 0
  and cust_status = 2
  and if(:user_size > 0, cust_manager_code in (:user_arr), 1);
