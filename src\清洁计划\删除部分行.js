var original_arr=[{id:1},{id:2},{id:3},{id:4}]
var data_arr=[{id:6},{id:2}]
//将data_arr中id不在original_arr中的id的数据加入original_arr并展示
for(var i=0;i<data_arr.length;i++){
    var flag=true
    for(var j=0;j<original_arr.length;j++){
        if(data_arr[i].id==original_arr[j].id){
            flag=false
            break
        }
    }
    if(flag){
        original_arr.push(data_arr[i])
    }
}
console.log(JSON.stringify(original_arr))