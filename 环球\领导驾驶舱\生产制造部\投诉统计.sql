select production_batch_number,
       complaint_time,
       product_name,
       complaint_type,
       complaint_content,
       treatment_measure
from (select production_batch_number,
             create_time                             as complaint_time,
             material_name                           as product_name,
             '内诉'                                  as complaint_type,
             abnormal_describe                       as complaint_content,
             (select QC_managers_measure
              from cockpit.ods_qms_abnormal_feedback_treatment t2
              where t2.flag_deleted = 0
                and t2.abnormal_feedback_id = t1.id) as treatment_measure
      from cockpit.ods_abnormal_feedback t1
      where t1.flag_deleted = 0
        AND ((:start_date is null or :start_date = '') or (t1.create_time >= :start_date))
        AND ((:end_date is null or :end_date = '') or (t1.create_time <= :end_date))
        AND t1.production_batch_number is not null
      union
      select batch_number as production_batch_number,
             create_time  as complaint_time,
             product_name,
             '客诉'       as complaint_type,
             complaint_content,
             final_remark as treatment_measure
      from cockpit.ods_customer_complaint t3
      where t3.flag_deleted = 0
        and batch_number is not null
        AND ((:start_date is null or :start_date = '') or (t3.create_time >= :start_date))
        AND ((:end_date is null or :end_date = '') or (t3.create_time <= :end_date))
      ) temp
order by production_batch_number, complaint_type
# limit :page_size offset :offset
;

