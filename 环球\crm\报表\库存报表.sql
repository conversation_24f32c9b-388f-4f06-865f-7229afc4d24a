
select * from dwd_crm_sale_inventory_detail where production_order='25030163A';
select * from dwd_crm_sale_inventory_detail where production_order='L25030163A';
select * from ods_crm_sales_order_product ocsop where sales_order_code='25030181A';
select order_quantity_after_split,ocsop.* from ods_crm_sales_order_product ocsop where sales_order_code='25030163A';


-- 库存数量,约1191 过滤掉非产品 212
select vbatchcode,
       materialcode,
       materialname,
       sum(nonhandnum)   nonhandnum,
       min(dinbounddate) dinbounddate
from ods_bip_inventory  t1
where  materialcode in (select product_code from ods_pdm_product_client where flag_deleted=0 group by product_code)
and vbatchcode='L25020231A0201'
group by vbatchcode, materialcode,materialname;
 ;
select * from ods_pdm_product_client oppc where product_code='ZDZY0000302';

-- mom 产品
select distinct t3.major_categories,
                t4.data_value main_class,
                t3.subclass,
                t5.data_value sub_class,
                t3.unit_price_no,
                t2.production_batch_number,
                t1.erp_production_order
from ods_pm_sale_order t1
         left join ods_pm_job_order t2 on t2.order_code = t1.order_number and t2.flag_deleted = 0
         left join ods_pm_order_product t3
                   on t2.order_code = t3.sales_number and t2.material_code = t3.product_number and
                      t3.flag_deleted = 0
left join ods_mes_parameter_data t4 on t4.id=t3.major_categories
left join ods_mes_parameter_data t5 on t5.id=t3.subclass

;

-- mom组合 1199
select distinct t1.vbatchcode,t1.materialcode,t1.materialname,t1.nonhandnum,t1.dinbounddate,t2.erp_production_order,t2.order_code, t2.main_class,t2.sub_class,t2.unit_price_no
from (select vbatchcode,
             materialcode,
             materialname,
             sum(nonhandnum)   nonhandnum,
             min(dinbounddate) dinbounddate
      from ods_bip_inventory  t1
      where  materialcode in (select product_code from ods_pdm_product_client where flag_deleted=0 group by product_code)
      group by vbatchcode, materialcode,materialname) t1
         left join (select distinct t3.major_categories,
                                    t4.data_value main_class,
                                    t3.subclass,
                                    t5.data_value sub_class,
                                    t3.unit_price_no,
                                    t2.production_batch_number,
                                    t2.order_code,
                                    t1.erp_production_order,
                                    t2.material_code
                    from ods_pm_sale_order t1
                             left join ods_pm_job_order t2 on t2.order_code = t1.order_number and t2.flag_deleted = 0
                             left join ods_pm_order_product t3
                                       on t2.order_code = t3.sales_number and t2.material_code = t3.product_number and
                                          t3.flag_deleted = 0
                             left join ods_mes_parameter_data t4 on t4.id=t3.major_categories
                             left join ods_mes_parameter_data t5 on t5.id=t3.subclass) t2
                   on t1.vbatchcode = t2.production_batch_number and t1.materialcode = t2.material_code
;

-- crm 1935
select distinct
       t3.deparment_code,
       t3.deparment_name,
       t3.department_region,
       t3.sales_assistant_code,
       t3.sales_assistant_name,
       t3.cust_manager_code,
       t3.cust_manager_name,
       t3.cust_type,
       t3.cust_code,
       t3.cust_name,
       t1.sales_order_code,
       t1.factory_assigned,
       t1.create_time,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       t2.unit_price_exclusive,
       t2.exchange_rate,
       sum(order_quantity_after_split) order_quantity_after_split
from ods_crm_sales_order t1
         left join cockpit.ods_crm_sales_order_product t2
                   on t1.sales_order_code = t2.sales_order_code and t2.flag_deleted = 0
         left join cockpit.ods_crm_cust_basic t3
                   on t3.cust_code = t1.cust_code and t3.flag_deleted = 0 and t3.cust_type not in (0, 1) and
                      cust_status = 2
where t1.flag_deleted = 0
group by t2.sales_order_code, t2.material_code,
         t3.deparment_code,
         t3.deparment_name,
         t3.department_region,
         t3.sales_assistant_code,
         t3.sales_assistant_name,
         t3.cust_manager_code,
         t3.cust_manager_name,
         t3.cust_type,
         t3.cust_code,
         t3.cust_name,
         t1.factory_assigned,
         t1.create_time,
         t2.main_class,
         t2.sub_class,
         t2.material_name,
         t2.unit_price_exclusive,
         t2.exchange_rate;
;

-- 第一次ETL,组产品无法显示
select  '西安环球'                                                                            sale_company,
        deparment_code,
        deparment_name,
        department_region region,
        sales_assistant_code,
        sales_assistant_name,
        cust_manager_code,
        cust_manager_name,
        cust_type,
        CASE
            WHEN cust_type = 0 THEN '新建客户'
            WHEN cust_type = 1 THEN '公海客户'
            WHEN cust_type = 2 THEN '合作客户'
            WHEN cust_type = 3 THEN '开发中客户'
            WHEN cust_type = 4 THEN '受限客户'
            END                                                                  cust_type_name,
        cust_code,
        cust_name,
        sales_order_code,
        factory_assigned,
        date_format(create_time, '%Y-%m-%d') sale_order_date,
        temp1.main_class,
        temp1.sub_class,
        temp1.materialcode material_code,
        temp1.materialname material_name,
        order_quantity_after_split                                                      order_quantity,       -- 订单数量
        order_code                                                                            production_order,     -- 生产单号
        vbatchcode                                                                            pk_batchcode,         -- 入库批号
        date_format(dinbounddate, '%Y-%m-%d')                                                 inventory_date,       -- 入库日期
        nonhandnum                                                                       qty,                  -- 在库数量
        round(unit_price_no, 4)                                        unit_price_exclusive, -- 未含税单价
        round(nonhandnum * unit_price_no, 2)                           inventory_cost,       -- 库存成本
        datediff(current_date, dinbounddate) item_age,                    -- 库龄
        temp2.product_version,
        temp2.remark,
        temp2.object_id
from (
      select distinct t1.vbatchcode,t1.materialcode,t1.materialname,t1.nonhandnum,t1.dinbounddate,t2.erp_production_order,t2.order_code, t2.main_class,t2.sub_class,t2.unit_price_no,t2.bip_detail_no
      from (select vbatchcode,
                   materialcode,
                   materialname,
                   sum(nonhandnum)   nonhandnum,
                   min(dinbounddate) dinbounddate
            from ods_bip_inventory t1
            where  materialcode in (select product_code from ods_pdm_product_client where flag_deleted=0 group by product_code)
            group by vbatchcode, materialcode,materialname) t1
               left join (select distinct t3.major_categories,
                                          t4.data_value main_class,
                                          t3.subclass,
                                          t5.data_value sub_class,
                                          t3.unit_price_no,
                                          t2.production_batch_number,
                                          t2.order_code,
                                          t1.erp_production_order,
                                          t2.material_code,
                                          t3.bip_detail_no
                          from ods_pm_sale_order t1
                                   left join ods_pm_job_order t2 on t2.order_code = t1.order_number and t2.flag_deleted = 0
                                   left join ods_pm_order_product t3
                                             on t2.order_code = t3.sales_number and
                                                t3.id=t2.source_detail_id and
                                                t2.material_code = t3.product_number and
                                                t3.flag_deleted = 0
                                   left join ods_mes_parameter_data t4 on t4.id=t3.major_categories
                                   left join ods_mes_parameter_data t5 on t5.id=t3.subclass) t2
                         on t1.vbatchcode = t2.production_batch_number and t1.materialcode = t2.material_code
               )temp1 left join
    (select distinct t3.deparment_code,
            t3.deparment_name,
            t3.department_region,
            t3.sales_assistant_code,
            t3.sales_assistant_name,
            t3.cust_manager_code,
            t3.cust_manager_name,
            t3.cust_type,
            t3.cust_code,
            t3.cust_name,
            t1.sales_order_code,
            t1.factory_assigned,
            t1.create_time,
            t2.main_class,
            t2.sub_class,
            t2.material_code,
            t2.material_name,
            t2.product_version,
            t2.unit_price_exclusive,
            t2.exchange_rate,
            sum(order_quantity_after_split) order_quantity_after_split,
            t2.remark,
            t2.csaleorderbid,
            t2.object_id
     from ods_crm_sales_order t1
              left join cockpit.ods_crm_sales_order_product t2
                        on t1.sales_order_code = t2.sales_order_code and t2.flag_deleted = 0
              left join cockpit.ods_crm_cust_basic t3
                        on t3.cust_code = t1.cust_code and t3.flag_deleted = 0 and t3.cust_type not in (0, 1) and
                           cust_status = 2
     where t1.flag_deleted = 0
     group by t2.sales_order_code, t2.material_code,
              t3.deparment_code,
              t3.deparment_name,
              t3.department_region,
              t3.sales_assistant_code,
              t3.sales_assistant_name,
              t3.cust_manager_code,
              t3.cust_manager_name,
              t3.cust_type,
              t3.cust_code,
              t3.cust_name,
              t1.factory_assigned,
              t1.create_time,
              t2.main_class,
              t2.sub_class,
              t2.material_name,
              t2.unit_price_exclusive,
              t2.exchange_rate,
              t2.object_id
     )temp2 on temp1.materialcode = temp2.material_code
and temp1.erp_production_order=temp2.sales_order_code
and temp1.bip_detail_no=temp2.csaleorderbid
order by dinbounddate desc;



-- 第二次ETL ,补全组产品数据
select  temp1.id,
       '西安环球'                   sale_company,
       temp2.deparment_code,
       temp2.deparment_name,
       temp2.department_region region,
       temp2.sales_assistant_code,
       temp2.sales_assistant_name,
       temp2.cust_manager_code,
       temp2.cust_manager_name,
       temp2.cust_type,
       CASE
           WHEN temp2.cust_type = 0 THEN '新建客户'
           WHEN temp2.cust_type = 1 THEN '公海客户'
           WHEN temp2.cust_type = 2 THEN '合作客户'
           WHEN temp2.cust_type = 3 THEN '开发中客户'
           WHEN temp2.cust_type = 4 THEN '受限客户'
           END                                                                  cust_type_name,
       temp2.cust_code,
       temp2.cust_name,
       temp2.sales_order_code,
       temp2.factory_assigned,
       date_format(temp2.create_time, '%Y-%m-%d') sale_order_date,
       temp2.main_class,
       temp2.sub_class,
       temp1.material_code,
       temp1.material_name,
       temp2.order_quantity_after_split                                                      order_quantity,       -- 订单数量
       temp1.production_order                                                                            production_order,     -- 生产单号
       temp1.inventory_lot_num                                                                            pk_batchcode,         -- 入库批号
       temp1.inventory_date                                                 inventory_date,       -- 入库日期
       temp1.inventory_qty                                                                         inventory_qty,        -- 在库数量                                                                       qty,                  -- 在库数量
       round(temp2.unit_price_exclusive * temp2.exchange_rate, 4)                                        unit_price_exclusive, -- 未含税单价
       round(temp1.inventory_qty * temp2.unit_price_exclusive * exchange_rate, 2)                      inventory_cost,       -- 库存成本
       temp1. item_age,              -- 库龄,
       temp2.product_version,
       temp2.remark
from
(select distinct t1.*,t2.product_code from dwd_crm_sale_inventory_detail t1
         join ods_pdm_product_bom t2 on t1.material_code=t2.material_code and categroy in (3,7)
        join ods_crm_sales_order_product t3 on t1.production_order=concat('L',t3.sales_order_code) and t3.material_code=t2.product_code
where t1.sales_order_code is null)temp1
left join (
    select distinct t3.deparment_code,
                    t3.deparment_name,
                    t3.department_region,
                    t3.sales_assistant_code,
                    t3.sales_assistant_name,
                    t3.cust_manager_code,
                    t3.cust_manager_name,
                    t3.cust_type,
                    t3.cust_code,
                    t3.cust_name,
                    t1.sales_order_code,
                    t1.factory_assigned,
                    t1.create_time,
                    t2.main_class,
                    t2.sub_class,
                    t2.material_code,
                    t2.material_name,
                    t2.unit_price_exclusive,
                    t2.exchange_rate,
                    sum(order_quantity_after_split) order_quantity_after_split,
                    t2.product_version,
                    t2.remark
    from ods_crm_sales_order t1
             left join cockpit.ods_crm_sales_order_product t2
                       on t1.sales_order_code = t2.sales_order_code and t2.flag_deleted = 0
             left join cockpit.ods_crm_cust_basic t3
                       on t3.cust_code = t1.cust_code and t3.flag_deleted = 0 and t3.cust_type not in (0, 1) and
                          cust_status = 2
    where t1.flag_deleted = 0
    group by t2.sales_order_code, t2.material_code,
             t3.deparment_code,
             t3.deparment_name,
             t3.department_region,
             t3.sales_assistant_code,
             t3.sales_assistant_name,
             t3.cust_manager_code,
             t3.cust_manager_name,
             t3.cust_type,
             t3.cust_code,
             t3.cust_name,
             t1.factory_assigned,
             t1.create_time,
             t2.main_class,
             t2.sub_class,
             t2.material_name,
             t2.unit_price_exclusive,
             t2.exchange_rate
)temp2 on temp1.production_order=concat('L',temp2.sales_order_code) and  temp1.product_code=temp2.material_code;



select t1.* from ods_pdm_product_bom t1 where t1.material_code in
(select material_code from dwd_crm_sale_inventory_detail where sales_order_code is null);



-- bip库存
drop table `ods_bip_inventory`;
CREATE TABLE `ods_bip_inventory` (
                                     `id` int NOT NULL AUTO_INCREMENT primary key COMMENT '主键',
                                     `materialname` varchar(255)  COMMENT '物料名称',
                                     `version` int  COMMENT '物料版本',
                                     `cunitid` varchar(255)  COMMENT '计量单位pk',
                                     `nonhandnum` varchar(255)  COMMENT '结存主数量',
                                     `pk_org` varchar(255)  COMMENT '组织主键',
                                     `dinbounddate` date  NULL COMMENT '首次入库日期',
                                     `measdocname` varchar(255)  COMMENT '计量单位',
                                     `vbatchcode` varchar(255)  COMMENT '批次',
                                     `materialcode` varchar(255)  COMMENT '物料编码',
                                     `materialvid` varchar(255)  COMMENT '物料版本主键',
                                     `materialoid` varchar(255)  COMMENT '物料主键',
                                     `pk_batchcode` varchar(255)  COMMENT '批次主键',
                                     `nonhandastnum` varchar(255)  COMMENT '结存辅数量'
)  COMMENT='bip库存表';

-- dwd 明细表
drop table dwd_crm_sale_inventory_detail;
create table dwd_crm_sale_inventory_detail
(
    id                   int auto_increment comment 'id'
        primary key,
    sale_company         varchar(100)   not null comment '销售公司',
    department_code      varchar(255)   null comment '部门编码',
    department_name      varchar(255)   null comment '部门名称',
    region               varchar(100)   null comment '区域',
    sales_assistant_code varchar(1000)  null comment '销售助理编号',
    sales_assistant_name varchar(1000)  null comment '销售助理名称',
    cust_manager_code    varchar(100)   null comment '负责人编号',
    cust_manager_name    varchar(100)   null comment '负责人名称',
    cust_type            varchar(100)   null comment '客户类别编码',
    cust_type_name       varchar(100)   null comment '客户类别名称',
    cust_code            varchar(100)   null comment '客户编号',
    cust_name            varchar(100)   null comment '客户名称',
    sales_order_code     varchar(100)   null comment '拆分订单号',
    factory_assigned     varchar(100)   null comment '下达工厂',
    sales_order_date     date           null comment '订单日期',
    main_class           varchar(100)   null comment '产品大类',
    sub_class            varchar(100)   null comment '产品小类',
    material_code        varchar(100)   null comment '产品编码',
    material_name        varchar(100)   null comment '产品名称',
    order_quantity       varchar(100)   null comment '订单数量',
    production_order     varchar(100)   null comment '生产单号',
    inventory_lot_num    varchar(100)   null comment '入库批号',
    inventory_date       date           null comment '入库日期',
    inventory_qty        varchar(100)   null comment '在库数量',
    unit_price_exclusive varchar(100)   null comment '未含税单价',
    inventory_cost       decimal(20, 2) null comment '库存成本',
    item_age             varchar(100)   null comment '产品库龄',
    product_version             varchar(50)   null comment '产品版本号',
    remark             varchar(550)   null comment '订单备注'
)
    comment 'crm库存报表' engine = InnoDB;

select * from dwd_crm_sale_inventory_detail where sales_order_code='25030181A';

-- 查询接口
select sale_company,
       department_code,
       department_name,
       region,
       cust_manager_code,
       cust_manager_name,
       cust_type,
       cust_type_name,
       cust_code,
       cust_name,
       sales_order_code,
       factory_assigned,
       sales_order_date,
       main_class,
       sub_class,
       material_code,
       material_name,
       order_quantity,
       production_order,
       inventory_lot_num,
       inventory_date,
       inventory_qty,
       round(unit_price_exclusive, 4) unit_price_exclusive,
       round(inventory_cost, 2)       inventory_cost,
       item_age,
       product_version,
       remark
from cockpit.dwd_crm_sale_inventory_detail
where
   if(:admin,
    1,
    if(:cust_code_size>0, cust_code in (:cust_code_arr), 1)
    )
  AND ((:deparment_code IS NULL OR :deparment_code = '') OR (department_code = :deparment_code))
  AND ((:region IS NULL OR :region = '') OR (region = :region))
  AND ((:cust_code IS NULL OR :cust_code = '') OR (cust_code = :cust_code))
  AND ((:cust_name IS NULL OR :cust_name = '') OR (cust_name like concat('%', :cust_name, '%')))
  AND ((:material_code IS NULL OR :material_code = '') OR (material_code = :material_code))
  AND ((:material_name IS NULL OR :material_name = '') OR (material_name like concat('%', :material_name, '%')))
  AND ((:inventory_start_date IS NULL OR :inventory_start_date = '') OR (inventory_date >= :inventory_start_date))
  AND ((:inventory_end_date IS NULL OR :inventory_end_date = '') OR (inventory_date <= :inventory_end_date))
  AND if(:cust_manager_size>0, cust_manager_code in (:cust_manager_arr), 1)
  AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (factory_assigned = :factory_assigned))
  AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR (sales_order_code = :sales_order_code))
  AND if(:sales_assistant_size>0,sales_assistant_code REGEXP :sales_assistant_str,1)
order by inventory_date asc
limit :page_size offset :offset
;

-- 合计
select '合计'                                                  sale_company,
       sum(inventory_qty)                                      inventory_qty,
       round(sum(inventory_cost), 2)                           inventory_cost
from cockpit.dwd_crm_sale_inventory_detail
where if(:admin,
         if(:condition, cust_manager_code in (:cust_manager_code_arr), 1),
         if(:condition,
            cust_manager_code in (:cust_manager_code_arr) and sales_assistant_code like concat('%', :user_id, '%'),
            cust_manager_code in (:cust_manager_code_arr) or sales_assistant_code like concat('%', :user_id, '%'))
      )
  AND ((:region IS NULL OR :region = '') OR (region = :region))
  AND ((:cust_code IS NULL OR :cust_code = '') OR (cust_code = :cust_code))
  AND ((:cust_name IS NULL OR :cust_name = '') OR (cust_name like concat('%', :cust_name, '%')))
  AND ((:material_code IS NULL OR :material_code = '') OR (material_code = :material_code))
  AND ((:material_name IS NULL OR :material_name = '') OR (material_name like concat('%', :material_name, '%')))
  AND ((:inventory_start_date IS NULL OR :inventory_start_date = '') OR (inventory_date >= :inventory_start_date))
  AND ((:inventory_end_date IS NULL OR :inventory_end_date = '') OR (inventory_date <= :inventory_end_date))
  AND if(:cust_manager_size>0, cust_manager_code in (:cust_manager_arr), 1)
  AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (factory_assigned = :factory_assigned))
  AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR (sales_order_code = :sales_order_code))
  AND if(:sales_assistant_size>0,sales_assistant_code REGEXP :sales_assistant_str,1)
;

