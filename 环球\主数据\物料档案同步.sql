select * from mes_material_file
where material_code  in ('1010101080000096');
select id,status,product_status,category,material_code,file_type from mes_product_version mpv
                                where material_code  in ('1010101080000096');
SELECT material_code
FROM mes_material_file
where flag_deleted=0
GROUP BY material_code
HAVING COUNT(*) > 1 ;

select * from mes_parameter_data mpd where id =8117;

select material_code,product_version,create_time from
(select t1.material_code t1_material_code, t1.product_version t1_product_version, t2.material_code, t2.product_version,t2.create_time
 from mes_product_version t1
          right join h3chq_pdmbusiness1704287270935.pdm_product_version t2
                     on t1.product_version = t2.product_version and t1.material_code = t2.material_code
 join h3chq_pdmbusiness1704287270935.pdm_product_bom t3 on t2.material_code=t3.material_code and t3.product_version=t2.product_version
 where t2.create_time>='2025-02-01 00:00:00' and t3.categroy=3 and t2.flag_deleted=0
 ) temp
where temp.t1_material_code is null order by  create_time desc
;
select * from h3chq_pdmbusiness1704287270935.pdm_material where category=4 and create_time>'2025-03-01 00:00:00';
select procure_unit,procure_unit_cn,standard_unit,standard_unit_cn,pm.* from mes_material_file pm
                                                                        where procure_unit is null;
