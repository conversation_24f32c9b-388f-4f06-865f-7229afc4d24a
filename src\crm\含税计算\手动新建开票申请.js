var applyObj={"outbound_line_no":"10","sales_person":"杨鹏","product_code":"SMSW0000090","outbound_date":"2025-02-21 09:51:10","tax_rate":"13","create_by":"张琳娟","update_time":"2025-03-12 15:49:33","outbound_status":"3","origin_ammount_exclude_tax":"28713.00","outbound_no":"1XC2502000005","enable_num":996767,"id":5,"update_by":"1731954709814677505","sales_person_id":"1838574991895494658","zj_code":"SMYY001913","flag_deleted":0,"outbound_header":"1001A61000000099HUSF","product_version":"3","customer_no":"C000590","create_time":"2025-02-21 14:53:56","exchange_rate":"1","split_order_no":"25020104A","actual_ship_quantity":1000000,"outbound_line_id":"1001A61000000099HUSG","origin_ammount_include_tax":"32446.00","product_name":"新冠说明书","ship_quantity":1000000,"version":"1.0","settlement_currency":"人民币","origin_unit_price_include_tax":"0.032446","split_order_line_no":"1","origin_unit_price_exclude_tax":"0.028713","price_including_tax":"0.032446","customer_name":"成都威斯克生物医药有限公司"}
var productObj={"settlement_currency_amount_exclusive":"28713.00","settlement_currency_price_exclusive":"0.028713","quantity_fluctuation":"±3","main_class":"说明书类","tax_rate":"13","product_quantity":"1000000","create_by":"1838574991891300357","settlement_currency_amount_inclusive":"32446.00","update_time":"2025-03-12 15:30:01","contract_product_line_number":"1","mnemonic_code":"SMYY001913","id":148,"update_by":"null","material_name":"新冠说明书","flag_deleted":0,"product_version":"3.1","amount_exclusive_tax":"28713.00","exchange_rate":"1","create_time":"2025-02-18 11:29:32","contract_management_code":"25020104","amount_tax_inclusive":"32446.00","unit_price":"0.032446","object_id":"210003","delivered_quantity":"1000000","settlement_currency":"人民币","total_order_quantity":"1000000","delivery_date":"2025-02-18","sub_class":"生物制品类","csaleorderbid":"1001A61000000099G1M2","sales_order_code":"25020104A","order_quantity_after_split":"1000000","current_issue_quantity":"1000000","issued_quantity":"0","invoiced_quantity":"3000","standard_unit":"张","grade_name":"重组新型冠状病毒疫苗（Sf9细胞）说明书（芬欧汇川双胶）","unit_price_exclusive":"0.028713","settlement_currency_price_inclusive":"0.032446","material_code":"SMSW0000090"}

//订单原结算币种单价（不含税）
applyObj.origin_unit_price_exclude_tax = Number(productObj.settlement_currency_price_exclusive).toFixed(6)
//拆分订单的结算币种单价（含税）
applyObj.origin_unit_price_include_tax = Number(productObj.settlement_currency_price_inclusive).toFixed(6)
//拆分订单的结算币种金额（不含税）
applyObj.origin_ammount_exclude_tax = Number(productObj.settlement_currency_amount_exclusive).toFixed(2)
//拆分订单的结算币种金额（含税）
applyObj.origin_ammount_include_tax = Number(productObj.settlement_currency_amount_inclusive).toFixed(2)

var product_quantity = Number(applyObj.enable_num)
var abs_tax_rate=Math.abs(productObj.tax_rate)
var exchange_rate=Number(productObj.exchange_rate)
//开票单价
applyObj.invoice_price = applyObj.origin_unit_price_exclude_tax
//结算币单价不含税
applyObj.jsb_unit_price_exclude_tax = Number(productObj.settlement_currency_price_exclusive).toFixed(6)
//结算币金额不含税
applyObj.jsb_amount_exclude_tax = (applyObj.jsb_unit_price_exclude_tax * product_quantity).toFixed(2)
//金额不含税
applyObj.amount_exclusive_tax = (Number(applyObj.jsb_amount_exclude_tax) * exchange_rate).toFixed(2)
//单价不含税
applyObj.unit_price_exclusive = (Number(applyObj['amount_exclusive_tax']) / product_quantity).toFixed(6)
//税额
applyObj.tax_diff = (Number(applyObj['amount_exclusive_tax']) * abs_tax_rate / 100).toFixed(2)
//金额含税
applyObj.amount_tax_inclusive = (Number(applyObj['amount_exclusive_tax']) + Number(applyObj['tax_diff'])).toFixed(2)
//结算币金额含税
applyObj.jsb_amount_include_tax = (Number(applyObj['amount_tax_inclusive']) / exchange_rate).toFixed(2)
//结算币种单价（含税）
applyObj.jsb_unit_price_include_tax = (Number(applyObj['jsb_amount_include_tax']) / product_quantity).toFixed(6)
//单价含税
applyObj.price_including_tax = (Number(applyObj['amount_tax_inclusive']) / product_quantity).toFixed(6)

return applyObj