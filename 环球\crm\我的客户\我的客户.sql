-- C001424->C004336
select cust_status, tax_rate,t1.*
from crm_cust_basic t1
where
    1=1
#    and cust_name like '%瀚邦%'
  and cust_code='C000331'
# and cust_mnemonic_code='C000331'
;

select id,
    cust_status,cust_manager_code,cust_manager_name,department_region,deparment_code,deparment_name,ccb.* from crm_cust_basic ccb
where cust_name like '%瀚邦%'
;
select * from crm_record where logic='bip客户_新增(ufinterface)' order by update_time desc;
select * from crm_record where logic='bip客户_修改' and paramL like '%滨瀚%' order by update_time desc;

select * from crm_restricted_strategy crs where status=0 and flag_deleted=0;
select *
from crm_cust_transport cct
where
    1=1
#   and cust_code = 'C003909'
and cust_mnemonic_code='C000523';
select *
from crm_cust_contact
where
    1=1
#   and cust_code = 'C003909'
  and cust_mnemonic_code='C000523';
select *
from crm_cust_plan
where
    1=1
#   and cust_code = 'C003909'
  and cust_mnemonic_code='C000523';
select *
from crm_cust_dp_template
where
    1=1
#   and cust_code = 'C003909'
and cust_mnemonic_code='C000523';
select *
from crm_cust_sale
where cust_mnemonic_code = 'C001465';
select cust_code, cust_mnemonic_code, sales_assistant_code, cust_status
from crm_cust_basic
where cust_code is null;
select * from crm_files where code='C001424';
select cust_code
from crm_cust_basic ccb
group by cust_code having count(*)>1;
select * from crm_cust_basic where cust_code = 'C000008';



SELECT date_format(create_time, '%Y-%m-%d')
FROM crm_cust_basic
WHERE (
    ((:cust_code IS NULL OR :cust_code = '') OR (cust_code LIKE CONCAT("%", :cust_code, "%")))
        AND ((:cust_name IS NULL OR :cust_name = '') OR (cust_name LIKE CONCAT("%", :cust_name, "%")))
        AND ((:company_code IS NULL OR :company_code = '') OR (company_code LIKE CONCAT("%", :company_code, "%")))
        AND ((:sales_assistant_name IS NULL OR :sales_assistant_name = '') OR
             (sales_assistant_name LIKE CONCAT("%", :sales_assistant_name, "%")))
        AND ((:cust_status IS NULL OR :cust_status = '') OR (cust_status LIKE CONCAT("%", :cust_status, "%")))
        AND ((:department_region IS NULL OR :department_region = '') OR
             (department_region LIKE CONCAT("%", :department_region, "%")))
        AND ((:cust_manager_name IS NULL OR :cust_manager_name = '') OR
             (cust_manager_name LIKE CONCAT("%", :cust_manager_name, "%")))
        AND ((:industry IS NULL OR :industry = '') OR (industry LIKE CONCAT("%", :industry, "%")))
        AND ((:create_time IS NULL OR :create_time = '') OR (create_time LIKE CONCAT("%", :create_time, "%")))
        AND ((:cust_type IS NULL OR :cust_type = '') OR (cust_type = :cust_type))
        AND ((:create_start_date IS NULL OR :create_start_date = '') OR
             (date_format(create_time, '%Y-%m-%d') >= :create_start_date))
        AND
    ((:create_end_date IS NULL OR :create_end_date = '') OR (date_format(create_time, '%Y-%m-%d') <= :create_end_date))
    )
  AND flag_deleted = 0
  AND cust_status <> '0'
  AND cust_status <> '3'
  AND cust_version = '1'
  AND cust_type <> '0'
  AND if(cust_type = 1, cust_status != '1', 1)
ORDER BY create_time DESC
LIMIT :pageNo, :pageSize;

-- CC005085,CC005085
select cust_code,cust_type,cust_mnemonic_code,company_code,cust_status,cust_version,oa_id from crm_cust_basic where flag_deleted =0 and
cust_code='C000162'
;
select * from crm_record_detail where table_id='C000454' ;
select * from crm_cust_basic ccb where oa_id='185127';

select * from crm_cust_basic ccb where cust_type='1' and cust_status!=''
                                   and cust_manager_code ='' and sales_assistant_code='';
update crm_cust_basic set cust_status='' where cust_type='1' and cust_status!=''
                            and cust_manager_code is null and sales_assistant_code is null;



select
    *
from
    crm_cust_plan
where
    flag_deleted = 0
    and cust_code='C003909'
#   and development_plan_status not in (4, 5, 6)
#   and development_end_date < '2025-06-16'

