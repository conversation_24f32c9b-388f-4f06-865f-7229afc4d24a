var request = require('request');
const mysql = require('mysql2');

//遍历日期,从2024-05-01 到2025-07-10
var start_date = new Date('2024-05-01');
var end_date = new Date('2025-07-10');
var date_arr = [];
while (start_date <= end_date) {
    date_arr.push(start_date.toISOString().slice(0, 10));
    start_date.setDate(start_date.getDate() + 1);
}


// 数据库连接配置
const dbConfig = {
    host: '**********', // 根据您的环境调整
    user: 'root', // 建议使用只读账号: 'readonly_user'
    password: '123456', // 对应只读账号密码: 'ReadOnly123!'
    database: 'screen' // 根据您的数据库名调整
};

// 创建请求选项的函数
function createRequestOptions(date) {
    return {
        'method': 'POST',
        'url': 'http://***********:9095/apiArrange/1881184065316159488/call/1914636695136149504',
        'headers': {
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Host': '***********:9095',
            'Connection': 'keep-alive',
            'Cookie': 'JSESSIONID=5A37B020E4DC70FBE7FCAD5C1F65EC73'
        },
        body: JSON.stringify({
            "date": date
        })
    };
}

//返回数据对象: 
// {
//     "date": "2025-07-16",
//     "code": 200,
//     "data": {
//         "收购蔗叶当日量": 0,
//         "当日甘蔗夹杂物": 0,
//         "当日暂存在FN原糖仓的原糖量": 0
//     }
// }
// 创建数据库连接
const connection = mysql.createConnection(dbConfig);

// 连接数据库
connection.connect((err) => {
    if (err) {
        console.error('数据库连接失败:', err);
        return;
    }
    console.log('数据库连接成功');
});

// 插入数据到数据库的函数
function insertDataToDatabase(responseData, callback) {
    try {
        const parsedData = JSON.parse(responseData);

        if (parsedData.code !== 200 || !parsedData.data) {
            console.log('API返回数据异常或无数据');
            callback();
            return;
        }

        const data = parsedData.data; // API返回的data是对象，不是数组
        const updateDate = parsedData.date; // 使用返回的日期

        // 构建插入SQL语句 - 使用INSERT INTO ... ON DUPLICATE KEY UPDATE实现插入或更新
        const sql = `
            INSERT INTO ods_material_weighing (
                sugarcane_leaf_purchase_quantity,
                sugarcane_impurities,
                sugar_quantity,
                update_time
            ) VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                sugarcane_leaf_purchase_quantity = VALUES(sugarcane_leaf_purchase_quantity),
                sugarcane_impurities = VALUES(sugarcane_impurities),
                sugar_quantity = VALUES(sugar_quantity)
        `;

        const values = [
            parseFloat(data["收购蔗叶当日量"]) || 0,
            parseFloat(data["当日甘蔗夹杂物"]) || 0,
            parseFloat(data["当日暂存在FN原糖仓的原糖量"]) || 0,
            updateDate
        ];

        connection.query(sql, values, (error, results) => {
            if (error) {
                console.error(`数据插入失败 (${updateDate}):`, error);
            } else {
                console.log(`数据插入成功 (${updateDate}):`, results.affectedRows, '行受影响');
            }
            callback();
        });

    } catch (parseError) {
        console.error('数据解析失败:', parseError);
        callback();
    }
}



// 处理单个日期的函数
function processDate(date, callback) {
    const options = createRequestOptions(date);

    console.log(`正在处理日期: ${date}`);

    request(options, function (error, response) {
        if (error) {
            console.error(`API请求失败 (${date}):`, error);
            callback();
            return;
        }

        console.log(`API响应 (${date}):`, response.body);

        // 将数据存储到数据库
        insertDataToDatabase(response.body, callback);
    });
}

// 处理所有日期的函数
function processAllDates() {
    let currentIndex = 0;

    function processNext() {
        if (currentIndex >= date_arr.length) {
            console.log('所有日期处理完成');
            connection.end();
            return;
        }

        const currentDate = date_arr[currentIndex];
        currentIndex++;

        console.log(`进度: ${currentIndex}/${date_arr.length}`);

        processDate(currentDate, () => {
            // 添加延迟避免请求过于频繁
            setTimeout(processNext, 10);
        });
    }

    processNext();
}

// 开始处理
processAllDates();

