CREATE TABLE `ods_bip_inventory` (
  `id` int NOT NULL AUTO_INCREMENT primary key COMMENT '主键',
  `materialname` varchar(100) NOT NULL COMMENT '物料名称',
  `version` int NOT NULL COMMENT '物料版本',
  `cunitid` varchar(50) NOT NULL COMMENT '计量单位pk',
  `nonhandnum` int NOT NULL COMMENT '结存主数量',
  `pk_org` varchar(50) NOT NULL COMMENT '组织主键',
  `dinbounddate` date NOT NULL COMMENT '首次入库日期',
  `measdocname` varchar(50) NOT NULL COMMENT '计量单位',
  `vbatchcode` varchar(20) NOT NULL COMMENT '批次',
  `materialcode` varchar(50) NOT NULL COMMENT '物料编码',
  `materialvid` varchar(50) NOT NULL COMMENT '物料版本主键',
  `materialoid` varchar(50) NOT NULL COMMENT '物料主键',
  `pk_batchcode` varchar(50) NOT NULL COMMENT '批次主键',
  `nonhandastnum` decimal(18,3) NOT NULL COMMENT '结存辅数量'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='库存表';
