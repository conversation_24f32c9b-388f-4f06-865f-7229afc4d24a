select year(now())-1 year,
       1 quater,
       ifnull(sum(case when metric_year=year(now())-1 then ifnull(payment_cycle_q1,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())-1;
select year(now())-1 year,
       2 quater,
       ifnull(sum(case when metric_year=year(now())-1 then ifnull(payment_cycle_q2,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())-1;