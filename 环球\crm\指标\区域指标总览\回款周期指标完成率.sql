select year(now())-1 year,
       1 quater,
       ifnull(sum(case when metric_year=year(now())-1 then ifnull(payment_cycle_q1,0) end),0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year =year(now())-1
;


select year(now()) - 1                                                                              year,
       1                                                                                            quater,
       ifnull(sum(case when metric_year = year(now()) - 1 then ifnull(payment_cycle_q1, 0) end), 0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year = year(now()) - 1
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
UNION ALL
select year(now()) - 1                                                                              year,
       2                                                                                            quater,
       ifnull(sum(case when metric_year = year(now()) - 1 then ifnull(payment_cycle_q2, 0) end), 0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year = year(now()) - 1
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
UNION ALL
select year(now()) - 1                                                                              year,
       3                                                                                            quater,
       ifnull(sum(case when metric_year = year(now()) - 1 then ifnull(payment_cycle_q3, 0) end), 0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year = year(now()) - 1
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
UNION ALL
select year(now()) - 1                                                                              year,
       4                                                                                            quater,
       ifnull(sum(case when metric_year = year(now()) - 1 then ifnull(payment_cycle_q4, 0) end), 0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year = year(now()) - 1
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
UNION ALL
select year(now())                                                                              `year`,
       1                                                                                        quater,
       ifnull(sum(case when metric_year = year(now()) then ifnull(payment_cycle_q1, 0) end), 0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
UNION ALL
select year(now())                                                                              `year`,
       2                                                                                        quater,
       ifnull(sum(case when metric_year = year(now()) then ifnull(payment_cycle_q2, 0) end), 0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
UNION ALL
select year(now())                                                                              `year`,
       3                                                                                        quater,
       ifnull(sum(case when metric_year = year(now()) then ifnull(payment_cycle_q3, 0) end), 0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
UNION ALL
select year(now())                                                                              `year`,
       4                                                                                        quater,
       ifnull(sum(case when metric_year = year(now()) then ifnull(payment_cycle_q4, 0) end), 0) payment_cycle
from cockpit.dws_payment_cycle_metric t1
where metric_year = year(now())
  AND if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
ORDER BY year, quater
;




