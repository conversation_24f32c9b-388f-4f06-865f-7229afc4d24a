-- 报价单号---合同编号---产品编号+产品名称---每个产品的销售毛利率
select t1.preliminary_quotation_code    '报价单号',
       t3.contract_management_code      '合同编号',
       t1.material_code                 '产品编号',
       t1.material_name                 '产品名称',
       t1.financial_gross_profit_margin '财务毛利率',
       t1.sales_gross_profit_margin     '销售毛利率'
from crm_preliminary_quotation_product t1
         join crm_preliminary_quotation t2 on t1.preliminary_quotation_code = t2.preliminary_quotation_code
         join crm_contract_management t3 on t3.quotation_code = t2.preliminary_quotation_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and t3.flag_deleted = 0
;

