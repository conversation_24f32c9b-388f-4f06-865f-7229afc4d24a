-- 毛利指标完成统计
SELECT '西安环球'                                                            sale_company,
       t3.deparment_code,
       t3.deparment_name,
       t3.department_region                                                  region,
       t3.cust_name,
       t4.sales_code                                                         cust_manager_code,
       t4.sales_name                                                         cust_manager_name,
       t3.cust_type,
       CASE
           WHEN t3.cust_type = 0 THEN '新建客户'
           WHEN t3.cust_type = 1 THEN '公海客户'
           WHEN t3.cust_type = 2 THEN '合作客户'
           WHEN t3.cust_type = 3 THEN '开发中客户'
           WHEN t3.cust_type = 4 THEN '受限客户'
           END                                                               cust_type_name,
       t1.sales_order_code,
       STR_TO_DATE(t1.create_time, '%Y-%m-%d')                               create_time,
       t1.factory_assigned,
       t1.status,
       case
           when t1.status = 10 then '已删除'
           when t1.status = 8 then '已取消'
           when t1.status = 7 then '已关闭'
           when t1.status = 6 then '已开票'
           when (select count(*)
                 from invoice_application ia
                 where ia.status IN ('0', '3', '4')
                   and ia.flag_deleted = 0
                   and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
               then '已发货未开票'
           when t1.status = 0 then '已拆分'
           when t1.status = 1 then '已下达'
           when t1.status = 2 then '已排产'
           when t1.status = 3 then '已领料'
           when t1.status = 4 then '生产中'
           when t1.status = 5 then '已入库'
           else '其他'
           end                                                               status_desc,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       t2.order_quantity_after_split                                         product_quantity,
       round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.unit_price_exclusive),
             4)                                                              unit_price_exclusive,
       round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.amount_exclusive_tax),
             2)                                                              amount_exclusive_tax,
       ifnull(t5.invoiced_quantity, 0)                                       invoiced_quantity,
       ifnull(t5.invoiced_amount, 0)                                         invoiced_amount,
       t7.standard_cost_unit_price                                           quotation_cost_unit_price,
       round(t7.standard_cost_unit_price * ifnull(t5.invoiced_quantity, 0), 2) quotation_cost_amount,
       round(ifnull(t5.invoiced_amount, 0) - t7.standard_cost_unit_price * ifnull(t5.invoiced_quantity, 0),
             2)                                                              profit_amount,
       round(100 * (ifnull(t5.invoiced_amount, 0) - t7.standard_cost_unit_price * ifnull(t5.invoiced_quantity, 0)) /
             ifnull(t5.invoiced_amount, 0), 4)                               profit_ratio,
       t6.profit_target                                                      profit_target,
       round(100 * (ifnull(t5.invoiced_amount, 0) - t7.standard_cost_unit_price * ifnull(t5.invoiced_quantity, 0)) /
             (ifnull(t6.sales_target, 0) * 10000), 4)                        completion_ratio,
       t5.invoiced_date,
       0                                                                     marginal_cost_unit_price,
       0                                                                     marginal_contribution_amount,
       0                                                                     marginal_contribution_rate,
       t2.contract_product_line_number
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
         left join (WITH deduplicated_sales AS (SELECT DISTINCT ccb.cust_code,
                                                                ccb.cust_manager_code,
                                                                ccb.cust_manager_name,
                                                                ifnull(ccs.sales_code, ccb.cust_manager_code) sales_code,
                                                                ifnull(ccs.sales_name, ccb.cust_manager_name) sales_name,
                                                                NULLIF(
                                                                        CAST(REPLACE(REPLACE(ccs.target_percentage, '%', ''), ' ', '') AS DECIMAL),
                                                                        0) as                                 cleaned_percentage
                                                FROM crm_cust_basic ccb
                                                         LEFT JOIN crm_cust_sale ccs
                                                                   ON ccb.cust_mnemonic_code = ccs.cust_mnemonic_code
                                                                       AND ccb.cust_version = ccs.cust_version
                                                                       AND ccs.flag_deleted = 0
                                                WHERE cust_status = 2
                                                  AND cust_type NOT IN (0, 1)
                                                  AND ccb.flag_deleted = 0)
                    SELECT cust_code,
                           sales_code,
                           sales_name,
                           COALESCE(
                                   cleaned_percentage,
                                   CASE
                                       WHEN SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END)
                                                OVER (PARTITION BY cust_code)
                                           = COUNT(*) OVER (PARTITION BY cust_code)
                                           THEN 100.0 / COUNT(*) OVER (PARTITION BY cust_code)
                                       ELSE (100 - SUM(COALESCE(cleaned_percentage, 0)) OVER (PARTITION BY cust_code)) /
                                            NULLIF(SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END)
                                                       OVER (PARTITION BY cust_code), 0)
                                       END,
                                   0
                           ) as target_percentage
                    FROM deduplicated_sales) t4 on t4.cust_code = t1.cust_code
         LEFT JOIN (select split_order_no            sales_order_code,
                           product_code              material_code,
                           split_order_line_no,
                           group_concat(outbound_no) shipment_code,
                           min(dbilldate)            invoiced_date,
                           max(outbound_date)        shipment_date,
                           sum(ship_quantity)        shipped_quantity,
                           sum(ifnull(nnum, 0))      invoiced_quantity,
                           sum(ifnull(norigmny, 0))  invoiced_amount
                    from bip_outbound_order_detail bood
                             join (select csrcid, csrcbid, dbilldate, sum(nnum) nnum, sum(norigmny) norigmny
                                   from crm_sales_invoice_details
                                   where flag_deleted = 0
                                   group by csrcid, csrcbid, dbilldate) csid
                                  on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
                    where bood.flag_deleted = 0
                    group by sales_order_code, material_code, split_order_line_no) t5
                   on t5.sales_order_code = t1.sales_order_code and t2.material_code = t5.material_code
                       and t2.contract_product_line_number = t5.split_order_line_no
         left join metric_person t6
                   on t6.metric_year = date_format(t1.create_time, '%Y')
                       and t6.flag_deleted = 0
                       and t6.cust_manager_code = t4.sales_code
         left join (select t2.standard_cost_unit_price,
                           t1.contract_management_code,
                           t1.contract_product_line_number
                    from crm_contract_management_product t1
                             join crm_preliminary_quotation_product t2
                                  on t1.quotation_product_id = t2.id and t2.flag_deleted = 0
                    where t1.flag_deleted = 0) t7 on t1.contract_management_code = t7.contract_management_code
    and t2.contract_product_line_number = t7.contract_product_line_number
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  and t3.cust_status = 2
  and t3.cust_type not in (0, 1)
  AND t1.status not in (8)
  and ifnull(t5.invoiced_amount, 0)>0
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t3.cust_code in (:cust_code_arr), 1)
      )
  AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
  AND ((:region IS NULL OR :region = '') OR (t3.department_region = :region))
  AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
  AND ((:cust_name IS NULL OR :cust_name = '') OR (t3.cust_name LIKE concat('%', :cust_name, '%')))
  AND ((:material_code IS NULL OR :material_code = '') OR (t2.material_code like concat('%', :material_code, '%')))
  AND ((:material_name IS NULL OR :material_name = '') OR (t2.material_name LIKE concat('%', :material_name, '%')))
  AND ((:contract_start_date IS NULL OR :contract_start_date = '') OR
       (date_format(t1.create_time, '%Y-%m-%d') >= :contract_start_date))
  AND ((:contract_end_date IS NULL OR :contract_end_date = '') OR
       (date_format(t1.create_time, '%Y-%m-%d') <= :contract_end_date))
  AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (t1.factory_assigned = :factory_assigned))
  AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR
       (t1.sales_order_code LIKE concat('%', :sales_order_code, '%')))
  AND ((:invoiced_start_date IS NULL OR :invoiced_start_date = '') OR
       (date_format(t5.invoiced_date, '%Y-%m-%d') >= :invoiced_start_date))
  AND ((:invoiced_end_date IS NULL OR :invoiced_end_date = '') OR
       (date_format(t5.invoiced_date, '%Y-%m-%d') <= :invoiced_end_date))
ORDER BY t1.id ASC
LIMIT :page_size offset :offset
;
-- 获取成本单价
select t2.standard_cost_unit_price,t1.contract_product_line_number,t1.contract_management_code from crm_contract_management_product t1
         join crm_preliminary_quotation_product t2 on t1.quotation_product_id = t2.id and t2.flag_deleted=0
         where t1.flag_deleted=0
#            and   contract_product_line_number=4
           and contract_management_code='25020002';


select * from crm_sales_order cso where sales_order_code='25020002B';
select * from crm_sales_order_product csop where sales_order_code='25040115A';
select * from crm_contract_management_product ccmp where contract_management_code='25020002';
select * from crm_contract_management ccmp where contract_management_code='25040115';
select * from crm_quotation cq where quotation_code='BJ2504000132';
select * from crm_preliminary_quotation cpqp where preliminary_quotation_code='BJ2504000132';
select * from crm_preliminary_quotation_product where preliminary_quotation_code='BJ2502000003' ;
select * from crm_preliminary_quotation_product cpqp  where id=4541;
select * from crm_cust_basic ccb where cust_code='C000029';
select * from crm_cust_sale ccs where cust_code='C000008';
select * from metric_person mp where cust_manager_name='黄鑫鹏';
-- 合计
SELECT '合计' AS                            sale_company,
       round(sum(invoiced_quantity), 2)     invoiced_quantity,
       round(sum(amount_exclusive_tax), 2)  amount_exclusive_tax,
       round(sum(quotation_cost_amount), 2) quotation_cost_amount,
       round(sum(profit_amount), 2)         profit_amount
FROM (
         SELECT '西安环球'                                                            sale_company,
                t3.deparment_code,
                t3.deparment_name,
                t3.department_region                                                  region,
                t3.cust_name,
                t4.sales_code                                                         cust_manager_code,
                t4.sales_name                                                         cust_manager_name,
                t3.cust_type,
                CASE
                    WHEN t3.cust_type = 0 THEN '新建客户'
                    WHEN t3.cust_type = 1 THEN '公海客户'
                    WHEN t3.cust_type = 2 THEN '合作客户'
                    WHEN t3.cust_type = 3 THEN '开发中客户'
                    WHEN t3.cust_type = 4 THEN '受限客户'
                    END                                                               cust_type_name,
                t1.sales_order_code,
                STR_TO_DATE(t1.create_time, '%Y-%m-%d')                               create_time,
                t1.factory_assigned,
                t1.status,
                case
                    when t1.status = 10 then '已删除'
                    when t1.status = 8 then '已取消'
                    when t1.status = 7 then '已关闭'
                    when t1.status = 6 then '已开票'
                    when (select count(*)
                          from invoice_application ia
                          where ia.status IN ('0', '3', '4')
                            and ia.flag_deleted = 0
                            and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
                        then '已发货未开票'
                    when t1.status = 0 then '已拆分'
                    when t1.status = 1 then '已下达'
                    when t1.status = 2 then '已排产'
                    when t1.status = 3 then '已领料'
                    when t1.status = 4 then '生产中'
                    when t1.status = 5 then '已入库'
                    else '其他'
                    end                                                               status_desc,
                t2.main_class,
                t2.sub_class,
                t2.material_code,
                t2.material_name,
                t2.order_quantity_after_split                                         product_quantity,
                round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                         t2.unit_price_exclusive),
                      4)                                                              unit_price_exclusive,
                round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                         t2.amount_exclusive_tax),
                      2)                                                              amount_exclusive_tax,
                ifnull(t5.invoiced_quantity, 0)                                       invoiced_quantity,
                ifnull(t5.invoiced_amount, 0)                                         invoiced_amount,
                t7.standard_cost_unit_price                                           quotation_cost_unit_price,
                round(t7.standard_cost_unit_price * ifnull(t5.invoiced_quantity, 0), 2) quotation_cost_amount,
                round(ifnull(t5.invoiced_amount, 0) - t7.standard_cost_unit_price * ifnull(t5.invoiced_quantity, 0),
                      2)                                                              profit_amount,
                round(100 * (ifnull(t5.invoiced_amount, 0) - t7.standard_cost_unit_price * ifnull(t5.invoiced_quantity, 0)) /
                      ifnull(t5.invoiced_amount, 0), 4)                               profit_ratio,
                t6.profit_target                                                      profit_target,
                round(100 * (ifnull(t5.invoiced_amount, 0) - t7.standard_cost_unit_price * ifnull(t5.invoiced_quantity, 0)) /
                      (ifnull(t6.sales_target, 0) * 10000), 4)                        completion_ratio,
                t5.invoiced_date,
                0                                                                     marginal_cost_unit_price,
                0                                                                     marginal_contribution_amount,
                0                                                                     marginal_contribution_rate,
                t2.contract_product_line_number
         FROM crm_sales_order t1
                  LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
                  LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
                  left join (WITH deduplicated_sales AS (SELECT DISTINCT ccb.cust_code,
                                                                         ccb.cust_manager_code,
                                                                         ccb.cust_manager_name,
                                                                         ifnull(ccs.sales_code, ccb.cust_manager_code) sales_code,
                                                                         ifnull(ccs.sales_name, ccb.cust_manager_name) sales_name,
                                                                         NULLIF(
                                                                                 CAST(REPLACE(REPLACE(ccs.target_percentage, '%', ''), ' ', '') AS DECIMAL),
                                                                                 0) as                                 cleaned_percentage
                                                         FROM crm_cust_basic ccb
                                                                  LEFT JOIN crm_cust_sale ccs
                                                                            ON ccb.cust_mnemonic_code = ccs.cust_mnemonic_code
                                                                                AND ccb.cust_version = ccs.cust_version
                                                                                AND ccs.flag_deleted = 0
                                                         WHERE cust_status = 2
                                                           AND cust_type NOT IN (0, 1)
                                                           AND ccb.flag_deleted = 0)
                             SELECT cust_code,
                                    sales_code,
                                    sales_name,
                                    COALESCE(
                                            cleaned_percentage,
                                            CASE
                                                WHEN SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END)
                                                         OVER (PARTITION BY cust_code)
                                                    = COUNT(*) OVER (PARTITION BY cust_code)
                                                    THEN 100.0 / COUNT(*) OVER (PARTITION BY cust_code)
                                                ELSE (100 - SUM(COALESCE(cleaned_percentage, 0)) OVER (PARTITION BY cust_code)) /
                                                     NULLIF(SUM(CASE WHEN cleaned_percentage IS NULL THEN 1 ELSE 0 END)
                                                                OVER (PARTITION BY cust_code), 0)
                                                END,
                                            0
                                    ) as target_percentage
                             FROM deduplicated_sales) t4 on t4.cust_code = t1.cust_code
                  LEFT JOIN (select split_order_no            sales_order_code,
                                    product_code              material_code,
                                    split_order_line_no,
                                    group_concat(outbound_no) shipment_code,
                                    min(dbilldate)            invoiced_date,
                                    max(outbound_date)        shipment_date,
                                    sum(ship_quantity)        shipped_quantity,
                                    sum(ifnull(nnum, 0))      invoiced_quantity,
                                    sum(ifnull(norigmny, 0))  invoiced_amount
                             from bip_outbound_order_detail bood
                                      join (select csrcid, csrcbid, dbilldate, sum(nnum) nnum, sum(norigmny) norigmny
                                            from crm_sales_invoice_details
                                            where flag_deleted = 0
                                            group by csrcid, csrcbid, dbilldate) csid
                                           on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
                             where bood.flag_deleted = 0
                             group by sales_order_code, material_code, split_order_line_no) t5
                            on t5.sales_order_code = t1.sales_order_code and t2.material_code = t5.material_code
                                and t2.contract_product_line_number = t5.split_order_line_no
                  left join metric_person t6
                            on t6.metric_year = date_format(t1.create_time, '%Y')
                                and t6.flag_deleted = 0
                                and t6.cust_manager_code = t4.sales_code
                  left join (select t2.standard_cost_unit_price,
                                    t1.contract_management_code,
                                    t1.contract_product_line_number
                             from crm_contract_management_product t1
                                      join crm_preliminary_quotation_product t2
                                           on t1.quotation_product_id = t2.id and t2.flag_deleted = 0
                             where t1.flag_deleted = 0) t7 on t1.contract_management_code = t7.contract_management_code
             and t2.contract_product_line_number = t7.contract_product_line_number
         WHERE t1.flag_deleted = 0
           AND t2.flag_deleted = 0
           AND t3.flag_deleted = 0
           and t3.cust_status = 2
           and t3.cust_type not in (0, 1)
           AND t1.status not in (8)
           and   ifnull(t5.invoiced_amount, 0)
           AND if(:admin,
                  1,
                  if(:cust_code_size > 0, t3.cust_code in (:cust_code_arr), 1)
               )
           AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
           AND ((:region IS NULL OR :region = '') OR (t3.department_region = :region))
           AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
           AND ((:cust_name IS NULL OR :cust_name = '') OR (t3.cust_name LIKE concat('%', :cust_name, '%')))
           AND ((:material_code IS NULL OR :material_code = '') OR (t2.material_code like concat('%', :material_code, '%')))
           AND ((:material_name IS NULL OR :material_name = '') OR (t2.material_name LIKE concat('%', :material_name, '%')))
           AND ((:contract_start_date IS NULL OR :contract_start_date = '') OR
                (date_format(t1.create_time, '%Y-%m-%d') >= :contract_start_date))
           AND ((:contract_end_date IS NULL OR :contract_end_date = '') OR
                (date_format(t1.create_time, '%Y-%m-%d') <= :contract_end_date))
           AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (t1.factory_assigned = :factory_assigned))
           AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR
                (t1.sales_order_code LIKE concat('%', :sales_order_code, '%')))
           AND ((:invoiced_start_date IS NULL OR :invoiced_start_date = '') OR
                (date_format(t5.invoiced_date, '%Y-%m-%d') >= :invoiced_start_date))
           AND ((:invoiced_end_date IS NULL OR :invoiced_end_date = '') OR
                (date_format(t5.invoiced_date, '%Y-%m-%d') <= :invoiced_end_date))
         ORDER BY t1.id ASC
     )temp;
