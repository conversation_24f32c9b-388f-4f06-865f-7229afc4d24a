-- 获取所有上级分类
select * from pdm_param_detail ppd where ppd.detail_code in
                                   ('101','10101','1010101','1010102','10102','1010201','1010202','1010203','1010204','1010205','1010206','10103','1010301','1010302','1010303','1010305','1010306','1010307','1010308','10104','1010401','1010402','1010403','10105','10106','10107','10108','10110','10111','101BQ','101JP','101QT','101SM','101WL','101WW','101ZD');
-- 获取详细分类
select t1.detail_code '物料基本分类编码',t1.detail_value '上级分类编码',t2.detail_code '上级分类编码',t2.detail_value '上级分类名称' from
(select * from pdm_param_detail ppd where parent_id in
        (select id from pdm_param_detail ppd where ppd.detail_code in
            ('101','10101','1010101','1010102','10102','1010201','1010202','1010203','1010204','1010205','1010206','10103','1010301','1010302','1010303','1010305','1010306','1010307','1010308','10104','1010401','1010402','1010403','10105','10106','10107','10108','10110','10111','101BQ','101JP','101QT','101SM','101WL','101WW','101ZD')
)) t1 join  pdm_param_detail t2 on t1.parent_id=t2.id