-- ETL建表语句
create table ods_lims_daily_report
(
    id          int auto_increment
        primary key,
    value       json        comment '结果',
    update_time varchar(20) comment '更新时间'
)comment 'lims日报数据';
select * from ods_lims_daily_report ;

-- ads指标
create table ads_lims_metric
(
    id          int auto_increment primary key,
    `metric_key` varchar (100) comment '指标名称',
    `metric_value`       varchar(100) comment '结果',
    update_time varchar(20) comment '更新时间'
) comment 'lims指标';
select * from ads_lims_metric ;


select id,average_tcd from average_tcd;
select * from group_daily gd;

;
select * from screen.orange_water_analysis owa;
select * from daily_r3_huirong_rate where id = 1;
update daily_r3_huirong_rate set r3 =0  where id = 1;
select * from sugar_color_value;
select * from bagasse_moisture_detail;

SELECT
    MAX(CASE WHEN mpip.project_name LIKE '%浓缩糖浆锤度%' THEN maximum_value END) AS max_birx,
    MIN(CASE WHEN mpip.project_name LIKE '%浓缩糖浆锤度%' THEN minimum_value END) AS min_brix,
    MAX(CASE WHEN mpip.project_name LIKE '%饱充后糖浆PH值%' THEN maximum_value END) AS max_ph,
    MIN(CASE WHEN mpip.project_name LIKE '%饱充后糖浆PH值%' THEN minimum_value END) AS min_ph,
    MAX(CASE WHEN mpip.project_name LIKE '%干滤泥糖度%' THEN standard_value END) AS standard_value
FROM
    monthly_process_indicator_project mpip
WHERE
    mpip.project_name LIKE '%浓缩糖浆锤度%'
   OR mpip.project_name LIKE '%饱充后糖浆PH值%'
   OR mpip.project_name LIKE '%干滤泥糖度%';
select * from acceptability_sugar_melting;
select * from orange_water_gravity;
select date_format(now(),'%Y-%m-%m %H:00:00');
