var data=[{"value": 1426, "quantity": 56301866, "major_categories": "6876"}, {"value": 123, "quantity": 36862724, "major_categories": "6901"}, {"value": 148, "quantity": 2113000, "major_categories": "6884"}, {"value": 1, "quantity": 15000, "major_categories": "6877"}, {"value": 17, "quantity": 160000, "major_categories": "6914"}, {"value": 0, "quantity": 30300, "major_categories": "6893"}]
var zhihe_value=0
var shuomingshu_value=0
var other_value=0
var total_value=0
var zhihe_quantity=0
var shuomingshu_quantity=0
var other_quantity=0
var total_quantity=0
for(var i;i<data.length;i++){
    total_value=total+data[i].value
    total_quantity=total+data[i].quantity
    if(data[i].major_categories==6876){
        zhihe_value=data[i].value
        zhihe_quantity=data[i].quantity
    }else if(data[i].major_categories==6901){
        shuomingshu_value=data[i].value
        shuomingshu_quantity=data[i].quantity
    }else{
        other_value=other_value+data[i].value
        other_quantity=other_quantity+data[i].quantity
    }
}
var res={
    "data":{
        "title": "待评交期",
        "subTitle": "更新时间:",
        "totalNum": total_value+"万元/"+total_quantity+"万只(张)",
        "list": [
            {
                "name": "折叠纸盒",
                "value": zhihe_value+"万元/"+zhihe_quantity+"万只(张)",
            },
            {
                "name": "说明书",
                "value": shuomingshu_value+"万元/"+shuomingshu_quantity+"万只(张)",
            },
            {
                "name": "其他",
                "value": other_value+"万元/"+other_quantity+"万只(张)",
            }
        ]
      },
    "code":200
}
console.log(JSON.stringify(res))
return res;