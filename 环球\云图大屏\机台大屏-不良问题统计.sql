select * from hqyw_adverse_causes_analysis;
select kind_name defective_name,
       1 defective_type,
       quantity defective_quantity,
       create_time
       from cockpit.ods_waste_disposal_item
        where date(create_time)=curdate();

SELECT
    defective_name AS 不良名称,
    ROUND(AVG(defective_quantity), 2) AS 不良数量（年度）,
    workshop
FROM
    hqyw_adverse_causes_analysis
WHERE
    YEAR(create_date) = YEAR(NOW()) and workshop=#
GROUP BY
    defective_name;

select
    kind_name defective_name,
    sum(quantity)  defective_quantity,
    t1.machine_id  machine_id
from pm_job_association t1
         join pm_job_detail t2 on t1.job_id = t2.id
         join pm_waste_disposal_item t3 on t2.task_list_code = t3.job_detail_id
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and t3.flag_deleted = 0
  and kind_name is not null
  and year(t3.create_time)=year(curdate())
group by kind_name,machine_id;

