CREATE TABLE ods_sugar_cane_daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    season VARCHAR(10) NOT NULL COMMENT '榨季',
    factory_code VARCHAR(10) NOT NULL COMMENT '工厂代码',
    entry_date DATE NOT NULL COMMENT '进厂时间',
    cane_quantity DECIMAL(12,3) NOT NULL COMMENT '甘蔗数量',
    season_cumulative_weight DECIMAL(12,3) NOT NULL COMMENT '当日榨季累计已过磅甘蔗量',
    last_season_same_period_weight DECIMAL(12,3) NOT NULL COMMENT '上榨季同期已过磅甘蔗量',
    manual_cut_quantity DECIMAL(12,3) NOT NULL COMMENT '人工砍蔗甘蔗数量',
    machine_cut_quantity DECIMAL(12,3) NOT NULL COMMENT '机收蔗甘蔗数量',
    season_cumulative_manual_cut DECIMAL(12,3) NOT NULL COMMENT '当日榨季累计人工砍蔗数量',
    season_cumulative_machine_cut DECIMAL(12,3) NOT NULL COMMENT '当日榨季累计机收蔗数量',
    machine_cut_percentage VARCHAR(10) NOT NULL COMMENT '机收蔗榨季累计数量占比',
    cane_variety VARCHAR(50) NOT NULL COMMENT '甘蔗品种名称',
    variety_daily_quantity DECIMAL(12,3) NOT NULL COMMENT '各甘蔗品种当日数量',
    variety_season_quantity DECIMAL(12,3) NOT NULL COMMENT '各甘蔗品种榨季累计数量',
    waiting_transport_quantity DECIMAL(12,3) NOT NULL COMMENT '当日待运已过磅甘蔗量',
    outside_factory_quantity DECIMAL(12,3) NOT NULL COMMENT '当日厂外已过磅甘蔗量',
    in_transit_quantity DECIMAL(12,3) NOT NULL COMMENT '当日在途已过磅甘蔗量',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_factory_date (factory_code, entry_date) COMMENT '工厂日期复合索引',
    INDEX idx_season (season) COMMENT '榨季索引',
    INDEX idx_variety (cane_variety) COMMENT '甘蔗品种索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='农务系统接口数据表';