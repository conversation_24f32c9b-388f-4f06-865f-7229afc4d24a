DELIMITER //

CREATE PROCEDURE populate_date_dimension()
BEGIN
    -- 变量声明必须放在BEGIN之后的第一行
    DECLARE start_date DATE;
    DECLARE end_date DATE;
    DECLARE current_date DATE;
    DECLARE year INT;
    DECLARE month INT;
    DECLARE day INT;
    DECLARE quarter INT;
    DECLARE day_of_week INT;
    DECLARE week_of_year INT;
    DECLARE is_weekend BOOLEAN;
    DECLARE is_holiday BOOLEAN;

    -- 初始化变量
    SET start_date = '2024-01-01';
    SET end_date = '2024-12-31';
    SET current_date = start_date;

    -- 循环插入数据
    WHILE current_date <= end_date DO
            SET year = YEAR(current_date);
            SET month = MONTH(current_date);
            SET day = DAY(current_date);
            SET quarter = QUARTER(current_date);
            SET day_of_week = DAYOFWEEK(current_date);
            SET week_of_year = WEEK(current_date, 3); -- 3表示周一作为一周的开始
            SET is_weekend = IF(DAYOFWEEK(current_date) IN (1, 7), 1, 0); -- 1=周日, 7=周六
            SET is_holiday = 0; -- 这里可以根据实际情况设置节假日

            INSERT INTO date_dimension (date_id, full_date, year, month, day, quarter, day_of_week, week_of_year, is_weekend, is_holiday)
            VALUES (DATE_FORMAT(current_date, '%Y%m%d'), current_date, year, month, day, quarter, day_of_week, week_of_year, is_weekend, is_holiday);

            SET current_date = DATE_ADD(current_date, INTERVAL 1 DAY);
        END WHILE;
END //

DELIMITER ;
