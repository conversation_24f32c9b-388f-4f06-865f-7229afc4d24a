-- 合同
select *
from crm_contract_management t1
where t1.cust_manager_code in ('111', '')
   or t1.cust_manager_code is null
   or t1.cust_manager_name ='';
-- 关联查询
select t1.cust_manager_code,t1.cust_manager_name,
       t2.cust_manager_code,t2.cust_manager_name
from crm_contract_management t1
join crm_cust_basic t2 on t1.cust_code=t2.cust_code
where (t1.cust_manager_code in ('111', '')
   or t1.cust_manager_code is null
    or t1.cust_manager_name ='')
and t2.cust_status=2
;
-- 更新操作
update
 crm_contract_management t1
    join crm_cust_basic t2 on t1.cust_code=t2.cust_code
set t1.cust_manager_code=t2.cust_manager_code
where (t1.cust_manager_code in ('111', '')
   or t1.cust_manager_code is null)
  and t2.cust_status=2;

-- 预报价
select *
from crm_preliminary_quotation
where cust_manager_code in ('111', '')
   or cust_manager_code is null;
-- 关联查询
select t1.cust_manager_code,t1.cust_manager_name,
       t2.cust_manager_code,t2.cust_manager_name
from crm_preliminary_quotation t1
         join crm_cust_basic t2 on t1.cust_code=t2.cust_code
where (t1.cust_manager_code in ('111', '')
    or t1.cust_manager_code is null
    or t1.cust_manager_name ='')
  and t2.cust_status=2;
-- 预报价更新
update
    crm_preliminary_quotation t1
        join crm_cust_basic t2 on t1.cust_code=t2.cust_code
set t1.cust_manager_code=t2.cust_manager_code
,t1.cust_manager_name=t2.cust_manager_name
where (t1.cust_manager_code in ('111', '')
    or t1.cust_manager_code is null)
  and t2.cust_status=2;
