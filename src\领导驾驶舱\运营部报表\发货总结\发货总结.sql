-- 计划发货批次=作业单生产订单计划发货日期在统计周期内的批次数量
SELECT
	opjo.production_batch_number,
	opjo.customer_name,
	opjo.material_code product_code,
	opjo.material_name product_name,
	opjo.release_quantity order_quantity,
	opjo.planned_delivery_date planned_shipment_date,
	'' actual_shipment_date,
	0 actual_shipment_quantity,
	CASE
		WHEN opjo.production_order_status = '0' THEN '未生成作业单'
		WHEN opjo.production_order_status = '1' THEN '有作业单未下达'
		WHEN opjo.production_order_status = '2' THEN '作业单全部下达'
		WHEN opjo.production_order_status = '3' THEN '已完成'
		WHEN opjo.production_order_status = '4' THEN '转外协'
		ELSE opjo.production_order_status
	END order_status,
	1 detail_type,
	YEAR(opjo.planned_delivery_date) update_year,
	WEEK(opjo.planned_delivery_date) week_of_year
FROM
	ods_pm_job_order opjo
WHERE
	opjo.flag_deleted = 0
	AND opjo.source_type = '1'
	AND YEAR(opjo.planned_delivery_date) = YEAR(CURDATE());
-- 实际发货批次=符合计划发货批次条件且配载单-出库时间在统计周期内批次数量
SELECT
	sbd.update_year,
	sbd.week_of_year,
	COUNT(*) actual_shipment_batches
FROM 
	(
	SELECT
		YEAR(pfl.create_time) update_year,
		WEEK(pfl.create_time) week_of_year,
		psd.batch_no AS production_batch_number
	FROM
		(
		SELECT
			pfl.flow_id,
			MAX(pfl.id) AS min_status_id
		FROM
			ods_pm_flow_log pfl
		WHERE
			pfl.flag_deleted = 0
			AND pfl.flow_name = '配载单管理'
			AND pfl.next_node_name = '已出库'
			AND YEAR(pfl.create_time) = YEAR(CURDATE())
		GROUP BY
			pfl.flow_id
	) msd
	LEFT JOIN ods_pm_flow_log pfl ON
		pfl.id = msd.min_status_id
	LEFT JOIN
    ods_pm_stowage_detail psd ON
		psd.parent_id = msd.flow_id
	WHERE
		pfl.flag_deleted = 0
		AND psd.flag_deleted = 0
) sbd
INNER JOIN (
	SELECT
		YEAR(opjo.planned_delivery_date) update_year,
		WEEK(opjo.planned_delivery_date) week_of_year,
		opjo.production_batch_number
	FROM
		ods_pm_job_order opjo
	WHERE
		opjo.flag_deleted = 0
		AND YEAR(opjo.planned_delivery_date) = YEAR(CURDATE())
) obd ON
	obd.update_year = sbd.update_year
	AND obd.week_of_year = sbd.week_of_year
	AND obd.production_batch_number = sbd.production_batch_number
GROUP BY
	sbd.update_year,
	sbd.week_of_year;
-- 实际发货箱数=符合实际发货批次条件的件数   
-- 实际发货数量=符合符合实际发货批次条件的数量
SELECT
	YEAR(ld.create_time) update_year,
	WEEK(ld.create_time) week_of_year,
	SUM(psd.piece_reality) actual_shipment_boxes,
	SUM(psd.num_reality) actual_shipment_quantity
FROM
	(
	SELECT
		pfl.flow_id,
		MAX(pfl.create_time) create_time
	FROM
		ods_pm_flow_log pfl
	WHERE
		pfl.flag_deleted = 0
		AND pfl.flow_name = '配载单管理'
		AND pfl.next_node_name = '已出库'
		AND YEAR(pfl.create_time) = YEAR(CURDATE())
	GROUP BY
		pfl.flow_id
) ld
LEFT JOIN ods_pm_stowage_detail psd ON
	psd.parent_id = ld.flow_id
WHERE
	psd.flag_deleted = 0
	AND psd.batch_no NOT LIKE 'DY%'
GROUP BY
	YEAR(ld.create_time),
	WEEK(ld.create_time);


-- 批次达成率=实际发货批次/计划发货批次
-- 实际发货批次
SELECT
	sbd.update_year,
	sbd.week_of_year,
	COUNT(*) actual_shipment_batches
FROM 
	(
	SELECT
		YEAR(pfl.create_time) update_year,
		WEEK(pfl.create_time) week_of_year,
		psd.batch_no AS production_batch_number
	FROM
		(
		SELECT
			pfl.flow_id,
			MAX(pfl.id) AS min_status_id
		FROM
			ods_pm_flow_log pfl
		WHERE
			pfl.flag_deleted = 0
			AND pfl.flow_name = '配载单管理'
			AND pfl.next_node_name = '已出库'
			AND YEAR(pfl.create_time) = YEAR(CURDATE())
		GROUP BY
			pfl.flow_id
	) msd
	LEFT JOIN ods_pm_flow_log pfl ON
		pfl.id = msd.min_status_id
	LEFT JOIN
    ods_pm_stowage_detail psd ON
		psd.parent_id = msd.flow_id
	WHERE
		pfl.flag_deleted = 0
		AND psd.flag_deleted = 0
) sbd
INNER JOIN (
	SELECT
		YEAR(opjo.planned_delivery_date) update_year,
		WEEK(opjo.planned_delivery_date) week_of_year,
		opjo.production_batch_number
	FROM
		ods_pm_job_order opjo
	WHERE
		opjo.flag_deleted = 0
		AND YEAR(opjo.planned_delivery_date) = YEAR(CURDATE())
) obd ON
	obd.update_year = sbd.update_year
	AND obd.week_of_year = sbd.week_of_year
	AND obd.production_batch_number = sbd.production_batch_number
GROUP BY
	sbd.update_year,
	sbd.week_of_year;
-- 计划发货批次
SELECT
	opjo.production_batch_number,
	opjo.customer_name,
	opjo.material_code product_code,
	opjo.material_name product_name,
	opjo.release_quantity order_quantity,
	opjo.planned_delivery_date planned_shipment_date,
	'' actual_shipment_date,
	0 actual_shipment_quantity,
	CASE
		WHEN opjo.production_order_status = '0' THEN '未生成作业单'
		WHEN opjo.production_order_status = '1' THEN '有作业单未下达'
		WHEN opjo.production_order_status = '2' THEN '作业单全部下达'
		WHEN opjo.production_order_status = '3' THEN '已完成'
		WHEN opjo.production_order_status = '4' THEN '转外协'
		ELSE opjo.production_order_status
	END order_status,
	1 detail_type,
	YEAR(opjo.planned_delivery_date) update_year,
	WEEK(opjo.planned_delivery_date) week_of_year
FROM
	ods_pm_job_order opjo
WHERE
	opjo.flag_deleted = 0
	AND opjo.source_type = '1'
	AND YEAR(opjo.planned_delivery_date) = YEAR(CURDATE());