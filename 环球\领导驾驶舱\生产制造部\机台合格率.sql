-- 机台合格率ETL
select t3.id                                                                           machine_id,
       t3.machine_name,
       date_format(t1.create_time, '%Y-%m')                                            update_month,
       sum(t1.feed_quantity)                                                           feed_quantity,       -- 投料量
       sum(t1.deliverables)                                                            deliverables,        -- 成品量
       sum(t1.machine_consumption)                                                     machine_consumption, -- 取样量
       sum(t1.quantity_rejects)                                                        quantity_rejects,    -- 废品(机台)
       sum(t2.quality_inspection)                                                      quality_inspection,  -- 废品(品检剔废)
       100 * (sum(t1.deliverables) + sum(t1.machine_consumption) - sum(t2.quality_inspection)) /
       (sum(t1.deliverables) + sum(t1.machine_consumption) + sum(t1.quantity_rejects)) rate
from ods_pm_final_batch_job_audit t1
         left join ods_pm_team_production_details t2 on t2.batch_job_audit_id = t1.id
         left join ods_machine_file t3 on t1.drilling_id = t3.id
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and t3.isdelete = 0
group by update_month, machine_id, t3.machine_name
order by update_month, t3.machine_name asc
;

drop table dwd_machine_pass_rate_month;
CREATE TABLE dwd_machine_pass_rate_month
(
    id                  int(10) auto_increment primary key not null comment '自增主键',
    machine_id          INT                                NOT NULL comment '班组id',
    machine_name        VARCHAR(255)                       NOT NULL comment '班组名称',
    update_month        varchar(10)                        NOT NULL comment '更新月份',
    feed_quantity       int           DEFAULT 0 comment '投料量',         -- 投料量
    deliverables        int           DEFAULT 0 comment '成品量',         -- 成品量
    machine_consumption int           DEFAULT 0 comment '取样量',         -- 取样量
    quantity_rejects    int           DEFAULT 0 comment '废品(机台)',     -- 废品(机台)
    quality_inspection  int           DEFAULT 0 comment '废品(品检剔废)', -- 废品(品检剔废)
    rate                DECIMAL(5, 2) DEFAULT 0 comment '合格率'-- 合格率
) comment '机台合格率';

-- 机台下拉框
select distinct machine_id value, machine_name label
from cockpit.dwd_machine_pass_rate_month
order by machine_name asc;

select machine_name,
       update_month,
       feed_quantity,
       deliverables,
       machine_consumption,
       quantity_rejects,
       quality_inspection,
       concat( rate, '%') rate
from cockpit.dwd_machine_pass_rate_month
where ((:year IS NULL OR :year = '') OR (left(update_month, 4) = :year))
  and ((:machine_id IS NULL OR :machine_id = '') OR (machine_id = :machine_id))
order by update_month asc, machine_name asc
limit :page_size offset :offset
;
