var craftArr=[{"craft_name":"原料分切","indexs":"1","technological_parameter":"三期赋码方式:激光灼烧,","product_code":"ZDZY0000722","create_by":"1838574991874523138","update_time":"2025-05-06 15:37:24","rate":"0","parent_code":"ZDZY0000722","ban_json_code":"版编号：LGP-ZDZY000631-BV2类型：印版类别：CTP版版名称：复方桔梗枇杷糖浆100毫升小盒版出本：51","r_craft_code":"D0035","ys_fixed_time":"0","id":574443,"update_by":"1838574991874523138","material_name":"编码：1010101080000028 名称：780 375g 浙江天琦白卡底PET亮银卡 index：8066311","source_name":"GP分切中心","r_work_code":"GPFQZX","flag_deleted":0,"ys_standing_time":"0","product_version":"1.2","work_code":"GP分切中心","create_time":"2025-05-06 15:37:24","craft_code":"原料分切","index":"1","material_json_code":"编码：1010101080000028 名称：780 375g 浙江天琦白卡底PET亮银卡 index：8066311","version":"1.0","ys_fixed_rate":"0","out_of_book":"51","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPFQZX"},{"craft_name":"印刷","indexs":"2","technological_parameter":"印刷类型:UV印刷,标准化印刷:否,印刷面:正版,咬口尺寸:12,印刷规矩:靠身,UV印刷色序及灯管位置:bm-bm-k-M-C-Y-mdxy,跟色标准:首次生产跟第一次打样印刷留样；再次生产跟标准色样：XGP-QEO-QMD/EXT/SC-，制作,正版联机上光组油:局部UV油,油辊类型:100线网纹辊,油版材质:橡皮布,过油效果:跟过油效果样,印刷条码等级:B级及以上,耐摩擦次数要求:UV印刷800次,纸张达因值:≥38达因,三期赋码方式:激光灼烧,","product_code":"ZDZY0000722","create_by":"1838574991874523138","update_time":"2025-05-06 15:37:24","rate":"0","parent_code":"ZDZY0000722","ban_json_code":"版编号：LGP-ZDZY000631-BV2类型：印版类别：CTP版版名称：复方桔梗枇杷糖浆100毫升小盒版出本：51","r_craft_code":"D0102","ys_fixed_time":"0","id":574444,"update_by":"1838574991874523138","material_name":"编码：1010201020000002 名称：UV特白油墨  tpu84(1.5kg/罐 杭华/金印联) index：8066220,编码：1010201020000002 名称：UV特白油墨  tpu84(1.5kg/罐 杭华/金印联) index：8066220,编码：1010201020000005 名称：UV油墨abilio-21 Process Black 黑(1kg*12罐/箱 深圳深日油墨有限公司) index：8066223,编码：1010201020000003 名称：油墨 UV油墨abilio-21 Process Magenta 红(1kg*12罐/箱 深圳深日油墨有限公司) index：8066443,编码：1010201020000033 名称：油墨 UV油墨abilio-21 Process Cyan 兰(1kg*12罐/箱 深圳深日油墨有限公司) index：8066458,编码：1010201020000004 名称：油墨 UV油墨abilio-21 Process Yellow 黄(1kg*12罐/箱 深圳深日油墨有限公司) index：8066444,编码：1010201040000035 名称：GOS59198逆向底油（细沙）(GOS59198逆向底油（细沙） 金印联) index：8066395,编码：1010202030000003 名称：盛威科逆向光油面油UVK-9831(20kg/桶 盛威科（上海）) index：8066432","source_name":"GP七色普通印刷中心","r_work_code":"GPYS7SZX","flag_deleted":0,"ys_standing_time":"0","product_version":"1.2","work_code":"GP七色普通印刷中心","create_time":"2025-05-06 15:37:24","craft_code":"印刷","index":"2","material_json_code":"编码：1010201020000002 名称：UV特白油墨  tpu84(1.5kg/罐 杭华/金印联) index：8066220,编码：1010201020000002 名称：UV特白油墨  tpu84(1.5kg/罐 杭华/金印联) index：8066220,编码：1010201020000005 名称：UV油墨abilio-21 Process Black 黑(1kg*12罐/箱 深圳深日油墨有限公司) index：8066223,编码：1010201020000003 名称：油墨 UV油墨abilio-21 Process Magenta 红(1kg*12罐/箱 深圳深日油墨有限公司) index：8066443,编码：1010201020000033 名称：油墨 UV油墨abilio-21 Process Cyan 兰(1kg*12罐/箱 深圳深日油墨有限公司) index：8066458,编码：1010201020000004 名称：油墨 UV油墨abilio-21 Process Yellow 黄(1kg*12罐/箱 深圳深日油墨有限公司) index：8066444,编码：1010201040000035 名称：GOS59198逆向底油（细沙）(GOS59198逆向底油（细沙） 金印联) index：8066395,编码：1010202030000003 名称：盛威科逆向光油面油UVK-9831(20kg/桶 盛威科（上海）) index：8066432","version":"1.0","ys_fixed_rate":"0.5","out_of_book":"51","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPYS7SZX"},{"craft_name":"喷码","indexs":"3","technological_parameter":"喷印次序:先印后喷,走纸方向:横向,喷码位置:正版,喷码类别:一维码,喷码分类:药品追溯码,喷印分辨率:510*408/1200*1200,咬口尺寸:12,喷头个数:3个,印版喷印文字:印刷咬口、产品编码及生产批号,一维码喷码参数:详见附图,喷码等级:B级及以上,喷码效果:跟喷码效果样样盒,走向长度（mm）:980.00,","product_code":"ZDZY0000722","create_by":"1838574991874523138","update_time":"2025-05-06 15:37:24","rate":"0","parent_code":"ZDZY0000722","ban_json_code":"版编号：LGP-ZDZY000631-BV2类型：印版类别：CTP版版名称：复方桔梗枇杷糖浆100毫升小盒版出本：51","r_craft_code":"D0121","ys_fixed_time":"0","id":574445,"update_by":"1838574991874523138","material_name":"编码：1010201060000002 名称：S系列黑色LED UV墨水 BLACK(1升/瓶 北京) index：8066248","source_name":"GP可变数据印刷中心","r_work_code":"GPYSKBSJYSZX","flag_deleted":0,"ys_standing_time":"0","product_version":"1.2","work_code":"GP可变数据印刷中心","create_time":"2025-05-06 15:37:24","craft_code":"喷码","index":"3","material_json_code":"编码：1010201060000002 名称：S系列黑色LED UV墨水 BLACK(1升/瓶 北京) index：8066248","version":"1.0","ys_fixed_rate":"0","out_of_book":"51","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPYSKBSJYSZX"},{"craft_name":"击凸（凹）","indexs":"4","technological_parameter":"击凸类型:单层击凸,击凸版材:2mm子母镁版,击凸高度:跟击凸效果样,击凸版面积:414.9167,","product_code":"ZDZY0000722","create_by":"1838574991874523138","update_time":"2025-05-06 15:37:24","rate":"0","parent_code":"ZDZY0000722","ban_json_code":"版编号：ZDZY000631-AV2类型：模切版类别：凹凸版版名称：复方桔梗枇杷糖浆100毫升小盒版出本：3","r_craft_code":"D0111","ys_fixed_time":"0","id":574446,"update_by":"1838574991874523138","material_name":"","source_name":"GP模切中心","r_work_code":"GPZHMQZX","flag_deleted":0,"ys_standing_time":"0","product_version":"1.2","work_code":"GP模切中心","create_time":"2025-05-06 15:37:24","craft_code":"击凸（凹）","index":"4","material_json_code":"","version":"1.0","ys_fixed_rate":"0","out_of_book":"3","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPZHMQZX"},{"craft_name":"模切","indexs":"5","technological_parameter":"底模类型:激光底膜,模切类型:单次模切,模切效果:公司内控标准,","product_code":"ZDZY0000722","create_by":"1838574991874523138","update_time":"2025-05-06 15:37:24","rate":"0","parent_code":"ZDZY0000722","ban_json_code":"版编号：ZDZY000631-MV1类型：模切版类别：模切版版名称：复方桔梗枇杷糖浆100毫升小盒版出本：3","r_craft_code":"X0023","ys_fixed_time":"0","id":574447,"update_by":"1838574991874523138","material_name":"","source_name":"GP模切中心","r_work_code":"GPZHMQZX","flag_deleted":0,"ys_standing_time":"0","product_version":"1.2","work_code":"GP模切中心","create_time":"2025-05-06 15:37:24","craft_code":"模切","index":"5","material_json_code":"","version":"1.0","ys_fixed_rate":"0","out_of_book":"3","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPZHMQZX"},{"craft_name":"打纸","indexs":"6","technological_parameter":"打纸标准:公司内控标准,","product_code":"ZDZY0000722","create_by":"1838574991874523138","update_time":"2025-05-06 15:37:24","rate":"0","parent_code":"ZDZY0000722","ban_json_code":"","r_craft_code":"X0108","ys_fixed_time":"0","id":574448,"update_by":"1838574991874523138","material_name":"","source_name":"GP清废中心","r_work_code":"GPZHQFZX","flag_deleted":0,"ys_standing_time":"0","product_version":"1.2","work_code":"GP清废中心","create_time":"2025-05-06 15:37:24","craft_code":"打纸","index":"6","material_json_code":"","version":"1.0","ys_fixed_rate":"0","out_of_book":"1","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPZHQFZX"},{"craft_name":"纸盒品检机选剔","indexs":"7","technological_parameter":"走向长度（mm）:392.00,","product_code":"ZDZY0000722","create_by":"1838574991874523138","update_time":"2025-05-06 15:37:24","rate":"0","parent_code":"ZDZY0000722","ban_json_code":"","r_craft_code":"X0104","ys_fixed_time":"0","id":574449,"update_by":"1838574991874523138","material_name":"","source_name":"GP品检中心","r_work_code":"GPZHPJZX","flag_deleted":0,"ys_standing_time":"0","product_version":"1.2","work_code":"GP品检中心","create_time":"2025-05-06 15:37:24","craft_code":"纸盒品检机选剔","index":"7","material_json_code":"","version":"1.0","ys_fixed_rate":"0","out_of_book":"1","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPZHPJZX"},{"craft_name":"粘盒","indexs":"8","technological_parameter":"盒型:异型盒,上胶方式:轮胶+喷胶,客户包装方式:上自动包装机,走向长度（mm）:392.00,开盒力:≤5N,压缩预留空隙:纸箱最上面一层纸盒，压缩后与纸箱内壁预留空隙30mm,","product_code":"ZDZY0000722","create_by":"1838574991874523138","update_time":"2025-05-06 15:37:24","rate":"0","parent_code":"ZDZY0000722","ban_json_code":"","r_craft_code":"D0036","ys_fixed_time":"2","id":574450,"update_by":"1838574991874523138","material_name":"编码：1010206010000001 名称：冠力糊盒胶MA-768(50kg/桶) index：8066213,编码：1010206010000002 名称：奥普糊盒胶(SW-600A（20kg/桶） 上海奥普化工有限公司) index：8066307,编码：1010301010000112 名称：纸箱483225 index：8066423,编码：10103040000001 名称：塑料袋 index：8066215","source_name":"GP品检粘盒中心","r_work_code":"GPZHPJNHZX","flag_deleted":0,"ys_standing_time":"0","product_version":"1.2","work_code":"GP品检粘盒中心","create_time":"2025-05-06 15:37:24","craft_code":"粘盒","index":"8","material_json_code":"编码：1010206010000001 名称：冠力糊盒胶MA-768(50kg/桶) index：8066213,编码：1010206010000002 名称：奥普糊盒胶(SW-600A（20kg/桶） 上海奥普化工有限公司) index：8066307,编码：1010301010000112 名称：纸箱483225 index：8066423,编码：10103040000001 名称：塑料袋 index：8066215","version":"1.0","ys_fixed_rate":"3","out_of_book":"1","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPZHPJNHZX"}]
var deleted_code_arr=["LGP-ZDZY000631-BV2"]

for (var z = 0; z < craftArr.length; z++) {  
    var ban_id = craftArr[z].ban_json_code;  
    if (ban_id != null && ban_id !== "" && ban_id) {  
        if (ban_id.indexOf(",") !== -1) {  
            var banArray = ban_id.split(",");  
            var mes = []; // 使用数组来收集未被删除的项  
            for (var x = 0; x < banArray.length; x++) {  
                var banElement = banArray[x];  
                var index = banElement.indexOf("版编号：");  
                var typeIndex = banElement.indexOf("类型：");  
  
                if (index !== -1 && typeIndex !== -1 && index < typeIndex) {  
                    var edition_code = banElement.substring(index + 4, typeIndex);  
  
                    // 使用传统的for循环来检查deleted_code_arr中是否包含edition_code  
                    var isDeleted = false;  
                  console.log("版编码:"+index)
                  console.log("版类型:"+typeIndex)
                  console.log("edition_code:"+edition_code)
                    for (var y = 0; y < deleted_code_arr.length; y++) {  
                          console.log("deleted_code_arr[y]："+deleted_code_arr[y])
                        if (deleted_code_arr[y] === edition_code) {
                            isDeleted = true;  
                            break; // 如果找到匹配的项，则跳出循环  
                        }  
                    }  
  
                    if (!isDeleted) {  
                        mes.push(banElement); // 如果未删除，则添加到数组中  
                    }  
                }  
            }  
            craftArr[z].ban_json_code = mes.join(","); // 将数组转换为逗号分隔的字符串  
        } else {  
            // 如果ban_id中没有逗号，则直接检查是否需要删除  
            var index = ban_id.indexOf("版编号：");  
            var typeIndex = ban_id.indexOf("类型：");  
  
            if (index !== -1 && typeIndex !== -1 && index < typeIndex) {  
                var edition_code = ban_id.substring(index + 4, typeIndex);  
  
                // 检查deleted_code_arr中是否包含edition_code  
                for (var y = 0; y < deleted_code_arr.length; y++) {  
                    if (deleted_code_arr[y] === edition_code) {  
                        craftArr[z].ban_json_code = ""; // 如果找到匹配的项，则设置为空字符串  
                        break; // 跳出循环  
                    }  
                }  
            }  
        }  
    } else {  
        craftArr[z].ban_json_code = ""; // 如果ban_id为空或无效，则设置为空字符串  
    }  
}
console.log("关联版显示："+JSON.stringify(craftArr))



return craftArr