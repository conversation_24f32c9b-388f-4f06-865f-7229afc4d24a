-- 优化后的查询 - 推荐方案
-- 主要优化点：
-- 1. 减少不必要的JOIN
-- 2. 优化WHERE条件顺序
-- 3. 使用EXISTS替代LEFT JOIN (当只需要判断存在性时)

-- ========================================
-- 方案1: 最简化版本 (推荐用于列表展示)
-- ========================================
SELECT 
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0 
  AND pv.status IN (1,2,3,4)
  AND (
    :fileFlag != '1' 
    OR EXISTS (
      SELECT 1 FROM pdm_upload_file pf 
      WHERE pf.material_code = pv.material_code 
        AND pf.product_version = pv.product_version 
        AND pf.file_id IS NOT NULL
    )
  )
ORDER BY pv.id DESC
LIMIT 10;

-- ========================================
-- 方案2: 需要关联数据时的优化版本
-- ========================================
SELECT 
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time,
    -- 如果需要关联表的数据，可以通过子查询获取
    (SELECT COUNT(*) FROM pdm_product_bom pb 
     WHERE pb.product_code = pv.material_code 
       AND pb.product_version = pv.product_version) as bom_count,
    (SELECT COUNT(*) FROM pdm_product_craft ppc 
     WHERE ppc.product_code = pv.material_code 
       AND ppc.product_version = pv.product_version) as craft_count,
    (SELECT COUNT(*) FROM pdm_upload_file pf 
     WHERE pf.material_code = pv.material_code 
       AND pf.product_version = pv.product_version 
       AND pf.file_id IS NOT NULL) as file_count
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0 
  AND pv.status IN (1,2,3,4)
  AND (
    :fileFlag != '1' 
    OR EXISTS (
      SELECT 1 FROM pdm_upload_file pf 
      WHERE pf.material_code = pv.material_code 
        AND pf.product_version = pv.product_version 
        AND pf.file_id IS NOT NULL
    )
  )
ORDER BY pv.id DESC
LIMIT 10;

-- ========================================
-- 方案3: 带完整搜索条件的优化版本
-- ========================================
SELECT 
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0 
  AND pv.status IN (1,2,3,4)
  -- 优化：将最有选择性的条件放在前面
  AND (:material_code IS NULL OR :material_code = '' OR pv.material_code LIKE CONCAT('%', :material_code, '%'))
  AND (:material_name IS NULL OR :material_name = '' OR pv.material_name LIKE CONCAT('%', :material_name, '%'))
  AND (:mnemonic_code IS NULL OR :mnemonic_code = '' OR pv.mnemonic_code LIKE CONCAT('%', :mnemonic_code, '%'))
  AND (:is_group IS NULL OR :is_group = '' OR pv.is_group_product = :is_group)
  AND (:status IS NULL OR :status = '' OR pv.status = :status)
  AND (:bom_type IS NULL OR :bom_type = '' OR pv.bom_type = :bom_type)
  AND (:product_version IS NULL OR :product_version = '' OR pv.product_version = :product_version)
  AND (:product_size IS NULL OR :product_size = '' OR pv.product_size LIKE CONCAT('%', :product_size, '%'))
  AND (:startcreatetime IS NULL OR :startcreatetime = '' OR pv.create_time >= :startcreatetime)
  AND (:endcreatetime IS NULL OR :endcreatetime = '' OR pv.create_time <= :endcreatetime)
  AND (:startupdatetime IS NULL OR :startupdatetime = '' OR pv.update_time >= :startupdatetime)
  AND (:endupdatetime IS NULL OR :endupdatetime = '' OR pv.update_time <= :endupdatetime)
  AND (:main_class_size <= 0 OR pv.main_class IN (:main_class))
  AND (:sub_class_size <= 0 OR pv.sub_class IN (:sub_class))
  -- 客户端名称搜索 - 使用EXISTS优化
  AND (:client_name IS NULL OR :client_name = '' OR 
       EXISTS (SELECT 1 FROM pdm_product_client pc 
               WHERE pc.product_code = pv.material_code 
                 AND pc.client_name LIKE CONCAT('%', :client_name, '%')))
  -- BOM相关搜索 - 使用EXISTS优化
  AND (:mater_code IS NULL OR :mater_code = '' OR 
       EXISTS (SELECT 1 FROM pdm_product_bom pb 
               WHERE pb.product_code = pv.material_code 
                 AND pb.product_version = pv.product_version
                 AND pb.material_code LIKE CONCAT('%', :mater_code, '%')))
  AND (:mater_name IS NULL OR :mater_name = '' OR 
       EXISTS (SELECT 1 FROM pdm_product_bom pb 
               WHERE pb.product_code = pv.material_code 
                 AND pb.product_version = pv.product_version
                 AND pb.material_name LIKE CONCAT('%', :mater_name, '%')))
  AND (:paper_size IS NULL OR :paper_size = '' OR 
       EXISTS (SELECT 1 FROM pdm_product_bom pb 
               WHERE pb.product_code = pv.material_code 
                 AND pb.product_version = pv.product_version
                 AND pb.component_size LIKE CONCAT('%', :paper_size, '%')
                 AND pb.categroy = '1'))
  -- 工艺相关搜索 - 使用EXISTS优化
  AND (:processname IS NULL OR :processname = '' OR 
       EXISTS (SELECT 1 FROM pdm_product_craft ppc 
               WHERE ppc.product_code = pv.material_code 
                 AND ppc.product_version = pv.product_version
                 AND ppc.craft_name LIKE CONCAT('%', :processname, '%')))
  -- 文件相关条件
  AND (
    :fileFlag != '1' 
    OR EXISTS (
      SELECT 1 FROM pdm_upload_file pf 
      WHERE pf.material_code = pv.material_code 
        AND pf.product_version = pv.product_version 
        AND pf.file_id IS NOT NULL
    )
  )
ORDER BY pv.id DESC
LIMIT 10;

-- ========================================
-- 方案4: 分步查询 (适用于复杂搜索场景)
-- ========================================

-- 第一步：获取符合条件的主键ID
WITH filtered_ids AS (
    SELECT pv.id
    FROM pdm_product_version pv
    WHERE pv.flag_deleted = 0
      AND pv.status IN (1,2,3,4)
      -- 添加其他搜索条件...
    ORDER BY pv.id DESC
    LIMIT 10
)
-- 第二步：根据ID获取完整数据
SELECT
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
INNER JOIN filtered_ids fi ON pv.id = fi.id
ORDER BY pv.id DESC;

-- ========================================
-- 方案5: 使用条件逻辑优化 (CASE/IF 优化法)
-- ========================================

-- 核心思想：避免不必要的JOIN，只在需要时才进行关联查询
SELECT
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0
  AND pv.status IN (1,2,3,4)
  -- 使用CASE避免不必要的子查询
  AND CASE
    WHEN :fileFlag = '1' THEN
      EXISTS (
        SELECT 1 FROM pdm_upload_file pf
        WHERE pf.material_code = pv.material_code
          AND pf.product_version = pv.product_version
          AND pf.file_id IS NOT NULL
      )
    ELSE TRUE
  END
  -- 客户端条件优化
  AND CASE
    WHEN :client_name IS NULL OR :client_name = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_client pc
      WHERE pc.product_code = pv.material_code
        AND pc.client_name LIKE CONCAT('%', :client_name, '%')
    )
  END
  -- BOM条件优化
  AND CASE
    WHEN :mater_code IS NULL OR :mater_code = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_bom pb
      WHERE pb.product_code = pv.material_code
        AND pb.product_version = pv.product_version
        AND pb.material_code LIKE CONCAT('%', :mater_code, '%')
    )
  END
  AND CASE
    WHEN :mater_name IS NULL OR :mater_name = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_bom pb
      WHERE pb.product_code = pv.material_code
        AND pb.product_version = pv.product_version
        AND pb.material_name LIKE CONCAT('%', :mater_name, '%')
    )
  END
  AND CASE
    WHEN :paper_size IS NULL OR :paper_size = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_bom pb
      WHERE pb.product_code = pv.material_code
        AND pb.product_version = pv.product_version
        AND pb.component_size LIKE CONCAT('%', :paper_size, '%')
        AND pb.categroy = '1'
    )
  END
  -- 工艺条件优化
  AND CASE
    WHEN :processname IS NULL OR :processname = '' THEN TRUE
    ELSE EXISTS (
      SELECT 1 FROM pdm_product_craft ppc
      WHERE ppc.product_code = pv.material_code
        AND ppc.product_version = pv.product_version
        AND ppc.craft_name LIKE CONCAT('%', :processname, '%')
    )
  END
ORDER BY pv.id DESC
LIMIT 10;
