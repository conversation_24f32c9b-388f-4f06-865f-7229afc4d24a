const mysql = require('mysql');
const util = require('util');

// 数据库配置
const dbConfig = {
    host: '**************',
    user: 'root',
    password: '123456',
    database: 'h3chq_crmbusiness1704287359505'
};



//1.连接数据库,查询所有数据 select id,csaleinvoicebid,csaleinvoiceid相同 from crm_sales_invoice_details
//2.遍历查询的数据,跟data数据匹配,csaleinvoicebid,csaleinvoiceid相同,则匹配成功
//3.用data数据csrcid,csrcbid 更新表 update crm_sales_invoice_details set csrcid=? ,csrcbid=?  where id=?



// 创建数据库连接池
const pool = mysql.createPool(dbConfig);
const query = util.promisify(pool.query).bind(pool);

async function updateInvoiceDetails(data) {
    try {
        // 1. 查询所有数据
        const selectQuery = `
            SELECT id, csaleinvoicebid, csaleinvoiceid 
            FROM crm_sales_invoice_details where csrcid is null or csrcid is null
        `;
        const results = await query(selectQuery);

        // 2. 遍历查询数据进行匹配
        for (const row of results) {
            const matchedData = data.find(item => 
                item.csaleinvoicebid === row.csaleinvoicebid && 
                item.csaleinvoiceid === row.csaleinvoiceid
            );

            if (matchedData) {
                // 3. 更新匹配的记录
                const updateQuery = `
                    UPDATE crm_sales_invoice_details 
                    SET csrcid = ?, csrcbid = ? 
                    WHERE   id = ?
                `;
                await query(updateQuery, [
                    matchedData.csrcid,
                    matchedData.csrcbid,
                    row.id
                ]);
            }
        }

        return { success: true, message: '数据更新成功' };
    } catch (error) {
        console.error('更新失败:', error);
        return { success: false, error: error.message };
    } finally {
        // 关闭连接池
        pool.end();
    }
}

async function fetchDataWithToken() {
    // 第一个请求 - 获取 token
    const tokenHeaders = new Headers();
    tokenHeaders.append("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
    tokenHeaders.append("Accept", "*/*");
    tokenHeaders.append("Host", "**************:30783");
    tokenHeaders.append("Connection", "keep-alive");

    const tokenRequestOptions = {
        method: 'GET',
        headers: tokenHeaders,
        redirect: 'follow'
    };

    try {
        // 获取 token
        const tokenResponse = await fetch("http://**************:30783/mom_test/hqErpSyn/erptoken", tokenRequestOptions);
        const tokenData = await tokenResponse.json();
        const token = tokenData.token;

        // 第二个请求 - 使用获取的 token
        const queryHeaders = new Headers();
        queryHeaders.append("access_token", token); // 使用获取的 token
        queryHeaders.append("ucg_flag", "y");
        queryHeaders.append("signature", "db9f1d9f52d5912af0abfda515e122224f026037efc5c8ee50a8ed49796de6e2");
        queryHeaders.append("repeat_check", "Y");
        queryHeaders.append("client_id", "jiekou");
        queryHeaders.append("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
        queryHeaders.append("Content-Type", "application/json;charset=utf-8");
        queryHeaders.append("Accept", "*/*");
        queryHeaders.append("Host", "************:5000");
        queryHeaders.append("Connection", "keep-alive");

        const raw = {
            "begintime": "2024-01-09",
            "endtime": "2026-10-09"
        };

        const queryRequestOptions = {
            method: 'POST',
            headers: queryHeaders,
            body: JSON.stringify(raw),
            redirect: 'follow'
        };

        // 发送带有 token 的请求
        const queryResponse = await fetch("http://************:5000/nccloud/api/so/saleinvoiceHQ/queryHQ", queryRequestOptions);
        const result = await queryResponse.json();
        return result;
    } catch (error) {
        console.error('Error:', error); // 错误处理
        throw error;
    }
}

// 主函数
async function main() {
    try {
        const response = await fetchDataWithToken();
        const data = response.data;
        
        if (!data) {
            throw new Error('No data received from API');
        }

        const result = await updateInvoiceDetails(data);
        console.log(result);
    } catch (error) {
        console.error('Error:', error);
    }
}

// 执行主函数
main();
