const axios = require('axios');
const jsonData = {
  deviceNumbers: ["QDCJ1"]
};

const options = {
  method: 'POST',
  url: 'http://*************:80/api/device2mom/getCuAllPropertiesByDeviceIds',
  headers: {
    'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Host': '*************:80',
    'Connection': 'keep-alive'
  },
  data: jsonData
};

axios.request(options).then((response) => {
  const my_data = response.data.data["QDCJ1"];

  let total_value = 0;
  for (const key in my_data) {
    if (my_data.hasOwnProperty(key)) {
      total_value += my_data[key].value;
    }
  }
  console.log(`总用电量${total_value}`);

  // 今日零点的数据
  const today_value = 993641;
  const value = total_value - today_value;
  console.log(`今日用电量${value}`);

}).catch((error) => {
  console.error(error);
});
