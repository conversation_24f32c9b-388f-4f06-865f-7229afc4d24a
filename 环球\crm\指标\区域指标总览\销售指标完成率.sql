select t1.update_time, round(ifnull(100 * cumulative_amount / (t2.sales_target * 10000)*t3.target_percentage/100,0),2) as complete_rate
from cockpit.dws_cust_sale_month t1
         left join cockpit.ods_metric_region  t2 on t1.update_year = t2.metric_year and t2.flag_deleted=0
          left join cockpit.dwd_cust_sale_percentage t3 on t1.cust_code = t3.cust_code and t2.cust_manager_code=t3.sales_code
where t2.department_region=:region
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t1.cust_code in (:cust_code_arr), 1)
      )
group by t1.update_time
;


select temp1.`year`, temp1.`month`, ifnull(temp2.complete_rate, 0) complete_rate
from (select `year_month`, `year`, `month`
      from cockpit.dim_month dm
      where dm.year in (year(now()), year(now()) - 1)) temp1
         left join (select t1.update_time,
                           round(ifnull(100 * cumulative_amount / (t2.sales_target * 10000) * t3.target_percentage /
                                        100, 0), 2) as complete_rate
                    from cockpit.dws_cust_sale_month t1
                             left join cockpit.ods_metric_region t2
                                       on t1.update_year = t2.metric_year and t2.flag_deleted = 0
                             left join cockpit.dwd_cust_sale_percentage t3
                                       on t1.cust_code = t3.cust_code
                    where t2.department_region = :region
                      AND if(:admin,
                             1,
                             if(:cust_code_size > 0, t1.cust_code in (:cust_code_arr), 1)
                          )
                    group by t1.update_time) temp2
                   on temp1.`year_month` = temp2.update_time
order by temp1.`year`, temp1.`month`
;



select temp1.`year`, temp1.`month`, ifnull(temp2.complete_rate, 0) complete_rate
from (select `year_month`, `year`, `month`
      from cockpit.dim_month dm
      where dm.year in (year(now()), year(now()) - 1)) temp1
         left join (select t1.update_time,
                           round(ifnull(100 * cumulative_amount / (t2.profit_target * 10000) * t3.target_percentage /
                                        100, 0), 2) as complete_rate
                    from cockpit.dws_cust_profit_month t1
                             left join cockpit.ods_metric_department t2
                                       on t1.update_year = t2.metric_year and t2.flag_deleted = 0
                             left join cockpit.dwd_cust_sale_percentage t3
                                       on t1.cust_code = t3.cust_code
                    where t2.department_code = 18
                      AND if(0,
                             1,
                             if(2 > 0, t1.cust_code in ('CC005085'), 1)
                          )
                    group by t1.update_time) temp2
                   on temp1.`year_month` = temp2.update_time
order by temp1.`year`, temp1.`month`;
