-- 已发货未开票报表
SELECT '西安环球'                               sale_company,
       t3.deparment_code,
       t3.deparment_name,
       t3.department_region region,
       t3.cust_name,
       t3.cust_manager_code,
       t3.cust_manager_name,
       t3.cust_type,
       CASE
           WHEN t3.cust_type = 0 THEN '新建客户'
           WHEN t3.cust_type = 1 THEN '公海客户'
           WHEN t3.cust_type = 2 THEN '合作客户'
           WHEN t3.cust_type = 3 THEN '开发中客户'
           WHEN t3.cust_type = 4 THEN '受限客户'
           END                                  cust_type_name,
       t1.sales_order_code,
       t1.status ,
       case
           when t1.status=10 then '已删除'
           when t1.status=8 then '已取消'
           when t1.status=7 then '已关闭'
           when t1.status=6 then '已开票'
           when (select count(*)
                 from invoice_application ia
                 where ia.status IN ('0', '3', '4')
                   and ia.flag_deleted = 0
                   and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
               then '已发货未开票'
           when t1.status=0 then '已拆分'
           when t1.status=1 then '已下达'
           when t1.status=2 then '已排产'
           when t1.status=3 then '已领料'
           when t1.status=4 then '生产中'
           when t1.status=5 then '已入库'
           else '其他'
           end status_desc,
       t1.factory_assigned,
       t4.shipment_code,
       t4.shipment_date,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       t2.product_version,
       t2.grade_name,
       t2.contract_management_code,
       t2.order_quantity_after_split,
       t4.shipped_quantity,
       round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.unit_price_exclusive ),
             4)                                 unit_price_exclusive,
       round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.amount_exclusive_tax ),
             2)                                 amount_exclusive_tax,
       t4.invoiced_quantity                       invoiced_quantity,
       t4.shipped_quantity - t4.invoiced_quantity uninvoiced_quantity,
       round((t4.shipped_quantity - t4.invoiced_quantity) * t2.unit_price_exclusive ,
             2)                                 uninvoiced_amount,
       datediff(current_date, t4.shipment_date)    uninvoiced_age,
       t2.object_id
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
         join (
            select split_order_no sales_order_code,
                   product_code material_code,
                   split_order_line_no,
                   group_concat(outbound_no) shipment_code,
                   max(outbound_date) shipment_date,
                   sum(ship_quantity) shipped_quantity,
                   sum(ifnull(nnum,0))  invoiced_quantity
            from bip_outbound_order_detail bood
                     left join (select csrcid, csrcbid, sum(nnum) nnum
                                from crm_sales_invoice_details
                                where flag_deleted = 0
                                group by csrcid, csrcbid) csid
                               on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
            where bood.flag_deleted=0
            group by sales_order_code,material_code,split_order_line_no
        ) t4 on t4.sales_order_code = t2.sales_order_code and t4.material_code = t2.material_code
                    and t4.split_order_line_no = t2.contract_product_line_number
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  and t3.cust_status=2 and t3.cust_type not in (0)
  and t4.shipped_quantity > t4.invoiced_quantity
  and t2.order_quantity_after_split>t4.invoiced_quantity
  and t1.status not in ('7','8')
and t1.sales_order_code='25020164A'
#   AND if(:admin,
#          1,
#          if(:cust_code_size > 0, t3.cust_code in (:cust_code_arr), 1)
#       )
#   AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
#   AND ((:region IS NULL OR :region = '') OR (department_region = :region))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
#   AND ((:material_code IS NULL OR :material_code = '') OR (t2.material_code like concat('%', :material_code, '%')))
#   AND ((:material_name IS NULL OR :material_name = '') OR (material_name LIKE concat('%', :material_name, '%')))
#   AND ((:shipment_start_date IS NULL OR :shipment_start_date = '') OR
#        (date_format(shipment_date, '%Y-%m-%d') >= :shipment_start_date))
#   AND ((:shipment_end_date IS NULL OR :shipment_end_date = '') OR
#        (date_format(shipment_date, '%Y-%m-%d') <= :shipment_end_date))
#   AND if(:cust_manager_size>0, t3.cust_manager_code in (:cust_manager_arr), 1)
#   AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (t1.factory_assigned = :factory_assigned))
#   AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR (t1.sales_order_code = :sales_order_code))
#   AND if(:sales_assistant_size>0,t3.sales_assistant_code REGEXP :sales_assistant_str,1)
ORDER BY uninvoiced_age desc
# LIMIT :page_size offset :offset
;
select * from crm_sales_order cso where sales_order_code='25020294A';

select csop.*,cso.* from crm_sales_order cso left join crm_sales_order_product csop
    on cso.sales_order_code=csop.sales_order_code where cso.sales_order_code='25020055B';
select * from bip_outbound_order_detail bood where split_order_no='25020331A';
select * from crm_sales_invoice_details where csrcid='1001A510000000FP8INI';
select status,cso.* from crm_sales_order cso where sales_order_code='25030592A';

-- 合计
SELECT '合计' AS                                                 sale_company,
       sum(shipped_quantity)                                  shipped_quantity,
       round(sum(amount_exclusive_tax), 2) amount_exclusive_tax,
       sum(ifnull(invoiced_quantity, 0))                                   invoiced_quantity,
       sum(ifnull(uninvoiced_quantity,0))        uninvoiced_quantity,
       round(sum(uninvoiced_amount), 2)                                                  uninvoiced_amount
FROM (
         SELECT '西安环球'                               sale_company,
                t3.deparment_code,
                t3.deparment_name,
                t3.department_region region,
                t3.cust_name,
                t3.cust_manager_code,
                t3.cust_manager_name,
                t3.cust_type,
                CASE
                    WHEN t3.cust_type = 0 THEN '新建客户'
                    WHEN t3.cust_type = 1 THEN '公海客户'
                    WHEN t3.cust_type = 2 THEN '合作客户'
                    WHEN t3.cust_type = 3 THEN '开发中客户'
                    WHEN t3.cust_type = 4 THEN '受限客户'
                    END                                  cust_type_name,
                t1.sales_order_code,
                t1.status ,
                case
                    when t1.status=10 then '已删除'
                    when t1.status=8 then '已取消'
                    when t1.status=7 then '已关闭'
                    when t1.status=6 then '已开票'
                    when (select count(*)
                          from invoice_application ia
                          where ia.status IN ('0', '3', '4')
                            and ia.flag_deleted = 0
                            and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
                        then '已发货未开票'
                    when t1.status=0 then '已拆分'
                    when t1.status=1 then '已下达'
                    when t1.status=2 then '已排产'
                    when t1.status=3 then '已领料'
                    when t1.status=4 then '生产中'
                    when t1.status=5 then '已入库'
                    else '其他'
                    end status_desc,
                t1.factory_assigned,
                t4.shipment_code,
                t4.shipment_date,
                t2.main_class,
                t2.sub_class,
                t2.material_code,
                t2.material_name,
                t2.product_version,
                t2.grade_name,
                t2.contract_management_code,
                t2.order_quantity_after_split,
                t4.shipped_quantity,
                round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                         t2.unit_price_exclusive ),
                      4)                                 unit_price_exclusive,
                round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                         t2.amount_exclusive_tax ),
                      2)                                 amount_exclusive_tax,
                t4.invoiced_quantity                       invoiced_quantity,
                t4.shipped_quantity - t4.invoiced_quantity uninvoiced_quantity,
                round((t4.shipped_quantity - t4.invoiced_quantity) * t2.unit_price_exclusive ,
                      2)                                 uninvoiced_amount,
                datediff(current_date, t4.shipment_date)    uninvoiced_age
         FROM crm_sales_order t1
                  LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
                  LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
                  join (
             select split_order_no sales_order_code,
                    product_code material_code,
                    split_order_line_no,
                    group_concat(outbound_no) shipment_code,
                    max(outbound_date) shipment_date,
                    sum(ship_quantity) shipped_quantity,
                    sum(ifnull(nnum,0))  invoiced_quantity
             from bip_outbound_order_detail bood
                      left join (select csrcid, csrcbid, sum(nnum) nnum
                                 from crm_sales_invoice_details
                                 where flag_deleted = 0
                                 group by csrcid, csrcbid) csid
                                on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
             where bood.flag_deleted=0
             group by sales_order_code,material_code,split_order_line_no
         ) t4 on t4.sales_order_code = t2.sales_order_code and t4.material_code = t2.material_code
             and t4.split_order_line_no = t2.contract_product_line_number
         WHERE t1.flag_deleted = 0
           AND t2.flag_deleted = 0
           AND t3.flag_deleted = 0
           and t3.cust_status=2 and t3.cust_type not in (0)
           and t4.shipped_quantity > t4.invoiced_quantity
           and t2.order_quantity_after_split>t4.invoiced_quantity
           AND if(:admin,
                  1,
                  if(:cust_code_size > 0, t3.cust_code in (:cust_code_arr), 1)
               )
           AND ((:deparment_code IS NULL OR :deparment_code = '') OR (t3.deparment_code = :deparment_code))
           AND ((:region IS NULL OR :region = '') OR (department_region = :region))
           AND ((:cust_code IS NULL OR :cust_code = '') OR (t3.cust_code like concat('%', :cust_code, '%')))
           AND ((:cust_name IS NULL OR :cust_name = '') OR (t3.cust_name LIKE concat('%', :cust_name, '%')))
           AND ((:material_code IS NULL OR :material_code = '') OR (t2.material_code like concat('%', :material_code, '%')))
           AND ((:material_name IS NULL OR :material_name = '') OR (material_name LIKE concat('%', :material_name, '%')))
           AND ((:shipment_start_date IS NULL OR :shipment_start_date = '') OR
                (date_format(shipment_date, '%Y-%m-%d') >= :shipment_start_date))
           AND ((:shipment_end_date IS NULL OR :shipment_end_date = '') OR
                (date_format(shipment_date, '%Y-%m-%d') <= :shipment_end_date))
           AND if(:cust_manager_size>0, t3.cust_manager_code in (:cust_manager_arr), 1)
           AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (t1.factory_assigned = :factory_assigned))
           AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR (t1.sales_order_code = :sales_order_code))
           AND if(:sales_assistant_size>0,t3.sales_assistant_code REGEXP :sales_assistant_str,1)
         ORDER BY t1.id ASC
     )temp;

