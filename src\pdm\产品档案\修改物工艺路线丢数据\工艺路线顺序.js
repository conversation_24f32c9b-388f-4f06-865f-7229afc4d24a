var childrenCraft = [
    {
        "craft_name": "原料裁切",
        "indexs": "1",
        "technological_parameter": "[{\"切纸要求\":\"纸张光三边\"},{\"切纸偏差\":\"切纸偏差（倾斜）≤0.5mm\"},{\"备注\":\"纸纹方向与说明书280mm方向平行\"},{}]",
        "product_code": "SMSW0000090",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:53",
        "rate": "0",
        "parent_code": "SMSW0000090",
        "ban_json_code": "版编号：SM03-SMYY001913-BZV5类型：印版类别：CTP版版名称：重组新型冠状病毒疫苗（Sf9细胞）说明书（芬欧汇川双胶）版-1出本：16",
        "ys_fixed_time": "0",
        "id": 569239,
        "update_by": "2",
        "material_name": "编码：1010102040000015 名称：889*1194 60g 江苏芬欧汇川UPM双胶 index：7924268",
        "source_name": "GP说明书裁切中心",
        "flag_deleted": 0,
        "ys_standing_time": "0",
        "product_version": "3.5",
        "create_time": "2025-03-15 10:18:53",
        "craft_code": "D0107",
        "material_json_code": "编码：1010102040000015 名称：889*1194 60g 江苏芬欧汇川UPM双胶 index：7924268",
        "version": "1.0",
        "ys_fixed_rate": "0",
        "out_of_book": "16",
        "fixed_consumption": "0",
        "ys_manual_speed": "0",
        "source_code": "GPSMCQZX"
    },
    {
        "ys_standing_time": "0",
        "product_version": "3.5",
        "craft_name": "击凸（凹）",
        "create_time": "2025-03-15 10:18:54",
        "craft_code": "D0111",
        "indexs": "10",
        "technological_parameter": "[]",
        "product_code": "SMSW0000090",
        "version": "1.0",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:54",
        "rate": "0",
        "ys_fixed_rate": "0",
        "out_of_book": "1",
        "parent_code": "SMSW0000090",
        "fixed_consumption": "0",
        "ys_fixed_time": "0",
        "id": 569248,
        "update_by": "2",
        "ys_manual_speed": "0",
        "flag_deleted": 0
    },
    {
        "ys_standing_time": "0",
        "product_version": "3.5",
        "craft_name": "压纹",
        "create_time": "2025-03-15 10:18:54",
        "craft_code": "D0112",
        "indexs": "11",
        "technological_parameter": "[]",
        "product_code": "SMSW0000090",
        "version": "1.0",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:54",
        "rate": "0",
        "ys_fixed_rate": "0",
        "out_of_book": "1",
        "parent_code": "SMSW0000090",
        "fixed_consumption": "0",
        "ys_fixed_time": "0",
        "id": 569249,
        "update_by": "2",
        "ys_manual_speed": "0",
        "flag_deleted": 0
    },
    {
        "ys_standing_time": "0",
        "product_version": "3.5",
        "craft_name": "烫印",
        "create_time": "2025-03-15 10:18:53",
        "craft_code": "D0108",
        "indexs": "2",
        "technological_parameter": "[]",
        "product_code": "SMSW0000090",
        "version": "1.0",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:53",
        "rate": "0",
        "ys_fixed_rate": "0",
        "out_of_book": "1",
        "parent_code": "SMSW0000090",
        "fixed_consumption": "0",
        "ys_fixed_time": "0",
        "id": 569240,
        "update_by": "2",
        "ys_manual_speed": "0",
        "flag_deleted": 0
    },
    {
        "ys_standing_time": "0",
        "product_version": "3.5",
        "craft_name": "UV冷转印",
        "create_time": "2025-03-15 10:18:53",
        "craft_code": "D0113",
        "indexs": "3",
        "technological_parameter": "[]",
        "product_code": "SMSW0000090",
        "version": "1.0",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:53",
        "rate": "0",
        "ys_fixed_rate": "0",
        "out_of_book": "1",
        "parent_code": "SMSW0000090",
        "fixed_consumption": "0",
        "ys_fixed_time": "0",
        "id": 569241,
        "update_by": "2",
        "ys_manual_speed": "0",
        "flag_deleted": 0
    },
    {
        "craft_name": "印刷",
        "indexs": "4",
        "technological_parameter": "[{\"印刷类型\":\"普通印刷\"},{\"印刷面\":\"正版\"},{\"咬口尺寸\":\"12\"},{\"印刷规矩\":\"正版靠身\"},{\"跟色标准\":\"跟客户已确认标准色样XGP-QEO-QMD/EXT/SC-3644\"},{},{\"备注\":\"纸纹方向与说明书280mm方向平行\"},{}]",
        "product_code": "SMSW0000090",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:54",
        "rate": "0",
        "parent_code": "SMSW0000090",
        "ban_json_code": "版编号：SM03-SMYY001913-BZV5类型：印版类别：CTP版版名称：重组新型冠状病毒疫苗（Sf9细胞）说明书（芬欧汇川双胶）版-1出本：16",
        "ys_fixed_time": "0",
        "id": 569242,
        "update_by": "2",
        "material_name": "编码：1010201030000034 名称：公司自调普通专色油墨(1kg*12罐/箱) index：7923732,编码：1010201030000034 名称：公司自调普通专色油墨(1kg*12罐/箱) index：7923732,编码：1010201010000012 名称：油墨PackSafe亮光快干环保油墨（黑）(200kg/桶) index：7923705",
        "source_name": "GP双色说明书印刷中心",
        "flag_deleted": 0,
        "ys_standing_time": "0",
        "product_version": "3.5",
        "create_time": "2025-03-15 10:18:54",
        "craft_code": "D0102",
        "material_json_code": "编码：1010201030000034 名称：公司自调普通专色油墨(1kg*12罐/箱) index：7923732,编码：1010201030000034 名称：公司自调普通专色油墨(1kg*12罐/箱) index：7923732,编码：1010201010000012 名称：油墨PackSafe亮光快干环保油墨（黑）(200kg/桶) index：7923705",
        "version": "1.0",
        "ys_fixed_rate": "0",
        "out_of_book": "16",
        "fixed_consumption": "0",
        "ys_manual_speed": "0",
        "source_code": "GPSMSSYSZX"
    },
    {
        "craft_name": "印刷",
        "indexs": "5",
        "technological_parameter": "[{\"印刷类型\":\"普通印刷\"},{\"印刷面\":\"背版\"},{\"咬口尺寸\":\"12\"},{\"印刷规矩\":\"背版靠外\"},{\"跟色标准\":\"跟客户已确认标准色样XGP-QEO-QMD/EXT/SC-3644\"},{},{\"备注\":\"纸纹方向与说明书280mm方向平行\"},{}]",
        "product_code": "SMSW0000090",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:54",
        "rate": "0",
        "parent_code": "SMSW0000090",
        "ban_json_code": "版编号：SM03-SMYY001913-BBV5类型：印版类别：CTP版版名称：重组新型冠状病毒疫苗（Sf9细胞）说明书（芬欧汇川双胶）版-1出本：16",
        "ys_fixed_time": "0",
        "id": 569243,
        "update_by": "2",
        "material_name": "编码：1010201010000012 名称：油墨PackSafe亮光快干环保油墨（黑）(200kg/桶) index：7923705",
        "source_name": "GP双色说明书印刷中心",
        "flag_deleted": 0,
        "ys_standing_time": "0",
        "product_version": "3.5",
        "create_time": "2025-03-15 10:18:54",
        "craft_code": "D0102",
        "material_json_code": "编码：1010201010000012 名称：油墨PackSafe亮光快干环保油墨（黑）(200kg/桶) index：7923705",
        "version": "1.0",
        "ys_fixed_rate": "0",
        "out_of_book": "16",
        "fixed_consumption": "0",
        "ys_manual_speed": "0",
        "source_code": "GPSMSSYSZX"
    },
    {
        "craft_name": "说明书大张品检",
        "indexs": "6",
        "technological_parameter": "[]",
        "product_code": "SMSW0000090",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:54",
        "rate": "0",
        "parent_code": "SMSW0000090",
        "ban_json_code": "版编号：SM03-SMYY001913-BZV5类型：印版类别：CTP版版名称：重组新型冠状病毒疫苗（Sf9细胞）说明书（芬欧汇川双胶）版-1出本：16",
        "ys_fixed_time": "0",
        "id": 569244,
        "update_by": "2",
        "material_name": "",
        "source_name": "GP说明书大张品检中心",
        "flag_deleted": 0,
        "ys_standing_time": "0",
        "product_version": "3.5",
        "create_time": "2025-03-15 10:18:54",
        "craft_code": "D0124",
        "material_json_code": "",
        "version": "1.0",
        "ys_fixed_rate": "0",
        "out_of_book": "16",
        "fixed_consumption": "0",
        "ys_manual_speed": "0",
        "source_code": "GPSMSDZPJZX"
    },
    {
        "craft_name": "成品裁切",
        "indexs": "7",
        "technological_parameter": "[{\"裁切标准\":\"公司内控标准\"},{\"备注\":\"纸纹方向与说明书280mm方向平行\"},{}]",
        "product_code": "SMSW0000090",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:54",
        "rate": "0",
        "parent_code": "SMSW0000090",
        "ban_json_code": "",
        "ys_fixed_time": "0",
        "id": 569245,
        "update_by": "2",
        "material_name": "",
        "source_name": "GP说明书裁切中心",
        "flag_deleted": 0,
        "ys_standing_time": "0",
        "product_version": "3.5",
        "create_time": "2025-03-15 10:18:54",
        "craft_code": "D0118",
        "material_json_code": "",
        "version": "1.0",
        "ys_fixed_rate": "0",
        "out_of_book": "1",
        "fixed_consumption": "0",
        "ys_manual_speed": "0",
        "source_code": "GPSMCQZX"
    },
    {
        "craft_name": "说明书人工选剔",
        "indexs": "8",
        "technological_parameter": "[{\"单包数量偏差\":\"±1‰\"},{}]",
        "product_code": "SMSW0000090",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:54",
        "rate": "0",
        "parent_code": "SMSW0000090",
        "ban_json_code": "",
        "ys_fixed_time": "0",
        "id": 569246,
        "update_by": "2",
        "material_name": "编码：10103040000001 名称：塑料袋 index：7923712,编码：1010301010000066 名称：纸箱413417 index：7924275",
        "source_name": "GP说明书人工选剔中心",
        "flag_deleted": 0,
        "ys_standing_time": "0",
        "product_version": "3.5",
        "create_time": "2025-03-15 10:18:54",
        "craft_code": "X0114",
        "material_json_code": "编码：10103040000001 名称：塑料袋 index：7923712,编码：1010301010000066 名称：纸箱413417 index：7924275",
        "version": "1.0",
        "ys_fixed_rate": "0",
        "out_of_book": "1",
        "fixed_consumption": "0",
        "ys_manual_speed": "0",
        "source_code": "GPSMRGXTZX"
    },
    {
        "ys_standing_time": "0",
        "product_version": "3.5",
        "craft_name": "过油",
        "create_time": "2025-03-15 10:18:54",
        "craft_code": "D0109",
        "indexs": "9",
        "technological_parameter": "[]",
        "product_code": "SMSW0000090",
        "version": "1.0",
        "create_by": "2",
        "update_time": "2025-03-15 10:18:54",
        "rate": "0",
        "ys_fixed_rate": "0",
        "out_of_book": "1",
        "parent_code": "SMSW0000090",
        "fixed_consumption": "0",
        "ys_fixed_time": "0",
        "id": 569247,
        "update_by": "2",
        "ys_manual_speed": "0",
        "flag_deleted": 0
    }
]
//将顺序调整
// 按照parent_code分组并重新排序index
function reorder_craft_index(craftArr) {
    // 创建一个对象来存储按parent_code分组的数据
    var grouped_by_parent = {};

    // 遍历craftArr，按parent_code分组
    for (var i = 0; i < craftArr.length; i++) {
        var item = craftArr[i];
        var parent_code = item.parent_code || "";

        if (!grouped_by_parent[parent_code]) {
            grouped_by_parent[parent_code] = [];
        }

        grouped_by_parent[parent_code].push(item);
    }

    // 对每个分组进行排序并重新赋值index
    for (var parent_code in grouped_by_parent) {
        if (grouped_by_parent.hasOwnProperty(parent_code)) {
            var group = grouped_by_parent[parent_code];

            // 按照indexs字段排序
            group.sort(function (a, b) {
                var index_a = String(a.indexs || "");
                var index_b = String(b.indexs || "");

                // 尝试转换为数字
                var num_a = Number(index_a);
                var num_b = Number(index_b);

                // 如果都是有效数字
                if (!isNaN(num_a) && !isNaN(num_b)) {
                    return num_a - num_b;
                }

                // 如果只有a是数字
                if (!isNaN(num_a)) {
                    return -1;
                }

                // 如果只有b是数字
                if (!isNaN(num_b)) {
                    return 1;
                }

                // 都不是数字,按字符串比较
                return index_a.localeCompare(index_b);
            });

            // 重新赋值index，从1开始
            for (var j = 0; j < group.length; j++) {
                group[j].index = String(j + 1);
                // 同时更新indexs字段，如果存在的话
                if (group[j].hasOwnProperty("indexs")) {
                    group[j].indexs = String(j + 1);
                }
            }
        }
    }

    // 将分组后的数据合并回一个数组
    var result = [];
    for (var parent_code in grouped_by_parent) {
        if (grouped_by_parent.hasOwnProperty(parent_code)) {
            result = result.concat(grouped_by_parent[parent_code]);
        }
    }

    return result;
}
childrenCraft = reorder_craft_index(childrenCraft)
console.log(JSON.stringify(childrenCraft))