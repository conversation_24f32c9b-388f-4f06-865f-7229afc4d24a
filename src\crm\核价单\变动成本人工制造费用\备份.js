var materialResult = JSON.parse(JSON.stringify(obj.materialResult))
var craftArr = JSON.parse(JSON.stringify(obj.craftArr)) // 传进来的参数工艺路线
var inCarftArr = [] //需要存核价单的工艺路线
var bjArr = []
//处理部件
for (var x = 0; x < materialResult.length; x++) {
  if (
    materialResult[x].categroy == 7 ||
    materialResult[x].categroy == 3 ||
    materialResult[x].categroy == 0
  ) {
    bjArr.push(materialResult[x])
  }
}

var max_out_of_book = 0
var multi_product = {};
for (var i = 0; i < craftArr.length; i++) {
  if(craftArr[i].sub_code){
    if (craftArr[i].process_type == "1") {
      if(!multi_product[craftArr[i].sub_code]){
        multi_product[craftArr[i].sub_code]=0
      }
    if (craftArr[i].out_of_book) {
      if (Number(craftArr[i].out_of_book) > multi_product[craftArr[i].sub_code]) {
        multi_product[craftArr[i].sub_code] = Number(craftArr[i].out_of_book)
      }
    }
  }
  }else{
    if (craftArr[i].process_type == "1") {
    if (craftArr[i].out_of_book) {
      if (Number(craftArr[i].out_of_book) > max_out_of_book) {
        max_out_of_book = Number(craftArr[i].out_of_book)
      }
    }
  }
  }
  
}

for (var x = 0; x < craftArr.length; x++) {
  var obj = {}
  obj['parent_code'] = craftArr[x].parent_code //parent_code
  obj['categroy'] = '10' //categroy
  obj['craft_name'] = craftArr[x].craft_name //工艺
  obj['production_resource'] = craftArr[x].work_code //资源
  
  if (craftArr[x].process_type == "1") {
    //大张工序
    if(craftArr[x].sub_code){
       obj['out_of_book'] = multi_product[craftArr[x].sub_code];
    }else{
      obj['out_of_book'] = max_out_of_book //出本
    }
  } else {
    //小张工序
    obj['out_of_book'] = 1 //出本
  }
  
  obj['process_type'] = craftArr[x].process_type
  for (var y = 0; y < workArr.length; y++) {
    if (craftArr[x].work_code == workArr[y].resource_code) {
      obj['resource_center_standard_hourly_wage'] = workArr[y].labor_hour_costs //资源中心标准时薪
      obj['resource_center_fixed_manufacturing_cost'] =
        workArr[y].manufacturing_expense_costs //资源中心制造费用
      if (craftArr[x].craft_code == workArr[y].process_code) {
        var zzcd = 1
        if (workArr[y].defuat_unit == '米/分') {
          if (craftArr[x].technological_parameter) {
            var tp = craftArr[x].technological_parameter.toString()
            tp = tp.replace('[', '')
            tp = tp.replace(']', '')
            tp = tp.replace(/\{|\}/g, '')
            tp = tp.replace(/\\"|"/g, '')
            if (tp.indexOf(',') != -1) {
              var cs_arr = tp.split(',')
              for (var a = 0; a < cs_arr.length; a++) {
                var cs = cs_arr[a].split(':')
                if (cs[0] == '走纸长度' || cs[0] == '走向长度') {
                  zzcd = cs[1] * 1
                }
              }
            } else {
              var cs = craftArr[x].technological_parameter.split(':')
              if (cs[0] == '走纸长度' || cs[0] == '走向长度') {
                zzcd = cs[1] * 1
              }
            }
          }
          obj['resource_quota'] = (workArr[y].standard_speed / zzcd) * 60 //资源定额
        } else {
          obj['resource_quota'] = workArr[y].standard_speed //资源定额
        }
      } 
    }
  }
  obj['process_operation_difficulty_factor'] = craftArr[x].ys_fixed_rate/1000 //工艺作业难度系数

  var sub_count = obj['sub_count'] || 1;
  sub_count = Number(sub_count);
  if(count_obj[obj['parent_code']]){
    obj['component_count'] = eval(count_obj[obj['parent_code']]);
  }else{
    obj['component_count'] = 1 //部件个数
  }
  if(!obj.resource_quota){
    obj['resource_quota'] = 0;
    obj['unit_product_operation_time'] = 0;
  }else{
    obj['unit_product_operation_time'] = (sub_count/(Number(obj.resource_quota)/(1+Number(obj.process_operation_difficulty_factor)))/obj.out_of_book*obj['component_count'])
    .toFixed(12)
  }

  
  
  //单位产品作业时
  if(craftArr[x].count && !isNaN(Number(craftArr[x].count))){
    obj['variable_labor_cost'] = (
    obj.unit_product_operation_time * obj.resource_center_standard_hourly_wage * Number(craftArr[x].count)
  ).toFixed(12) //*(craftArr[x].count*1) 有系数的变动人工成本
  obj['variable_manufacturing_cost'] = (
    obj.unit_product_operation_time *
    obj.resource_center_fixed_manufacturing_cost * Number(craftArr[x].count)
  ).toFixed(12) //*(craftArr[x].count*1) 有系数的变动制造费用
  }else{
    obj['variable_labor_cost'] = (
    obj.unit_product_operation_time * obj.resource_center_standard_hourly_wage
  ).toFixed(12) //*(craftArr[x].count*1) 变动人工成本
  obj['variable_manufacturing_cost'] = (
    obj.unit_product_operation_time *
    obj.resource_center_fixed_manufacturing_cost
  ).toFixed(12) //*(craftArr[x].count*1) 变动制造费用
  }
  
  for (var y = 0; y < workArr.length; y++) {
    if (craftArr[x].work_code == workArr[y].resource_code) {
      var costs = JSON.parse(workArr[y].manufacturing_expense_json)
      // 初始化一个变量来存储找到的最后一项
      var lastNonZeroItem = null
      // 遍历数组
      for (var i = 0; i < costs.length; i++) {
        // 检查当前项的proportion是否不为0
        if (costs[i].proportion !== 0) {
          // 如果不为0，则更新最后一项
          lastNonZeroItem = costs[i].cost_item
        }
      }
      var price = 0
      for (var z = 0; z < costs.length; z++) {
        if (costs[z].cost_item == '管理人员工资') {
          obj['executive_salary'] =
            (
              obj.variable_manufacturing_cost *
              ((costs[z].proportion * 1) / 100)
            ).toFixed(12) * 1
          if (costs[z].cost_item != lastNonZeroItem) {
            price += obj.executive_salary
          }
        }
        if (costs[z].cost_item == '折旧费（设备及其他类）') {
          obj['depreciation_expense_other'] =
            (
              obj.variable_manufacturing_cost *
              ((costs[z].proportion * 1) / 100)
            ).toFixed(12) * 1
          if (costs[z].cost_item != lastNonZeroItem) {
            price += obj.depreciation_expense_other
          }
        }
        if (costs[z].cost_item == '折旧费（房屋建筑物类）') {
          obj['depreciation_expense_buildings'] =
            (
              obj.variable_manufacturing_cost *
              ((costs[z].proportion * 1) / 100)
            ).toFixed(12) * 1
          if (costs[z].cost_item != lastNonZeroItem) {
            price += obj.depreciation_expense_buildings
          }
        }
        if (costs[z].cost_item == '修理费') {
          obj['repair_charge'] =
            (
              obj.variable_manufacturing_cost *
              ((costs[z].proportion * 1) / 100)
            ).toFixed(12) * 1
          if (costs[z].cost_item != lastNonZeroItem) {
            price += obj.repair_charge
          }
        }
        if (costs[z].cost_item == '水费') {
          obj['water_rate'] =
            (
              obj.variable_manufacturing_cost *
              ((costs[z].proportion * 1) / 100)
            ).toFixed(12) * 1
          if (costs[z].cost_item != lastNonZeroItem) {
            price += obj.water_rate
          }
        }
        if (costs[z].cost_item == '电气费') {
          obj['electric_charge'] =
            (
              obj.variable_manufacturing_cost *
              ((costs[z].proportion * 1) / 100)
            ).toFixed(12) * 1
          if (costs[z].cost_item != lastNonZeroItem) {
            price += obj.electric_charge
          }
        }
        if (costs[z].cost_item == '仓储费') {
          obj['storage_charge'] =
            (
              obj.variable_manufacturing_cost *
              ((costs[z].proportion * 1) / 100)
            ).toFixed(12) * 1
          if (costs[z].cost_item != lastNonZeroItem) {
            price += obj.storage_charge
          }
        }
        if (lastNonZeroItem == costs[z].cost_item) {
          if (costs[z].cost_item == '管理人员工资') {
            obj['executive_salary'] =
              (obj.variable_manufacturing_cost - price).toFixed(12) * 1
          }
          if (costs[z].cost_item == '折旧费（设备及其他类）') {
            obj['depreciation_expense_other'] =
              (obj.variable_manufacturing_cost - price).toFixed(12) * 1
          }
          if (costs[z].cost_item == '折旧费（房屋建筑物类）') {
            obj['depreciation_expense_buildings'] =
              (obj.variable_manufacturing_cost - price).toFixed(12) * 1
          }
          if (costs[z].cost_item == '修理费') {
            obj['repair_charge'] =
              (obj.variable_manufacturing_cost - price).toFixed(12) * 1
          }
          if (costs[z].cost_item == '水费') {
            obj['water_rate'] =
              (obj.variable_manufacturing_cost - price).toFixed(12) * 1
          }
          if (costs[z].cost_item == '电气费') {
            obj['electric_charge'] =
              (obj.variable_manufacturing_cost - price).toFixed(12) * 1
          }
          if (costs[z].cost_item == '仓储费') {
            obj['storage_charge'] =
              (obj.variable_manufacturing_cost - price).toFixed(12) * 1
          }
        }
      }
    }
  }
  inCarftArr.push(obj)
}
var inArr = []
inArr = inArr.concat(inCarftArr)
inArr = inArr.concat(bjArr)
for (var k = 0; k < inArr.length; k++) {
  inArr[k].quotation_code = quotation_code
  inArr[k].new_version = new_version
  inArr[k].version = '1.0'
}
return inArr
