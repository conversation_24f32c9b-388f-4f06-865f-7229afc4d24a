var request = require('request');
const mysql = require('mysql2');

//遍历日期,从2024-05-01 到2025-07-10
var start_date = new Date('2024-05-01');
var end_date = new Date('2025-07-10');
var date_arr = [];
while (start_date <= end_date) {
    date_arr.push(start_date.toISOString().slice(0, 10));
    start_date.setDate(start_date.getDate() + 1);
}


// 数据库连接配置
const dbConfig = {
    host: '**********', // 根据您的环境调整
    user: 'root', // 建议使用只读账号: 'readonly_user'
    password: '123456', // 对应只读账号密码: 'ReadOnly123!'
    database: 'screen' // 根据您的数据库名调整
};

// 创建请求选项的函数
function createRequestOptions(date) {
    return {
        'method': 'POST',
        'url': 'http://***********:9095/apiArrange/1881184065316159488/call/1922528023574831104',
        'headers': {
            'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Host': '***********:9095',
            'Connection': 'keep-alive',
            'Cookie': 'JSESSIONID=5A37B020E4DC70FBE7FCAD5C1F65EC73'
        },
        body: JSON.stringify({
            "api_id": "1921761926367416320",
            "date": date
        })
    };
}

//返回数据对象: {"date":"2025-03-15","code":200,"data":[{"DIANBIHUIRONGTANG":"39.**********","JINGGUOTIANSHU":null,"ZHENGQIBIHUIRONGTANG":"1.**********","SUNSHI_FM":"0.**********","SUNSHI_LN":"0.**********","SUNSHI_BUKECE":"1.**********","RRTL_PJ":"682.**********","SHIHUI":"1181976.**********","ZHENGQIBICHANPINTANG":"1.**********","DIANBICHANPINTANG":"42.**********","CTL_ZZP":"97.**********","SHL_LT_TM":"98.**********","CTL":"88.**********","ID":"c3a6640f-0100-11f0-9caf-00163e0ac7c1","SHEBEIANQUANLV":"99.**********"}], "jgts": "236"}

// 创建数据库连接
const connection = mysql.createConnection(dbConfig);

// 连接数据库
connection.connect((err) => {
    if (err) {
        console.error('数据库连接失败:', err);
        return;
    }
    console.log('数据库连接成功');
});

// 插入数据到数据库的函数
function insertDataToDatabase(responseData, callback) {
    try {
        const parsedData = JSON.parse(responseData);

        if (parsedData.code !== 200 || !parsedData.data || parsedData.data.length === 0) {
            console.log('API返回数据异常或无数据');
            callback();
            return;
        }

        const data = parsedData.data[0]; // 取第一条数据
        const updateDate = parsedData.date; // 使用返回的日期
        const jgts = parsedData.jgts; // 使用返回的日期
        // 构建插入SQL语句 - 使用REPLACE INTO实现插入或更新
        const sql = `
            REPLACE INTO bi_jtribao_fac_3 (
                id,
                update_time,
                jgts,
                shebeianquanlv,
                shihui,
                dianbihuirongtang,
                dianbichanpintang,
                zhengqibihuirongtang,
                zhengqibichanpintang,
                ctl,
                ctl_zzp,
                sunshi_ln,
                sunshi_fm,
                sunshi_bukece,
                shl_lt_tm,
                rrtl_pj
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ,?,?)
        `;

        const values = [
            data.ID , // 使用返回的ID或生成新ID
            updateDate,
            jgts,
            parseFloat(data.SHEBEIANQUANLV) || null,
            parseFloat(data.SHIHUI) || null,
            parseFloat(data.DIANBIHUIRONGTANG) || null,
            parseFloat(data.DIANBICHANPINTANG) || null,
            parseFloat(data.ZHENGQIBIHUIRONGTANG) || null,
            parseFloat(data.ZHENGQIBICHANPINTANG) || null,
            parseFloat(data.CTL) || null,
            parseFloat(data.CTL_ZZP) || null,
            parseFloat(data.SUNSHI_LN) || null,
            parseFloat(data.SUNSHI_FM) || null,
            parseFloat(data.SUNSHI_BUKECE) || null,
            parseFloat(data.SHL_LT_TM) || null,
            parseFloat(data.RRTL_PJ) || null
        ];

        connection.query(sql, values, (error, results) => {
            if (error) {
                console.error(`数据插入失败 (${updateDate}):`, error);
            } else {
                console.log(`数据插入成功 (${updateDate}):`, results.affectedRows, '行受影响');
            }
            callback();
        });

    } catch (parseError) {
        console.error('数据解析失败:', parseError);
        callback();
    }
}



// 处理单个日期的函数
function processDate(date, callback) {
    const options = createRequestOptions(date);

    console.log(`正在处理日期: ${date}`);

    request(options, function (error, response) {
        if (error) {
            console.error(`API请求失败 (${date}):`, error);
            callback();
            return;
        }

        console.log(`API响应 (${date}):`, response.body);

        // 将数据存储到数据库
        insertDataToDatabase(response.body, callback);
    });
}

// 处理所有日期的函数
function processAllDates() {
    let currentIndex = 0;

    function processNext() {
        if (currentIndex >= date_arr.length) {
            console.log('所有日期处理完成');
            connection.end();
            return;
        }

        const currentDate = date_arr[currentIndex];
        currentIndex++;

        console.log(`进度: ${currentIndex}/${date_arr.length}`);

        processDate(currentDate, () => {
            // 添加延迟避免请求过于频繁
            setTimeout(processNext, 10);
        });
    }

    processNext();
}

// 开始处理
processAllDates();

