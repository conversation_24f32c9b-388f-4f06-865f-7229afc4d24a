var data_arr = [
  {
      "metric_year": "2024",
      "cust_size": 0,
      "invoiced_amount_new": 0.00,
      "invoiced_amount_old": 0.00
    },
  {
    "metric_year": "2025",
    "cust_size": 465,
    "invoiced_amount_new": 26139047.41,
    "invoiced_amount_old": 0.00
  }
  
];

var years = data_arr.map(function(item) {
  return item.metric_year + '年';
});

var option = {
  title: {
      text: "当年新客户开发对比"
  },
  tooltip: {
      trigger: "axis",
      axisPointer: {
          type: "shadow"
      }
  },
  legend: {
      data: years
  },
  grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
  },
  xAxis: {
      type: "category",
      data: years
  },
  yAxis: {
      type: "value",
      max: function() {
          var maxValue = Math.max.apply(null, data_arr.map(function(item) {
              return item.cust_size;
          }));
          return Math.ceil(maxValue / 100) * 100;
      }()
  },
  series: data_arr.map(function(item, index) {
      return {
          name: item.metric_year + '年',
          type: "bar",
          data: data_arr.map(function(d) {
              return d.metric_year === item.metric_year ? d.cust_size : 0;
          }),
          itemStyle: {
              color: index === 0 ? "#5470c6" : "#91cc75"
          }
      };
  })
};
return option;