var carftArr = [{"craft_name":"原料分切","indexs":"ZDQX0001028_C10000007","technological_parameter":"","product_code":"ZDQX0001028","create_by":"1821396441761148929","update_time":"2025-03-10 10:41:36","rate":"1","parent_code":"ZDQX0001028_C1","ban_json_code":"","r_craft_code":"D0067","ys_fixed_time":"0","id":144926,"update_by":"1821396441761148929","material_name":"编码：1010101010000001 名称：800 245g 芬兰Pro FBB Bright白卡 index：ZDQX0001028_C10000006","source_name":"12","r_work_code":"12","flag_deleted":0,"ys_standing_time":"0","product_version":"1.10","work_code":"12","create_time":"2025-03-10 10:41:36","craft_code":"原料分切","count":"1","index":"ZDQX0001028_C10000007","material_json_code":"编码：1010101010000001 名称：800 245g 芬兰Pro FBB Bright白卡","version":"1.0","ys_fixed_rate":"0","out_of_book":"1","fixed_consumption":"0","ys_manual_speed":"0","source_code":"12"},{"craft_name":"一个工序","indexs":"ZDQX0001028_C20000003","technological_parameter":"","product_code":"ZDQX0001028","create_by":"1821396441761148929","update_time":"2025-03-10 10:41:36","rate":0,"parent_code":"ZDQX0001028_C2","ban_json_code":"","r_craft_code":"D0049","ys_fixed_time":0,"id":144927,"update_by":"1821396441761148929","material_name":"编码：1010201010000006 名称：新金冠 红（高光） index：ZDQX0001028_C20000001","source_name":"12","r_work_code":"12","flag_deleted":0,"ys_standing_time":0,"product_version":"1.10","work_code":"12","create_time":"2025-03-10 10:41:36","craft_code":"一个工序","count":"1","index":"ZDQX0001028_C20000003","material_json_code":"编码：1010201010000006 名称：新金冠 红（高光）","version":"1.0","ys_fixed_rate":0,"out_of_book":"1","fixed_consumption":0,"ys_manual_speed":0,"source_code":"12"}]
var wi_arr=[{"material_name":"889 320g 芬兰Pro FBB Bright白卡","index":"ZDQX0001028_C20000002","material_code":"1010101010000004"},{"material_name":"新金冠 红（高光）","index":"ZDQX0001028_C20000001","material_code":"1010201010000006"}]
for (var i = 0; i < carftArr.length; i++) {
  var material_code = carftArr[i].material_name
  // 编码：1010101010000097 名称：787 300g 山东华夏太阳白卡 index：12 ,
  //   编码：1010301010000107 名称：纸箱433826,
  //   编码：1010302020000112 名称：隔档3825
  var result = []
  if (material_code != null && material_code != "") {
    if (material_code.indexOf(",") != -1) {
    var material = material_code.split(",")
    for (var x = 0; x < material.length; x++) {
      var material_name  = material[x]
      if (material_name.indexOf(" index：") != -1) {
        var index = material_name.split(" index：")[1]; // 提取index  
        var obj = {}
        obj['index'] = index
        result.push(obj)
      }
    }
  } else {
      if (material_code.indexOf("index：") != -1) {
        var index = material_code.split("index：")[1]; // 提取index  
        var obj = {}
        obj['index'] = index
        result.push(obj)
      }
    }
  }

  var result1 = []
  for (var k = 0; k < result.length; k++) {
    for (var j = 0; j < wi_arr.length; j++) {
      if (result[k].index == wi_arr[j].index) {
        var item = {}
        item['index'] = wi_arr[j].index
        item['material_name'] = wi_arr[j].material_name
        item['material_code'] = wi_arr[j].material_code
        result1.push(item);
      }
    }
  }

  var xzwl_arr = result1
  var wl_msg_index = ""
  var wl_msg = ""
  for (var y = 0; y < xzwl_arr.length; y++) {
    wl_msg_index = wl_msg_index +"编码："+xzwl_arr[y].material_code+" 名称："+xzwl_arr[y].material_name+" index："+xzwl_arr[y].index+","
    wl_msg = wl_msg +"编码："+xzwl_arr[y].material_code+" 名称："+xzwl_arr[y].material_name+","
  }
  
  // 确保即使没有匹配项也能保留原始值
  if (wl_msg_index !== "") {
    wl_msg_index = wl_msg_index.substring(0,wl_msg_index.length-1)
    carftArr[i]['material_name'] = wl_msg_index
  }
  
  if (wl_msg !== "") {
    wl_msg = wl_msg.substring(0,wl_msg.length-1)
    carftArr[i]['material_json_code'] = wl_msg
  } else {
    // 如果没有找到匹配项，保留原始的material_json_code
    if (!carftArr[i]['material_json_code']) {
      carftArr[i]['material_json_code'] = ""
    }
  }
}
console.log("debug_carftArr2",JSON.stringify(carftArr))
return carftArr