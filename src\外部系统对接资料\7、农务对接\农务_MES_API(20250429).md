# 农务_mes_api (20250429)  使用文档

## 概述

这份文档详细描述了如何使用API（农务_mes_api），包含了API的详细解释、调试示例、调用示例。

---

## API说明

#### API名称: 农务_mes_api

**API ID:** 1917103515637911552

**API类型:** guide

**请求类型:** `POST`

**请求URL:** http://172.16.32.11/data_service/api/v2/api_service/query/1917103515637911552

**请求参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| sugarseason | string | false | "\"2022/23\"" | 榨季 |
| entrytime | date | false | "\"2025-03-16\"" | 进厂时间 |
| pageSize | int | false | -- | 分页个数 |
| pageNum | int | false | -- | 分页页码 |

**返回参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| sugarseason | string | false | -- | 榨季 |
| factorycode | string | false | -- | 工厂代码 |
| entrytime | date | false | -- | 进厂时间 |
| sugarcanevarietyname | string | false | -- | 甘蔗品种名称 |
| sugarcanevarietycode | string | false | -- | 甘蔗品种代码 |
| sugarcanequantity | double | false | -- | 甘蔗数量 |
| seasonaltotalquantityofeachsugarcanevariety | double | false | -- | 各甘蔗品种榨季累计数量 |
| seasonaltotalpercentageofmechanicallyharvestedsugarcane | string | false | -- | 机收蔗榨季累计数量占比 |
| sameperiodlastseasonweighedsugarcane | double | false | -- | 上榨季同期已过磅甘蔗量 |
| mechanicallyharvestedsugarcanequantity | double | false | -- | 机收蔗甘蔗数量 |
| manuallycutsugarcanequantity | double | false | -- | 人工砍蔗甘蔗数量 |
| dailyweighedsugarcaneoutsidefactory | double | false | -- | 当日厂外已过磅甘蔗量 |
| dailyweighedsugarcanetobetransported | double | false | -- | 当日待运已过磅甘蔗量 |
| dailyweighedsugarcaneintransit | double | false | -- | 当日在途已过磅甘蔗量 |
| dailyseasonaltotalweighedsugarcane | double | false | -- | 当日榨季累计已过磅甘蔗量 |
| dailyseasonaltotalmechanicallyharvestedsugarcane | double | false | -- | 当日榨季累计机收蔗数量 |
| dailyseasonaltotalmanuallycutsugarcane | double | false | -- | 当日榨季累计人工砍蔗数量 |
| dailyquantityofeachsugarcanevariety | double | false | -- | 各甘蔗品种当日数量 |

```
{
    "ApiID": "1917103515637911552",
    "Option": [
        {
            "Id": 100,
            "Val": 0,
            "Val_": "eyJhbGciOiJBRVMiLCJ0eXAiOiJKV1QifQ.eyJhcHAiOiJsaW1zLnRvLm1lcyIsImlhdCI6MTgsInRpZCI6IjIiLCJ1bml4IjoxNzQyNTQ2MDU4fQ.Djlt6wprQPEua-XRQR-pCEAAC8H5LehpJ0Awu_ul8jbkWHqi70VjP8omQ66H1SQzu2cMNppRsx_f7-sHSlub6WPlE8EZ7cbrgeMPcDkSo4NiXazTM0UkFSjMyYfobNSlVdBjKnoiY-DBBeqkQ6R9Xnk"
        }
    ],
    "Params": {
    "sugarseason": "2024/25",
    "entrytime": "2025-03-16",
    "pageNum": 1,
    "pageSize": 15
}
}
```

**正常响应示例:**

```json
{
    "Status": {
        "Code": 0,
        "Message": ""
    },
    "Result": {
        "Meta": [
            {
                "Name": "id",
                "Type": "int"
            },
            {
                "Name": "name",
                "Type": "string"
            }
        ],
        "Data": [
            {
                "id": 123,
                "name": "test"
            }
        ]
    }
}
```

**错误响应示例:**

```json
{
    "Status": {
        "Code": 4,
        "Message": "[10001] api access denied"
    },
    "Result": {
        "Meta": [],
        "Data": []
    }
}
```

**错误代码:**

## ErrorCode 常见状态码相关：
|  |  |  |
| --- | --- | --- |
| 状态码 | 名称 | 含义 |
| 0 | QueryErrorType_OK | 查询成功 |
| 1 | QueryErrorType_PARSER_ERROR | 解析报错 |
| 2 | QueryErrorType_ILLGEAL_INPUT_ERROR | 非法参数报错 |
| 3 | QueryErrorType_RATE_LIMIT_ERROR | 限流报错 |
| 4 | QueryErrorType_AUTH_ERROR | 权限报错 |
| 5 | QueryErrorType_QUERY_TIMEOUT | 查询超时报错 |
| 6 | QueryErrorType_DS_TIMEOUT | 数据源超时报错 |
| 7 | QueryErrorType_INTERNAL_ERROR | 程序内部报错 |
| 8 | QueryErrorType_META_ERROR | 元信息报错 |
| 9 | QueryErrorType_DS_RATE_LIMIT_ERROR | 数据源限流报错 |
| 255 | QueryErrorType_UNKNOWN_ERROR | 未知错误 |
## One Service 常见错误码相关：
|  |  |  |
| --- | --- | --- |
| 错误码 | 名称 | 含义 |
| 10000 | UnknownQueryEngine | 未知查询引擎 |
| 10001 | NoPermission | 没有权限 |
| 10002 | MetaErr | 元信息错误 |
| 10003 | ParamsParseErr | 参数解析错误 |
| 10004 | ApiRateLimitExceed | Api的QPS超限额 |
| 10005 | ParseErr | 解析错误 |
| 10006 | ExecuteErr | 执行错误 |
| 10007 | UnknownQueryType | 未知查询类型 |
| 10008 | QueryRequestError | 查询请求错误 |
| 10009 | QueryEngineMismatchError | 查询引擎不匹配错误 |


**调用说明:**

###### HTTP调用示例
```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  -H 'dpstoken: $DPS_TOKEN' \
  http://172.16.32.11/data_service/api/v2/api_service/query/1917103515637911552 \
  -d '{"sugarseason":"\"2022/23\"","entrytime":"\"2025-03-16\"","pageSize":0,"pageNum":0}'
```
###### 如果使用动态密钥功能,可以使用如下接口动态获取token信息

```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  http://172.16.32.11/data_service/api/v2/api_service/token \
  -d '{"AppKey": "you_app_key","AppSecret": "you_app_secret"}'
```
###### 返回样例：
```
{
    "code": 0,
    "message": "Success",
    "data": {
        "AppKey": "you_app_key",
        "Token": "token_str"
    },
    "meta": {
        "text": "",
        "redirect": ""
    }
}
```
###### 解析其中的Token 调用的时候将$DPS_TOKEN替换为“该应用密钥”，动态token有一定有效期且会动态变化，请不要缓存动态token或者对动态token有任何假设，每次请求获取即可

**开启分页调用说明:**

向导式API分页调用说明
1、在API页面开启高级配置的分页，在API页面测试时系统会自动添加pageNum和pageSize参数。
2、调用API的时候需要将pageNum和pageSize填入请求参数中。
例如：{
  "pageNum": 1,
  "pageSize": 100
}

脚本式API分页调用说明
1、用SQL来进行分页操作，例如：SELECT * FROM table ORDER BY id LIMIT 10 OFFSET 0;
2、如果想要获取本次查询的total总数，请参考：https://bytedance.larkoffice.com/docx/HJKudzKHVoEAejxAmUncZ1LanIc

**API调用常见问题:**

1、api xxx meta not exists
答：一般是因为API没有在对应的环境发布，可在API页面版本信息中查看是否已发布到对应环境。

2、10001 api access denied
答：API调用时未输入DpsToken或者DpsToken与已对API授权的应用不一致。

---

## 使用 Postman 调试API说明

Postman是一个强大的HTTP API测试工具，可以帮助开发者快速开发和测试API。

### POST 请求示例:

1. 打开Postman应用程序。
2. 在请求方式空间中选择 `POST`。
3. 在请求URL空间中输入 http://172.16.32.11/data_service/api/v2/api_service/query/1917103515637911552，然后点击 `Body` 的选项卡，然后选择 `raw`。
4. 在 `Body` 中填写 `Json` 格式的请求参数，然后点击 `Send` 按钮。
5. 如果一切正常，则会在下方的 `Body` 中看到响应结果。

---

## 使用 Java 调用API说明

如果你在Java编程语言调用API，可以使用HttpClient等库。

### HttpClient库为例子，POST请求的代码示例

```java

// 导入需要的Apache HttpClient库包
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import java.io.IOException;

public class ApiTest {

    public static void main(String[] args) throws Exception {
        // 定义基础URL
        String baseUrl = "http://172.16.32.11/data_service/api/v2/api_service/query/";
        // 定义API ID
        String apiId = "1917103515637911552";
        // 定义API TOKEN
        String dpstoken = "$DPS_TOKEN";
        // 定义请求主体
        String body = "{\"sugarseason\":\"\\"2022/23\\"\",\"entrytime\":\"\\"2025-03-16\\"\",\"pageSize\":0,\"pageNum\":0}";
        // 调用performPostRequest函数发送POST请求并在控制台输出返回的结果
        System.out.println(performPostRequest(baseUrl, apiId, dpstoken, body));
    }

    // 定义函数performPostRequest发送POST请求
    private static String performPostRequest(String baseUrl, String apiId, String dpstoken, String body) throws IOException {
        // 创建一个默认的CloseableHttpClient实例
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建一个HttpPost实例，参数为目标URL（基础URL+API ID）
        HttpPost httpPost = new HttpPost(baseUrl + apiId);

        // 设置httpPost的header
        httpPost.setHeader("dpstoken", dpstoken);

        // 设置httpPost的body
        httpPost.setEntity(new StringEntity(body));

        // 使用httpclient执行httpPost，获取响应结果
        CloseableHttpResponse response = httpclient.execute(httpPost);
        try {
            // 从response中获取HttpEntity
            HttpEntity entity = response.getEntity();

            // 如果HttpEntity不为null，则将其转化为String类型，并返回
            return entity != null ? EntityUtils.toString(entity) : null;
            // 确保response在执行完毕后被关闭，避免资源泄漏
        } finally {
            response.close();
        }
    }
}
/*
需要在pom.xml中添加的dependency如下
<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpclient</artifactId>
    <version>4.5.13</version>
</dependency>

<!-- https://mvnrepository.com/artifact/org.json/json -->
<dependency>
    <groupId>org.json</groupId>
    <artifactId>json</artifactId>
    <version>20210307</version>
</dependency>
 */
```