# cqms_mes_api (20250429)  使用文档

## 概述

这份文档详细描述了如何使用API（cqms_mes_api），包含了API的详细解释、调试示例、调用示例。

---

## API说明

#### API名称: cqms_mes_api

**API ID:** 1917065208161898496

**API类型:** guide

**请求类型:** `POST`

**请求URL:** http://172.16.32.11/data_service/api/v2/api_service/query/1917065208161898496

**请求参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| SugarSeason | string | false | "\"2024/25\"" | 榨季 |
| EntryTime | date | false | "\"2025-03-16\"" | 进厂时间 |
| pageSize | int | false | -- | 分页个数 |
| pageNum | int | false | -- | 分页页码 |

**返回参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| SugarSeason | string | false | -- | 榨季 |
| FactoryCode | string | false | -- | 工厂代码 |
| EntryTime | date | false | -- | 进厂时间 |
| ManuallyCutSugarcanePurity | double | false | -- | 人工砍蔗甘蔗简纯度 |
| MechanicallyHarvestedSugarcanePurity | double | false | -- | 机收蔗甘蔗简纯度 |
| ManuallyCutSugarcaneSugarContent | double | false | -- | 人工砍蔗甘蔗糖分 |
| MechanicallyHarvestedSugarcaneSugarContent | double | false | -- | 机收蔗甘蔗糖分 |
| DailySeasonalTotalManuallyCutSugarcanePurity | double | false | -- | 当日榨季累计人工砍蔗简纯度 |
| DailySeasonalTotalManuallyCutSugarcaneSugarContent | double | false | -- | 当日榨季累计人工砍蔗甘蔗糖分 |
| DailySeasonalTotalMechanicallyHarvestedSugarcanePurity | double | false | -- | 当日榨季累计机收蔗简纯度 |
| DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent | double | false | -- | 当日榨季累计机收蔗甘蔗糖分 |
| SugarcaneVarietyName | string | false | -- | 甘蔗品种名称 |
| SugarcaneVarietyCode | string | false | -- | 甘蔗品种代码 |
| DailySugarContentOfEachSugarcaneVariety | double | false | -- | 各甘蔗品种当日糖分 |
| SeasonalTotalSugarContentOfEachSugarcaneVariety | double | false | -- | 各甘蔗品种榨季累计糖分 |
| SugarcaneSugarContent | double | false | -- | 甘蔗糖分 |

```json
{
    "ApiID": "1917103515637911552",
    "Option": [
        {
            "Id": 100,
            "Val": 0,
            "Val_": "eyJhbGciOiJBRVMiLCJ0eXAiOiJKV1QifQ.eyJhcHAiOiJsaW1zLnRvLm1lcyIsImlhdCI6MTgsInRpZCI6IjIiLCJ1bml4IjoxNzQyNTQ2MDU4fQ.Djlt6wprQPEua-XRQR-pCEAAC8H5LehpJ0Awu_ul8jbkWHqi70VjP8omQ66H1SQzu2cMNppRsx_f7-sHSlub6WPlE8EZ7cbrgeMPcDkSo4NiXazTM0UkFSjMyYfobNSlVdBjKnoiY-DBBeqkQ6R9Xnk"
        }
    ],
    "Params": {
    "sugarseason": "2024/25",
    "entrytime": "2025-03-16",
    "pageNum": 1,
    "pageSize": 15
}
}
```

**正常响应示例:**

```json
{
	"Meta": [
		{
			"Name": "SugarSeason",
			"Type": "string"
		},
		{
			"Name": "FactoryCode",
			"Type": "string"
		},
		{
			"Name": "EntryTime",
			"Type": "date"
		},
		{
			"Name": "ManuallyCutSugarcanePurity",
			"Type": "float"
		},
		{
			"Name": "MechanicallyHarvestedSugarcanePurity",
			"Type": "float"
		},
		{
			"Name": "ManuallyCutSugarcaneSugarContent",
			"Type": "float"
		},
		{
			"Name": "MechanicallyHarvestedSugarcaneSugarContent",
			"Type": "float"
		},
		{
			"Name": "DailySeasonalTotalManuallyCutSugarcanePurity",
			"Type": "float"
		},
		{
			"Name": "DailySeasonalTotalManuallyCutSugarcaneSugarContent",
			"Type": "float"
		},
		{
			"Name": "DailySeasonalTotalMechanicallyHarvestedSugarcanePurity",
			"Type": "float"
		},
		{
			"Name": "DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent",
			"Type": "float"
		},
		{
			"Name": "SugarcaneVarietyName",
			"Type": "string"
		},
		{
			"Name": "SugarcaneVarietyCode",
			"Type": "string"
		},
		{
			"Name": "DailySugarContentOfEachSugarcaneVariety",
			"Type": "float"
		},
		{
			"Name": "SeasonalTotalSugarContentOfEachSugarcaneVariety",
			"Type": "float"
		},
		{
			"Name": "SugarcaneSugarContent",
			"Type": "float"
		}
	],
	"Data": [
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":84.28,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":5396.110000000001,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"T03\",\"SugarcaneVarietyName\":\"中糖3号             \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":1457.7000000000014,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":334037.17000000016,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"Z09\",\"SugarcaneVarietyName\":\"中蔗9号             \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":61.36,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":3859.96,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"Z06\",\"SugarcaneVarietyName\":\"壮糖6号             \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":95.11,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":76549.07999999996,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"C22\",\"SugarcaneVarietyName\":\"新台糖22号          \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":0,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":80164.56999999999,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"E36\",\"SugarcaneVarietyName\":\"桂柳05136           \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":438.0200000000001,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":50140.93,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"E50\",\"SugarcaneVarietyName\":\"桂柳07150           \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":401.57,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":825700.4199999998,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"G42\",\"SugarcaneVarietyName\":\"桂糖42号            \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":262.32000000000005,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":25108.200000000004,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"G44\",\"SugarcaneVarietyName\":\"桂糖44号            \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":230.98,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":4628.88,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"G46\",\"SugarcaneVarietyName\":\"桂糖46号            \"}",
		"{\"DailySeasonalTotalManuallyCutSugarcanePurity\":8392850.296814999,\"DailySeasonalTotalManuallyCutSugarcaneSugarContent\":1464168.7300000002,\"DailySeasonalTotalMechanicallyHarvestedSugarcanePurity\":78762.96949,\"DailySeasonalTotalMechanicallyHarvestedSugarcaneSugarContent\":13821.380000000003,\"DailySugarContentOfEachSugarcaneVariety\":594.54,\"EntryTime\":\"2025-03-16\",\"FactoryCode\":\"FN        \",\"ManuallyCutSugarcanePurity\":17088.225334000006,\"ManuallyCutSugarcaneSugarContent\":3020.0900000000033,\"MechanicallyHarvestedSugarcanePurity\":3430.220918,\"MechanicallyHarvestedSugarcaneSugarContent\":605.7900000000003,\"SeasonalTotalSugarContentOfEachSugarcaneVariety\":33063.53999999999,\"SugarSeason\":\"2024/25\",\"SugarcaneSugarContent\":3625.88,\"SugarcaneVarietyCode\":\"G55\",\"SugarcaneVarietyName\":\"桂糖55号            \"}"
	],
	"BaseResp": {
		"StatusMessage": "",
		"StatusCode": 0,
		"Extra": {
			"data_size": "8079",
			"time_cost": "143"
		}
	}
}
```

**错误响应示例:**

```json
{
    "Status": {
        "Code": 4,
        "Message": "[10001] api access denied"
    },
    "Result": {
        "Meta": [],
        "Data": []
    }
}
```

**错误代码:**

## ErrorCode 常见状态码相关：
|  |  |  |
| --- | --- | --- |
| 状态码 | 名称 | 含义 |
| 0 | QueryErrorType_OK | 查询成功 |
| 1 | QueryErrorType_PARSER_ERROR | 解析报错 |
| 2 | QueryErrorType_ILLGEAL_INPUT_ERROR | 非法参数报错 |
| 3 | QueryErrorType_RATE_LIMIT_ERROR | 限流报错 |
| 4 | QueryErrorType_AUTH_ERROR | 权限报错 |
| 5 | QueryErrorType_QUERY_TIMEOUT | 查询超时报错 |
| 6 | QueryErrorType_DS_TIMEOUT | 数据源超时报错 |
| 7 | QueryErrorType_INTERNAL_ERROR | 程序内部报错 |
| 8 | QueryErrorType_META_ERROR | 元信息报错 |
| 9 | QueryErrorType_DS_RATE_LIMIT_ERROR | 数据源限流报错 |
| 255 | QueryErrorType_UNKNOWN_ERROR | 未知错误 |
## One Service 常见错误码相关：
|  |  |  |
| --- | --- | --- |
| 错误码 | 名称 | 含义 |
| 10000 | UnknownQueryEngine | 未知查询引擎 |
| 10001 | NoPermission | 没有权限 |
| 10002 | MetaErr | 元信息错误 |
| 10003 | ParamsParseErr | 参数解析错误 |
| 10004 | ApiRateLimitExceed | Api的QPS超限额 |
| 10005 | ParseErr | 解析错误 |
| 10006 | ExecuteErr | 执行错误 |
| 10007 | UnknownQueryType | 未知查询类型 |
| 10008 | QueryRequestError | 查询请求错误 |
| 10009 | QueryEngineMismatchError | 查询引擎不匹配错误 |


**调用说明:**

###### HTTP调用示例
```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  -H 'dpstoken: $DPS_TOKEN' \
  http://172.16.32.11/data_service/api/v2/api_service/query/1917065208161898496 \
  -d '{"SugarSeason":"\"2024/25\"","EntryTime":"\"2025-03-16\"","pageSize":0,"pageNum":0}'
```
###### 如果使用动态密钥功能,可以使用如下接口动态获取token信息

```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  http://172.16.32.11/data_service/api/v2/api_service/token \
  -d '{"AppKey": "you_app_key","AppSecret": "you_app_secret"}'
```
###### 返回样例：
```
{
    "code": 0,
    "message": "Success",
    "data": {
        "AppKey": "you_app_key",
        "Token": "token_str"
    },
    "meta": {
        "text": "",
        "redirect": ""
    }
}
```
###### 解析其中的Token 调用的时候将$DPS_TOKEN替换为“该应用密钥”，动态token有一定有效期且会动态变化，请不要缓存动态token或者对动态token有任何假设，每次请求获取即可

**开启分页调用说明:**

向导式API分页调用说明
1、在API页面开启高级配置的分页，在API页面测试时系统会自动添加pageNum和pageSize参数。
2、调用API的时候需要将pageNum和pageSize填入请求参数中。
例如：{
  "pageNum": 1,
  "pageSize": 100
}

脚本式API分页调用说明
1、用SQL来进行分页操作，例如：SELECT * FROM table ORDER BY id LIMIT 10 OFFSET 0;
2、如果想要获取本次查询的total总数，请参考：https://bytedance.larkoffice.com/docx/HJKudzKHVoEAejxAmUncZ1LanIc

**API调用常见问题:**

1、api xxx meta not exists
答：一般是因为API没有在对应的环境发布，可在API页面版本信息中查看是否已发布到对应环境。

2、10001 api access denied
答：API调用时未输入DpsToken或者DpsToken与已对API授权的应用不一致。

---

## 使用 Postman 调试API说明

Postman是一个强大的HTTP API测试工具，可以帮助开发者快速开发和测试API。

### POST 请求示例:

1. 打开Postman应用程序。
2. 在请求方式空间中选择 `POST`。
3. 在请求URL空间中输入 http://172.16.32.11/data_service/api/v2/api_service/query/1917065208161898496，然后点击 `Body` 的选项卡，然后选择 `raw`。
4. 在 `Body` 中填写 `Json` 格式的请求参数，然后点击 `Send` 按钮。
5. 如果一切正常，则会在下方的 `Body` 中看到响应结果。

---

## 使用 Java 调用API说明

如果你在Java编程语言调用API，可以使用HttpClient等库。

### HttpClient库为例子，POST请求的代码示例

```java

// 导入需要的Apache HttpClient库包
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import java.io.IOException;

public class ApiTest {

    public static void main(String[] args) throws Exception {
        // 定义基础URL
        String baseUrl = "http://172.16.32.11/data_service/api/v2/api_service/query/";
        // 定义API ID
        String apiId = "1917065208161898496";
        // 定义API TOKEN
        String dpstoken = "$DPS_TOKEN";
        // 定义请求主体
        String body = "{\"SugarSeason\":\"\\"2024/25\\"\",\"EntryTime\":\"\\"2025-03-16\\"\",\"pageSize\":0,\"pageNum\":0}";
        // 调用performPostRequest函数发送POST请求并在控制台输出返回的结果
        System.out.println(performPostRequest(baseUrl, apiId, dpstoken, body));
    }

    // 定义函数performPostRequest发送POST请求
    private static String performPostRequest(String baseUrl, String apiId, String dpstoken, String body) throws IOException {
        // 创建一个默认的CloseableHttpClient实例
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建一个HttpPost实例，参数为目标URL（基础URL+API ID）
        HttpPost httpPost = new HttpPost(baseUrl + apiId);

        // 设置httpPost的header
        httpPost.setHeader("dpstoken", dpstoken);

        // 设置httpPost的body
        httpPost.setEntity(new StringEntity(body));

        // 使用httpclient执行httpPost，获取响应结果
        CloseableHttpResponse response = httpclient.execute(httpPost);
        try {
            // 从response中获取HttpEntity
            HttpEntity entity = response.getEntity();

            // 如果HttpEntity不为null，则将其转化为String类型，并返回
            return entity != null ? EntityUtils.toString(entity) : null;
            // 确保response在执行完毕后被关闭，避免资源泄漏
        } finally {
            response.close();
        }
    }
}
/*
需要在pom.xml中添加的dependency如下
<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpclient</artifactId>
    <version>4.5.13</version>
</dependency>

<!-- https://mvnrepository.com/artifact/org.json/json -->
<dependency>
    <groupId>org.json</groupId>
    <artifactId>json</artifactId>
    <version>20210307</version>
</dependency>
 */
```