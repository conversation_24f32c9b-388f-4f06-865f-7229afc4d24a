select * from  crm_cust_transport
where street NOT REGEXP '^[0-9]+$'
  or region not REGEXP '^[0-9]+$'
  or city not REGEXP '^[0-9]+$'
  or province not REGEXP '^[0-9]+$'
;

-- 更新省
select * from crm_cust_transport where
province NOT REGEXP '^[0-9]+$';

update  crm_cust_transport cct join
              hq_mdm_b.mes_area_mode b on cct.city=b.mode_id
set province=if(parent_mode_id=0,mode_id,parent_mode_id)
where province NOT REGEXP '^[0-9]+$';
-- 更新市
select * from crm_cust_transport where
    city NOT REGEXP '^[0-9]+$';
update  crm_cust_transport cct join
    hq_mdm_b.mes_area_mode b on cct.region=b.mode_id
set city=if(parent_mode_id=0,mode_id,parent_mode_id)
where city NOT REGEXP '^[0-9]+$';

select * from hq_mdm_b.mes_area_mode where mode_id=120000000000;
select * from hq_mdm_b.mes_area_mode where mode_name like '%滨海新区%';

-- 查询region正常得数据
select * from crm_cust_transport cct
where street NOT REGEXP '^[0-9]+$'
and region REGEXP '^[0-9]+$'
;
-- 更新1
update  crm_cust_transport set street=region
where street NOT REGEXP '^[0-9]+$'
  and region REGEXP '^[0-9]+$';

-- 更新2
update  crm_cust_transport set street=city
where street NOT REGEXP '^[0-9]+$'
  and region NOT REGEXP '^[0-9]+$'
  and city REGEXP '^[0-9]+$';
