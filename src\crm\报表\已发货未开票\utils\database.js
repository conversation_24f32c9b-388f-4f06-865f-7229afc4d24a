const mysql = require('mysql');
const moment = require('moment');

class Database {
    constructor(config) {
        this.config = config;
        this.connection = null;
    }

    async connect() {
        this.connection = mysql.createConnection(this.config);
        return new Promise((resolve, reject) => {
            this.connection.connect(err => {
                if (err) {
                    reject(err);
                    return;
                }
                resolve();
            });
        });
    }

    async query(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.connection.query(sql, params, (err, results) => {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(results);
            });
        });
    }

    async close() {
        return new Promise((resolve) => {
            if (this.connection) {
                this.connection.end(() => resolve());
            } else {
                resolve();
            }
        });
    }

    // 常用查询方法封装
    async getInvoiceData(startDate, endDate) {
        const sql = `
            SELECT 
                a.id, a.csrcid, a.billno, a.amount, 
                DATE_FORMAT(a.billdate, '%Y-%m-%d') as billdate
            FROM invoice a
            WHERE a.billdate BETWEEN ? AND ?
        `;
        return await this.query(sql, [startDate, endDate]);
    }

    async getShipmentData(startDate, endDate) {
        const sql = `
            SELECT 
                a.id, a.csrcid, a.shipno, a.amount,
                DATE_FORMAT(a.shipdate, '%Y-%m-%d') as shipdate
            FROM shipment a
            WHERE a.shipdate BETWEEN ? AND ?
        `;
        return await this.query(sql, [startDate, endDate]);
    }
}

module.exports = Database; 