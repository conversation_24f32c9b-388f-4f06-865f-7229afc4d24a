var paramL={"xyDataL":[{"invoicing_period":"15","margin_variance_percentage":"15","x":"0","y":"0.92","margin_threshold_percentage":"40"}],"custDataL":[],"custListL":[],"productDataL":[],"currencyDataL":[{"label":"人民币","value":"CNY"},{"label":"新台币","value":"TWD"},{"label":"英镑","value":"GBP"},{"label":"澳元","value":"AUD"},{"label":"港币","value":"HKD"},{"label":"美元","value":"USD"},{"label":"日元","value":"JPY"},{"label":"欧元","value":"EUR"},{"label":"澳门币","value":"MOP"}],"custResL":[{"roleCount":1,"sex":0,"roleList":[{"flagDelete":0,"roleCode":"APP_ADMIN","sortOrder":1,"roleName":"应用管理员","category":"ROLE","roleType":0,"status":0}],"userId":"2","lockFlag":"0","tenantName":"西安环球印务股份有限公司","phone":"","name":"环球管理员","nickname":"环球管理员","roleName":"应用管理员","tenantId":2,"state":0,"email":"","username":"<EMAIL>"},{"deptName":"药包华东华南区","dockUserId":"116","roleCount":0,"sex":1,"deptId":99,"extJson":"{\"jobactivityid\":\"\",\"tempresidentnumber\":\"\",\"createdate\":\"2023-09-07\",\"joblevel\":\"0\",\"subcompanyid1\":\"26\",\"subcompanyname\":\"西安环球印务股份有限公司\",\"startdate\":\"2023-03-01\",\"userid\":\"116\",\"password\":\"DAA90547451C70CBE5A7345B6B31BCE1\",\"jobactivitydesc\":\"\",\"subcompanycode\":\"1\",\"bememberdate\":\"\",\"mobilecall\":\"\",\"nativeplace\":\"北京市\",\"certificatenum\":\"61030319870721165X\",\"fax\":\"\",\"height\":\"181\",\"loginid\":\"000057\",\"bepartydate\":\"2007-12-20\",\"degree\":\"\",\"weight\":\"\",\"telephone\":\"********\",\"isAdAccount\":\"\",\"residentplace\":\"西安市莲湖区沣镐东路348号6栋2单元15层1号\",\"healthinfo\":\"0\",\"lastname\":\"黄鑫鹏\",\"enddate\":\"\",\"maritalstatus\":\"已婚\",\"departmentname\":\"药包华东华南区\",\"folk\":\"汉族\",\"jobgroupid\":\"\",\"status\":\"1\",\"birthday\":\"1987-07-21\",\"lastChangdate\":\"2024-10-08\",\"accounttype\":\"0\",\"textfield1\":\"市场部门\",\"textfield2\":\"\",\"jobcall\":\"\",\"managerid\":\"104\",\"workroom\":\"\",\"assistantid\":\"\",\"departmentcode\":\"*********\",\"email\":\"<EMAIL>\",\"seclevel\":\"20\",\"policy\":\"中共党员\",\"jobtitle\":\"40\",\"workcode\":\"000057\",\"departmentid\":\"38\",\"homeaddress\":\"\",\"sex\":\"男\",\"mobile\":\"***********\",\"systemlanguage\":\"\",\"educationlevel\":\"22\",\"islabouunion\":\"1\",\"locationid\":\"22\",\"regresidentplace\":\"宝鸡市金台区\",\"dsporder\":\"222.16\"}","dockDeptId":"38","roleList":[],"userId":"1867047194716098562","lockFlag":"0","tenantName":"西安环球印务股份有限公司","phone":"***********","name":"黄鑫鹏","tenantId":2,"state":0,"deptCode":"*********","email":"<EMAIL>","username":"000057"}],"custIds":["1867047194716098562","2"]}
var cust_info=[]
var id="1867047194728681474"
var crm_preliminary_quotation ={"delivery_address":"江苏省 连云港市 连云区 连云经济技术开发区东晋路5号,陈刚,13605135718","raw_materials":"3624.01","total_cost":"11302.71","remark":"旧版，天津生产","type":"1","tax_rate":"13","productArr":[{"float_rate":"±5","total_cost":4613.92,"crm_one_time_cost_sumAmount":0,"delivery_expense":0,"gd_depreciation_expense_other":385,"bd_depreciation_expense_buildings":0.00257268524,"type":"1","show_update_by":"张悦","dw_sum_amount":0.050970711946,"variable_auxiliary_materials_sumAmount":0.005207535296,"mnemonic_code":"ZDYY007740","bd_depreciation_expense_other":0.00257268524,"id":"ZDHY0000402_2.2_C000157","sales_amount_usd":10500,"paper_all_cost":"934.54","utilities_labor":"2432.08","quotation_factory":"天津","create_time":"2025-03-10 17:36:33","sales_gross_profit_margin":"50.35","show_create_by":"张悦","gd_sum_amount":2829.94840447125,"sales_amount":10500,"sales_gross_profit":4678.12,"unit_price_usd":0.3,"financial_gross_profit_margin":"50.35","object_id":"80740B02","unit_price_excluding":0.265487,"version":"2","float_mark":"±","default_factory":"天津","sub_class":"化药类","purchase_price":"0.27600","zz_unit_variable_labor_cost_sumAmount":0.007718055719999999,"auxiliary_materials_sumAmount":0,"rg_unit_variable_labor_cost_sumAmount":0.**********,"raw_material_cost":"1231.75","handling_expense":0,"product_weight":0.00476,"gd_depreciation_expense_buildings":385,"sales_amount_excluding_usd":9292.04,"main_class":"折叠纸盒类","sales_amount_excluding":9292.04,"productTotal":35000,"product_quantity":35000,"create_by":"1867047194716098566","update_time":"2025-03-10 17:36:33","quotation_code":"HJ251031905","standard_cost_unit_price":"0.1318","cost_accounting_sumAmount":0.***********,"fixed_paper_sumAmount":114.**************,"cust_name":"江苏豪森药业集团有限公司","depreciation_expense":"950.09","quotation_version":"2","labor_rg_sumAmount":790,"update_by":"1867047194716098566","material_name":"注射用米卡芬净钠小盒 （美国）\u000100mg*1瓶/盒","labor_bd_sumAmount":1155,"flag_deleted":0,"product_version":"2.2","unit_price_excluding_usd":0.265487,"preliminary_quotation_code":"BJ2503000175","unit_price":0.3,"float_info":"5","freight_cost":0,"carrier_feign_cost":0,"cust_code":"C000157","break_even_quantity":"13192.23","out_of_book":0,"auxiliary_ctp_sumAmount":0,"grade_name":"注射用米卡芬净钠小盒 （美国）\u000100mg *1瓶/盒","product_big_version":"2","standard_unit":"只","material_code":"ZDHY0000402"},{"float_rate":"±5","total_cost":6688.79,"crm_one_time_cost_sumAmount":0,"delivery_expense":0,"gd_depreciation_expense_other":415,"bd_depreciation_expense_buildings":0.************,"type":"1","show_update_by":"张悦","dw_sum_amount":0.270221654578,"variable_auxiliary_materials_sumAmount":0.042087254801,"mnemonic_code":"ZDYY010727","bd_depreciation_expense_other":0.0129083333,"id":"ZDHY0000404_2.2_C000157","sales_amount_usd":13260,"paper_all_cost":"1669.21","utilities_labor":"3116.92","quotation_factory":"天津","create_time":"2025-03-10 17:36:33","sales_gross_profit_margin":"43.00","show_create_by":"张悦","gd_sum_amount":3175.************,"sales_amount":13260,"sales_gross_profit":5045.72,"unit_price_usd":1.02,"financial_gross_profit_margin":"43.00","object_id":"602241B04","unit_price_excluding":0.902655,"version":"2","float_mark":"±","default_factory":"天津","sub_class":"化药类","purchase_price":"0.93840","zz_unit_variable_labor_cost_sumAmount":0.************,"auxiliary_materials_sumAmount":0,"rg_unit_variable_labor_cost_sumAmount":0.***********,"raw_material_cost":"2392.26","handling_expense":0,"product_weight":0.020681,"gd_depreciation_expense_buildings":422.5,"sales_amount_excluding_usd":11734.51,"main_class":"折叠纸盒类","sales_amount_excluding":11734.51,"productTotal":13000,"product_quantity":13000,"create_by":"1867047194716098566","update_time":"2025-03-10 17:36:33","quotation_code":"HJ251031881","standard_cost_unit_price":"0.5145","cost_accounting_sumAmount":0.************,"fixed_paper_sumAmount":175.************,"cust_name":"江苏豪森药业集团有限公司","depreciation_expense":"1179.62","quotation_version":"2","labor_rg_sumAmount":900,"update_by":"1867047194716098566","material_name":"注射用米卡芬净钠小盒（美国）100mg 10支/盒（602241B04）","labor_bd_sumAmount":1262.5,"flag_deleted":0,"product_version":"2.2","unit_price_excluding_usd":0.902655,"preliminary_quotation_code":"BJ2503000175","unit_price":1.02,"float_info":"5","freight_cost":0,"carrier_feign_cost":0,"cust_code":"C000157","break_even_quantity":"5021.73","out_of_book":0,"auxiliary_ctp_sumAmount":0,"grade_name":"注射用米卡芬净钠小盒（美国）100mg 10支/盒（602241B04）","product_big_version":"2","standard_unit":"只","material_code":"ZDHY0000404"}],"total_sales_gross_profit":"9723.84","quotation_code":"","deparment":"华东华南区","cust_manager_code":"1867047194716098562","cust_name":"江苏豪森药业集团有限公司","depreciation_expense":"2129.71","total_sales":"21026.55","utilities_labor":"5549.00","overall_sales_margin_rate":"46.25%","exchange_rate":"1","preliminary_quotation_code":"BJ2503000175","freight_cost":"0.00","settlement_currency":"CNY","cust_code":"C000157","delivery_date":"2025-03-22","imposition_quotation":"否","cust_manager_name":"黄鑫鹏","status":"2"}
var crm_preliminary_quotation_product 
var preliminary_quotation_code ="BJ2503000175"
var custIds = [];
var newDateL=new Date()
var custResL = paramL['custResL'];
var custIds = paramL['custIds'];
var xyDataL = paramL['xyDataL'];
var productDataL = [];

var custMapL = {};
for (var i = 0; i < custResL.length; i++) {
	custMapL[custResL[i]['userId']] = custResL[i]['dockUserId'] + '***' + custResL[i]['dockDeptId'];
}

// 审批主信息
var mainDataL = crm_preliminary_quotation;
var custInfoL = custMapL[crm_preliminary_quotation['cust_manager_code']];
if (custInfoL) {
	var tempL = custInfoL.split('***');
	mainDataL['cust_manager_code'] = tempL[0];
	mainDataL['deparment_code'] = tempL[1];
}

if (!mainDataL['preliminary_quotation_code']) mainDataL['preliminary_quotation_code'] = preliminary_quotation_code;
var custDeptL = custMapL[id];
if (custDeptL) {
	var tempDeptL = custDeptL.split('***');
	mainDataL['create_by'] = tempDeptL[0];
	mainDataL['deparment_code'] = tempDeptL[1];
}

mainDataL['create_time'] = new Date(newDateL);
if (xyDataL && xyDataL.length > 0) {
	mainDataL['margin_threshold_percentage'] = xyDataL[0]['margin_threshold_percentage'];
	mainDataL['margin_variance_percentage'] = xyDataL[0]['margin_variance_percentage'];
}


// 查询主产品
var makeup_product_name = '';
for (var i = 0; i < crm_preliminary_quotation_product.length; i++) {
	var makeup_product = crm_preliminary_quotation_product[i]['makeup_product'];

	if ([1, '1', true, 'true'].indexOf(makeup_product) != -1) {
		makeup_product_name = crm_preliminary_quotation_product[i]['grade_name'];
		break;
	}
}


var zhxsmll = 0;
var zxsmle = 0;
var zxse = 0;
var zcb = 0;
var ycl = 0;
var sdrg = 0;
var zj = 0;
var yf = 0;
for (var i = 0; i < crm_preliminary_quotation_product.length; i++) {
	var makeup_product = crm_preliminary_quotation_product[i]['makeup_product'];
	if ([1, '1', true, 'true'].indexOf(makeup_product) == -1) {
		crm_preliminary_quotation_product[i]['makeup_product_name'] = makeup_product_name;
	}

	// 销售毛利额
	var sales_gross_profit = parseFloat(crm_preliminary_quotation_product[i]['sales_gross_profit']);
	// 总销售额
	var sales_amount_excluding = parseFloat(crm_preliminary_quotation_product[i]['sales_amount_excluding']);
	// 总成本
	var total_cost = parseFloat(crm_preliminary_quotation_product[i]['total_cost']);
	// 原材料
	var raw_material_cost = parseFloat(crm_preliminary_quotation_product[i]['raw_material_cost']);
	// 水电人力
	var utilities_labor = parseFloat(crm_preliminary_quotation_product[i]['utilities_labor']);
	// 折旧费
	var depreciation_expense = parseFloat(crm_preliminary_quotation_product[i]['depreciation_expense']);
	// 运费
	var freight_cost = parseFloat(crm_preliminary_quotation_product[i]['freight_cost']);

	if (sales_gross_profit && sales_gross_profit != 'NaN') zxsmle += sales_gross_profit;
	if (sales_amount_excluding && sales_amount_excluding != 'NaN') zxse += sales_amount_excluding;
	if (total_cost && total_cost != 'NaN') zcb += total_cost;
	if (raw_material_cost && raw_material_cost != 'NaN') ycl += raw_material_cost;
	if (utilities_labor && utilities_labor != 'NaN') sdrg += utilities_labor;
	if (depreciation_expense && depreciation_expense != 'NaN') zj += depreciation_expense;
	if (freight_cost && freight_cost != 'NaN') yf += freight_cost;
}


if (zxsmle != 0) zxsmle = zxsmle.toFixed(4);
if (zxse != 0) zxse = zxse.toFixed(4);
if (zcb != 0) zcb = zcb.toFixed(4);
if (ycl != 0) ycl = ycl.toFixed(4);
if (sdrg != 0) sdrg = sdrg.toFixed(4);
if (zj != 0) zj = zj.toFixed(4);
if (yf != 0) yf = yf.toFixed(4);
if (zxse) zhxsmll = (zxsmle / zxse * 100).toFixed(4);
mainDataL['zhxsmll'] = zhxsmll;
mainDataL['zxsmle'] = zxsmle;
mainDataL['zxse'] = zxse;
mainDataL['zcb'] = zcb;
mainDataL['ycl'] = ycl;
mainDataL['sdrg'] = sdrg;
mainDataL['zj'] = zj;
mainDataL['yf'] = yf;

var custListL = [];
var productDataL = [];

// 普通报价
if (cust_info && cust_info.length > 0) {
	for (var i = 0; i < cust_info.length; i++) {
		var custInfoL2 = custMapL[cust_info[i]['cust_manager_code']];
		if (custInfoL2) {
			var tempL2 = custInfoL2.split('***');
			cust_info[i]['cust_manager_code'] = tempL2[0];
			cust_info[i]['deparment_code'] = tempL2[1];
		}
	}
	custListL = cust_info;
} else {
	mainDataL['department_region'] = crm_preliminary_quotation['deparment'];
	custListL.push(mainDataL);
}

productDataL = crm_preliminary_quotation_product

var vipArrL = [];
for (var i = 0; i < custListL.length; i++) {
	vipArrL.push(custListL[i]['cust_vip']);
}

var main_cust_vip = '';
var sortArrL = [null, undefined, '', 'C', 'B', 'A'];
for (var i = 0; i < sortArrL.length; i++) {
	if (vipArrL.indexOf(sortArrL[i]) != -1) {
		main_cust_vip = sortArrL[i];
		break;
	}
}
if([null, undefined].indexOf(main_cust_vip) != -1) main_cust_vip = '';

var vipMapL = {
	'A': 0,
	'B': 1,
	'C': 2
}
if (vipMapL[main_cust_vip]) mainDataL['main_cust_vip'] = vipMapL[main_cust_vip];
else mainDataL['main_cust_vip'] = '';

paramL['mainDataL'] = mainDataL;
paramL['custListL'] = custListL;
paramL['productDataL'] = productDataL;

var custTempL = {
	custIds: custIds,
	custResL: custResL,
	custMapL: custMapL
}
paramL['custMapJsonL'] = JSON.stringify(custTempL);

return paramL;