import csv
from typing import Dict, List, <PERSON><PERSON>

def read_csv_file(file_path: str) -> Dict[str, List[str]]:
    """读取CSV文件并返回以编码为键的字典"""
    data = {}
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # 跳过表头
        for row in reader:
            if len(row) >= 4:  # 确保行至少有4个字段
                code = row[0]  # 物料基本分类编码
                data[code] = row
    return data

def compare_files(file_198: str, file_bip: str) -> Tuple[List[List[str]], List[List[str]]]:
    """比较两个CSV文件的差异"""
    data_198 = read_csv_file(file_198)
    data_bip = read_csv_file(file_bip)
    
    # 存储差异
    extra_items = []  # 198.csv中多出的条目
    inconsistent_items = []  # 内容不一致的条目
    
    # 遍历198.csv中的所有条目
    for code, row_198 in data_198.items():
        if code not in data_bip:
            # 198.csv中多出的条目
            extra_items.append(row_198)
        else:
            # 检查内容是否一致
            row_bip = data_bip[code]
            if row_198 != row_bip:
                # 内容不一致的条目
                inconsistent_items.append([
                    code,
                    "198.csv: " + ", ".join(row_198),
                    "bip.csv: " + ", ".join(row_bip)
                ])
    
    return extra_items, inconsistent_items

def main():
    file_198 = "198.csv"
    file_bip = "bip.csv"
    
    extra_items, inconsistent_items = compare_files(file_198, file_bip)
    
    print("\n=== 198.csv中多出的条目 ===")
    for item in extra_items:
        print(f"编码: {item[0]}")
        print(f"完整数据: {', '.join(item)}")
        print("-" * 50)
    
    print("\n=== 内容不一致的条目 ===")
    for item in inconsistent_items:
        print(f"编码: {item[0]}")
        print(item[1])
        print(item[2])
        print("-" * 50)

if __name__ == "__main__":
    main() 