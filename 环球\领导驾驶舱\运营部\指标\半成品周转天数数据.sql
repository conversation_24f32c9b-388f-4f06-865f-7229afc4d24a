-- ETL:运营部周转天数统计数据->dwd_turnover_month
SELECT ny.update_month,
       ny.product_big_category,
       ny.product_big_category_name,
       ny.turnover,
       CASE
           WHEN COALESCE(ly.turnover, 0) = 0 THEN 0
           ELSE ROUND((ny.turnover - ly.turnover) / ly.turnover * 100, 2)
           END turnover_yoy,
       CASE
           WHEN COALESCE(ny2.turnover, 0) = 0 THEN 0
           ELSE ROUND((ny.turnover - ny2.turnover) / ny2.turnover * 100, 2)
           END turnover_mom
FROM (SELECT code                                       product_big_category,
             name                                       product_big_category_name,
             DATE_FORMAT(subquery.min_time, '%Y-%m') AS update_month,
             ROUND(AVG(time_difference), 1)          AS turnover
      FROM (SELECT dpbc.code,
                   dpbc.name,
                   DATEDIFF(ww.min_time, w.actual_start_time_operation) AS time_difference,
                   c.production_batch_number,
                   ww.min_time
            FROM dim_product_big_category dpbc
                     LEFT JOIN ods_pm_job_order c ON
                c.large_category = dpbc.code
                     JOIN ods_pm_job_order_management d ON
                c.production_batch_number = d.production_batch_number
                     JOIN ods_pm_job_detail i ON
                d.task_code = i.task_code
                     JOIN ods_pm_final_batch_job_audit w ON
                i.id = w.job_strip_number
                     JOIN
                 (SELECT pro_batch,
                         MIN(create_time) AS min_time
                  FROM ods_pm_finsih_project_stock
                  WHERE YEAR(create_time) = YEAR(CURDATE())
                  GROUP BY pro_batch) ww ON
                     c.production_batch_number = ww.pro_batch
            WHERE c.is_collage = 1
              AND c.flag_deleted = 0
              AND (i.before_process_code IS NULL OR i.before_process_code = '')
              AND i.special_type IS NULL
              AND YEAR(ww.min_time) = YEAR(CURDATE())
            UNION ALL
            SELECT dpbc.code,
                   dpbc.name,
                   DATEDIFF(ww.min_time, w.actual_start_time_operation) AS time_difference,
                   c.production_batch_number,
                   ww.min_time
            FROM dim_product_big_category dpbc
                     LEFT JOIN ods_pm_job_order c ON
                c.large_category = dpbc.code
                     LEFT JOIN ods_pm_job_order_middle f ON
                c.production_batch_number = f.production_batch_number
                     JOIN ods_pm_job_order_management d ON
                f.task_code = d.task_code
                     JOIN ods_pm_job_detail i ON
                d.task_code = i.task_code
                     JOIN ods_pm_final_batch_job_audit w ON
                i.id = w.job_strip_number
                     JOIN
                 (SELECT pro_batch,
                         min(create_time) AS min_time
                  FROM ods_pm_finsih_project_stock
                  WHERE YEAR(create_time) = YEAR(CURDATE())
                  GROUP BY pro_batch) ww ON
                     c.production_batch_number = ww.pro_batch
            WHERE c.is_collage = 0
              AND f.flag_deleted = 0
              AND c.flag_deleted = 0
              AND (i.before_process_code IS NULL OR i.before_process_code = '')
              AND i.special_type IS NULL
              AND YEAR(ww.min_time) = YEAR(CURDATE())) AS subquery
      GROUP BY product_big_category,
               product_big_category_name,
               update_month) ny
         LEFT JOIN (SELECT code                                       product_big_category,
                           name                                       product_big_category_name,
                           DATE_FORMAT(subquery.min_time, '%Y-%m') AS update_month,
                           ROUND(AVG(time_difference), 1)          AS turnover
                    FROM (SELECT dpbc.code,
                                 dpbc.name,
                                 DATEDIFF(ww.min_time, w.actual_start_time_operation) AS time_difference,
                                 c.production_batch_number,
                                 ww.min_time
                          FROM dim_product_big_category dpbc
                                   LEFT JOIN ods_pm_job_order c ON
                              c.large_category = dpbc.code
                                   JOIN ods_pm_job_order_management d ON
                              c.production_batch_number = d.production_batch_number
                                   JOIN ods_pm_job_detail i ON
                              d.task_code = i.task_code
                                   JOIN ods_pm_final_batch_job_audit w ON
                              i.id = w.job_strip_number
                                   JOIN
                               (SELECT pro_batch,
                                       MIN(create_time) AS min_time
                                FROM ods_pm_finsih_project_stock
                                WHERE YEAR(create_time) = YEAR(CURDATE() - INTERVAL 1 YEAR)
                                GROUP BY pro_batch) ww ON
                                   c.production_batch_number = ww.pro_batch
                          WHERE c.is_collage = 1
                            AND c.flag_deleted = 0
                            AND (i.before_process_code IS NULL OR i.before_process_code = '')
                            AND i.special_type IS NULL
                            AND YEAR(ww.min_time) = YEAR(CURDATE() - INTERVAL 1 YEAR)
                          UNION ALL
                          SELECT dpbc.code,
                                 dpbc.name,
                                 DATEDIFF(ww.min_time, w.actual_start_time_operation) AS time_difference,
                                 c.production_batch_number,
                                 ww.min_time
                          FROM dim_product_big_category dpbc
                                   LEFT JOIN ods_pm_job_order c ON
                              c.large_category = dpbc.code
                                   LEFT JOIN ods_pm_job_order_middle f ON
                              c.production_batch_number = f.production_batch_number
                                   JOIN ods_pm_job_order_management d ON
                              f.task_code = d.task_code
                                   JOIN ods_pm_job_detail i ON
                              d.task_code = i.task_code
                                   JOIN ods_pm_final_batch_job_audit w ON
                              i.id = w.job_strip_number
                                   JOIN
                               (SELECT pro_batch,
                                       min(create_time) AS min_time
                                FROM ods_pm_finsih_project_stock
                                WHERE YEAR(create_time) = YEAR(CURDATE() - INTERVAL 1 YEAR)
                                GROUP BY pro_batch) ww ON
                                   c.production_batch_number = ww.pro_batch
                          WHERE c.is_collage = 0
                            AND f.flag_deleted = 0
                            AND c.flag_deleted = 0
                            AND (i.before_process_code IS NULL OR i.before_process_code = '')
                            AND i.special_type IS NULL
                            AND YEAR(ww.min_time) = YEAR(CURDATE() - INTERVAL 1 YEAR)) AS subquery
                    GROUP BY product_big_category,
                             product_big_category_name,
                             update_month) ly ON ly.product_big_category = ny.product_big_category
    AND ly.update_month = ny.update_month
         LEFT JOIN (SELECT code                                       product_big_category,
                           name                                       product_big_category_name,
                           DATE_FORMAT(subquery.min_time, '%Y-%m') AS update_month,
                           ROUND(AVG(time_difference), 1)          AS turnover
                    FROM (SELECT dpbc.code,
                                 dpbc.name,
                                 DATEDIFF(ww.min_time, w.actual_start_time_operation) AS time_difference,
                                 c.production_batch_number,
                                 ww.min_time
                          FROM dim_product_big_category dpbc
                                   LEFT JOIN ods_pm_job_order c ON
                              c.large_category = dpbc.code
                                   JOIN ods_pm_job_order_management d ON
                              c.production_batch_number = d.production_batch_number
                                   JOIN ods_pm_job_detail i ON
                              d.task_code = i.task_code
                                   JOIN ods_pm_final_batch_job_audit w ON
                              i.id = w.job_strip_number
                                   JOIN
                               (SELECT pro_batch,
                                       MIN(create_time) AS min_time
                                FROM ods_pm_finsih_project_stock
                                WHERE YEAR(create_time) = YEAR(CURDATE())
                                GROUP BY pro_batch) ww ON
                                   c.production_batch_number = ww.pro_batch
                          WHERE c.is_collage = 1
                            AND c.flag_deleted = 0
                            AND (i.before_process_code IS NULL OR i.before_process_code = '')
                            AND i.special_type IS NULL
                            AND YEAR(ww.min_time) = YEAR(CURDATE())
                          UNION ALL
                          SELECT dpbc.code,
                                 dpbc.name,
                                 DATEDIFF(ww.min_time, w.actual_start_time_operation) AS time_difference,
                                 c.production_batch_number,
                                 ww.min_time
                          FROM dim_product_big_category dpbc
                                   LEFT JOIN ods_pm_job_order c ON
                              c.large_category = dpbc.code
                                   LEFT JOIN ods_pm_job_order_middle f ON
                              c.production_batch_number = f.production_batch_number
                                   JOIN ods_pm_job_order_management d ON
                              f.task_code = d.task_code
                                   JOIN ods_pm_job_detail i ON
                              d.task_code = i.task_code
                                   JOIN ods_pm_final_batch_job_audit w ON
                              i.id = w.job_strip_number
                                   JOIN
                               (SELECT pro_batch,
                                       min(create_time) AS min_time
                                FROM ods_pm_finsih_project_stock
                                WHERE YEAR(create_time) = YEAR(CURDATE())
                                GROUP BY pro_batch) ww ON
                                   c.production_batch_number = ww.pro_batch
                          WHERE c.is_collage = 0
                            AND f.flag_deleted = 0
                            AND c.flag_deleted = 0
                            AND (i.before_process_code IS NULL OR i.before_process_code = '')
                            AND i.special_type IS NULL
                            AND YEAR(ww.min_time) = YEAR(CURDATE())) AS subquery
                    GROUP BY product_big_category,
                             product_big_category_name,
                             update_month) ny2 ON ny2.product_big_category = ny.product_big_category
    AND ny2.update_month =
        DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(ny.update_month, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m')
;
select * from dwd_turnover_month dtmt;
