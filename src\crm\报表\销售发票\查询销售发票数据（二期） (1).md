## 全局公共参数
#### 全局Header参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 全局Query参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 全局Body参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 全局认证方式
```text
noauth
```
#### 全局预执行脚本
```javascript
暂无预执行脚本
```
#### 全局后执行脚本
```javascript
暂无后执行脚本
```
## /2024环球二期（测试环境）
```text
暂无描述
```
#### Header参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### Query参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### Body参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
## /2024环球二期（测试环境）/查询销售发票数据（二期）
```text
查询BIP客户
```
#### 接口状态
> 开发中

#### 接口URL
> http://************:5000/nccloud/api/so/saleinvoiceHQ/queryHQ

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
access_token | 95870bf371e773b7e532c7cc3a8b4792 | String | 是 | toke生成
ucg_flag | y | String | 是 | -
signature | db9f1d9f52d5912af0abfda515e122224f026037efc5c8ee50a8ed49796de6e2 | String | 是 | -
repeat_check | Y | String | 是 | -
client_id | jiekou | String | 是 | -
Content-Type | application/json;charset=utf-8 | String | 是 | -
#### 请求Body参数
```javascript
{
    "begintime": "2024-12-17 10:15:05", //发票日期（开始），必填YYYY-MM-DD HH:mm:ss
    "endtime": "2025-12-30 15:15:05" //发票日期（结束），必填YYYY-MM-DD HH:mm:ss
}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
begintime | 2024-09-01 15:15:05 | String | 是 | 发票日期（开始），必填YYYY-MM-DD HH:mm:ss
endtime | 2024-10-30 15:15:05 | String | 是 | 发票日期（结束），必填YYYY-MM-DD HH:mm:ss
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{
	"code": "200",
	"data": [
		{
			"norignetprice": 0.11152,
			"vdef20": null,
			"dsfbtzj": null,
			"code": "ZDHY0000852",
			"materialmnecode": "ZDYY000894",
			"csrcid": "1001A6100000003IK8NR",
			"csrcbid": "1001A6100000003IK8NS",
			"norigtaxmny": 126020,
			"ntax": 14497.88,
			"vsrcrowno": "10",
			"dbilldate": "2025-01-14 14:16:57",
			"user_code": "HQ03",
			"dsfbtzjorder_b": "23",
			"csaleinvoicebid": "1001A6100000003IKJL2",
			"dsfbtzj_b": "23",
			"csaleinvoiceid": "1001A6100000003IKJL0",
			"nnum": 1000000,
			"fstatusflag": 2,
			"version": 9,
			"ntaxmny": 126020,
			"ntaxrate": 13,
			"name": "复方皂矾丸小盒",
			"vsrccode": "1XC2501000003",
			"norigmny": 111522.12,
			"dsfbtzjorder": "229",
			"fopposeflag": "正常"
		}
	],
	"message": "查询成功"
}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
code | 200 | String | -
data | - | Array | -
data.norignetprice | 0.11152 | Number | 开票单价（不含税）
data.vdef20 | null | Null | 实际开具的电子发票编号
data.dsfbtzj | null | Null | CRM开票申请ID(销售发票表头主键)
data.code | ZDHY0000852 | String | 产品编码
data.materialmnecode | ZDYY000894 | String | 助记码
data.csrcid | 1001A6100000003IK8NR | String | BIP表头ID(来源单据主键)
data.csrcbid | 1001A6100000003IK8NS | String | BIP出库单行ID(来源单据表体主键)
data.norigtaxmny | 126020 | Integer | 价税总计
data.ntax | 14497.88 | Number | 税额
data.vsrcrowno | 10 | String | BIP出库单行号(来源单据表体行号)
data.dbilldate | 2025-01-14 14:16:57 | String | 开票日期
data.user_code | HQ03 | String | 制单人编码
data.dsfbtzjorder_b | 23 | String | CRM拆分订单行(销售订单表体主键)
data.csaleinvoicebid | 1001A6100000003IKJL2 | String | BIP销售发票子表主键（唯一标识）
data.dsfbtzj_b | 23 | String | CRM开票申请行ID(销售发票表体主键)
data.csaleinvoiceid | 1001A6100000003IKJL0 | String | BIP销售发票主表主键
data.nnum | 1000000 | Integer | 开票数量
data.fstatusflag | 2 | Integer | 发票状态1=自由;
2=审批通过;
3=冻结;
4=关闭;
7=审批中;
8=审批不通过;
5=失效;
data.version | 9 | Integer | 物料版本
data.ntaxmny | 126020 | Integer | 本币价税合计
data.ntaxrate | 13 | Integer | 税率
data.name | 复方皂矾丸小盒 | String | 产品名称
data.vsrccode | 1XC2501000003 | String | BIP出库单号(来源单据号)
data.norigmny | 111522.12 | Number | 开票金额（不含税）
data.dsfbtzjorder | 229 | String | CRM拆分订单编号(销售订单表头主键)
data.fopposeflag | 正常 | String | 红冲/开票标识
message | 查询成功 | String | -
#### 错误响应示例
```javascript
{
	"code": "500",
	"data": "",
	"message": "无数据"
}
```