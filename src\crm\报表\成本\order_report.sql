CREATE TABLE dws_budget_final_settlement_report (
    id INT AUTO_INCREMENT PRIMARY KEY,
    split_order_number VARCHAR(255) COMMENT '拆分订单号',
    quotation_number VARCHAR(255) COMMENT '报价单号',
    split_order_status VARCHAR(255) COMMENT '拆分订单状态',
    split_order_line_number VARCHAR(255) COMMENT '拆分订单行号',
    invoice_number VARCHAR(255) COMMENT '发票编号',
    is_ganged BOOLEAN COMMENT '是否拼版',
    customer_code VARCHAR(255) COMMENT '客户编码',
    customer_name VARCHAR(255) COMMENT '客户名称',
    product_name VARCHAR(255) COMMENT '产品名称',
    product_code VARCHAR(255) COMMENT '产品编号',
    mnemonic_code VARCHAR(255) COMMENT '助记码',
    product_version VARCHAR(255) COMMENT '产品版本',
    main_product VARCHAR(255) COMMENT '主产品',
    ganged_area varchar(200) COMMENT '拼板面积',
    quotation_factory VARCHAR(255) COMMENT '报价工厂',
    quantity INT COMMENT '数量',
    invoice_quantity INT COMMENT '开票数量',
    float_rate varchar(200) COMMENT '浮动率',
    sales_unit_price_excluding_tax_rmb varchar(200) COMMENT '不含税销售单价（RMB）',
    sales_amount_excluding_tax_rmb varchar(200) COMMENT '不含税销售金额（RMB）',
    invoice_unit_price varchar(200) COMMENT '开票单价',
    invoice_amount varchar(200) COMMENT '开票金额',
    budget_gross_profit varchar(200) COMMENT '预算毛利',
    budget_gross_profit_margin_percent varchar(200) COMMENT '预算毛利率%',
    total_cost varchar(200) COMMENT '总成本',
    raw_material_cost varchar(200) COMMENT '原辅料成本',
    water_electricity_labor varchar(200) COMMENT '水电人工',
    depreciation_fee varchar(200) COMMENT '折旧费',
    freight varchar(200) COMMENT '运费',
    standard_cost_price varchar(200) COMMENT '标准成本价',
    final_settlement_total_quantity INT COMMENT '决算总数量',
    final_settlement_total_cost varchar(200) COMMENT '决算总成本',
    final_settlement_raw_material_cost varchar(200) COMMENT '决算原辅料成本',
    final_settlement_water_electricity_labor varchar(200) COMMENT '决算水电人工',
    final_settlement_depreciation_fee varchar(200) COMMENT '决算折旧费',
    final_settlement_freight varchar(200) COMMENT '决算运费',
    final_settlement_gross_profit varchar(200) COMMENT '决算毛利',
    final_settlement_gross_profit_margin_percent varchar(200) COMMENT '决算毛利率%'
)comment '预算与决算报表';