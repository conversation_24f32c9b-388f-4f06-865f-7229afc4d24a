-- MySQL dump 10.13  Distrib 8.4.4, for Win64 (x86_64)
--
-- Host: ***********    Database: H3Ceas_mombusiness1732179827625
-- ------------------------------------------------------
-- Server version	8.0.35

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ageing_dcs`
--

DROP TABLE IF EXISTS `ageing_dcs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ageing_dcs` (
  `air_humidity_index` varchar(255) DEFAULT NULL COMMENT '陈化仓进风湿度指标',
  `air_temperature_index` varchar(255) DEFAULT NULL COMMENT '陈化仓进风温度指标(℃)',
  `out_sugar_temperature` varchar(255) DEFAULT NULL COMMENT '出陈化仓糖温(℃)',
  `wind_temperature` varchar(255) DEFAULT NULL COMMENT '出陈化仓风温(℃)',
  `top_pressure` varchar(255) DEFAULT NULL COMMENT '顶部压力(pa)',
  `sugar_temperature` varchar(255) DEFAULT NULL COMMENT '进糖温度',
  `indoor_humidity` varchar(255) DEFAULT NULL COMMENT '室内湿度',
  `indoor_temperature` varchar(255) DEFAULT NULL COMMENT '室内温度',
  `water_temperature` varchar(255) DEFAULT NULL COMMENT '冷却水温度(℃)',
  `export_pressure` varchar(255) DEFAULT NULL COMMENT '出口风压(Kpa)',
  `frequency_conver` varchar(255) DEFAULT NULL COMMENT '鼓风机变频',
  `air_pressure` varchar(255) DEFAULT NULL COMMENT '出风压力',
  `humidity` varchar(255) DEFAULT NULL COMMENT '湿度',
  `temperature` varchar(255) DEFAULT NULL COMMENT '温度',
  `code` varchar(255) DEFAULT NULL COMMENT '除湿机/鼓风机编号',
  `chenhua_warehouse` varchar(255) DEFAULT NULL COMMENT '陈化仓料位',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `type` varchar(255) DEFAULT NULL COMMENT '1:DCS1  2:DCS2',
  `frequency_conver2` varchar(10) DEFAULT NULL COMMENT '鼓风机变频2',
  `air_pressure2` varchar(10) DEFAULT NULL COMMENT '出风压力2',
  `humidity2` varchar(10) DEFAULT NULL COMMENT '湿度2',
  `temperature2` varchar(10) DEFAULT NULL COMMENT '温度2',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=72177 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='陈化糖斗_dcs';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `classification_filtering_configuration_table`
--

DROP TABLE IF EXISTS `classification_filtering_configuration_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `classification_filtering_configuration_table` (
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `replace_after_replacement` varchar(255) DEFAULT NULL COMMENT '更换后筛网',
  `number_after_replacement` varchar(255) DEFAULT NULL COMMENT '更换后编号',
  `replace_before_replacement` varchar(255) DEFAULT NULL COMMENT '更换前筛网',
  `number_before_replacement` varchar(255) DEFAULT NULL COMMENT '更换前编号',
  `replaced` varchar(255) DEFAULT NULL COMMENT '是否更换',
  `inspection_results` varchar(255) DEFAULT NULL COMMENT '检查结果',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `position` varchar(255) DEFAULT NULL COMMENT '部位',
  `project` varchar(255) DEFAULT NULL COMMENT '检查项目',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `check_type` varchar(255) DEFAULT NULL COMMENT '类型（1是没有更换输入框2 有更换输入框）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分类筛定期巡检配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `classification_filtering_details`
--

DROP TABLE IF EXISTS `classification_filtering_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `classification_filtering_details` (
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `replace_after_replacement` varchar(255) DEFAULT NULL COMMENT '更换后筛网',
  `number_after_replacement` varchar(255) DEFAULT NULL COMMENT '更换后编号',
  `replace_before_replacement` varchar(255) DEFAULT NULL COMMENT '更换前筛网',
  `number_before_replacement` varchar(255) DEFAULT NULL COMMENT '更换前编号',
  `replaced` varchar(255) DEFAULT NULL COMMENT '是否更换',
  `inspection_results` varchar(255) DEFAULT NULL COMMENT '检验结果',
  `check_type` varchar(255) DEFAULT NULL COMMENT '类型（1是没有更换输入框2 有更换输入框）',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `position` varchar(255) DEFAULT NULL COMMENT '部位',
  `project` varchar(255) DEFAULT NULL COMMENT '检查项目',
  `inspect_code` varchar(255) DEFAULT NULL COMMENT '巡检记录编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=211 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='分类筛定期巡检参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `complaint_record`
--

DROP TABLE IF EXISTS `complaint_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `complaint_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `complaint_code` varchar(20) DEFAULT NULL COMMENT '投诉编号',
  `complaint_type` varchar(20) DEFAULT NULL COMMENT '投诉类型',
  `result` varchar(20) DEFAULT NULL COMMENT '分析结果',
  `other_complaint_type` varchar(30) DEFAULT NULL COMMENT '其他投诉类型',
  `other_result` varchar(30) DEFAULT NULL COMMENT '其他分析结果',
  `defective_num` double(21,3) DEFAULT NULL COMMENT '不合格数量(吨)',
  `loss` double(21,3) DEFAULT NULL COMMENT '损失(元)',
  `status` int DEFAULT '0' COMMENT '状态',
  `isrectified` int DEFAULT '1' COMMENT '是否整改',
  `rectification_role` varchar(30) DEFAULT NULL COMMENT '整改负责人',
  `description` varchar(1000) DEFAULT NULL COMMENT '原因描述',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='投诉记录基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `corrective_measures`
--

DROP TABLE IF EXISTS `corrective_measures`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `corrective_measures` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `complaint_code` varchar(20) DEFAULT NULL COMMENT '投诉编号',
  `rectification_part` varchar(50) DEFAULT NULL COMMENT '整改部门',
  `rectification_role` varchar(20) DEFAULT NULL COMMENT '负责人',
  `completion_progress` varchar(20) DEFAULT NULL COMMENT '完成进度',
  `completion_date` datetime DEFAULT NULL COMMENT '完成时间时间',
  `rectification_measures` varchar(1000) DEFAULT NULL COMMENT '整改措施',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='整改措施基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dcs_gather_info`
--

DROP TABLE IF EXISTS `dcs_gather_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dcs_gather_info` (
  `infiltrate_quantity` varchar(255) DEFAULT NULL COMMENT '新建字段',
  `amount` varchar(255) DEFAULT NULL COMMENT '榨蔗量',
  `end_time` datetime DEFAULT NULL COMMENT '采集结束时间',
  `strat_time` datetime DEFAULT NULL COMMENT '采集开始时间',
  `code` varchar(255) DEFAULT NULL COMMENT 'DCS采集序号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `infiltrate_temperature` int DEFAULT NULL COMMENT '新建字段',
  `mix_water` varchar(255) DEFAULT NULL COMMENT '混合汁量',
  `ntemperatemperature` varchar(255) DEFAULT NULL COMMENT '渗透水温',
  `infiltration` varchar(255) DEFAULT NULL COMMENT '渗透水量',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `last_amount` varchar(255) DEFAULT NULL COMMENT '上一次榨蔗量',
  `last_mix_water` varchar(255) DEFAULT NULL COMMENT '上一次混合汁量',
  `last__ntemperatemperature` varchar(255) DEFAULT NULL COMMENT '上一次渗透水温',
  `last_infiltration` varchar(255) DEFAULT NULL COMMENT '上一次渗透水量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3421 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='DCS采集数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dcs_sugar_hopper`
--

DROP TABLE IF EXISTS `dcs_sugar_hopper`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dcs_sugar_hopper` (
  `type` varchar(255) DEFAULT NULL COMMENT '类型判断是s1还是s1',
  `time` varchar(255) DEFAULT NULL COMMENT '时间',
  `value` varchar(255) DEFAULT NULL COMMENT '值',
  `field` varchar(255) DEFAULT NULL COMMENT '（数采/检验）名，如Mpa,KPa等',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `code` varchar(255) DEFAULT NULL COMMENT '编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=236461 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='陈化糖斗DCS数采';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `desiccator_record`
--

DROP TABLE IF EXISTS `desiccator_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `desiccator_record` (
  `record_association_code` varchar(255) DEFAULT NULL COMMENT '记录关联编码',
  `desiccator_upper_layer_pressure_difference` varchar(255) DEFAULT NULL COMMENT '脱色罐上层压差',
  `desiccator_lower_layer_pressure` varchar(255) DEFAULT NULL COMMENT '脱色罐下层压差',
  `desiccator_top_pressure` varchar(255) DEFAULT NULL COMMENT '脱色罐顶部压力',
  `desiccator_middle_pressure` varchar(255) DEFAULT NULL COMMENT '脱色罐中部压力',
  `desiccator_bottom_pressure` varchar(255) DEFAULT NULL COMMENT '脱色罐底部压力',
  `material_flow_rate` varchar(255) DEFAULT NULL COMMENT '机料流量',
  `jar_number` varchar(255) DEFAULT NULL COMMENT '罐号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=108 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='脱色罐记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_accessory_usage`
--

DROP TABLE IF EXISTS `dev_accessory_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_accessory_usage` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(30) DEFAULT NULL COMMENT '检修任务编码，关联检修任务',
  `device_id` varchar(50) DEFAULT NULL COMMENT '设备关联编码，关联设备台账',
  `material_name` varchar(50) DEFAULT NULL COMMENT '物料名称',
  `material_code` varchar(50) DEFAULT NULL COMMENT '物料编码',
  `specification_model` varchar(50) DEFAULT NULL COMMENT '规格型号',
  `pick_no` varchar(50) DEFAULT NULL COMMENT '五金仓出库单号',
  `batch` varchar(50) DEFAULT NULL COMMENT '出库批次',
  `supplier_name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
  `commitment_item_name` varchar(100) DEFAULT NULL COMMENT '承诺项目名称',
  `commitment_item_code` varchar(50) DEFAULT NULL COMMENT '承诺项目编号',
  `out_quantity` varchar(10) DEFAULT NULL COMMENT '领用数量',
  `unit` varchar(10) DEFAULT NULL COMMENT '单位',
  `remark` varchar(100) DEFAULT NULL COMMENT '物料备注',
  `type` int DEFAULT NULL COMMENT '使用类型(1 检修使用；2 维修使用)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `unit_price` varchar(255) DEFAULT NULL COMMENT '单价/元',
  `status` int DEFAULT NULL COMMENT '申请状态(1 已发送出库申请)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='备品备件使用表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_accident_record`
--

DROP TABLE IF EXISTS `dev_accident_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_accident_record` (
  `accident_nature` varchar(50) DEFAULT NULL COMMENT '事故性质',
  `accident_level` varchar(50) DEFAULT NULL COMMENT '事故级别',
  `accident_name` varchar(30) DEFAULT NULL COMMENT '事故名称',
  `is_send` int DEFAULT NULL COMMENT '是否下发事故报告单(0 否；1 是)',
  `submit_time` varchar(30) DEFAULT NULL COMMENT '提交时间',
  `report_status` int DEFAULT NULL COMMENT '上报状态(1 待上报；2 已上报)',
  `submit_status` int DEFAULT NULL COMMENT '提交状态(1 待提交；2 已提交)',
  `measure` varchar(2000) DEFAULT NULL COMMENT '措施',
  `reason` varchar(2000) DEFAULT NULL COMMENT '原因',
  `loss_time` varchar(20) DEFAULT NULL COMMENT '损失时间',
  `end_time` varchar(30) DEFAULT NULL COMMENT '事故结束时间',
  `start_time` varchar(30) DEFAULT NULL COMMENT '事故开始时间',
  `occur_date` varchar(30) DEFAULT NULL COMMENT '事故发生日期',
  `work_shop` varchar(30) NOT NULL COMMENT '工段',
  `accident_code` varchar(30) NOT NULL COMMENT '事故编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=65 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='事故记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_attachment`
--

DROP TABLE IF EXISTS `dev_attachment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_attachment` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `image_description` varchar(255) DEFAULT NULL COMMENT '图片描述',
  `image_url` varchar(255) DEFAULT NULL COMMENT '图片路径',
  `file_url` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `image_name` varchar(255) DEFAULT NULL COMMENT '图片名称',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `type` int NOT NULL COMMENT '类型，1是图片，2是文件',
  `related_id` int NOT NULL COMMENT '关联备件ID',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备附件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_bom_parts`
--

DROP TABLE IF EXISTS `dev_bom_parts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_bom_parts` (
  `section` varchar(10) DEFAULT NULL COMMENT '工段',
  `parts_name` varchar(30) DEFAULT NULL COMMENT '部位名称',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `parent_id` int DEFAULT '0' COMMENT '父节点id',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='bom部位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_cane_obstruction`
--

DROP TABLE IF EXISTS `dev_cane_obstruction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_cane_obstruction` (
  `status` int DEFAULT NULL COMMENT '状态',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `end_time` varchar(255) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(255) DEFAULT NULL COMMENT '开始时间',
  `occur_date` varchar(255) DEFAULT NULL COMMENT '发生日期',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `loss_time` varchar(255) DEFAULT NULL COMMENT '损失时间',
  `record_code` varchar(255) DEFAULT NULL COMMENT '记录编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='蔗料阻碍记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_check_item`
--

DROP TABLE IF EXISTS `dev_check_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_check_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `item_code` varchar(30) DEFAULT NULL COMMENT '点检项编码',
  `item_name` varchar(30) DEFAULT NULL COMMENT '点检项目',
  `item_content` varchar(30) DEFAULT NULL COMMENT '点检内容',
  `item_method` varchar(2000) DEFAULT NULL COMMENT '点检方法',
  `item_type` int DEFAULT NULL COMMENT '点检值类型(1 单选；2 读数)',
  `value_msg` varchar(50) DEFAULT NULL COMMENT '勾选值',
  `reference_value` varchar(20) DEFAULT NULL COMMENT '参考值',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `max_value` varchar(20) DEFAULT NULL COMMENT '上限值',
  `min_value` varchar(20) DEFAULT NULL COMMENT '下限值',
  `status` int DEFAULT NULL COMMENT '状态(0 禁用；1 启用)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检项表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_check_plan`
--

DROP TABLE IF EXISTS `dev_check_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_check_plan` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plan_code` varchar(30) DEFAULT NULL COMMENT '点检计划编码',
  `plan_name` varchar(30) DEFAULT NULL COMMENT '点检计划名称',
  `work_shop` varchar(30) DEFAULT NULL COMMENT '工段',
  `description` varchar(2000) DEFAULT NULL COMMENT '计划描述',
  `status` int DEFAULT NULL COMMENT '状态(0 禁用；1 启用)',
  `frequency` int DEFAULT NULL COMMENT '频率(1 不重复；2 每班；3 每天；4 每周；5 每月)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检计划表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_check_plan_content`
--

DROP TABLE IF EXISTS `dev_check_plan_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_check_plan_content` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plan_code` varchar(30) DEFAULT NULL COMMENT '点检计划编码',
  `device_id` varchar(30) DEFAULT NULL COMMENT '设备关联编码',
  `item_code` varchar(30) DEFAULT NULL COMMENT '点检项编码',
  `part` varchar(50) DEFAULT NULL COMMENT '点检部位',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1111 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检计划内容表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_check_plan_device`
--

DROP TABLE IF EXISTS `dev_check_plan_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_check_plan_device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plan_code` varchar(30) DEFAULT NULL COMMENT '点检计划编码',
  `device_id` varchar(30) DEFAULT NULL COMMENT '设备关联编码',
  `device_name` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `part` varchar(50) DEFAULT NULL COMMENT '点检部位',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=144 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检计划与设备关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_check_plan_item`
--

DROP TABLE IF EXISTS `dev_check_plan_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_check_plan_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plan_code` varchar(30) DEFAULT NULL COMMENT '点检计划编码',
  `item_code` varchar(30) DEFAULT NULL COMMENT '点检项编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=570 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检计划与项目关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_check_task`
--

DROP TABLE IF EXISTS `dev_check_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_check_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(30) DEFAULT NULL COMMENT '点检任务编码',
  `start_time` varchar(30) DEFAULT NULL COMMENT '任务开始时间',
  `end_time` varchar(30) DEFAULT NULL COMMENT '任务结束时间',
  `plan_date` varchar(30) DEFAULT NULL COMMENT '任务计划日期',
  `status` int DEFAULT NULL COMMENT '任务状态(1 未到期；2 待执行；3 按期执行；4 逾期执行；5 逾期未执行)',
  `frequency` int DEFAULT NULL COMMENT '频率(1 不重复；2 每班；3 每天；4 每周；5 每月)',
  `execute_by` varchar(30) DEFAULT NULL COMMENT '实际执行人',
  `device_code` varchar(30) DEFAULT NULL COMMENT '设备编码',
  `device_name` varchar(30) DEFAULT NULL COMMENT '设备名称',
  `device_id` varchar(50) DEFAULT NULL COMMENT '设备id(设备关联编码)',
  `plan_code` varchar(30) DEFAULT NULL COMMENT '点检计划编码',
  `relation_code` varchar(30) DEFAULT NULL COMMENT '关联编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `execute_time` varchar(30) DEFAULT NULL COMMENT '实际执行时间',
  `plan_name` varchar(50) DEFAULT NULL COMMENT '计划名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1077 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_check_task_detail`
--

DROP TABLE IF EXISTS `dev_check_task_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_check_task_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `item_name` varchar(50) DEFAULT NULL COMMENT '点检项目',
  `item_content` varchar(50) DEFAULT NULL COMMENT '点检内容',
  `item_method` varchar(2000) DEFAULT NULL COMMENT '点检方法',
  `reference_value` varchar(50) DEFAULT NULL COMMENT '参考值',
  `result` varchar(50) DEFAULT NULL COMMENT '点检结果',
  `notes` varchar(2000) DEFAULT NULL COMMENT '备注',
  `value_msg` varchar(50) DEFAULT NULL COMMENT '枚举值',
  `item_type` int DEFAULT NULL COMMENT '点检值类型',
  `task_code` varchar(50) DEFAULT NULL COMMENT '点检任务编码',
  `device_id` varchar(50) DEFAULT NULL COMMENT '设备关联编码',
  `item_code` varchar(50) DEFAULT NULL COMMENT '点检项编码',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '计划编码',
  `relation_code` varchar(50) DEFAULT NULL COMMENT '关联编码',
  `is_normal` int DEFAULT NULL COMMENT '是否正常(1 正常；2 异常)',
  `part` varchar(50) DEFAULT NULL COMMENT '点检部位',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6660 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检任务明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_daily_repair`
--

DROP TABLE IF EXISTS `dev_daily_repair`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_daily_repair` (
  `operation_status` varchar(50) DEFAULT NULL COMMENT '运转情况(1 运转；2 观察)',
  `measures` varchar(2000) DEFAULT NULL COMMENT '再发生预防措施',
  `situation` varchar(1000) DEFAULT NULL COMMENT '维修情况',
  `check_process` varchar(1000) DEFAULT NULL COMMENT '排查过程',
  `end_time` varchar(50) DEFAULT NULL COMMENT '维修结束时间',
  `repair_type` varchar(50) DEFAULT NULL COMMENT '维修路线(1 自主维修；2 外协)',
  `estimate_hours` varchar(50) DEFAULT NULL COMMENT '预估维修工时',
  `start_time` varchar(50) DEFAULT NULL COMMENT '维修开始时间',
  `status` int DEFAULT NULL COMMENT '状态(1、待维修2、维修中3、验收中4、最终完成)',
  `current_by` varchar(50) DEFAULT NULL COMMENT '当前经办人',
  `report_time` varchar(255) DEFAULT NULL COMMENT '报修时间',
  `repair_by` varchar(50) DEFAULT NULL COMMENT '维修人',
  `is_caused` varchar(50) DEFAULT NULL COMMENT '是否引发事故(0 否；1 是)',
  `report_by` varchar(50) DEFAULT NULL COMMENT '报修人',
  `description` varchar(1000) DEFAULT NULL COMMENT '故障描述',
  `fault_type` varchar(50) DEFAULT NULL COMMENT '故障类型(1 操作故障；2 设备故障)',
  `parts` varchar(50) DEFAULT NULL COMMENT '故障部位',
  `device_id` varchar(50) DEFAULT NULL COMMENT '故障设备',
  `repair_code` varchar(50) DEFAULT NULL COMMENT '维修单编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `fault_name` varchar(50) DEFAULT NULL COMMENT '故障名称',
  `workshop_section` varchar(50) DEFAULT NULL COMMENT '所属工段',
  `fault_result` varchar(1000) DEFAULT NULL COMMENT '故障原因',
  `opinion` varchar(10) DEFAULT NULL COMMENT '验收意见',
  `remark` varchar(2000) DEFAULT NULL COMMENT '验收备注',
  `accept_time` varchar(50) DEFAULT NULL COMMENT '验收日期',
  `accept_by` varchar(50) DEFAULT NULL COMMENT '验收人',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日常维修单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_device`
--

DROP TABLE IF EXISTS `dev_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `dev_device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `dev_device_type_id` varchar(255) DEFAULT NULL COMMENT '设备ID',
  `section` varchar(255) DEFAULT NULL COMMENT '工段',
  `dev_device_status` int DEFAULT NULL COMMENT '状态',
  `dev_device_code` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `device_type_id` varchar(255) DEFAULT NULL COMMENT '设备类型',
  `quota_power` varchar(255) DEFAULT NULL COMMENT '额定功率',
  `humidity_accuracy` varchar(255) DEFAULT NULL COMMENT '温度测量精度',
  `humidity_range` varchar(255) DEFAULT NULL COMMENT '温度测量范围',
  `temperature_range` varchar(255) DEFAULT NULL COMMENT '温度测量范围',
  `is_deleted` int DEFAULT NULL COMMENT '删除标记',
  `Department` varchar(255) DEFAULT NULL COMMENT '部门id',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `postal_code` varchar(255) DEFAULT NULL COMMENT '邮政编码',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `Vendor_phone` varchar(255) DEFAULT NULL COMMENT '厂商电话',
  `Vendor_contact` varchar(255) DEFAULT NULL COMMENT '厂商联系人',
  `manufacture_name` varchar(255) DEFAULT NULL COMMENT '生产厂名',
  `country` varchar(255) DEFAULT NULL COMMENT '生产国',
  `Factory_number` varchar(255) DEFAULT NULL COMMENT '出厂编号',
  `manufacture_date` date DEFAULT NULL COMMENT '出厂日期',
  `vendor` varchar(255) DEFAULT NULL COMMENT '供应商',
  `Maker` varchar(255) DEFAULT NULL COMMENT '制作商',
  `Specifications` varchar(255) DEFAULT NULL COMMENT '规格型号',
  `original_asset` varchar(255) DEFAULT NULL COMMENT '资产原值',
  `Fixed_asset _code` varchar(255) DEFAULT NULL COMMENT '固定资产编码',
  `Asset_class` varchar(255) DEFAULT NULL COMMENT '资产类别',
  `start_time` date DEFAULT NULL COMMENT '启用日期',
  `dev_head` varchar(255) DEFAULT NULL COMMENT '设备负责人',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `temperature_accuracy` varchar(255) DEFAULT NULL COMMENT '温度测量精度',
  `temperature_accurac` varchar(255) DEFAULT NULL COMMENT '温度测量精度2',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备台账表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_device_efficiency`
--

DROP TABLE IF EXISTS `dev_device_efficiency`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_device_efficiency` (
  `actual_current` varchar(100) DEFAULT NULL COMMENT '实际电流',
  `quota_current` varchar(100) DEFAULT NULL COMMENT '额定电流',
  `device_code` varchar(100) DEFAULT NULL COMMENT '设备编码',
  `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `device_type` varchar(64) DEFAULT NULL COMMENT '设备类型',
  `section_name` varchar(64) DEFAULT NULL COMMENT '工段/特种设备里使用单位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备效率表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_device_item_plan_relation`
--

DROP TABLE IF EXISTS `dev_device_item_plan_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_device_item_plan_relation` (
  `device_uu_code` varchar(50) DEFAULT NULL COMMENT '设备uucode，关联设备台账表',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '巡检计划编码，关联巡检计划表',
  `item_code` varchar(50) DEFAULT NULL COMMENT '巡检项目编码，关联巡检项目表',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `data_source` int DEFAULT NULL COMMENT '数据来源(1 手动录入；2 DCS采集)',
  `iot_device` varchar(50) DEFAULT NULL COMMENT 'Iot设备',
  `attribute` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备属性',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1787 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检计划与设备和项目关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_device_ledger`
--

DROP TABLE IF EXISTS `dev_device_ledger`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_device_ledger` (
  `rated_power` varchar(255) DEFAULT NULL COMMENT '额定功率',
  `accuracy2` int DEFAULT NULL COMMENT '温度测量精度2',
  `accuracy1` int DEFAULT NULL COMMENT '温度测量精度1',
  `measurement_range_2` varchar(255) DEFAULT NULL COMMENT '温度测量范围2',
  `measurement_range_1` varchar(255) DEFAULT NULL COMMENT '温度测量范围1',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `postal_code` varchar(255) DEFAULT NULL COMMENT '邮政编码',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `vendor_phone` varchar(255) DEFAULT NULL COMMENT '厂商联系电话',
  `vendor_contact` varchar(255) DEFAULT NULL COMMENT '厂商联系人',
  `manufacture` varchar(255) DEFAULT NULL COMMENT '出产厂名',
  `country` varchar(255) DEFAULT NULL COMMENT '出产国',
  `factory_number` varchar(255) DEFAULT NULL COMMENT '出厂编号',
  `manufacture_date` date DEFAULT NULL COMMENT '出厂日期',
  `vendor` varchar(255) DEFAULT NULL COMMENT '供应商',
  `maker` varchar(255) DEFAULT NULL COMMENT '制造商',
  `specifications` varchar(255) DEFAULT NULL COMMENT '规格型号',
  `original_asset` varchar(255) DEFAULT NULL COMMENT '资产原值',
  `asset_class` varchar(255) DEFAULT NULL COMMENT '资产类别',
  `assetcode` varchar(255) DEFAULT NULL COMMENT '固定资产编码',
  `start_time` date DEFAULT NULL COMMENT '启用日期',
  `dev_head` varchar(255) DEFAULT NULL COMMENT '设备负责人',
  `section` varchar(255) DEFAULT NULL COMMENT '工段',
  `equipment_type` varchar(255) DEFAULT NULL COMMENT '设备类型',
  `state` varchar(255) DEFAULT NULL COMMENT '状态',
  `code` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `device_type_id` varchar(255) DEFAULT NULL COMMENT '设备id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `flag_deleted` int DEFAULT NULL COMMENT '是否删除',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备台账表ledger';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_device_subs`
--

DROP TABLE IF EXISTS `dev_device_subs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_device_subs` (
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备台账表id',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `money` varchar(255) DEFAULT NULL COMMENT '金额',
  `number` int DEFAULT NULL COMMENT '数量',
  `dev_device_subs_unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `dev_device_subs_spc` varchar(255) DEFAULT NULL COMMENT '附属设备规格',
  `dev_device_subs_name` varchar(255) DEFAULT NULL COMMENT '附属设备名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备台账附属设备表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_device_type`
--

DROP TABLE IF EXISTS `dev_device_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_device_type` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `name` varchar(50) DEFAULT NULL COMMENT '设备类型名称',
  `code` varchar(20) DEFAULT NULL COMMENT '设备类型ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=183 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备类型';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_device_type_expand`
--

DROP TABLE IF EXISTS `dev_device_type_expand`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_device_type_expand` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(20) DEFAULT NULL COMMENT '设备类型ID',
  `name` varchar(30) DEFAULT NULL COMMENT '名称',
  `enums` varchar(10) DEFAULT NULL COMMENT '枚举值',
  `type` varchar(10) DEFAULT NULL COMMENT '类型',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `flag_required` varchar(10) DEFAULT '0' COMMENT '是否必填',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1941 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备类型拓展表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_devicetask_status`
--

DROP TABLE IF EXISTS `dev_devicetask_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_devicetask_status` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `repair_code` varchar(30) DEFAULT NULL COMMENT '关联维修字段',
  `status` varchar(10) DEFAULT NULL COMMENT '状态',
  `record_by` varchar(30) DEFAULT NULL COMMENT '当前经办人',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='停机维修状态记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_index_analysis`
--

DROP TABLE IF EXISTS `dev_index_analysis`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_index_analysis` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `device_name` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `device_id` varchar(50) DEFAULT NULL COMMENT '设备关联编码(关联设备台账)',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `item_name` varchar(50) DEFAULT NULL COMMENT '项目名称',
  `value` varchar(50) DEFAULT NULL COMMENT '振动值',
  `check_time` varchar(30) DEFAULT NULL COMMENT '点检时间',
  `gen_curve` int DEFAULT NULL COMMENT '是否生成曲线(1 不生成；2 生成)',
  `rem_excep` int DEFAULT NULL COMMENT '是否标注异常(1 否；2 是)',
  `set_danger` int DEFAULT NULL COMMENT '设置危险值(1 不设置；2 设置)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备指标分析表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_inspect_item`
--

DROP TABLE IF EXISTS `dev_inspect_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_inspect_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspect_item` varchar(30) DEFAULT NULL COMMENT '巡检项目',
  `inspect_item_content` varchar(500) DEFAULT NULL COMMENT '巡检内容',
  `inspect_item_method` varchar(2000) DEFAULT NULL COMMENT '巡检方法',
  `inspect_item_type` int DEFAULT NULL COMMENT '巡检值类型(1 单选；2 读数)',
  `unit` varchar(10) DEFAULT NULL COMMENT '数值单位',
  `marked_exception` int DEFAULT NULL COMMENT '是否标注异常(0 否；1 是)',
  `labeled_curve` int DEFAULT NULL COMMENT '是否标注曲线(0 否；1 是)',
  `status` int DEFAULT NULL COMMENT '状态(0 禁用；1 启用)',
  `max_value` varchar(50) DEFAULT NULL COMMENT '读数型存上限值',
  `inspect_item_code` varchar(20) DEFAULT NULL COMMENT '巡检项编码',
  `min_value` varchar(50) DEFAULT NULL COMMENT '读数型存下限值',
  `value_msg` varchar(200) DEFAULT NULL COMMENT '枚举值，用“,”隔开',
  `reference_value` varchar(255) DEFAULT NULL COMMENT '参考值，展示时使用',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=495 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检项表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_inspect_order`
--

DROP TABLE IF EXISTS `dev_inspect_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_inspect_order` (
  `description` varchar(255) DEFAULT NULL COMMENT '计划描述',
  `frequency` int DEFAULT NULL COMMENT '巡检计划频率',
  `method` int DEFAULT NULL COMMENT '计划生成方式(1 自动生成；2 手动触发)',
  `status` int DEFAULT NULL COMMENT '状态(0 禁用；1 启用)',
  `inspect_order_name` varchar(30) DEFAULT NULL COMMENT '巡检计划名称',
  `inspect_order_code` varchar(20) DEFAULT NULL COMMENT '巡检计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspect_type` varchar(20) DEFAULT NULL COMMENT '巡检类型(dev 设备；pro 生产)',
  `belonging` varchar(50) DEFAULT NULL COMMENT '所属表单',
  `work_shop` varchar(50) DEFAULT NULL COMMENT '工段',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `flag_default` int DEFAULT NULL COMMENT '生产选中计划(0 未选中；1 已选中)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=193 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备巡检计划表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_inspect_order_item`
--

DROP TABLE IF EXISTS `dev_inspect_order_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_inspect_order_item` (
  `inspect_item_code` varchar(20) DEFAULT NULL COMMENT '巡检项编码，关联巡检项表',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '巡检计划编码，关联巡检计划表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=938 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检计划与项目关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_inspect_task`
--

DROP TABLE IF EXISTS `dev_inspect_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_inspect_task` (
  `inspect_plan_time` varchar(100) DEFAULT NULL COMMENT '巡检计划时间',
  `status` int DEFAULT NULL COMMENT '巡检状态(1 未到期；2 待执行；3 按期执行；4 逾期执行；5 逾期未执行)',
  `frequency` int DEFAULT NULL COMMENT '巡检频率',
  `execution_person` varchar(20) DEFAULT NULL COMMENT '执行人',
  `execution_time` datetime DEFAULT NULL COMMENT '执行时间',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(20) DEFAULT NULL COMMENT '巡检任务编码',
  `start_time` varchar(20) DEFAULT NULL COMMENT '开始时间',
  `end_time` varchar(20) DEFAULT NULL COMMENT '结束时间',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `device_name` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `device_uu_code` varchar(50) DEFAULT NULL COMMENT '设备uucode，关联设备表',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '巡检计划编码，关联巡检计划表',
  `relation_code` varchar(50) DEFAULT NULL COMMENT '巡检任务关联编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `plan_name` varchar(50) DEFAULT NULL COMMENT '巡检计划名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15445 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_inspect_task_record`
--

DROP TABLE IF EXISTS `dev_inspect_task_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_inspect_task_record` (
  `inspect_item` varchar(255) DEFAULT NULL COMMENT '巡检项目',
  `reference_value` varchar(50) DEFAULT NULL COMMENT '参考值',
  `result` varchar(100) DEFAULT NULL COMMENT '巡检结果',
  `notes` varchar(255) DEFAULT NULL COMMENT '备注',
  `reference_curve` int DEFAULT NULL COMMENT '参考曲线',
  `value_msg` varchar(255) DEFAULT NULL COMMENT '枚举值',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `item_type` int DEFAULT NULL COMMENT '巡检值类型(1 单选型；2 读数型)',
  `source` int DEFAULT NULL COMMENT '数据来源(1 手动录入；2 DCS采集)',
  `device_uu_code` varchar(50) DEFAULT NULL COMMENT '设备uucode，关联设备表',
  `task_code` varchar(50) DEFAULT NULL COMMENT '巡检任务编码',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '巡检计划编码，关联巡检计划表',
  `item_code` varchar(50) DEFAULT NULL COMMENT '巡检项目编码，关联巡检项目表',
  `relation_code` varchar(50) DEFAULT NULL COMMENT '巡检任务关联编码，关联巡检任务表',
  `marked_exception` int DEFAULT NULL COMMENT '是否标注异常',
  `attribute` varchar(50) DEFAULT NULL COMMENT '设备属性',
  `iot_device` varchar(50) DEFAULT NULL COMMENT 'Iot设备',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `excepted` int DEFAULT NULL COMMENT '是否异常(1 正常；2 异常)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73649 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检任务_巡检记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_inventory_summary`
--

DROP TABLE IF EXISTS `dev_inventory_summary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_inventory_summary` (
  `total_quantity` int DEFAULT NULL COMMENT '库存数量',
  `unit_price` double(21,3) DEFAULT NULL COMMENT '单价',
  `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `commitment_item_code` varchar(255) DEFAULT NULL COMMENT '承诺项目编号',
  `commitment_item_name` varchar(255) DEFAULT NULL COMMENT '承诺项目名称',
  `section_code` varchar(255) DEFAULT NULL COMMENT '工段号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库_库存统计总表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_lubrication_item`
--

DROP TABLE IF EXISTS `dev_lubrication_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_lubrication_item` (
  `quantity` varchar(255) DEFAULT NULL COMMENT '计划添加量',
  `specification_model` varchar(255) DEFAULT NULL COMMENT '规格型号',
  `item_method` varchar(2000) DEFAULT NULL COMMENT '润滑方法/基准',
  `parts` varchar(255) DEFAULT NULL COMMENT '润滑部位',
  `item_name` varchar(255) DEFAULT NULL COMMENT '润滑项目',
  `item_code` varchar(30) NOT NULL COMMENT '润滑项编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `section_code` varchar(255) DEFAULT NULL COMMENT '工段编号',
  `material_code` varchar(255) NOT NULL COMMENT '物料编码',
  `status` int NOT NULL COMMENT '状态(0 禁用；1 启用)',
  `manufacturer` varchar(255) DEFAULT NULL COMMENT '生产厂家',
  `supplier_name` varchar(255) DEFAULT NULL COMMENT '供应商',
  `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_润滑项表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_lubrication_plan`
--

DROP TABLE IF EXISTS `dev_lubrication_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_lubrication_plan` (
  `frequency` int DEFAULT NULL COMMENT '频率(1 不重复；2 每班；3 每天；4 每周；5 每月)',
  `status` int DEFAULT NULL COMMENT '状态(0 禁用；1 启用)',
  `description` varchar(800) DEFAULT NULL COMMENT '计划描述',
  `section_code` varchar(255) NOT NULL COMMENT '工段编码',
  `plan_name` varchar(255) DEFAULT NULL COMMENT '润滑计划名称',
  `plan_code` varchar(255) NOT NULL COMMENT '润滑计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_润滑计划表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_lubrication_plan_content`
--

DROP TABLE IF EXISTS `dev_lubrication_plan_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_lubrication_plan_content` (
  `item_code` varchar(255) NOT NULL COMMENT '润滑项编码',
  `device_id` varchar(255) NOT NULL COMMENT '设备id(设备关联编码)',
  `plan_code` varchar(255) NOT NULL COMMENT '润滑计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=447 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑计划内容表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_lubrication_plan_device`
--

DROP TABLE IF EXISTS `dev_lubrication_plan_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_lubrication_plan_device` (
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `device_id` varchar(255) NOT NULL COMMENT '设备id(设备关联编码)',
  `plan_code` varchar(255) NOT NULL COMMENT '润滑计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=146 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑计划与设备关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_lubrication_plan_item`
--

DROP TABLE IF EXISTS `dev_lubrication_plan_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_lubrication_plan_item` (
  `item_code` varchar(255) NOT NULL COMMENT '润滑项编码',
  `plan_code` varchar(255) NOT NULL COMMENT '润滑计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑计划与项目关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_lubrication_task`
--

DROP TABLE IF EXISTS `dev_lubrication_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_lubrication_task` (
  `relation_code` varchar(255) DEFAULT NULL COMMENT '关联编码',
  `plan_code` varchar(255) DEFAULT NULL COMMENT '润滑计划编码',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备id(设备关联编码 )',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `execute_time` varchar(255) DEFAULT NULL COMMENT '实际执行时间',
  `execute_by` varchar(255) DEFAULT NULL COMMENT '实际执行人',
  `frequency` int DEFAULT NULL COMMENT '频率(1 不重复；2 每天；3 每周；4 每月；5 每半年；6 每年)',
  `status` int DEFAULT NULL COMMENT '任务状态(1 未到期；2 待执行；3 按期执行；4 逾期执行；5 逾期未执行)',
  `plan_date` varchar(255) DEFAULT NULL COMMENT '任务计划日期',
  `end_time` varchar(255) DEFAULT NULL COMMENT '任务结束时间',
  `start_time` varchar(255) DEFAULT NULL COMMENT '任务开始时间',
  `task_code` varchar(255) DEFAULT NULL COMMENT '润滑任务编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `plan_name` varchar(255) DEFAULT NULL COMMENT '计划名称',
  `section_code` varchar(255) DEFAULT NULL COMMENT '工段编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4813 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_lubrication_task_detail`
--

DROP TABLE IF EXISTS `dev_lubrication_task_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_lubrication_task_detail` (
  `relation_code` varchar(255) DEFAULT NULL COMMENT '关联编码',
  `result` varchar(255) DEFAULT NULL COMMENT '润滑结果',
  `actual_quantity` varchar(255) DEFAULT NULL COMMENT '实际添加量',
  `quantity` varchar(255) DEFAULT NULL COMMENT '计划添加量',
  `specification_model` varchar(255) DEFAULT NULL COMMENT '规格型号',
  `supplier_name` varchar(255) DEFAULT NULL COMMENT '供应商',
  `manufacturer` varchar(255) DEFAULT NULL COMMENT '生产厂家',
  `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `item_method` varchar(255) DEFAULT NULL COMMENT '润滑方法/基准',
  `parts` varchar(255) DEFAULT NULL COMMENT '润滑部位',
  `item_name` varchar(255) DEFAULT NULL COMMENT '润滑项目',
  `item_code` varchar(255) DEFAULT NULL COMMENT '润滑项编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `notes` varchar(255) DEFAULT NULL COMMENT '备注',
  `task_code` varchar(255) DEFAULT NULL COMMENT '润滑任务编码',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备关联编码',
  `plan_code` varchar(255) DEFAULT NULL COMMENT '计划编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13956 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑任务明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_overhaul_item`
--

DROP TABLE IF EXISTS `dev_overhaul_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_overhaul_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `item_code` varchar(30) DEFAULT NULL COMMENT '检修项及检查项编码',
  `item_name` varchar(30) DEFAULT NULL COMMENT '检修项目',
  `item_content` varchar(500) DEFAULT NULL COMMENT '检修内容',
  `check_type` int DEFAULT NULL COMMENT '检修类型(1 常规；2 专项)',
  `item_method` varchar(500) DEFAULT NULL COMMENT '检修方法',
  `item_type` int DEFAULT NULL COMMENT '检修值类型(1 单选型；2 读数型)',
  `value_msg` varchar(50) DEFAULT NULL COMMENT '勾选值',
  `reference_value` varchar(30) DEFAULT NULL COMMENT '参考值',
  `unit` varchar(20) DEFAULT NULL COMMENT '数值单位',
  `max_value` varchar(20) DEFAULT NULL COMMENT '上限值',
  `min_value` varchar(20) DEFAULT NULL COMMENT '下限值',
  `status` int DEFAULT NULL COMMENT '状态(0 禁用；1 启用)',
  `type` int DEFAULT NULL COMMENT '检修项类型(1 检修项；2 单体试机检查项)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修项表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_overhaul_plan`
--

DROP TABLE IF EXISTS `dev_overhaul_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_overhaul_plan` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '检修计划编码',
  `plan_name` varchar(30) DEFAULT NULL COMMENT '检修计划名称',
  `overhaul_num` varchar(50) DEFAULT NULL COMMENT '已检修设备/设备总数',
  `status` int DEFAULT NULL COMMENT '计划状态(0 待下发；1 已下发)',
  `total` int DEFAULT NULL COMMENT '设备总数',
  `checked` int DEFAULT NULL COMMENT '已检修设备数',
  `work_shop` varchar(30) DEFAULT NULL COMMENT '所属工段名称',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `uu_code` varchar(50) DEFAULT NULL COMMENT '工段关联编码',
  `plan_date` varchar(20) DEFAULT NULL COMMENT '计划检修时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修计划表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_overhaul_plan_item`
--

DROP TABLE IF EXISTS `dev_overhaul_plan_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_overhaul_plan_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `project_code` varchar(30) DEFAULT NULL COMMENT '检修方案编码',
  `item_type` int DEFAULT NULL COMMENT '检修项类型(1 检修项；2 对中校验；3 单体调机检查)',
  `device_id` varchar(30) DEFAULT NULL COMMENT '设备关联编码',
  `item_code` varchar(30) DEFAULT NULL COMMENT '检修项目编码',
  `issue` varchar(1000) DEFAULT NULL COMMENT '榨季中存在的问题',
  `consumables` varchar(200) DEFAULT NULL COMMENT '预计耗材',
  `hours` varchar(20) DEFAULT NULL COMMENT '预计工时',
  `cost` varchar(30) DEFAULT NULL COMMENT '预计费用',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '检修计划编码，关联检修计划表',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修计划方案表与检修项关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_overhaul_plan_project`
--

DROP TABLE IF EXISTS `dev_overhaul_plan_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_overhaul_plan_project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `project_code` varchar(30) DEFAULT NULL COMMENT '检修方案编码',
  `flag_verify` int DEFAULT NULL COMMENT '是否需要对中校验(0 否；1 是)',
  `test_run` int DEFAULT NULL COMMENT '是否需要单体试机(0 否；1 是)',
  `key_device` int DEFAULT NULL COMMENT '是否关键设备(0 否；1 是)',
  `food_security_check` int DEFAULT NULL COMMENT '是否食品安全检查(0 否；1 是)',
  `device_id` varchar(30) DEFAULT NULL COMMENT '设备关联id',
  `record_by` varchar(50) DEFAULT NULL COMMENT '责任人',
  `budget` varchar(30) DEFAULT NULL COMMENT '预算费用',
  `total_hours` varchar(30) DEFAULT NULL COMMENT '预估工时-总计',
  `plan_code` varchar(30) DEFAULT NULL COMMENT '检修计划编码(关联检修计划表)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修计划方案表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_overhaul_project`
--

DROP TABLE IF EXISTS `dev_overhaul_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_overhaul_project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `project_code` varchar(30) DEFAULT NULL COMMENT '检修方案编码',
  `flag_verify` int DEFAULT NULL COMMENT '是否需要对中校验(0 否；1 是)',
  `test_run` int DEFAULT NULL COMMENT '是否需要单体试机(0 否；1 是)',
  `key_devic` int DEFAULT NULL COMMENT '是否关键设备(0 否；1 是)',
  `food_security_check` int DEFAULT NULL COMMENT '是否食品安全检查(0 否；1 是)',
  `device_id` varchar(30) DEFAULT NULL COMMENT '设备id(设备关联编码)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `work_shop` varchar(30) DEFAULT NULL COMMENT '工段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修方案表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_overhaul_status`
--

DROP TABLE IF EXISTS `dev_overhaul_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_overhaul_status` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(50) DEFAULT NULL COMMENT '检修任务编码，关联检修任务',
  `status` varchar(10) DEFAULT NULL COMMENT '状态',
  `record_by` varchar(50) DEFAULT NULL COMMENT '当前经办人',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修任务状态记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_overhaul_task`
--

DROP TABLE IF EXISTS `dev_overhaul_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_overhaul_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(30) DEFAULT NULL COMMENT '检修任务编码',
  `plan_date` varchar(30) DEFAULT NULL COMMENT '任务计划日期',
  `device_name` varchar(30) DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(30) DEFAULT NULL COMMENT '设备编码',
  `device_id` varchar(30) DEFAULT NULL COMMENT '设备关联编码，关联设备台账表',
  `plan_code` varchar(30) DEFAULT NULL COMMENT '检修计划编码，关联检修计划表',
  `record_by` varchar(50) DEFAULT NULL COMMENT '检修记录人',
  `execute_by` varchar(50) DEFAULT NULL COMMENT '实际执行人',
  `status` int DEFAULT NULL COMMENT '任务状态',
  `plan_name` varchar(30) DEFAULT NULL COMMENT '检修计划名称',
  `complete_progress` varchar(10) DEFAULT NULL COMMENT '完成进度百分比',
  `this_rate` varchar(50) DEFAULT NULL COMMENT '本周进度',
  `next_plan` varchar(50) DEFAULT NULL COMMENT '下周计划',
  `start_end_time` varchar(60) DEFAULT NULL COMMENT '检修起止时间',
  `duration` varchar(20) DEFAULT NULL COMMENT '检修时长',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `work_shop` varchar(30) DEFAULT NULL COMMENT '所属工段名称',
  `uu_code` varchar(50) DEFAULT NULL COMMENT '工段关联编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `flag_verify` int DEFAULT NULL COMMENT '是否需要对中校验(0 否；1 是)',
  `test_run` int DEFAULT NULL COMMENT '是否需要单体试机(0 否；1 是)',
  `key_device` int DEFAULT NULL COMMENT '是否关键设备(0 否；1 是)',
  `food_security_check` int DEFAULT NULL COMMENT '是否食品安全检查(0 否；1 是)',
  `budget` varchar(30) DEFAULT NULL COMMENT '预算费用',
  `total_hours` varchar(30) DEFAULT NULL COMMENT '预估工时-总计',
  `project_code` varchar(30) DEFAULT NULL COMMENT '检修方案编码',
  `respon_by` varchar(50) DEFAULT NULL COMMENT '负责人',
  `accessory_cost` varchar(20) DEFAULT NULL COMMENT '备品备件使用成本',
  `support_cost` varchar(20) DEFAULT NULL COMMENT '外协成本',
  `record_time` varchar(30) DEFAULT NULL COMMENT '检修记录时间',
  `reject_status` int DEFAULT NULL COMMENT '驳回前状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_overhaul_task_item`
--

DROP TABLE IF EXISTS `dev_overhaul_task_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_overhaul_task_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(30) DEFAULT NULL COMMENT '检修任务编码',
  `item_type` int DEFAULT NULL COMMENT '检修项类型(1 检修项；2 对中校验；3 单体调机检查)',
  `device_id` varchar(30) DEFAULT NULL COMMENT '设备关联编码',
  `item_code` varchar(30) DEFAULT NULL COMMENT '检修项目编码',
  `issue` varchar(1000) DEFAULT NULL COMMENT '榨季中存在的问题',
  `consumables` varchar(200) DEFAULT NULL COMMENT '预计耗材',
  `hours` varchar(20) DEFAULT NULL COMMENT '预计工时',
  `cost` varchar(30) DEFAULT NULL COMMENT '预计费用',
  `result` varchar(50) DEFAULT NULL COMMENT '结果',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `verify_result` varchar(10) DEFAULT NULL COMMENT '是否通过对中校验',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `type` int DEFAULT NULL COMMENT '检修值类型(1 单选型；2 读数型)',
  `reference_value` varchar(30) DEFAULT NULL COMMENT '参考值',
  `value_msg` varchar(50) DEFAULT NULL COMMENT '勾选值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=141 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修任务内容表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_plan_device_relation`
--

DROP TABLE IF EXISTS `dev_plan_device_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_plan_device_relation` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `device_name` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '巡检计划编码，关联巡检计划表',
  `device_uu_code` varchar(50) DEFAULT NULL COMMENT '设备uucode，关联设备表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=642 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检计划与设备关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_project_item`
--

DROP TABLE IF EXISTS `dev_project_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_project_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `project_code` varchar(30) DEFAULT NULL COMMENT '检修方案编码，关联检修方案表',
  `item_type` int DEFAULT NULL COMMENT '检修项类型(1 检修项；2 对中校验；3 单体调机检查)',
  `item_code` varchar(30) DEFAULT NULL COMMENT '项目编码，关联检修项表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=99 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修方案明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_regular_content`
--

DROP TABLE IF EXISTS `dev_regular_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_regular_content` (
  `device_type` varchar(255) DEFAULT NULL COMMENT '设备类型',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备关联编码',
  `check_method` varchar(255) DEFAULT NULL COMMENT '检验方式',
  `content` varchar(255) DEFAULT NULL COMMENT '定检内容',
  `location` varchar(255) DEFAULT NULL COMMENT '定检位置',
  `model` varchar(255) DEFAULT NULL COMMENT '规格型号',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `plan_code` varchar(255) DEFAULT NULL COMMENT '定检计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `type` varchar(255) DEFAULT NULL COMMENT '判断是设备台账还是特种设备',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='维护定检内容的基础信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_regular_plan`
--

DROP TABLE IF EXISTS `dev_regular_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_regular_plan` (
  `type` varchar(255) DEFAULT NULL COMMENT '定检类型(1 普通设备定检；2 特种设备定检)',
  `device_num` varchar(255) DEFAULT NULL COMMENT '定检设备数量',
  `status` varchar(255) DEFAULT NULL COMMENT '状态(0 停用；1 启用)',
  `plan_date` varchar(255) DEFAULT NULL COMMENT '计划执行日期',
  `frequency` varchar(255) DEFAULT NULL COMMENT '定检频率(1 年；2 月；3 周)',
  `cycle` varchar(255) DEFAULT NULL COMMENT '定检周期',
  `advance` varchar(255) DEFAULT NULL COMMENT '提前通知天数',
  `last_time` varchar(255) DEFAULT NULL COMMENT '上次检验日期',
  `description` varchar(2000) DEFAULT NULL COMMENT '计划描述',
  `plan_name` varchar(255) DEFAULT NULL COMMENT '定检计划名称',
  `plan_code` varchar(255) DEFAULT NULL COMMENT '定检计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定检计划的基础信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_regular_task_record`
--

DROP TABLE IF EXISTS `dev_regular_task_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_regular_task_record` (
  `plan_code` varchar(255) DEFAULT NULL COMMENT '计划编码',
  `current` varchar(255) DEFAULT NULL COMMENT '本次检验日期',
  `last_time` varchar(255) DEFAULT NULL COMMENT '上次检验日期',
  `status` varchar(255) DEFAULT NULL COMMENT '状态',
  `cycle` varchar(255) DEFAULT NULL COMMENT '定检周期',
  `advance` varchar(255) DEFAULT NULL COMMENT '提前通知天数',
  `plan_date` varchar(255) DEFAULT NULL COMMENT '计划检验日期',
  `task_code` varchar(255) DEFAULT NULL COMMENT '定检任务编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `cycleunit` varchar(255) DEFAULT NULL COMMENT '定检周期单位',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `inspector` varchar(255) DEFAULT NULL COMMENT '检验人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=567 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定检任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_regular_task_result`
--

DROP TABLE IF EXISTS `dev_regular_task_result`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_regular_task_result` (
  `result` varchar(255) DEFAULT NULL COMMENT '检验结果(1 合格；2 不合格)',
  `device_type` varchar(255) DEFAULT NULL COMMENT '设备类型',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备关联编码 关联设备台账表',
  `check_method` varchar(255) DEFAULT NULL COMMENT '检验方式',
  `content` varchar(255) DEFAULT NULL COMMENT '定检内容',
  `location` varchar(255) DEFAULT NULL COMMENT '定检位置',
  `model` varchar(255) DEFAULT NULL COMMENT '规格型号',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `task_code` varchar(255) DEFAULT NULL COMMENT '定检任务编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspector` varchar(255) DEFAULT NULL COMMENT '检验人',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定检结果表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_scheduled_task`
--

DROP TABLE IF EXISTS `dev_scheduled_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_scheduled_task` (
  `cycle` varchar(255) DEFAULT NULL COMMENT '周期',
  `cycle_unit` varchar(255) DEFAULT NULL COMMENT '周期单位',
  `plan_code` varchar(255) DEFAULT NULL COMMENT '计划编码，关联计划表',
  `plan_date` varchar(255) DEFAULT NULL COMMENT '计划日期',
  `publish_time` varchar(255) DEFAULT NULL COMMENT '任务发布时间',
  `next_publish_time` varchar(255) DEFAULT NULL COMMENT '下一次任务发布时间',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `last_time` varchar(255) DEFAULT NULL COMMENT '上次检验日期',
  `advance` varchar(255) DEFAULT NULL COMMENT '提前通知天数',
  `status` varchar(255) DEFAULT NULL COMMENT '启用或者禁用',
  `type` varchar(255) DEFAULT NULL COMMENT '判断是普通设备还是特种设备',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定检待执行任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_slowly_squeeze_information`
--

DROP TABLE IF EXISTS `dev_slowly_squeeze_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_slowly_squeeze_information` (
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `type` varchar(10) DEFAULT NULL COMMENT '类型(1 农务引起慢榨；2 生产引起慢榨)',
  `status` varchar(255) DEFAULT NULL COMMENT '状态(1待提交；2已提交农务慢榨开始时间；3已提交农务慢榨结束时间)',
  `loss_time` varchar(30) DEFAULT NULL COMMENT '损失时间',
  `day_quantity_plan` varchar(30) DEFAULT NULL COMMENT '日计划榨量',
  `loss_quantity` varchar(30) DEFAULT NULL COMMENT '损失榨量',
  `plan_quantity` varchar(30) DEFAULT NULL COMMENT '慢榨期间计划量',
  `quantity` varchar(30) DEFAULT NULL COMMENT '当下慢榨量',
  `end_time` varchar(30) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(30) DEFAULT NULL COMMENT '开始时间',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `record_code` varchar(40) DEFAULT NULL COMMENT '记录编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='慢榨记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_task_accept`
--

DROP TABLE IF EXISTS `dev_task_accept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_task_accept` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(30) DEFAULT NULL COMMENT '任务编码',
  `is_passed` varchar(20) DEFAULT NULL COMMENT '验收意见',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `type` int DEFAULT NULL COMMENT '验收类型(1 检修工段；2 检修车间；3 调机工段；4 调机车间；5 调机领导；6 调机质量)',
  `task_type` int DEFAULT NULL COMMENT '任务类型(1 检修；2 维修)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备任务验收表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_technical_renovation`
--

DROP TABLE IF EXISTS `dev_technical_renovation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_technical_renovation` (
  `status` varchar(255) DEFAULT NULL COMMENT '状态(1 待执行 2 执行中 3已完成)',
  `program_name` varchar(255) DEFAULT NULL COMMENT '承诺项目名称',
  `program_code` varchar(255) DEFAULT NULL COMMENT '承诺项目编号',
  `is_device_linked` varchar(10) DEFAULT NULL COMMENT '是否关联设备',
  `section_code` varchar(255) DEFAULT NULL COMMENT '工段号',
  `description` varchar(2000) DEFAULT NULL COMMENT '技改需求描述',
  `name` varchar(255) DEFAULT NULL COMMENT '技改需求名称',
  `renovation_code` varchar(255) DEFAULT NULL COMMENT '技改需求编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `file` varchar(255) DEFAULT NULL COMMENT '附件',
  `completion_time` varchar(255) DEFAULT NULL COMMENT '执行完成时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `device_id` varchar(255) DEFAULT NULL COMMENT '关联设备id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备技改表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_technical_renovation_execution`
--

DROP TABLE IF EXISTS `dev_technical_renovation_execution`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_technical_renovation_execution` (
  `record_time` varchar(255) DEFAULT NULL COMMENT '记录时间',
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人',
  `execute_by` varchar(255) DEFAULT NULL COMMENT '执行人',
  `issue` varchar(255) DEFAULT NULL COMMENT '存在的问题',
  `content` varchar(255) DEFAULT NULL COMMENT '工作内容',
  `date` varchar(255) DEFAULT NULL COMMENT '日期',
  `renovation_code` varchar(255) NOT NULL COMMENT '技改需求编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备技改执行表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_third_relation`
--

DROP TABLE IF EXISTS `dev_third_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_third_relation` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(30) DEFAULT NULL COMMENT '检修任务编码，关联检修任务',
  `srm_code` varchar(50) DEFAULT NULL COMMENT 'SRM申请编码',
  `type` int DEFAULT NULL COMMENT '使用类型(1 检修使用；2 维修使用)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='外协使用情况表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_upload_file`
--

DROP TABLE IF EXISTS `dev_upload_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_upload_file` (
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `file_type` int DEFAULT NULL COMMENT '文件类型: 1 图片，2文件',
  `busi_type` int DEFAULT NULL COMMENT '业务类型 1 巡检项；2 点检项（详细见字典）',
  `file_id` varchar(255) DEFAULT NULL COMMENT '文件id',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `source_code` varchar(50) DEFAULT NULL COMMENT '主信息编码，关联主表',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=159 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='上传附件设备模块';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_upload_file_periodical`
--

DROP TABLE IF EXISTS `dev_upload_file_periodical`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_upload_file_periodical` (
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `plan_code` varchar(255) DEFAULT NULL COMMENT '主信息编码，关联主表',
  `file_id` varchar(255) DEFAULT NULL COMMENT '文件id',
  `file_type` varchar(255) DEFAULT NULL COMMENT '文件类型：1图片，2文件',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='上传文件定检';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_details`
--

DROP TABLE IF EXISTS `dev_warehouse_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_details` (
  `uu_code` varchar(255) NOT NULL COMMENT '编码',
  `inventory_quantity` int NOT NULL COMMENT '领用数量',
  `enter_out_quantity` int NOT NULL COMMENT '入库数量/出库数量',
  `material_code` varchar(255) NOT NULL COMMENT '物料编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库出入库明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_in`
--

DROP TABLE IF EXISTS `dev_warehouse_in`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_in` (
  `name` varchar(255) DEFAULT NULL COMMENT '公司名称',
  `operator` varchar(255) DEFAULT NULL COMMENT '入库人',
  `note` varchar(600) DEFAULT NULL COMMENT '备注',
  `section_code` varchar(255) DEFAULT NULL COMMENT '工段号',
  `in_type` varchar(20) DEFAULT NULL COMMENT '入库类型(1-工段内不归还 2-工厂外部归还)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `in_time` varchar(255) DEFAULT NULL COMMENT '入库时间',
  `in_code` varchar(255) DEFAULT NULL COMMENT '入库单号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库_入库记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_in_detail`
--

DROP TABLE IF EXISTS `dev_warehouse_in_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_in_detail` (
  `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `quantity` int DEFAULT NULL COMMENT '入库数量',
  `inventory_id` int DEFAULT NULL COMMENT '库存表id',
  `in_code` varchar(255) DEFAULT NULL COMMENT '入库单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库_入库明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_inventory`
--

DROP TABLE IF EXISTS `dev_warehouse_inventory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_inventory` (
  `section_code` varchar(255) NOT NULL COMMENT '工段号',
  `remarks` varchar(255) DEFAULT NULL COMMENT '物料备注',
  `unit` varchar(50) NOT NULL COMMENT '单位',
  `specification_model` varchar(255) NOT NULL COMMENT ' 规格型号',
  `promise_project_code` varchar(255) NOT NULL COMMENT '承诺项目编号',
  `promise_project_name` varchar(200) NOT NULL COMMENT '承诺项目名称',
  `material_name` varchar(30) NOT NULL COMMENT '物料名称',
  `inventory_quantity` int NOT NULL COMMENT '库存数量',
  `unit_price` varchar(50) NOT NULL COMMENT '新建字段',
  `odd_numbers` varchar(100) NOT NULL COMMENT '申领单号',
  `material_code` varchar(100) NOT NULL COMMENT '物料编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库库存表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_out`
--

DROP TABLE IF EXISTS `dev_warehouse_out`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_out` (
  `out_time` varchar(255) DEFAULT NULL COMMENT '出库时间',
  `out_code` varchar(255) DEFAULT NULL COMMENT '出库单号/申请单号',
  `upload_pictures` varchar(255) DEFAULT NULL COMMENT '上传图片',
  `operator` varchar(255) DEFAULT NULL COMMENT '借用人/操作人',
  `name` varchar(255) DEFAULT NULL COMMENT '工段号/工厂名称',
  `section_code` varchar(255) DEFAULT NULL COMMENT '工段号',
  `out_type` varchar(30) DEFAULT NULL COMMENT '出库去向(1-工段内部2-工段调拨3-工厂借用4-设备维修5-退料）',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `note` varchar(600) DEFAULT NULL COMMENT '备注',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `repair_code` varchar(255) DEFAULT NULL COMMENT '维修单号',
  `audit_by` varchar(255) DEFAULT NULL COMMENT '审核人员',
  `apply_time` varchar(255) DEFAULT NULL COMMENT '退料申请时间',
  `status` varchar(30) DEFAULT NULL COMMENT '状态(1 待提交；2 待审核；3 待出库；4 已出库；5 已驳回；5 已出库)',
  `applicant` varchar(255) DEFAULT NULL COMMENT '申请人员',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库_出库记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_out_detail`
--

DROP TABLE IF EXISTS `dev_warehouse_out_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_out_detail` (
  `quantity` int DEFAULT NULL COMMENT '出库数量',
  `inventory_id` int DEFAULT NULL COMMENT '库存表id',
  `out_code` varchar(255) NOT NULL COMMENT '出库单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库出库明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_out_enter_bound`
--

DROP TABLE IF EXISTS `dev_warehouse_out_enter_bound`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_out_enter_bound` (
  `out_enter_destination` varchar(255) DEFAULT NULL COMMENT '出库去向',
  `type` int NOT NULL COMMENT '出入库类型',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `uu_code` varchar(255) DEFAULT NULL COMMENT '编码',
  `section_code` varchar(255) NOT NULL COMMENT '工段号',
  `remarks` varchar(1000) DEFAULT NULL COMMENT '备注',
  `upload_pictures` varchar(255) DEFAULT NULL COMMENT '上传图片',
  `operator` varchar(255) DEFAULT NULL COMMENT '操作人',
  `task_name` varchar(255) DEFAULT NULL COMMENT '入库工段名称',
  `task_factory_name` varchar(255) DEFAULT NULL COMMENT '工段名称/工厂名称/公司名称',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库出入库记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_out_file`
--

DROP TABLE IF EXISTS `dev_warehouse_out_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_out_file` (
  `out_code` varchar(255) DEFAULT NULL COMMENT '出库单号',
  `file_url` varchar(255) DEFAULT NULL COMMENT '文件url',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_id` varchar(255) DEFAULT NULL COMMENT '文件id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库出库文件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `dev_warehouse_whitelist`
--

DROP TABLE IF EXISTS `dev_warehouse_whitelist`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `dev_warehouse_whitelist` (
  `section_code` varchar(50) NOT NULL COMMENT '工段号',
  `remarks` varchar(50) DEFAULT NULL COMMENT '物料备注',
  `unit` varchar(50) DEFAULT NULL COMMENT '单位',
  `unit_price` varchar(50) DEFAULT NULL COMMENT '单价/元',
  `specification_model` varchar(200) DEFAULT NULL COMMENT ' 规格型号',
  `material_code` varchar(100) DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(30) DEFAULT NULL COMMENT '物料名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备管理_二级库白名单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_affiliated`
--

DROP TABLE IF EXISTS `device_affiliated`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_affiliated` (
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备id',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `price` varchar(100) DEFAULT NULL COMMENT '金额/元',
  `quantity` int DEFAULT NULL COMMENT '数量',
  `units` varchar(100) DEFAULT NULL COMMENT '单位',
  `affiliated_model` varchar(100) DEFAULT NULL COMMENT '附属设备规格',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `affiliated_name` varchar(100) DEFAULT NULL COMMENT '附属设备名称',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=564 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='附属设备';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_bom`
--

DROP TABLE IF EXISTS `device_bom`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_bom` (
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `spare_part_id` varchar(64) DEFAULT NULL COMMENT '备件ID',
  `attachment` varchar(1024) DEFAULT NULL COMMENT '附件',
  `technology` varchar(1024) DEFAULT NULL COMMENT '技术要求',
  `specification` varchar(64) DEFAULT NULL COMMENT '规格型号',
  `spare_part_name` varchar(64) DEFAULT NULL COMMENT '备件名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `component_id` int DEFAULT NULL COMMENT '部位id',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备BOM_备件详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_component`
--

DROP TABLE IF EXISTS `device_component`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_component` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `p_id` int NOT NULL DEFAULT '0' COMMENT '上级id',
  `device_code` varchar(100) NOT NULL COMMENT '设备编码',
  `name` varchar(255) NOT NULL COMMENT '部件名称',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备部件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_lubrication_plan`
--

DROP TABLE IF EXISTS `device_lubrication_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_lubrication_plan` (
  `section` varchar(64) NOT NULL COMMENT '工段',
  `image_id` varchar(255) DEFAULT NULL COMMENT '示意图',
  `status` varchar(10) NOT NULL COMMENT '状态',
  `mark` varchar(255) DEFAULT NULL COMMENT '描述',
  `plan_name` varchar(64) NOT NULL COMMENT '润滑计划名称',
  `plan_id` varchar(64) NOT NULL COMMENT '润滑计划ID',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑计划';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_lubrication_task`
--

DROP TABLE IF EXISTS `device_lubrication_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_lubrication_task` (
  `operator` varchar(64) DEFAULT NULL COMMENT '添加人',
  `actual_amount` varchar(64) DEFAULT NULL COMMENT '实际添加量',
  `actual_date` datetime DEFAULT NULL COMMENT '实际添加日期',
  `status` varchar(10) NOT NULL COMMENT '状态',
  `latest_date` datetime NOT NULL COMMENT '最新润滑日期',
  `second` varchar(64) NOT NULL COMMENT '次数',
  `frequency` varchar(64) NOT NULL COMMENT '频率',
  `plan_amount` varchar(64) NOT NULL COMMENT '计划添加量',
  `manufacturer` varchar(64) NOT NULL COMMENT '生产厂家',
  `specifications` varchar(64) NOT NULL COMMENT '规格型号',
  `task_name` varchar(64) NOT NULL COMMENT '润滑名称',
  `position` varchar(64) NOT NULL COMMENT '润滑位置',
  `device_code` varchar(64) NOT NULL COMMENT '设备编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑任务';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_reform`
--

DROP TABLE IF EXISTS `device_reform`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_reform` (
  `project_name` varchar(64) DEFAULT NULL COMMENT '承诺项目名称',
  `project_code` varchar(64) DEFAULT NULL COMMENT '承诺项目编号',
  `requirement_name` varchar(64) NOT NULL COMMENT '需求名称',
  `section` varchar(64) NOT NULL COMMENT '工段',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `approval_status` varchar(20) DEFAULT NULL COMMENT '审批状态',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `reform_order_number` varchar(50) NOT NULL COMMENT '技改工单',
  `device_code` varchar(50) NOT NULL COMMENT '设备编号',
  `status` varchar(20) NOT NULL COMMENT '状态',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `requirement` varchar(1024) DEFAULT NULL COMMENT '需求描述',
  `requirement_file` varchar(1024) DEFAULT NULL COMMENT '需求描述附件',
  `excute_time` datetime DEFAULT NULL COMMENT '技改执行时间',
  `excute_by` varchar(50) DEFAULT NULL COMMENT '技改执行人',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备技改';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_repair`
--

DROP TABLE IF EXISTS `device_repair`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_repair` (
  `fault_type` varchar(100) DEFAULT NULL COMMENT '故障类型',
  `preventive_measure` varchar(255) DEFAULT NULL COMMENT '预防措施',
  `repair_situation` varchar(255) DEFAULT NULL COMMENT '维修情况',
  `fault_result` varchar(255) DEFAULT NULL COMMENT '故障原因',
  `troubleshooting_process` varchar(255) DEFAULT NULL COMMENT '排查过程',
  `work_hour` varchar(50) DEFAULT NULL COMMENT '预估维修工时',
  `end_time` datetime DEFAULT NULL COMMENT '维修结束时间',
  `start_time` datetime DEFAULT NULL COMMENT '维修开始时间',
  `status` varchar(50) DEFAULT NULL COMMENT '状态',
  `operator_id` varchar(100) DEFAULT NULL COMMENT '当前经办人',
  `repair_time` datetime DEFAULT NULL COMMENT '报修时间',
  `repair_applicant` varchar(100) DEFAULT NULL COMMENT '报修人',
  `repair_person` varchar(100) DEFAULT NULL COMMENT '维修人',
  `accidents` varchar(50) DEFAULT NULL COMMENT '是否引发事故  0否 1是',
  `fault_description` varchar(255) DEFAULT NULL COMMENT '故障描述',
  `fault_location` varchar(100) DEFAULT NULL COMMENT '故障部位',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备id',
  `repair_code` varchar(100) DEFAULT NULL COMMENT '维修单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `time_type` varchar(255) DEFAULT NULL COMMENT '工时类型',
  `run_situation` varchar(50) DEFAULT NULL COMMENT '运转情况0：正常1：观察',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `check_person` varchar(100) DEFAULT NULL COMMENT '验收人',
  `repair_load` varchar(100) DEFAULT NULL COMMENT '维修路线 1自主维修2外协',
  `completed_by` varchar(255) DEFAULT NULL COMMENT '完成维修人',
  `check_time` datetime DEFAULT NULL COMMENT '验收时间',
  `check_opinion` varchar(255) DEFAULT NULL COMMENT '验收意见1通过2驳回',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备维修';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_repair_task`
--

DROP TABLE IF EXISTS `device_repair_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_repair_task` (
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `repair_code` varchar(255) DEFAULT NULL COMMENT '停机维修单号',
  `task_name` varchar(60) DEFAULT NULL COMMENT '任务名称',
  `task_id` varchar(60) DEFAULT NULL COMMENT '任务id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `status` varchar(20) DEFAULT NULL COMMENT '状态1、待执行2、待验收3、已验收',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `repair_by` varchar(30) DEFAULT NULL COMMENT '任务维修人',
  `is_plan` varchar(10) DEFAULT NULL COMMENT '是否计划1、是2、否',
  `dept_name` varchar(30) DEFAULT NULL COMMENT '部门名称',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `is_accepted` varchar(255) DEFAULT NULL COMMENT '验收意见',
  `out_code` varchar(40) DEFAULT NULL COMMENT '关联出库单号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='停机维修任务';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_shutdown_repair`
--

DROP TABLE IF EXISTS `device_shutdown_repair`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_shutdown_repair` (
  `approval_person` varchar(255) DEFAULT NULL COMMENT '审批人',
  `confirmed_time` datetime DEFAULT NULL COMMENT '确认时间',
  `confirmed_by` varchar(255) DEFAULT NULL COMMENT '确认人',
  `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
  `check_opinion` varchar(255) DEFAULT NULL COMMENT '验收意见',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `end_time` datetime DEFAULT NULL COMMENT '实际停机结束时间',
  `start_time` datetime DEFAULT NULL COMMENT '实际停机开始时间',
  `repair_content` varchar(500) DEFAULT NULL COMMENT '计划维修内容',
  `plan_hour` varchar(255) DEFAULT NULL COMMENT '计划停机时长',
  `plan_end_time` datetime DEFAULT NULL COMMENT '计划停机结束时间',
  `plan_start_time` datetime DEFAULT NULL COMMENT '计划停机开始时间',
  `dept_id` varchar(255) DEFAULT NULL COMMENT '部门',
  `device_id` varchar(100) DEFAULT NULL COMMENT '维修设备id',
  `repair_code` varchar(100) DEFAULT NULL COMMENT '维修单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `status` varchar(20) DEFAULT '0' COMMENT '状态状态(1 待提交；2 待审批；3 待确认；4 已确认)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `quality_need` varchar(10) DEFAULT NULL COMMENT '是否需要质量部门验收(1是；2 否)',
  `repair_type` varchar(10) DEFAULT NULL COMMENT '类型(1 小期维修；2 蔗料阻碍；3 其他)',
  `repair_name` varchar(100) DEFAULT NULL COMMENT '维修单名称',
  `actual_time` varchar(255) DEFAULT NULL COMMENT '实际停机时间',
  `is_plan` varchar(10) DEFAULT NULL COMMENT '(1、计划 2、非计划)',
  `is_create` varchar(10) DEFAULT NULL COMMENT '是否创建任务',
  `workshop_section` varchar(100) DEFAULT NULL COMMENT '工段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='停机维修';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `device_task_project`
--

DROP TABLE IF EXISTS `device_task_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_task_project` (
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `is_complete` varchar(255) DEFAULT NULL COMMENT '完成情况',
  `team_id` varchar(255) DEFAULT NULL COMMENT '班组id',
  `task_date` datetime DEFAULT NULL COMMENT '日期',
  `person` varchar(255) DEFAULT NULL COMMENT '负责人',
  `work_project` varchar(255) DEFAULT NULL COMMENT '停机维修任务工作项',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备id',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `repair_code` varchar(255) DEFAULT NULL COMMENT '维修编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='停机维修设备工作项';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `disinfection_record`
--

DROP TABLE IF EXISTS `disinfection_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `disinfection_record` (
  `hand_time` varchar(255) DEFAULT NULL COMMENT '记录日期',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `hand_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `disinfection_code` varchar(255) DEFAULT NULL COMMENT '消毒记录编号',
  `task_code` varchar(255) DEFAULT NULL COMMENT '关联编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='消毒记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `disinfection_record_detail`
--

DROP TABLE IF EXISTS `disinfection_record_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `disinfection_record_detail` (
  `disinfection_code` varchar(50) DEFAULT NULL COMMENT '消毒编号',
  `stop_time` varchar(50) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(50) DEFAULT NULL COMMENT '开始时间',
  `disinfection_location` varchar(50) DEFAULT NULL COMMENT '消毒位置',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `code` int DEFAULT NULL COMMENT '序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='消毒内容关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `duty_schedule_record`
--

DROP TABLE IF EXISTS `duty_schedule_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `duty_schedule_record` (
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `material_jam_loss_time` varchar(255) DEFAULT NULL COMMENT '原料阻碍损失时间(h)',
  `minor_repair_loss_time` varchar(255) DEFAULT NULL COMMENT '小期检修损失时间(h)',
  `other_loss_time` varchar(255) DEFAULT NULL COMMENT '其它损失损失时间(h)',
  `sugarcane_jam_loss_time` varchar(255) DEFAULT NULL COMMENT '蔗料阻碍损失时间(h)',
  `equip_fault_loss_time` varchar(255) DEFAULT NULL COMMENT '设备故障损失时间(h)',
  `pro_slow_press_equiv_loss_time` varchar(255) DEFAULT NULL COMMENT '生产引起慢榨等折损失时间(h)',
  `pro_slow_press_loss_time` varchar(255) DEFAULT NULL COMMENT '生产引起慢榨损失时间(h)',
  `farm_slow_press_equiv_loss_time` varchar(255) DEFAULT NULL COMMENT '农务引起慢榨等折损失时间(h)',
  `farm_slow_press_loss_time` varchar(255) DEFAULT NULL COMMENT '农务引起慢榨损失时间(h)',
  `end_time` varchar(255) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(255) DEFAULT NULL COMMENT '开始时间',
  `classes` varchar(255) DEFAULT NULL COMMENT '班次',
  `record_date` varchar(255) DEFAULT NULL COMMENT '记录日期',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `schedule_record_code` varchar(255) DEFAULT NULL COMMENT '调度记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `status` varchar(255) DEFAULT NULL COMMENT '是否为草稿：1.是；2.否',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='值班调度记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `duty_schedule_record_detail`
--

DROP TABLE IF EXISTS `duty_schedule_record_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `duty_schedule_record_detail` (
  `schedule_record_code` varchar(255) DEFAULT NULL COMMENT '调度记录编号',
  `fault_description` varchar(255) DEFAULT NULL COMMENT '故障描述',
  `loss_time` double DEFAULT NULL,
  `end_time` varchar(255) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(255) DEFAULT NULL COMMENT '开始时间',
  `fault_cause` varchar(255) DEFAULT NULL COMMENT '故障原因',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='值班调度记录明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_accident_records`
--

DROP TABLE IF EXISTS `em_accident_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_accident_records` (
  `reporting_status` varchar(10) NOT NULL COMMENT '上报状态',
  `submission_status` varchar(10) NOT NULL COMMENT '提交状态',
  `measure` varchar(255) DEFAULT NULL COMMENT '措施',
  `reason` varchar(255) DEFAULT NULL COMMENT '原因',
  `lost_time` varchar(64) NOT NULL COMMENT '损失时间',
  `starting_and_ending_time` varchar(255) NOT NULL COMMENT '事故起止时间',
  `date_of_occurrence` datetime NOT NULL COMMENT '事故发生日期',
  `section` varchar(64) NOT NULL COMMENT '工段',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='事故记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_association_between`
--

DROP TABLE IF EXISTS `em_association_between`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_association_between` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `knowledge_code` varchar(50) DEFAULT NULL COMMENT '维修知识库编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `repair_code` varchar(50) DEFAULT NULL COMMENT '维修单号',
  `report_time` varchar(255) DEFAULT NULL COMMENT '报修时间',
  `report_by` varchar(255) DEFAULT NULL COMMENT '报修人',
  `repair_by` varchar(255) DEFAULT NULL COMMENT '维修人',
  `description` varchar(255) DEFAULT NULL COMMENT '故障描述',
  `fault_type` varchar(255) DEFAULT NULL COMMENT '故障类型',
  `parts` varchar(255) DEFAULT NULL COMMENT '故障部位',
  `device_id` varchar(255) DEFAULT NULL COMMENT '故障设备',
  `fault_name` varchar(255) DEFAULT NULL COMMENT '故障名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='维修知识库关联案例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_device_coordination`
--

DROP TABLE IF EXISTS `em_device_coordination`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_device_coordination` (
  `maintenance_content` longtext COMMENT '外协维修内容',
  `maintenance_reason` longtext COMMENT '外协维修原因',
  `reality_cost` decimal(12,5) DEFAULT NULL COMMENT '实际费用',
  `status` varchar(10) DEFAULT NULL COMMENT '状态1.未提交2.审批中3.审批通过4.审批不通过5.验收完成',
  `use_to` varchar(100) DEFAULT '——' COMMENT '用途',
  `technology_illustrate` longtext COMMENT '技术要求说明',
  `deliver_date` date DEFAULT NULL COMMENT '要求交付日期',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `priority` varchar(10) NOT NULL COMMENT '优先级1.一般2.紧急',
  `specifications` varchar(30) DEFAULT NULL COMMENT '规格',
  `estimate_cost` decimal(12,5) DEFAULT NULL COMMENT '预估费用',
  `process_num` int DEFAULT NULL COMMENT '加工数量',
  `process_date` date DEFAULT NULL COMMENT '加工日期',
  `process_name` varchar(100) DEFAULT NULL COMMENT '加工件名称',
  `department` varchar(30) NOT NULL COMMENT '部门',
  `applicant` varchar(30) NOT NULL COMMENT '申请人',
  `coordination_type` varchar(10) NOT NULL COMMENT '外协类型1.外协加工2.外协维修',
  `client` varchar(50) NOT NULL COMMENT '委托方',
  `maintain_code` varchar(50) DEFAULT '——' COMMENT '设备维修申请单号',
  `oa_code` varchar(50) DEFAULT '—' COMMENT 'OA申请单号',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `score` int DEFAULT NULL COMMENT '评分',
  `status_time` datetime DEFAULT NULL COMMENT '状态更新时间',
  `notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `reality_date` date DEFAULT NULL COMMENT '实际交付日期',
  `accept_time` datetime DEFAULT NULL COMMENT '验收时间',
  `oa_time` datetime DEFAULT NULL COMMENT 'oa审批时间',
  `department_name` varchar(255) DEFAULT NULL COMMENT '部门名称',
  `mkid` varchar(255) DEFAULT NULL COMMENT 'oa回传id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备外协';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_equipment_maintenance_plan`
--

DROP TABLE IF EXISTS `em_equipment_maintenance_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_equipment_maintenance_plan` (
  `device_code` varchar(64) NOT NULL COMMENT '设备编码',
  `food_safety` varchar(10) DEFAULT NULL COMMENT '是否食品安全检查',
  `crux` varchar(10) DEFAULT NULL COMMENT '是否关键设备',
  `single_unit_adjustment` varchar(10) DEFAULT NULL COMMENT '是否需要单体调机',
  `centering_verification` varchar(10) DEFAULT NULL COMMENT '是否对中校验',
  `image_id` varchar(255) NOT NULL COMMENT '示意图',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修方案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_file_upload`
--

DROP TABLE IF EXISTS `em_file_upload`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_file_upload` (
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `file_type` int DEFAULT NULL COMMENT '文件类型:1.图片',
  `busi_type` int DEFAULT NULL COMMENT '业务类型:1.点检项;2.点检结果',
  `file_id` varchar(255) DEFAULT NULL COMMENT '文件id',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `source_id` int NOT NULL COMMENT '主信息id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件上传';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_fixed_assets`
--

DROP TABLE IF EXISTS `em_fixed_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_fixed_assets` (
  `dept_id` varchar(100) DEFAULT NULL COMMENT '部门id',
  `assets_code` varchar(100) DEFAULT NULL COMMENT '固定资产编号',
  `quota_power` varchar(100) DEFAULT NULL COMMENT '额定功率',
  `humidity_accuracy` varchar(100) DEFAULT NULL COMMENT '湿度测量精度',
  `temperature_accuracy` varchar(100) DEFAULT NULL COMMENT '温度测量精度',
  `humidity_range` varchar(100) DEFAULT NULL COMMENT '湿度测量范围',
  `temperature_range` varchar(100) DEFAULT NULL COMMENT '温度测量范围',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `post_code` varchar(100) DEFAULT NULL COMMENT '邮政编码',
  `detailed_address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `factory_tel` varchar(100) DEFAULT NULL COMMENT '厂商电话',
  `factory_person` varchar(100) DEFAULT NULL COMMENT '厂商联系人',
  `factory_name` varchar(100) DEFAULT NULL COMMENT '生产厂名',
  `country_name` varchar(100) DEFAULT NULL COMMENT '生产国',
  `production_code` varchar(100) DEFAULT NULL COMMENT '出厂编号',
  `production_date` datetime DEFAULT NULL COMMENT '出厂日期',
  `supplier` varchar(100) DEFAULT NULL COMMENT '供应商',
  `maker` varchar(100) DEFAULT NULL COMMENT '制作商',
  `model` varchar(100) DEFAULT NULL COMMENT '规格型号',
  `assets_value` varchar(100) DEFAULT NULL COMMENT '资产原值',
  `assets_type` varchar(100) DEFAULT NULL COMMENT '资产类别',
  `enable_date` datetime DEFAULT NULL COMMENT '启用日期',
  `device_person` varchar(100) DEFAULT NULL COMMENT '设备负责人',
  `device_person_id` varchar(100) DEFAULT NULL COMMENT '设备负责人id',
  `workshop_section` varchar(64) DEFAULT NULL COMMENT '工段/特种设备里使用单位',
  `device_type_id` varchar(64) DEFAULT NULL COMMENT '设备类型',
  `status` varchar(64) DEFAULT NULL COMMENT '状态',
  `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(100) DEFAULT NULL COMMENT '设备编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备id',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `index_analysis` varchar(100) DEFAULT '1' COMMENT '指标分析',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `type` varchar(255) DEFAULT NULL COMMENT '判断是设备台账还是特种设备',
  `specifications` varchar(255) DEFAULT NULL COMMENT '规格',
  `registration_code` varchar(255) DEFAULT NULL COMMENT '注册代码',
  `use_time` varchar(255) DEFAULT NULL COMMENT '投入使用时间',
  `certificate_code` varchar(255) DEFAULT NULL COMMENT '使用证编号',
  `facture_rec_unit` varchar(255) DEFAULT NULL COMMENT '制造整改/整改单位',
  `product_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  `dev_device_attribute` varchar(255) DEFAULT NULL COMMENT '设备属性',
  `next_time` varchar(255) DEFAULT NULL COMMENT '特种设备下一次检验日期',
  `last_time` varchar(255) DEFAULT NULL COMMENT '特种设备上一次检验日期',
  PRIMARY KEY (`id`),
  KEY `device_id` (`device_id` DESC)
) ENGINE=InnoDB AUTO_INCREMENT=469 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备台账';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_fixed_assets_log`
--

DROP TABLE IF EXISTS `em_fixed_assets_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_fixed_assets_log` (
  `message` varchar(255) DEFAULT NULL COMMENT '记录信息',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备关联字段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=386 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备台账日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_fixed_assets_technical`
--

DROP TABLE IF EXISTS `em_fixed_assets_technical`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_fixed_assets_technical` (
  `type` varchar(50) DEFAULT NULL COMMENT '类型',
  `file_url` text COMMENT '文件路径',
  `file_name` longtext COMMENT '文件名称',
  `file_id` varchar(100) DEFAULT NULL COMMENT '文件id',
  `device_id` varchar(100) DEFAULT NULL COMMENT '设备id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `maintenance` varchar(255) DEFAULT NULL COMMENT '维修知识库关联字段(和上传文件有关)',
  `state` int DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备技术资料';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_fixed_record_detail`
--

DROP TABLE IF EXISTS `em_fixed_record_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_fixed_record_detail` (
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备台账关联字段(device_id)',
  `name` varchar(255) DEFAULT NULL COMMENT '字段名称',
  `type` varchar(255) DEFAULT NULL COMMENT '字段类型(1为输入框2为单选)',
  `value` varchar(255) DEFAULT NULL COMMENT '记录的值',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `flag_required` varchar(255) DEFAULT NULL COMMENT '是否必填',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备台账参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_knowledge_document`
--

DROP TABLE IF EXISTS `em_knowledge_document`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_knowledge_document` (
  `konwledge_file_name` varchar(50) NOT NULL COMMENT '知识文档名称',
  `konwledge_file_id` varchar(255) NOT NULL COMMENT '知识文档id',
  `knowledge_code` varchar(50) NOT NULL COMMENT '维修知识库编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='维修知识库知识文档';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_knowledge`
--

DROP TABLE IF EXISTS `em_maintenance_knowledge`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_knowledge` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `knowledge_code` varchar(50) NOT NULL COMMENT '知识编码',
  `knowledge_name` varchar(50) NOT NULL COMMENT '知识名称',
  `fault_type` varchar(50) NOT NULL COMMENT '故障类型',
  `fault_phenomenon` varchar(225) NOT NULL COMMENT '故障现象',
  `cause_analysis` varchar(225) NOT NULL COMMENT '故障原因分析',
  `measures` varchar(225) NOT NULL COMMENT '维修措施',
  `details` varchar(225) NOT NULL COMMENT '知识详情',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='维修知识库';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_plan`
--

DROP TABLE IF EXISTS `em_maintenance_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_plan` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `data_source_type` int DEFAULT '1' COMMENT '1:点检;2:巡检',
  `update_by_name` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `create_by_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `remark` text COMMENT '计划描述',
  `status` int DEFAULT NULL COMMENT '状态:0:启用;1:禁用',
  `type` int DEFAULT NULL COMMENT '类型:1:保养;2:TPM',
  `plan_name` varchar(255) DEFAULT NULL COMMENT '名称',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '保养计划编码:BYJH+日期+6位流水号',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检巡检计划';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_plan_details`
--

DROP TABLE IF EXISTS `em_maintenance_plan_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_plan_details` (
  `project_code` varchar(64) NOT NULL COMMENT '检修项编码',
  `device_code` varchar(64) NOT NULL COMMENT '设备编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修方案明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_plan_device`
--

DROP TABLE IF EXISTS `em_maintenance_plan_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_plan_device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `data_source_type` int DEFAULT '1' COMMENT '1:点检;2:巡检',
  `device_id` int DEFAULT NULL COMMENT '设备id',
  `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `execution_role` varchar(255) DEFAULT NULL COMMENT '执行人',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `parent_id` int DEFAULT NULL COMMENT '关联em_maintenance_plan中的id',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检巡检计划设备明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_plan_frequency`
--

DROP TABLE IF EXISTS `em_maintenance_plan_frequency`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_plan_frequency` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `data_source_type` int DEFAULT '1' COMMENT '1:点检;2:巡检',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `date` varchar(255) DEFAULT NULL COMMENT '日期',
  `end_time` varchar(100) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(100) DEFAULT NULL COMMENT '开始时间',
  `week` varchar(20) DEFAULT NULL COMMENT '周一,周二,周三,周四,周五,周六,周日',
  `is_skip` varchar(20) DEFAULT '2' COMMENT '是否跳过节假日:1:是;2:否',
  `type` int DEFAULT NULL COMMENT '类型:1:不重复，2每天，3每周，4每月',
  `parent_id` int DEFAULT NULL COMMENT '父级id,关联em_maintenance_plan中的id',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检巡检计划频率';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_plan_programme`
--

DROP TABLE IF EXISTS `em_maintenance_plan_programme`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_plan_programme` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `data_source_type` int DEFAULT '1' COMMENT '1:点检;2:巡检',
  `programme_name` varchar(100) DEFAULT NULL COMMENT '方案名称:关联em_maintenance_programme中的方案名称字段',
  `programme_id` int DEFAULT NULL COMMENT '方案id:关联em_maintenance_programme中的id',
  `parent_id` int DEFAULT NULL COMMENT '父级id,关联em_maintenance_plan中的id',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检巡检计划方案明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_programme`
--

DROP TABLE IF EXISTS `em_maintenance_programme`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_programme` (
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `programme_code` varchar(20) DEFAULT NULL COMMENT '点检方案编码',
  `programme_name` varchar(50) DEFAULT NULL COMMENT '保养方案名称',
  `remark` longtext COMMENT '描述',
  `status` int DEFAULT NULL COMMENT '状态:0:启用;1:禁用',
  `data_source_type` int DEFAULT '1' COMMENT '1:点检;2:巡检',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检方案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_programme_Item`
--

DROP TABLE IF EXISTS `em_maintenance_programme_Item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_programme_Item` (
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `parent_id` int DEFAULT NULL COMMENT '点检方案id',
  `project_id` int DEFAULT NULL COMMENT '点检项目id',
  `data_source_type` int DEFAULT '1' COMMENT '1:点检;2:巡检',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检方案明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_project`
--

DROP TABLE IF EXISTS `em_maintenance_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `data_source_type` int NOT NULL DEFAULT '1' COMMENT '1:点检;2:巡检',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `project_code` varchar(50) NOT NULL COMMENT '点检项编码',
  `type_bom_msg` varchar(200) NOT NULL COMMENT '点检项目',
  `content` varchar(200) NOT NULL COMMENT '点检内容',
  `method` varchar(200) NOT NULL COMMENT '点检方法',
  `value_type` int NOT NULL COMMENT '点检项类型;(01.单选型;02 读数型)',
  `unit` varchar(50) DEFAULT NULL COMMENT '数值型:单位',
  `max_value` varchar(200) DEFAULT NULL COMMENT '单选时存参考值；读数时存上限值',
  `min_value` varchar(200) DEFAULT NULL COMMENT '数值时存下限值',
  `status` int NOT NULL COMMENT '0:启用;1:禁用',
  `value_msg` varchar(200) DEFAULT NULL COMMENT '单选型的勾选值，将各项值 以“，”形式隔开存储；详情需要逐项展示',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检项';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_project1`
--

DROP TABLE IF EXISTS `em_maintenance_project1`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_project1` (
  `refer_value` varchar(50) DEFAULT NULL COMMENT '单选型参考值',
  `min_value` varchar(50) DEFAULT NULL COMMENT '读数时存下限值',
  `max_value` varchar(50) DEFAULT NULL COMMENT '读数时存上限值',
  `unit` varchar(50) DEFAULT NULL COMMENT '单位',
  `value_type` int DEFAULT NULL COMMENT '点检值类型;(1.单选型;2 读数型)',
  `project_method` varchar(200) DEFAULT NULL COMMENT '点检项方法',
  `project_content` varchar(200) DEFAULT NULL COMMENT '点检项内容',
  `project_name` varchar(200) DEFAULT NULL COMMENT '点检项名称',
  `project_code` varchar(50) DEFAULT NULL COMMENT '点检项编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `status` int DEFAULT NULL COMMENT '0：启用，1：禁用',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检项废弃';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_result`
--

DROP TABLE IF EXISTS `em_maintenance_result`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_result` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `data_source_type` int NOT NULL DEFAULT '1' COMMENT '1:点检;2:巡检',
  `executor_name` varchar(50) DEFAULT NULL COMMENT '执行人名称',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `cost_amount` decimal(22,2) DEFAULT NULL COMMENT '成本价',
  `duration` decimal(12,2) DEFAULT NULL COMMENT '保养时长/单位为:时(h)',
  `end_time` datetime DEFAULT NULL COMMENT '保养结束时间',
  `start_time` datetime DEFAULT NULL COMMENT '保养开始时间',
  `executor` varchar(50) NOT NULL COMMENT '执行人',
  `task_id` int NOT NULL COMMENT '任务id，关联em_maintenance_task中的id',
  `result_code` varchar(50) NOT NULL COMMENT '结果编码:BYJG+日期+6位流水号',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检巡检结果';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_result_material`
--

DROP TABLE IF EXISTS `em_maintenance_result_material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_result_material` (
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `task_id` int DEFAULT NULL COMMENT '关联em_maintenance_task中的id',
  `result_id` int DEFAULT NULL COMMENT '关联em_maintenance_result中的id',
  `material_code` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(100) DEFAULT NULL COMMENT '物料名称',
  `specification` varchar(50) DEFAULT NULL COMMENT '规格型号',
  `out_quantity` decimal(26,6) DEFAULT NULL COMMENT '出库数量',
  `registered_usage_quantity` decimal(26,6) DEFAULT NULL COMMENT '已登记使用数量',
  `usage_quantity` decimal(26,6) DEFAULT NULL COMMENT '当前使用数量',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `unit_price` decimal(12,2) DEFAULT NULL COMMENT '单价/元',
  `total_price` decimal(12,2) DEFAULT NULL COMMENT '总价/元',
  `data_source_type` int NOT NULL DEFAULT '1' COMMENT '1:保养;2:巡检',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='保养巡检结果物料使用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_result_project`
--

DROP TABLE IF EXISTS `em_maintenance_result_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_result_project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `data_source_type` int NOT NULL DEFAULT '1' COMMENT '1:点检;2:巡检',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `result` varchar(38) DEFAULT NULL COMMENT '保养结果',
  `reference_value` varchar(50) DEFAULT NULL COMMENT '参考值',
  `content` varchar(255) DEFAULT NULL COMMENT '保养内容',
  `type_bom_msg` varchar(255) DEFAULT NULL COMMENT '保养部位/项目',
  `project_id` int DEFAULT NULL COMMENT '关联em_maintenance_project中的id',
  `result_id` int DEFAULT NULL COMMENT '关联em_maintenance_result中的id',
  `task_id` int NOT NULL COMMENT '关联em_maintenance_task中的id',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检巡检结果项记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_skip`
--

DROP TABLE IF EXISTS `em_maintenance_skip`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_skip` (
  `holiday` date NOT NULL COMMENT '节假日期',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检节假日';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_task`
--

DROP TABLE IF EXISTS `em_maintenance_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_task` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `tpm_verify_time` datetime DEFAULT NULL COMMENT 'TPM验证,OA审核时间回显',
  `mkid` varchar(255) DEFAULT NULL COMMENT 'OA审批流id',
  `frequency_type` int DEFAULT NULL COMMENT '频率类型:1：不重复，2：每天，3：每周;，4：每月',
  `data_source_type` int DEFAULT '1' COMMENT '1:点检;2:巡检',
  `verify_report` int DEFAULT NULL COMMENT '(TPM:验证报告)/(普通:保养结果):1:已经提交;0:未提交；',
  `device_id` int DEFAULT NULL COMMENT '设备id',
  `update_by_name` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `create_by_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int DEFAULT NULL COMMENT '状态',
  `plan_name` varchar(100) DEFAULT NULL COMMENT '所属计划',
  `type` varchar(20) DEFAULT NULL COMMENT '类型',
  `plan_id` int DEFAULT NULL COMMENT '所属计划id',
  `execution_role` varchar(100) DEFAULT NULL COMMENT '执行角色',
  `frequency` varchar(255) DEFAULT NULL COMMENT '频率',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `plan_date` date DEFAULT NULL COMMENT '任务日期',
  `task_code` varchar(50) DEFAULT NULL COMMENT '点检任务编码:',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点检巡检任务';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_overhaul_plan`
--

DROP TABLE IF EXISTS `em_overhaul_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_overhaul_plan` (
  `status` varchar(10) NOT NULL COMMENT '状态',
  `plan_name` varchar(64) NOT NULL COMMENT '检修计划名称',
  `plan_id` varchar(64) NOT NULL COMMENT '检修计划id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修计划';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_spare_apply`
--

DROP TABLE IF EXISTS `em_spare_apply`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_spare_apply` (
  `status` varchar(10) NOT NULL COMMENT '状态',
  `type` varchar(64) NOT NULL COMMENT '申请类型(1:申领；2:退库)',
  `order_number` varchar(64) NOT NULL COMMENT '申请单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='备件申领退库';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_spare_apply_details`
--

DROP TABLE IF EXISTS `em_spare_apply_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_spare_apply_details` (
  `number` int NOT NULL COMMENT '数量',
  `device_code` varchar(64) NOT NULL COMMENT '备件编号',
  `order_number` varchar(64) NOT NULL COMMENT '单号',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='备件申领退库明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `equipment_clean_record`
--

DROP TABLE IF EXISTS `equipment_clean_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `equipment_clean_record` (
  `status` int DEFAULT NULL COMMENT '状态1待提交/2待验收/3工段已验收/4车间已验收',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `record_time` varchar(255) DEFAULT NULL COMMENT '清洗时间',
  `record_date` varchar(255) DEFAULT NULL COMMENT '清洗日期',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `record_code` varchar(255) DEFAULT NULL COMMENT '清洗记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `section_by` varchar(255) DEFAULT NULL COMMENT '工段验收人',
  `workshop_by` varchar(255) DEFAULT NULL COMMENT '车间验收人',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备清洗记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `equipment_clean_record_project`
--

DROP TABLE IF EXISTS `equipment_clean_record_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `equipment_clean_record_project` (
  `end_time` varchar(255) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(255) DEFAULT NULL COMMENT '开始时间',
  `clean_method` varchar(255) DEFAULT NULL COMMENT '清洗方法',
  `clean_person` varchar(255) DEFAULT NULL COMMENT '清洗负责人',
  `clean_position` varchar(255) DEFAULT NULL COMMENT '清洗部位',
  `equipment_code` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `equipment_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `record_code` varchar(255) DEFAULT NULL COMMENT '关联记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备清洗记录项目';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `equipment_clean_requirements`
--

DROP TABLE IF EXISTS `equipment_clean_requirements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `equipment_clean_requirements` (
  `clean_method` varchar(1000) DEFAULT NULL COMMENT '清洗方法',
  `clean_position` varchar(255) DEFAULT NULL COMMENT '清洗部位',
  `equipment_code` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `equipment_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `end_time` varchar(255) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(255) DEFAULT NULL COMMENT '开始时间',
  `clean_person` varchar(1000) DEFAULT NULL COMMENT '清洗负责人',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备清洗要求信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `equipment_downtime_record`
--

DROP TABLE IF EXISTS `equipment_downtime_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `equipment_downtime_record` (
  `reason` varchar(1000) DEFAULT NULL COMMENT '停榨原因',
  `synchronous` varchar(255) DEFAULT NULL COMMENT '是否同步化验部门(1、同步 2、不同步)',
  `start_datetime` varchar(255) DEFAULT NULL COMMENT '开榨时间',
  `stop_datetime` varchar(255) DEFAULT NULL COMMENT '停榨时间',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_number` varchar(255) DEFAULT NULL COMMENT '停榨编号',
  `task_code` varchar(255) DEFAULT NULL COMMENT '编号',
  `loss_time` varchar(255) DEFAULT NULL COMMENT '损失时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `record_time` varchar(60) DEFAULT NULL COMMENT '发生时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备停榨表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `execution_requisition_record`
--

DROP TABLE IF EXISTS `execution_requisition_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `execution_requisition_record` (
  `loss_reason` varchar(255) DEFAULT NULL COMMENT '损耗原因',
  `loss_quantity` varchar(255) DEFAULT NULL COMMENT '损耗数量',
  `left_quantity` varchar(255) DEFAULT NULL COMMENT '剩余数量',
  `use_quantity` varchar(255) DEFAULT NULL COMMENT '使用数量',
  `weight` varchar(255) DEFAULT NULL COMMENT '内袋/外袋重量(g)',
  `record_time` varchar(255) DEFAULT NULL COMMENT '记录时间',
  `supplier_name` varchar(255) DEFAULT NULL COMMENT '供应商名称',
  `supplier_code` varchar(255) DEFAULT NULL COMMENT '供应商编码',
  `batch` varchar(255) DEFAULT NULL COMMENT '库存批次',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `model` varchar(255) DEFAULT NULL COMMENT '规格型号',
  `accessory_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `accessory_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `team` varchar(255) DEFAULT NULL COMMENT '领用班组',
  `pick_by` varchar(255) DEFAULT NULL COMMENT '领用人员',
  `pick_time` datetime DEFAULT NULL COMMENT '领用时间',
  `pick_code` varchar(255) DEFAULT NULL COMMENT '领用单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `type` varchar(255) DEFAULT NULL COMMENT '状态(1、原料批次2、内袋3、外袋4、糖袋)',
  `packing_code` varchar(255) DEFAULT NULL COMMENT '包装编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `is_flag` varchar(255) DEFAULT NULL COMMENT '是否合格(0,不合格;1,合格)',
  `wh_requisition_quantity` varchar(255) DEFAULT NULL COMMENT '仓库领用数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='执行领用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `external_system_comparison`
--

DROP TABLE IF EXISTS `external_system_comparison`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `external_system_comparison` (
  `task_code` varchar(255) DEFAULT NULL COMMENT '菜单编码',
  `association_field_2` varchar(255) DEFAULT NULL COMMENT '关联字段2',
  `association_field_1` varchar(255) DEFAULT NULL COMMENT '关联字段1',
  `system_code` varchar(255) DEFAULT NULL COMMENT '外接系统编码',
  `external_field` varchar(255) DEFAULT NULL COMMENT '外部系统对应字段',
  `mes_field` varchar(255) DEFAULT NULL COMMENT 'mes字段',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='外界系统对照字段表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `gold_machine_expansion_table`
--

DROP TABLE IF EXISTS `gold_machine_expansion_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gold_machine_expansion_table` (
  `lower_right` varchar(255) DEFAULT NULL COMMENT '下右',
  `lower_middle` varchar(255) DEFAULT NULL COMMENT '下中',
  `lower_left` varchar(255) DEFAULT NULL COMMENT '下左',
  `up_right` varchar(255) DEFAULT NULL COMMENT '上右',
  `up_middle` varchar(255) DEFAULT NULL COMMENT '上中',
  `up_left` varchar(255) DEFAULT NULL COMMENT '上左',
  `second` varchar(255) DEFAULT NULL COMMENT '第二次',
  `first` varchar(255) DEFAULT NULL COMMENT '第一次',
  `test_piece_specifications` varchar(255) DEFAULT NULL COMMENT '测试片规格',
  `test_piece_material` varchar(255) DEFAULT NULL COMMENT '测试片材质',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `record_code` varchar(255) DEFAULT NULL COMMENT '记录编号',
  `scale_code` varchar(50) DEFAULT NULL COMMENT '电子称编号',
  `weight` varchar(50) DEFAULT NULL COMMENT '砝码重量',
  `middle` varchar(50) DEFAULT NULL COMMENT '中间',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `cook_or_white_sugar_nub` varchar(255) DEFAULT NULL COMMENT '煮糖/白砂糖编号',
  `pro_line_number` varchar(255) DEFAULT NULL COMMENT '产线编号',
  `second_gold_inspection_machine` varchar(255) DEFAULT NULL COMMENT '2#龙门金检机',
  `first_gold_inspection_machine` varchar(255) DEFAULT NULL COMMENT '1#龙门金检机',
  `foreign_object_code` varchar(255) DEFAULT NULL COMMENT '异物编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=95 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='金检机表格拓展表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group_pump_configuration_table`
--

DROP TABLE IF EXISTS `group_pump_configuration_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group_pump_configuration_table` (
  `check_type` varchar(255) DEFAULT NULL COMMENT '1,两个机号2，正常异常卫领用3.三个机号',
  `inspection_items` varchar(255) DEFAULT NULL COMMENT '检验项目',
  `equipment_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `inspection_results` varchar(255) DEFAULT NULL COMMENT '检验结果',
  `abnormal_description` varchar(255) DEFAULT NULL COMMENT '异常描述',
  `sort` varchar(255) DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=72 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='群泵管理配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `group_pump_configuration_table_value`
--

DROP TABLE IF EXISTS `group_pump_configuration_table_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `group_pump_configuration_table_value` (
  `inspection_results` varchar(255) DEFAULT NULL COMMENT '检验结果',
  `check_type` varchar(255) DEFAULT NULL COMMENT '1,两个机号2，正常异常卫领用3.三个机号',
  `inspection_items` varchar(255) DEFAULT NULL COMMENT '检验项目',
  `equipment_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspect_code` varchar(255) DEFAULT NULL COMMENT '巡检记录编号',
  `abnormal_description` varchar(255) DEFAULT NULL COMMENT '异常描述',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=561 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='群泵管理参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inkjet_coding_records`
--

DROP TABLE IF EXISTS `inkjet_coding_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inkjet_coding_records` (
  `product_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  `check_time` varchar(255) DEFAULT NULL COMMENT '检查时间',
  `check_date` varchar(255) DEFAULT NULL COMMENT '检查日期',
  `check_by` varchar(255) DEFAULT NULL COMMENT '检查人员',
  `status` varchar(10) DEFAULT NULL COMMENT '状态(1、未调用2、已调用)',
  `inkjet_time` varchar(255) DEFAULT NULL COMMENT '喷码时间',
  `inkjet_date` varchar(255) DEFAULT NULL COMMENT '喷码日期',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `inkjet_by` varchar(50) DEFAULT NULL COMMENT '换码人员',
  `inkjet_code` varchar(250) DEFAULT NULL COMMENT '喷码编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `notes` varchar(3000) DEFAULT NULL COMMENT '备注',
  `inkjet_id` varchar(255) DEFAULT NULL COMMENT '喷码id(PM+id)',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `diluents_num` varchar(20) DEFAULT NULL COMMENT '稀释剂数量',
  `inks_num` varchar(20) DEFAULT NULL COMMENT '墨水数量',
  `inkjet_code3` varchar(255) DEFAULT NULL COMMENT '喷码编码字段3',
  `inkjet_code2` varchar(255) DEFAULT NULL COMMENT '喷码编码字段2',
  `inkjet_code1` varchar(255) DEFAULT NULL COMMENT '喷码编码字段1',
  `number_plate` varchar(255) DEFAULT NULL COMMENT '车牌',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='喷码监控记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `inventory_table`
--

DROP TABLE IF EXISTS `inventory_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_table` (
  `table_name` varchar(255) DEFAULT NULL COMMENT '库表名称',
  `table_code` varchar(255) DEFAULT NULL COMMENT '库表编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库_库表信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lubrication_content`
--

DROP TABLE IF EXISTS `lubrication_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lubrication_content` (
  `latest_lubrication_time` date DEFAULT NULL COMMENT '最新润滑日期',
  `lubrication_numbe` varchar(50) DEFAULT NULL COMMENT '次数',
  `frequency` varchar(50) DEFAULT NULL COMMENT '频率',
  `plan_quantity_num` varchar(50) DEFAULT NULL COMMENT '计划添加量',
  `manufacturer` varchar(255) DEFAULT NULL COMMENT '生产厂家',
  `specification_model` varchar(50) DEFAULT NULL COMMENT '规格型号',
  `lubrication_oil_name` varchar(255) DEFAULT NULL COMMENT '润滑油名称',
  `lubrication_location` varchar(50) DEFAULT NULL COMMENT '润滑位置',
  `device_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(50) DEFAULT NULL COMMENT '设备编码',
  `lubrication_plan_code` varchar(50) DEFAULT NULL COMMENT '润滑计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑内容';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lubrication_plan`
--

DROP TABLE IF EXISTS `lubrication_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lubrication_plan` (
  `create_person_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `schematic_diagram` text COMMENT '示意图（上传附件）',
  `lubrication_plan_description` text COMMENT '润滑计划描述',
  `workshop_section` varchar(5) DEFAULT NULL COMMENT '工段',
  `status` int DEFAULT NULL COMMENT '状态(1启用，2禁用)',
  `lubrication_plan_name` varchar(255) DEFAULT NULL COMMENT '润滑计划名称',
  `lubrication_plan_code` varchar(50) DEFAULT NULL COMMENT '润滑计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`),
  KEY `lubrication_plan_code` (`lubrication_plan_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑计划';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `lubrication_task`
--

DROP TABLE IF EXISTS `lubrication_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `lubrication_task` (
  `lubrication_plan_name` varchar(255) DEFAULT NULL COMMENT '润滑计划名称',
  `quantity_person_name` varchar(50) DEFAULT NULL COMMENT '添加人名称',
  `actual_quantity_num` varchar(50) DEFAULT NULL COMMENT '实际添加量',
  `actual_add_date` date DEFAULT NULL COMMENT '实际添加日期',
  `lubrication_time` datetime DEFAULT NULL COMMENT '润滑时间',
  `record_person_name` varchar(50) DEFAULT NULL COMMENT '记录人名称',
  `status` int DEFAULT NULL COMMENT '状态(1待执行，2已执行)',
  `lubrication_content_id` int DEFAULT NULL COMMENT '润滑内容id',
  `workshop_section` varchar(5) DEFAULT NULL COMMENT '工段',
  `lubrication_plan_code` varchar(50) DEFAULT NULL COMMENT '润滑计划编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `previously_lubrication_time` date DEFAULT NULL COMMENT '上次润滑日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='润滑任务';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `maintenance_items`
--

DROP TABLE IF EXISTS `maintenance_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `maintenance_items` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `data_source_type` int NOT NULL DEFAULT '1' COMMENT '1:检修;2:检查',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `project_code` varchar(50) NOT NULL COMMENT '检修项编码',
  `device_type_id` int DEFAULT NULL COMMENT '设备类型【废弃】',
  `type_bom_msg` varchar(200) NOT NULL COMMENT '检修项名称【改为文本框】',
  `content` varchar(200) NOT NULL COMMENT '检修内容',
  `method` varchar(200) NOT NULL COMMENT '检修方法',
  `value_type` int NOT NULL COMMENT '检修值类型;(01.单选型;02 读数型)',
  `unit` varchar(50) DEFAULT NULL COMMENT '数值型:单位',
  `max_value` varchar(200) DEFAULT NULL COMMENT '单选时存参考值；读数时存上限值',
  `min_value` varchar(200) DEFAULT NULL COMMENT '数值时存下限值',
  `status` int NOT NULL COMMENT '0:启用;1:禁用',
  `value_msg` varchar(200) DEFAULT NULL COMMENT '单选型的勾选值，将各项值 以“，”形式隔开存储；详情需要逐项展示',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检修项与检查项';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `material_requisition_form`
--

DROP TABLE IF EXISTS `material_requisition_form`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `material_requisition_form` (
  `user_count` varchar(255) DEFAULT NULL COMMENT '使用数量',
  `product_specification` varchar(255) DEFAULT NULL COMMENT '规格',
  `ending_time` varchar(255) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(255) DEFAULT NULL COMMENT '开始时间',
  `product_grade` varchar(255) DEFAULT NULL COMMENT '产品级别',
  `sugar_boiling_code` varchar(255) DEFAULT NULL COMMENT '煮糖编号',
  `record_code` varchar(255) DEFAULT NULL COMMENT '记录编号',
  `record_date` varchar(255) DEFAULT NULL COMMENT '记录时间',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `product_types` varchar(255) DEFAULT NULL COMMENT '种类',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段关联字段',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='包装岗领用记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_information`
--

DROP TABLE IF EXISTS `mdm_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_information` (
  `level` varchar(255) DEFAULT NULL COMMENT '级别',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `responsible_person` varchar(255) DEFAULT NULL COMMENT '负责人',
  `lifecycle_status` varchar(255) DEFAULT NULL COMMENT '生命状态',
  `contact_title` varchar(255) DEFAULT NULL COMMENT '联系人称谓',
  `contact_phone` varchar(255) DEFAULT NULL COMMENT '联系人电话',
  `contact_address` varchar(255) DEFAULT NULL COMMENT '联系人地址',
  `contact_name` varchar(255) DEFAULT NULL COMMENT '联系人姓名',
  `contact_department` varchar(255) DEFAULT NULL COMMENT '联系人部门',
  `is_settlement_party` varchar(255) DEFAULT NULL COMMENT '是否结算方',
  `business_type` varchar(255) DEFAULT NULL COMMENT '业务类型',
  `responsible_department` varchar(255) DEFAULT NULL COMMENT '负责人所属部门',
  `short_name` varchar(255) DEFAULT NULL COMMENT '简称',
  `initials` varchar(255) DEFAULT NULL COMMENT '首字母简称',
  `code_crm` varchar(255) DEFAULT NULL COMMENT '编号（CRM）',
  `nature` varchar(255) DEFAULT NULL COMMENT '性质',
  `category` varchar(255) DEFAULT NULL COMMENT '分类',
  `name` varchar(255) DEFAULT NULL COMMENT '名称',
  `english_name` varchar(255) DEFAULT NULL COMMENT '英文名称',
  `code_sap` varchar(255) DEFAULT NULL COMMENT '编号（SAP）',
  `parent_customer` varchar(255) DEFAULT NULL COMMENT '上级客户',
  `account_group` varchar(255) DEFAULT NULL COMMENT '科目组',
  `distribution_channel` varchar(255) DEFAULT NULL COMMENT '分销渠道',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `type` varchar(255) DEFAULT NULL COMMENT '数据类型（1.客户数据、2.）供应商数据',
  `supplier_status` varchar(255) DEFAULT NULL COMMENT '状态',
  `code_srm` varchar(255) DEFAULT NULL COMMENT '编号（SRM）',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5443 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='客商主数据基本信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_material_expand`
--

DROP TABLE IF EXISTS `mdm_material_expand`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_material_expand` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `material_id` int DEFAULT NULL COMMENT '物料id',
  `state` int DEFAULT NULL COMMENT '状态，0是停用1是启用',
  `material_description` varchar(100) DEFAULT NULL COMMENT '物料描述',
  `material_type` int DEFAULT NULL COMMENT '物料类型',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料数据拓展表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_material_information`
--

DROP TABLE IF EXISTS `mdm_material_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_material_information` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(50) DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(50) DEFAULT NULL COMMENT '物料名称',
  `specification_model` varchar(50) DEFAULT NULL COMMENT '规格型号',
  `unit` varchar(50) DEFAULT NULL COMMENT '单位',
  `category_id` int DEFAULT NULL COMMENT '物料类型id',
  `product_group` varchar(225) DEFAULT NULL COMMENT '产品组',
  `weight` double(21,3) DEFAULT NULL COMMENT '重量（类型限制：2位小数）',
  `volume` double(21,3) DEFAULT NULL COMMENT '体积（类型限制：2位小数）',
  `length` double(21,3) DEFAULT NULL COMMENT '长度（类型限制：2位小数）',
  `wide` double(21,3) DEFAULT NULL COMMENT '宽度（类型限制：2位小数）',
  `height` double(21,3) DEFAULT NULL COMMENT '高度（类型限制：2位小数）',
  `minimum_inventory_quantity` int DEFAULT NULL COMMENT '最小库存数',
  `maximum_inventory_quantity` int DEFAULT NULL COMMENT '最大库存数',
  `validity_period_management` varchar(10) DEFAULT NULL COMMENT '有效期管理（如果值为1则为“是”，如果值为0则为“否”）',
  `validity_period_day` int DEFAULT NULL COMMENT '物料有效期（天）',
  `batch_management` varchar(10) DEFAULT NULL COMMENT '批次管理(如果值为1则为“是”，如果值为0则为“否”）)',
  `item_group` varchar(100) DEFAULT NULL COMMENT '物料组',
  `material_attachment` varchar(255) DEFAULT NULL COMMENT '产品附件',
  `material_product_type` int DEFAULT NULL COMMENT '类型（值为0代表产品，1代表物料',
  `outer_norm` varchar(255) DEFAULT NULL COMMENT '外包装规格',
  `inner_norm` varchar(255) DEFAULT NULL COMMENT '内包装规格',
  `locked_status` varchar(255) DEFAULT NULL COMMENT '锁定状态',
  `responsible_person` varchar(255) DEFAULT NULL COMMENT '负责人',
  `attribution_department` varchar(255) DEFAULT NULL COMMENT '归属部门',
  `third_level_classification_name` varchar(255) DEFAULT NULL COMMENT '三级分类名称',
  `product_classification` varchar(255) DEFAULT NULL COMMENT '产品分类',
  `net_weight` varchar(255) DEFAULT NULL COMMENT '净重',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `item_category` varchar(255) DEFAULT NULL COMMENT '物料类型',
  `factory` varchar(255) DEFAULT NULL COMMENT '工厂（来源：主数据平台）',
  `second_level_classification_name` varchar(255) DEFAULT NULL COMMENT '二级分类名称',
  `first_level_classification_name` varchar(255) DEFAULT NULL COMMENT '一级分类名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=108 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_material_supplier`
--

DROP TABLE IF EXISTS `mdm_material_supplier`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_material_supplier` (
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `manufacturer_name` varchar(50) DEFAULT NULL COMMENT '生产厂家',
  `supplier_code` varchar(50) DEFAULT NULL COMMENT '供应商编码(客商主数据)',
  `material_code` varchar(50) DEFAULT NULL COMMENT '物料编码(物料主数据主键)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料供应商关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_materialcategory_information`
--

DROP TABLE IF EXISTS `mdm_materialcategory_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_materialcategory_information` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `type_name` varchar(50) DEFAULT NULL COMMENT '类型名称',
  `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
  `status` varchar(50) DEFAULT NULL COMMENT '状态，0是停用1是启用',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `operation_time` varchar(255) DEFAULT NULL COMMENT '操作时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料类别列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_product_annex`
--

DROP TABLE IF EXISTS `mdm_product_annex`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_product_annex` (
  `product_attachment` varchar(255) DEFAULT NULL COMMENT '产品附件',
  `product_id` int DEFAULT NULL COMMENT '产品信息表id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品附件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_product_expand`
--

DROP TABLE IF EXISTS `mdm_product_expand`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_product_expand` (
  `product_id` int DEFAULT NULL COMMENT '产品信息表id',
  `product_description` varchar(100) DEFAULT NULL COMMENT '产品描述',
  `state` int DEFAULT NULL COMMENT '状态，0是停用1是启用',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `product_type` int DEFAULT NULL COMMENT '产品类型',
  `inner_norm` varchar(255) DEFAULT NULL COMMENT '内包装规格',
  `outer_norm` varchar(255) DEFAULT NULL COMMENT '外包装规格',
  `outer_quantity` varchar(255) DEFAULT NULL COMMENT '外包装件数',
  `inner_quantity` varchar(255) DEFAULT NULL COMMENT '内包装件数',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='删产品拓展表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_region_information`
--

DROP TABLE IF EXISTS `mdm_region_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_region_information` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(50) NOT NULL COMMENT '区域编号',
  `area_name` varchar(50) NOT NULL COMMENT '区域名称',
  `state` varchar(255) NOT NULL COMMENT '状态',
  `remarks` varchar(1000) DEFAULT NULL COMMENT '备注信息',
  `uu_code` varchar(255) DEFAULT NULL COMMENT '用于关联表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='区域管理基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_scheduling_detail`
--

DROP TABLE IF EXISTS `mdm_scheduling_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_scheduling_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `relation_code` varchar(50) DEFAULT NULL COMMENT '关联编码，关联排班管理基础信息表',
  `shift_code` varchar(50) DEFAULT NULL COMMENT '班次编码',
  `shift` varchar(20) DEFAULT NULL COMMENT '班次',
  `date` varchar(20) DEFAULT NULL COMMENT '日期',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5390 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='排班管理明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_scheduling_manage`
--

DROP TABLE IF EXISTS `mdm_scheduling_manage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_scheduling_manage` (
  `month` varchar(50) DEFAULT NULL COMMENT '年月',
  `department` varchar(50) DEFAULT NULL COMMENT '部门(精糖部/原糖部)',
  `relation_code` varchar(50) DEFAULT NULL COMMENT '关联编码',
  `team_id` varchar(50) DEFAULT NULL COMMENT '班组id',
  `team_name` varchar(50) DEFAULT NULL COMMENT '班组(甲班/乙班/丙班/丁班)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=178 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='排班管理基本信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_section_baseinfo`
--

DROP TABLE IF EXISTS `mdm_section_baseinfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_section_baseinfo` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(50) DEFAULT NULL COMMENT '工段编号',
  `section_name` varchar(50) DEFAULT NULL COMMENT '工段名称',
  `region_id` varchar(50) DEFAULT NULL COMMENT '区域id',
  `sort` int DEFAULT NULL COMMENT '排序',
  `workshop_name` varchar(50) DEFAULT NULL COMMENT '生产车间名称',
  `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
  `state` varchar(10) DEFAULT NULL COMMENT '状态，0是停用   1是启用',
  `flag_deleted` int DEFAULT NULL COMMENT '是否删除',
  `uu_code` varchar(255) DEFAULT NULL COMMENT '用于关联表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `dept_code` varchar(50) DEFAULT NULL COMMENT '部门编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工段管理基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_section_device`
--

DROP TABLE IF EXISTS `mdm_section_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_section_device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `equipment_number` varchar(50) DEFAULT NULL COMMENT '设备编号',
  `equipment_name` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `equipment_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
  `sectionbaseinfo_id` int DEFAULT NULL COMMENT '工段管理基础信息表id',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `flag_deleted` int DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工段设备表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_section_employee`
--

DROP TABLE IF EXISTS `mdm_section_employee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_section_employee` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `employee_number` varchar(50) DEFAULT NULL COMMENT '工号',
  `contact_number` varchar(50) DEFAULT NULL COMMENT '联系方式',
  `login_account` varchar(50) DEFAULT NULL COMMENT '登录账号',
  `team` varchar(50) DEFAULT NULL COMMENT '班组',
  `flag_deleted` int DEFAULT NULL COMMENT '是否删除',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `section_code` varchar(50) DEFAULT NULL COMMENT '工段编号',
  `user_id` varchar(50) DEFAULT NULL COMMENT '用户',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工段成员信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_shift_info`
--

DROP TABLE IF EXISTS `mdm_shift_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_shift_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `shift_schedule` varchar(50) NOT NULL COMMENT '班次名称',
  `class_abbreviation` varchar(50) NOT NULL COMMENT '班次简称',
  `classes_start_time` varchar(10) NOT NULL COMMENT '班次开始时间',
  `classes_end_time` varchar(10) NOT NULL COMMENT '班次结束时间',
  `status` int NOT NULL COMMENT '班次状态(0 停用；1 生效)',
  `remarks` varchar(50) DEFAULT NULL COMMENT '班次备注',
  `shift_code` varchar(50) DEFAULT NULL COMMENT '班次编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='班次基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_squeeze_basicinfo`
--

DROP TABLE IF EXISTS `mdm_squeeze_basicinfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_squeeze_basicinfo` (
  `squeeze_state` int DEFAULT NULL COMMENT '榨季装态',
  `stop_squeeze` date DEFAULT NULL COMMENT '预计停榨日期',
  `sugar_end_time` date DEFAULT NULL COMMENT '炼糖结束日期',
  `sugar_start_time` date DEFAULT NULL COMMENT '炼糖开始日期',
  `squeeze_end_time` date DEFAULT NULL COMMENT '开榨期结束日期',
  `squeeze_start_time` varchar(255) DEFAULT NULL COMMENT '开榨期开始日期',
  `squeeze` varchar(255) DEFAULT NULL COMMENT '榨季',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='榨季基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_squeeze_information`
--

DROP TABLE IF EXISTS `mdm_squeeze_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_squeeze_information` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `squeeze` varchar(50) DEFAULT NULL COMMENT '榨季',
  `squeeze_start_time` date DEFAULT NULL COMMENT '开榨期开始日期',
  `squeeze_end_time` date DEFAULT NULL COMMENT '开榨期结束日期',
  `sugar_start_time` date DEFAULT NULL COMMENT '炼糖开始日期',
  `sugar_end_time` date DEFAULT NULL COMMENT '炼糖结束日期',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `stop_squeeze` date DEFAULT NULL COMMENT '预计停榨日期',
  `status` int DEFAULT NULL COMMENT '榨季状态',
  `sq_state` int DEFAULT NULL COMMENT '榨季状态',
  `squeeze_state` int DEFAULT NULL COMMENT '榨季状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='榨季信息表delete';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_supplier_information`
--

DROP TABLE IF EXISTS `mdm_supplier_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_supplier_information` (
  `supplier_code_sap` varchar(255) DEFAULT NULL COMMENT '供应商编号（SAP）',
  `supplier_english_name` varchar(50) DEFAULT NULL COMMENT '英文名称',
  `supplier_name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `contact_department` varchar(19) DEFAULT NULL COMMENT '联系人部门',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_address` varchar(50) DEFAULT NULL COMMENT '联系人地址',
  `contact_phone` varchar(50) DEFAULT NULL COMMENT '联系人电话',
  `contact_title` varchar(50) DEFAULT NULL COMMENT '联系人称谓',
  `lifecycle_status` varchar(50) DEFAULT NULL COMMENT '生命状态',
  `responsible_person` varchar(255) DEFAULT NULL COMMENT '负责人',
  `remarks` varchar(50) DEFAULT NULL COMMENT '备注',
  `supplier_level` varchar(50) DEFAULT NULL COMMENT '供应商级别',
  `supplier_category` varchar(50) DEFAULT NULL COMMENT '供应商分类',
  `supplier_nature` varchar(50) DEFAULT NULL COMMENT '供应商性质',
  `supplier_code_srm` varchar(50) DEFAULT NULL COMMENT '供应商编号（SRM）',
  `initials` varchar(50) DEFAULT NULL COMMENT '首字母简写',
  `short_name` varchar(50) DEFAULT NULL COMMENT '简称',
  `responsible_department` varchar(50) DEFAULT NULL COMMENT '负责人所属部门',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `is_settlement_party` int DEFAULT NULL COMMENT '是否结算方',
  `distribution_channel` varchar(50) DEFAULT NULL COMMENT '分销渠道',
  `account_group` varchar(19) DEFAULT NULL COMMENT '科目组',
  `supplier_status` varchar(50) DEFAULT NULL COMMENT '状态',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='供应商数据基本信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `monthly_process_indicator_project`
--

DROP TABLE IF EXISTS `monthly_process_indicator_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monthly_process_indicator_project` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `remarks` varchar(1000) DEFAULT NULL COMMENT '备注',
  `key_indicator` varchar(255) DEFAULT NULL COMMENT '是否关键指标',
  `maximum_value` varchar(255) DEFAULT NULL COMMENT '最大值',
  `minimum_value` varchar(255) DEFAULT NULL COMMENT '最小值',
  `standard_value` varchar(255) DEFAULT NULL COMMENT '标准值',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `operator` varchar(255) DEFAULT NULL COMMENT '操作符',
  `belonging_from` varchar(255) DEFAULT NULL COMMENT '所属表单',
  `belonging_process` varchar(255) DEFAULT NULL COMMENT '所属工序',
  `project_name` varchar(255) DEFAULT NULL COMMENT '项目名称',
  `project_code` varchar(255) DEFAULT NULL COMMENT '项目编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `material` varchar(255) DEFAULT NULL COMMENT '物料',
  `is_display_item` int NOT NULL DEFAULT '1' COMMENT '是否发布页呈现1是0否',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=218 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月工艺指标项目配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `non_conforming_record`
--

DROP TABLE IF EXISTS `non_conforming_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `non_conforming_record` (
  `record_time` datetime DEFAULT NULL COMMENT '记录时间',
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `nc_reason` varchar(255) DEFAULT NULL COMMENT '不合格原因',
  `quantity` varchar(255) DEFAULT NULL COMMENT '数量',
  `packing_code` varchar(255) DEFAULT NULL COMMENT '包装记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `solution` varchar(255) DEFAULT NULL COMMENT '处理办法',
  `inkjet_code` varchar(255) DEFAULT NULL COMMENT '喷码编号',
  `model` varchar(255) DEFAULT NULL COMMENT '规格',
  `pack_quantity` varchar(255) DEFAULT NULL COMMENT '包数',
  `type` varchar(255) DEFAULT NULL COMMENT '类型(1、不合格2、等外糖)',
  `record_code` varchar(100) DEFAULT NULL COMMENT '不合格记录编码',
  `code` varchar(60) DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(60) DEFAULT NULL COMMENT '物料名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=227 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='不合格记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `opening_period_report`
--

DROP TABLE IF EXISTS `opening_period_report`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `opening_period_report` (
  `pass_rate` varchar(50) DEFAULT NULL COMMENT '合格率',
  `month` varchar(40) DEFAULT NULL COMMENT '月份',
  `item_index` varchar(10) DEFAULT NULL COMMENT '序号',
  `item_project` varchar(40) DEFAULT NULL COMMENT '指标名称',
  `team` varchar(20) DEFAULT NULL COMMENT '班组',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `zj` varchar(40) DEFAULT NULL COMMENT '榨季',
  `date_total` int DEFAULT NULL COMMENT '工单总数量',
  `pass_count` int DEFAULT NULL COMMENT '合格数量',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `sort_field` varchar(10) DEFAULT NULL COMMENT '班组排序字段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='开榨期报表甲膏蜜';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `operating_record`
--

DROP TABLE IF EXISTS `operating_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `operating_record` (
  `table_id` int DEFAULT NULL COMMENT '操作表id',
  `operations_table` varchar(50) DEFAULT NULL COMMENT '操作表',
  `operation_type` varchar(50) DEFAULT NULL COMMENT '操作类型(新增编辑删除)',
  `module_name` varchar(50) DEFAULT NULL COMMENT '操作模块名称',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `dept_name` varchar(30) NOT NULL COMMENT '部门名称',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `packaging_record`
--

DROP TABLE IF EXISTS `packaging_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `packaging_record` (
  `min_suger_bag` varchar(255) DEFAULT NULL COMMENT '袋糖范围(kg)下限',
  `max_suger_bag` varchar(255) DEFAULT NULL COMMENT '袋糖范围(kg)上限',
  `scheduled_date` date DEFAULT NULL COMMENT '排产年月',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `pl_code` varchar(255) DEFAULT NULL COMMENT '产线编号',
  `notes` varchar(3000) DEFAULT NULL COMMENT '备注',
  `packing_notes` varchar(3000) DEFAULT NULL COMMENT '包装备注',
  `business_type` varchar(255) DEFAULT NULL COMMENT '业务类型',
  `order_type` varchar(255) DEFAULT NULL COMMENT '订单类型',
  `order_pro_line` varchar(255) DEFAULT NULL COMMENT '订单产品行号',
  `order_code` varchar(255) DEFAULT NULL COMMENT '销售订单编号',
  `sold_party` varchar(255) DEFAULT NULL COMMENT '售达方',
  `inkjet_request` varchar(255) DEFAULT NULL COMMENT '喷码要求',
  `take_request` varchar(255) DEFAULT NULL COMMENT '取糖要求',
  `plan_code` varchar(255) DEFAULT NULL COMMENT '小包装计划编号',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `model` varchar(255) DEFAULT NULL COMMENT '规格',
  `product_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `sample_quantity` varchar(255) DEFAULT NULL COMMENT '取样数量(kg)',
  `ng_quantity` varchar(255) DEFAULT NULL COMMENT '不合格数量(kg)',
  `packing_quantity` varchar(255) DEFAULT NULL COMMENT '包装数量(吨)',
  `team` varchar(50) DEFAULT NULL COMMENT '班组',
  `packing_by` varchar(50) DEFAULT NULL COMMENT '包装人员',
  `status` int DEFAULT NULL COMMENT '执行状态(1、执行中，2、暂停 3、已完成)',
  `packing_code` varchar(50) DEFAULT NULL COMMENT '小包装记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `wrap_form` varchar(255) DEFAULT NULL COMMENT '包装形式',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段编号',
  `line_side_quantity` varchar(255) DEFAULT NULL COMMENT '线边剩余量',
  `sugar_bin_quantity` varchar(255) DEFAULT NULL COMMENT '糖斗剩余量',
  `nostandard_quantity` varchar(255) DEFAULT NULL COMMENT '等外糖数量(吨)',
  `packing_quantity1000` varchar(255) DEFAULT NULL COMMENT '规格1000kg(吨)',
  `packing_quantity50` varchar(255) DEFAULT NULL COMMENT '规格50kg(吨)',
  `packing_quantity30` varchar(255) DEFAULT NULL COMMENT '规格30kg(吨)',
  `packing_quantity25` varchar(255) DEFAULT NULL COMMENT '规格25kg(吨)',
  `warehouseman` varchar(255) DEFAULT NULL COMMENT '仓管员',
  `packing_type` varchar(255) DEFAULT NULL COMMENT '装包类型',
  `boiled_code` varchar(255) DEFAULT NULL COMMENT '煮糖/白砂糖编号',
  `is_aging` varchar(255) DEFAULT NULL COMMENT '是否经过陈化仓(0:否，1:是，2:不限)',
  `campaign` varchar(255) DEFAULT NULL COMMENT '榨季',
  `ranks` varchar(255) DEFAULT NULL COMMENT '等级',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `csr_name` varchar(255) DEFAULT NULL COMMENT '货主',
  `inkjet_id` varchar(255) DEFAULT NULL COMMENT '喷码id(散装糖关联)',
  `number_plate` varchar(255) DEFAULT NULL COMMENT '车牌',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=202 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='包装包装单记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `packaging_record_details`
--

DROP TABLE IF EXISTS `packaging_record_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `packaging_record_details` (
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `inkjet_code` varchar(255) DEFAULT NULL COMMENT '喷码编号',
  `quantity` varchar(255) DEFAULT NULL COMMENT '数量(kg)',
  `model` varchar(255) DEFAULT NULL COMMENT '规格',
  `wrap_form` varchar(255) DEFAULT NULL COMMENT '包装形式',
  `outer_pack_quantity` varchar(255) DEFAULT NULL COMMENT '外包装数量',
  `packing_code` varchar(255) DEFAULT NULL COMMENT '包装记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_time` datetime DEFAULT NULL COMMENT '记录时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `csr_name` varchar(255) DEFAULT NULL COMMENT '货主',
  `pro_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `pro_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  `sold_party` varchar(255) DEFAULT NULL COMMENT '售达方',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `relative_humidity` varchar(255) DEFAULT NULL COMMENT '相对湿度',
  `heat` varchar(255) DEFAULT NULL COMMENT '温度',
  `is_seal` varchar(10) DEFAULT NULL COMMENT '袋口封好/扎好(0,否；1，是)',
  `end_date` datetime DEFAULT NULL COMMENT '结束时间',
  `start_date` datetime DEFAULT NULL COMMENT '开始时间',
  `ranks` varchar(255) DEFAULT NULL COMMENT '等级',
  `packing_type` varchar(255) DEFAULT NULL COMMENT '装包类型',
  `boiled_code` varchar(255) DEFAULT NULL COMMENT '白砂糖编号',
  `pln_id` varchar(255) DEFAULT NULL COMMENT '计划产品id',
  `pln_code` varchar(255) DEFAULT NULL COMMENT '计划表编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=349 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='包装单产出记录详情表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `packing_material`
--

DROP TABLE IF EXISTS `packing_material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `packing_material` (
  `material_code` varchar(255) DEFAULT NULL COMMENT '新建字段',
  `material_name` varchar(255) DEFAULT NULL COMMENT '原料名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `status` int DEFAULT NULL COMMENT '状态',
  `word` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='小包装原料表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `packing_product_material`
--

DROP TABLE IF EXISTS `packing_product_material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `packing_product_material` (
  `word` varchar(255) DEFAULT NULL COMMENT '备注',
  `product_code` varchar(255) DEFAULT NULL COMMENT '原料关联的产品名称',
  `product_name` varchar(255) DEFAULT NULL COMMENT '原料关联的产品名称',
  `material_name` varchar(255) DEFAULT NULL COMMENT '原料名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '0为删除，1为没有删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `status` int DEFAULT NULL COMMENT '状态1为开启，2为停止',
  `material_code` varchar(255) DEFAULT NULL COMMENT '原料编码',
  `save` int DEFAULT NULL COMMENT '判断是否保存，1为保存，0为非保存',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='小包装物料信息配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `plan_raw_sugar_production`
--

DROP TABLE IF EXISTS `plan_raw_sugar_production`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `plan_raw_sugar_production` (
  `dcs_amount` varchar(255) DEFAULT NULL COMMENT 'DCS实际榨蔗量',
  `weighbridge_amount` varchar(255) DEFAULT NULL COMMENT '地磅实际榨蔗量',
  `confirm_amount` varchar(255) DEFAULT NULL COMMENT '确认榨蔗计划量',
  `supply_amount` varchar(255) DEFAULT NULL COMMENT '农务甘蔗供应量',
  `plan_amount` varchar(255) DEFAULT NULL COMMENT '生产榨蔗计划',
  `planPro_date` date DEFAULT NULL COMMENT '计划生产日期',
  `rawPlan_code` varchar(255) DEFAULT NULL COMMENT '原糖周计划编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='周计划产量表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `plan_raw_sugar_week`
--

DROP TABLE IF EXISTS `plan_raw_sugar_week`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `plan_raw_sugar_week` (
  `campaign` varchar(255) DEFAULT NULL COMMENT '榨季',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `scheduled_date` varchar(255) DEFAULT NULL COMMENT '排产年月',
  `code` varchar(255) DEFAULT NULL COMMENT '原糖周计划编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` varchar(255) DEFAULT NULL COMMENT '订单状态',
  `actual_amount` varchar(255) DEFAULT NULL COMMENT '实际榨蔗量',
  `confirm_amount` varchar(255) DEFAULT NULL COMMENT '确认榨蔗计划',
  `supply_amount` varchar(255) DEFAULT NULL COMMENT '农务甘蔗供应量',
  `plan_amount` varchar(255) DEFAULT NULL COMMENT '生产榨蔗计划',
  `remark` varchar(2000) DEFAULT NULL COMMENT '新建字段备注',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='原糖周计划信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `plan_refined_month_product`
--

DROP TABLE IF EXISTS `plan_refined_month_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `plan_refined_month_product` (
  `remark` varchar(2000) DEFAULT NULL COMMENT '备注',
  `flag_aged` int DEFAULT NULL COMMENT '是否过陈化仓',
  `cooking_type` varchar(255) DEFAULT NULL COMMENT '煮制类型',
  `displacement` varchar(10) DEFAULT '0' COMMENT '排产量(t)',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `norm` varchar(255) DEFAULT NULL COMMENT '规格',
  `pro_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  `pro_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `csr_name` varchar(255) DEFAULT NULL COMMENT '货主',
  `sold_party` varchar(255) DEFAULT NULL COMMENT '售达方',
  `prd_date` date DEFAULT NULL COMMENT '开始生产日期',
  `prd_code` varchar(255) DEFAULT NULL COMMENT '生产订单编号',
  `order_pro_code` varchar(255) DEFAULT NULL COMMENT '订单产品表id',
  `pln_code` varchar(255) DEFAULT NULL COMMENT '精糖计划表编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `type` int DEFAULT NULL COMMENT '常规生产/订单产品',
  `inkjet_request` varchar(255) DEFAULT NULL COMMENT '喷码要求',
  `take_request` varchar(255) DEFAULT NULL COMMENT '取糖要求',
  `wrap_form` varchar(255) DEFAULT NULL COMMENT '包装形式',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `inner_norm` varchar(255) DEFAULT NULL COMMENT '内包装规格',
  `output` varchar(10) DEFAULT NULL COMMENT '产出量(t)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='精糖计划产品表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `plan_refined_sugar_month`
--

DROP TABLE IF EXISTS `plan_refined_sugar_month`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `plan_refined_sugar_month` (
  `status` varchar(255) DEFAULT NULL COMMENT '计划状态',
  `output` varchar(10) DEFAULT '0' COMMENT '产出数量(吨)',
  `scheduled` varchar(10) DEFAULT '0' COMMENT '排产数量(吨)',
  `plan_capacity` varchar(255) DEFAULT '0' COMMENT '计划产能(吨)',
  `daily_capacity` decimal(12,2) DEFAULT NULL COMMENT '日产能(吨)',
  `campaign` varchar(255) DEFAULT NULL COMMENT '榨季',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `scheduled_date` date DEFAULT NULL COMMENT '排产年月',
  `type` varchar(255) DEFAULT NULL COMMENT '计划类型 白砂糖0/金砂糖1/小包装2',
  `code` varchar(255) DEFAULT NULL COMMENT '计划编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `remark` varchar(2000) DEFAULT NULL COMMENT '备注信息',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `flag_submit` int DEFAULT '0' COMMENT '是否排产大于计划产能',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='精糖计划表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `plan_year_upload`
--

DROP TABLE IF EXISTS `plan_year_upload`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `plan_year_upload` (
  `state` int DEFAULT NULL COMMENT '状态 启/停 1/2',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `attachment` varchar(255) DEFAULT NULL COMMENT '计划附件excel',
  `content` varchar(255) DEFAULT NULL COMMENT '计划内容',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `type` varchar(255) DEFAULT NULL COMMENT '上传类型',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `flag_resolve` int DEFAULT '2' COMMENT '是否解析 1/2/3',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年度计划上传记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prd_DCS_record`
--

DROP TABLE IF EXISTS `prd_DCS_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prd_DCS_record` (
  `produce_code` varchar(255) DEFAULT NULL COMMENT '生产记录编号 管理生产记录表编码',
  `gather_time` datetime DEFAULT NULL COMMENT '采集时间',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `value` varchar(255) DEFAULT NULL COMMENT '采集值',
  `field` varchar(255) DEFAULT NULL COMMENT '设备属性字段',
  `title` varchar(255) DEFAULT NULL COMMENT '采集标题',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11896298 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产阶段DCS数采信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prd_complete_suger_plan`
--

DROP TABLE IF EXISTS `prd_complete_suger_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prd_complete_suger_plan` (
  `r3_size_upper` varchar(255) DEFAULT NULL COMMENT 'R3筛前尺寸指标上限(mm)',
  `r3_size_lower` varchar(255) DEFAULT NULL COMMENT 'R3筛前尺寸指标下限(mm)',
  `r3_level` varchar(255) DEFAULT NULL COMMENT 'R3级别',
  `r3_color_upper` varchar(255) DEFAULT NULL COMMENT 'R3色值指标上限(UI)',
  `r3_color_lower` varchar(255) DEFAULT NULL COMMENT 'R3色值指标下限(UI)',
  `r2_size_upper` varchar(255) DEFAULT NULL COMMENT 'R2筛前尺寸指标上限(mm)',
  `r2_size_lower` varchar(255) DEFAULT NULL COMMENT 'R2筛前尺寸指标下限(mm)',
  `r2_level` varchar(255) DEFAULT NULL COMMENT 'R2级别',
  `r2_color_upper` varchar(255) DEFAULT NULL COMMENT 'R2色值指标上限(UI)',
  `r2_color_lower` varchar(255) DEFAULT NULL COMMENT 'R2色值指标下限(UI)',
  `r1_cook_upper` varchar(255) DEFAULT NULL COMMENT 'R1回煮上限(m³)',
  `r1_size_upper` varchar(255) DEFAULT NULL COMMENT 'R1筛前尺寸指标上限(mm)',
  `r1_size_lower` varchar(255) DEFAULT NULL COMMENT 'R1筛前尺寸指标下限(mm)',
  `r1_level` varchar(255) DEFAULT NULL COMMENT 'R1级别',
  `r1_color_upper` varchar(255) DEFAULT NULL COMMENT 'R1色值指标上限(UI)',
  `r1_color_lower` varchar(255) DEFAULT NULL COMMENT 'R1色值指标下限(UI)',
  `start_time` varchar(255) DEFAULT NULL COMMENT '开始时间',
  `start_date` datetime DEFAULT NULL COMMENT '开始日期',
  `issued_by` varchar(255) DEFAULT NULL COMMENT '下发人员',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(255) DEFAULT NULL COMMENT '成糖计划编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='成糖生产计划';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prd_enter_data`
--

DROP TABLE IF EXISTS `prd_enter_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prd_enter_data` (
  `produce_code` varchar(255) DEFAULT NULL COMMENT '生产记录编号 关联生产记录表编码',
  `material_produce_code` varchar(255) DEFAULT NULL COMMENT '原料生产记录编号_关联对应物料的生产记录编码',
  `material_type` int DEFAULT NULL COMMENT '入料类型（1.糖浆 2.糖糊 3.糖蜜 4.水 5.投粉 6.酒精）',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `quantity` varchar(255) DEFAULT NULL COMMENT '数量',
  `PH` varchar(255) DEFAULT NULL COMMENT 'PH值',
  `IU` varchar(255) DEFAULT NULL COMMENT '色度',
  `AP` varchar(255) DEFAULT NULL COMMENT '纯度',
  `Bx` varchar(255) DEFAULT NULL COMMENT '锤度',
  `material_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `material_time` datetime DEFAULT NULL COMMENT '入料时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `vendor` varchar(255) DEFAULT NULL COMMENT '供应商',
  `inventory_id` varchar(255) DEFAULT NULL COMMENT '投粉库存记录表',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2018 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入料情况记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prd_equipment_maintenance`
--

DROP TABLE IF EXISTS `prd_equipment_maintenance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prd_equipment_maintenance` (
  `remarks` varchar(1000) DEFAULT NULL COMMENT '备注',
  `replacement_frequency_days` int DEFAULT NULL COMMENT '更换频率(天)',
  `cleaning_frequency_days` int DEFAULT NULL COMMENT '清洗频率(天)',
  `filter_bag_specification` varchar(255) DEFAULT NULL COMMENT '过滤袋规格',
  `code` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `equipment_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务编码',
  `uu_code` varchar(255) DEFAULT NULL COMMENT '工段编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `equipment_type` varchar(255) DEFAULT NULL COMMENT '设备类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备信息维护基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prd_filter_cleaning_equipment`
--

DROP TABLE IF EXISTS `prd_filter_cleaning_equipment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prd_filter_cleaning_equipment` (
  `code` varchar(255) DEFAULT NULL COMMENT '拆洗设备编号',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `replaced_bag_count` int DEFAULT NULL COMMENT '更换过滤袋数量(只)',
  `impurity_type` varchar(255) DEFAULT NULL COMMENT '收到杂质种类',
  `replacement_frequency_days` int DEFAULT NULL COMMENT '更换频率(天)',
  `cleaning_frequency_days` int DEFAULT NULL COMMENT '清洗频率(天)',
  `filter_bag_type` varchar(255) DEFAULT NULL COMMENT '过滤袋类型',
  `equipment_name` varchar(255) DEFAULT NULL COMMENT '拆洗设备名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `filter_cleaning_code` varchar(255) DEFAULT NULL COMMENT '关联袋滤机拆洗记录基础信息表code',
  `filter_bag_check` int DEFAULT NULL COMMENT '过滤袋检查',
  `work_content` int DEFAULT NULL COMMENT '工作内容（0代表都不选中，1代表选中拆洗，2代表选中更换，3代表都选中）',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `equipment_type` varchar(255) DEFAULT NULL COMMENT '设备类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='拆洗设备记录基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prd_filter_cleaning_record`
--

DROP TABLE IF EXISTS `prd_filter_cleaning_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prd_filter_cleaning_record` (
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `cleaning_time` varchar(255) DEFAULT NULL COMMENT '拆洗时间',
  `cleaning_date` varchar(255) DEFAULT NULL COMMENT '拆洗日期',
  `team_name` varchar(255) DEFAULT NULL COMMENT '班组',
  `recorder_name` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `code` varchar(255) DEFAULT NULL COMMENT '拆洗记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务编码',
  `sum_replaced_bag_count` int DEFAULT NULL COMMENT '更换过滤袋数量(只)汇总',
  `sum_filter_bag` varchar(255) DEFAULT NULL COMMENT '过滤袋检查汇总',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='拆洗记录基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prd_operate_record`
--

DROP TABLE IF EXISTS `prd_operate_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prd_operate_record` (
  `produce_code` varchar(255) DEFAULT NULL COMMENT '生产记录编号 管理生产记录表编码',
  `content` text COMMENT '操作内容',
  `title` varchar(255) DEFAULT NULL COMMENT '操作标题',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5709 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `prd_sugar_info`
--

DROP TABLE IF EXISTS `prd_sugar_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `prd_sugar_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `boiled_code` varchar(50) DEFAULT NULL COMMENT '煮糖编号',
  `can_number` int DEFAULT NULL COMMENT '罐号(1,2,3)',
  `type` varchar(100) DEFAULT NULL COMMENT '种类(A糖，B糖，C糖)',
  `data` varchar(100) DEFAULT NULL COMMENT '年/月/日',
  `start_time` datetime DEFAULT NULL COMMENT '煮糖开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '煮糖结束时间',
  `usage_time` datetime DEFAULT NULL COMMENT '实用时间（结束时间-开始时间）',
  `sugar_man` varchar(50) DEFAULT NULL COMMENT '煮糖人员',
  `status` varchar(50) DEFAULT NULL COMMENT '状态（执行中，已完成）',
  `inspection` int DEFAULT NULL COMMENT '检验申请（申请，不申请）',
  `notes` varchar(19) DEFAULT NULL COMMENT '备注',
  `number` varchar(20) DEFAULT NULL COMMENT '放料数量',
  `laboratory_code` varchar(20) DEFAULT NULL COMMENT '检验单号',
  `operate_id` int DEFAULT NULL COMMENT '操作记录id',
  `remove_number` varchar(20) DEFAULT NULL COMMENT '卸下箱号',
  `product_id` int DEFAULT NULL COMMENT '生产记录id',
  `operate` varchar(20) DEFAULT NULL COMMENT '操作',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `work_group` varchar(255) DEFAULT NULL COMMENT '班组',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='煮糖基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_dcs_record`
--

DROP TABLE IF EXISTS `pro_dcs_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_dcs_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_code` varchar(30) DEFAULT NULL COMMENT '生产记录编号',
  `device_no` varchar(20) DEFAULT NULL COMMENT '设备/机器编号',
  `start_time` varchar(30) DEFAULT NULL COMMENT '开始时间',
  `end_time` varchar(30) DEFAULT NULL COMMENT '结束时间',
  `speed` varchar(20) DEFAULT NULL COMMENT '转速',
  `bottom` varchar(30) DEFAULT NULL COMMENT '水表表底数',
  `consumption` varchar(30) DEFAULT NULL COMMENT '用水量',
  `task_code` varchar(30) DEFAULT NULL COMMENT '所属表单',
  `action_record` int DEFAULT NULL COMMENT '是否记录',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=143 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产DCS记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_detection_record`
--

DROP TABLE IF EXISTS `pro_detection_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_detection_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_by` varchar(50) DEFAULT NULL COMMENT '操作人员',
  `record_date` varchar(50) DEFAULT NULL COMMENT '校对日期',
  `team` varchar(50) DEFAULT NULL COMMENT '班组',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `confirm_by` varchar(50) DEFAULT NULL COMMENT '确认人员',
  `record_code` varchar(50) DEFAULT NULL COMMENT '记录编号',
  `first_result` varchar(50) DEFAULT NULL COMMENT '1#称结果',
  `second_result` varchar(50) DEFAULT NULL COMMENT '2#称结果',
  `third_result` varchar(50) DEFAULT NULL COMMENT '3#称结果',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `task_code` varchar(50) DEFAULT NULL COMMENT '所属表单',
  `fourth_result` varchar(50) DEFAULT NULL COMMENT '4#称结果',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='检测记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_inspection_detail`
--

DROP TABLE IF EXISTS `pro_inspection_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_inspection_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `device_code` varchar(20) DEFAULT NULL COMMENT '设备编号',
  `device_name` varchar(20) DEFAULT NULL COMMENT '设备名称',
  `inspect_item` varchar(50) DEFAULT NULL COMMENT '检验项目',
  `inspect_item_content` varchar(50) DEFAULT NULL COMMENT '检验内容',
  `unit` varchar(50) DEFAULT NULL COMMENT '单位',
  `inspection_result` varchar(10) DEFAULT NULL COMMENT '检验结果',
  `inspect_code` varchar(20) DEFAULT NULL COMMENT '巡检单号',
  `project_id` varchar(20) DEFAULT NULL COMMENT '巡检项目id',
  `value_type` int DEFAULT NULL COMMENT '检验项目类型(1 单选型; 2 多选型; 3 输入型；4 DCS采集)',
  `value_source` varchar(50) DEFAULT NULL COMMENT '选项内容',
  `selected_value` varchar(50) DEFAULT NULL COMMENT '选项的值',
  `attribute` varchar(50) DEFAULT NULL COMMENT '设备属性',
  `status` int DEFAULT '0' COMMENT '状态',
  `exception_description` varchar(1000) DEFAULT NULL COMMENT '异常描述',
  `data_source` int DEFAULT NULL COMMENT '数据来源(1 手动录入；2 DCS采集)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=177048 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_inspection_record`
--

DROP TABLE IF EXISTS `pro_inspection_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_inspection_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_id` varchar(20) DEFAULT NULL COMMENT '巡检任务id',
  `plan_id` varchar(20) DEFAULT NULL COMMENT '巡检计划id',
  `exception_description` varchar(255) DEFAULT NULL COMMENT '异常说明',
  `status` int DEFAULT NULL COMMENT '任务状态(0 待执行; 1 已完成；2已关闭)',
  `inspect_date` varchar(20) DEFAULT NULL COMMENT '巡检日期',
  `team` varchar(10) DEFAULT NULL COMMENT '班组',
  `inspect_code` varchar(20) DEFAULT NULL COMMENT '巡检记录编号',
  `inspect_by` varchar(50) DEFAULT NULL COMMENT '巡检人',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务编码',
  `workshop_code` varchar(255) DEFAULT NULL COMMENT '工段编码 关联工段信息uu_code',
  `inspect_time` varchar(255) DEFAULT NULL COMMENT '巡检时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `equipment_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `verification_by` varchar(255) DEFAULT NULL COMMENT '验证人员',
  `verification_result_explanation` varchar(255) DEFAULT NULL COMMENT '验证结果说明',
  `verification_results` varchar(255) DEFAULT NULL COMMENT '验证结果',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_inventory`
--

DROP TABLE IF EXISTS `pro_inventory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_inventory` (
  `notes` varchar(5000) DEFAULT NULL COMMENT '备注',
  `table_code` varchar(255) DEFAULT NULL COMMENT '仓库编号',
  `supplier_code` varchar(255) DEFAULT NULL COMMENT '供应商编码',
  `batch_quantity` varchar(50) DEFAULT NULL COMMENT '批次数量',
  `batch` varchar(255) DEFAULT NULL COMMENT '库存批次',
  `inventory_quantity` varchar(50) DEFAULT NULL COMMENT '库存数量',
  `accessory_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `prd_code` varchar(50) DEFAULT NULL COMMENT '生产记录编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `treat_quantity` varchar(255) DEFAULT NULL COMMENT '待入库数量',
  `storage_quantity` varchar(255) DEFAULT NULL COMMENT '待入库数量',
  `team` varchar(50) DEFAULT NULL COMMENT '班组',
  `record_by` varchar(50) DEFAULT NULL COMMENT '记录人员',
  `status` int DEFAULT NULL COMMENT '入库状态(1 待申请；2 待入库；3 已入库)',
  `current_data` varchar(50) DEFAULT NULL COMMENT '原糖称当前数据',
  `record_date` varchar(50) DEFAULT NULL COMMENT '记录日期',
  `last_data` varchar(50) DEFAULT NULL COMMENT '原糖称上一次数据',
  `relevancy_code` varchar(255) DEFAULT NULL COMMENT '关联编码',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `detail_id` varchar(255) DEFAULT NULL COMMENT '包装明细id',
  `mat_type` int DEFAULT NULL COMMENT '物料类型',
  `pick_row_item` varchar(255) DEFAULT NULL COMMENT '领料明细号',
  `pick_no` varchar(255) DEFAULT NULL COMMENT '五金仓出库单号',
  `commitment_item_name` varchar(255) DEFAULT NULL COMMENT '承诺项目名称',
  `commitment_item_code` varchar(255) DEFAULT NULL COMMENT '承诺项目编号',
  `unit_price` double(21,3) DEFAULT NULL COMMENT '单价',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=657 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库_库存表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_leader_indicate`
--

DROP TABLE IF EXISTS `pro_leader_indicate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_leader_indicate` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` int DEFAULT NULL COMMENT '任务状态',
  `feedback_by` varchar(50) DEFAULT NULL COMMENT '反馈人员',
  `feedback_time` datetime DEFAULT NULL COMMENT '反馈时间',
  `workshop_id` varchar(20) DEFAULT NULL COMMENT '工段id',
  `task_content` longtext COMMENT '任务内容',
  `feedback_content` longtext COMMENT '执行反馈',
  `leader_indicate_task_code` varchar(255) DEFAULT NULL COMMENT '任务号（领导指示的编号）',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='领导指示记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_magnet_inspect_record`
--

DROP TABLE IF EXISTS `pro_magnet_inspect_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_magnet_inspect_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `foreign_weight` varchar(10) DEFAULT NULL COMMENT '异物重量(含糖分)(g)',
  `magnet_code` varchar(20) DEFAULT NULL COMMENT '吸铁器编号',
  `result` int DEFAULT NULL COMMENT '检查结果(是否异常 1 正常；2 异常)',
  `main_components` varchar(50) DEFAULT NULL COMMENT '异物主要成分',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `record_code` varchar(20) DEFAULT NULL COMMENT '巡检记录编码，关联生产巡检记录表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `pro_line_code` varchar(50) DEFAULT NULL COMMENT '产线编号',
  `boiled_code` varchar(50) DEFAULT NULL COMMENT '煮糖编号',
  `cleaning_time` varchar(255) DEFAULT NULL COMMENT '清洗时间',
  `is_it_cleaned` varchar(255) DEFAULT NULL COMMENT '是否清洗',
  `cleaning_date` varchar(255) DEFAULT NULL COMMENT '清理日期',
  `model` varchar(255) DEFAULT NULL COMMENT '规格',
  `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `product_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='特殊巡检记录明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_process_template`
--

DROP TABLE IF EXISTS `pro_process_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_process_template` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `template_code` varchar(255) DEFAULT NULL COMMENT '工艺模板编码',
  `template_name` varchar(255) DEFAULT NULL COMMENT '工艺模板名称',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `state` int DEFAULT NULL COMMENT '工艺模板状态(1.是已发布 2.是未发布)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月工艺指标管理_工艺模板配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_process_template_expand`
--

DROP TABLE IF EXISTS `pro_process_template_expand`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_process_template_expand` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `remarks` varchar(1000) DEFAULT NULL COMMENT '备注',
  `key_indicator` varchar(255) DEFAULT NULL COMMENT '是否关键指标',
  `maximum_value` varchar(255) DEFAULT NULL COMMENT '最大值',
  `minimum_value` varchar(255) DEFAULT NULL COMMENT '最小值',
  `standard_value` varchar(255) DEFAULT NULL COMMENT '标准值',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `operator` varchar(255) DEFAULT NULL COMMENT '操作符',
  `belonging_from` varchar(255) DEFAULT NULL COMMENT '所属表单',
  `belonging_process` varchar(255) DEFAULT NULL COMMENT '所属工序',
  `project_name` varchar(255) DEFAULT NULL COMMENT '项目名称',
  `project_code` varchar(255) DEFAULT NULL COMMENT '项目编号',
  `template_code` varchar(255) DEFAULT NULL COMMENT '模板配置编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `material` varchar(255) DEFAULT NULL COMMENT '物料',
  `is_display_item` int NOT NULL DEFAULT '1' COMMENT '是否呈现1是0否',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1841 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月工艺指标管理_工艺模板配置拓展表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_process_template_expand_delete`
--

DROP TABLE IF EXISTS `pro_process_template_expand_delete`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_process_template_expand_delete` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月工艺指标管理_工艺模板配置拓展表delete';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_process_upload_records`
--

DROP TABLE IF EXISTS `pro_process_upload_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_process_upload_records` (
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `template_code` varchar(255) DEFAULT NULL COMMENT '工艺模板编码',
  `process_indicator_name` varchar(255) DEFAULT NULL COMMENT '上传工艺指标名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月工艺指标管理_工艺指标上传记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_product_record`
--

DROP TABLE IF EXISTS `pro_product_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_product_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段任务编码对应工段任务表',
  `record_date` date DEFAULT NULL COMMENT '记录日期',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `record_code` varchar(255) DEFAULT NULL COMMENT '生产记录编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `state` int DEFAULT NULL COMMENT '记录状态',
  `inspection_code` varchar(255) DEFAULT NULL COMMENT '检验单号',
  `box_num` varchar(255) DEFAULT NULL COMMENT '精糖煮糖记录卸下箱号',
  `discharges` varchar(255) DEFAULT NULL COMMENT '精糖煮糖记录放料数量',
  `utility_time` varchar(255) DEFAULT NULL COMMENT '精糖煮糖记录实用时间',
  `end_time` datetime DEFAULT NULL COMMENT '精糖煮糖记录结束时间',
  `boil_class` varchar(255) DEFAULT NULL COMMENT '煮糖记录种类',
  `tank_num` varchar(255) DEFAULT NULL COMMENT '精糖煮糖记录罐号',
  `material_code` varchar(255) DEFAULT NULL COMMENT '物料编码关联物料数据表',
  `start_time` datetime DEFAULT NULL COMMENT '精糖煮糖记录开始时间',
  `boiled_code` varchar(255) DEFAULT NULL COMMENT '煮糖编号分蜜任务使用',
  `flag_quote` int DEFAULT '0' COMMENT '是否引用',
  `machine_code` varchar(1000) DEFAULT NULL COMMENT '机器编号',
  `produce_type` varchar(255) DEFAULT NULL COMMENT '产出成品',
  `molasses_type` varchar(255) DEFAULT NULL COMMENT '产出糖蜜',
  `pastes_type` varchar(255) DEFAULT NULL COMMENT '产出糖糊',
  `disable_type` varchar(255) DEFAULT NULL COMMENT '是否禁用',
  `end_time_discharge` varchar(255) DEFAULT NULL COMMENT '出料结束时间筛选中出料）',
  `starting_time_discharge` varchar(255) DEFAULT NULL COMMENT '出料开始时间（筛选中进料）',
  `sugar_number` varchar(255) DEFAULT NULL COMMENT '陈化糖斗白砂糖编号',
  `entry_exit_types` varchar(255) DEFAULT NULL COMMENT '出入类型(筛选中是否经陈化仓)单选',
  `page_type` varchar(255) DEFAULT NULL COMMENT '相同工段不同页面',
  `bucket_number` varchar(255) DEFAULT NULL COMMENT '糖斗编号（储糖斗）',
  `collecting_code` varchar(255) DEFAULT NULL COMMENT '领用编码',
  `machine_code_two` varchar(255) DEFAULT NULL COMMENT '多选编号2',
  `record_time` varchar(255) DEFAULT NULL COMMENT '记录时间',
  `check_material_type` varchar(255) DEFAULT NULL COMMENT '检验物料类型',
  `bag_bucket_number` varchar(255) DEFAULT NULL COMMENT '包装糖斗编号',
  `boiled_sugar_code` varchar(255) DEFAULT NULL COMMENT '煮糖/白砂糖编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1661 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_product_record_detail`
--

DROP TABLE IF EXISTS `pro_product_record_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_product_record_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `value` varchar(500) DEFAULT NULL COMMENT '值',
  `value_source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '单选框选项内容',
  `check_type` int DEFAULT NULL COMMENT '检查类型(1 下拉框; 2 输入; 3 搜索框; 4 数采类型; 5 主表参数；6 时间段)',
  `project` varchar(255) DEFAULT NULL COMMENT '生产项目名称',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `record_code` varchar(255) DEFAULT NULL COMMENT '生产记录编号',
  `sfm` varchar(255) DEFAULT NULL COMMENT '时分秒',
  `ymd` char(255) DEFAULT NULL COMMENT '年月日',
  `process_indicators` varchar(255) DEFAULT NULL COMMENT '关联工艺指标唯一标识',
  `field` varchar(255) DEFAULT NULL COMMENT '字段名',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `seqencing` int DEFAULT NULL COMMENT '排序',
  `inspection_field` varchar(255) DEFAULT NULL COMMENT '对应检验单field',
  `page_type` varchar(255) DEFAULT NULL COMMENT '相同工段不同页面',
  `flag_display` int DEFAULT NULL COMMENT '是否显示在主表',
  `mandatory` int DEFAULT NULL COMMENT '是否必填',
  `auto_calculate` int DEFAULT NULL COMMENT '是否自动计算(1 计算)',
  `flag_push` int DEFAULT NULL COMMENT '是否推送(lims)',
  `disable_field` varchar(255) DEFAULT NULL COMMENT 'lims对应检验项名称',
  `is_display_item` int NOT NULL DEFAULT '1' COMMENT '是否呈现1是0否',
  PRIMARY KEY (`id`),
  KEY `record_code` (`record_code`)
) ENGINE=InnoDB AUTO_INCREMENT=23683 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产记录参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_product_record_detail_config`
--

DROP TABLE IF EXISTS `pro_product_record_detail_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_product_record_detail_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `value_source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '输入框内容',
  `check_type` int DEFAULT NULL COMMENT '详情见字典',
  `project` varchar(255) DEFAULT NULL COMMENT '检查项目名称',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段任务编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `process_indicators` varchar(255) DEFAULT NULL COMMENT '关联工艺指标唯一标识',
  `field` varchar(255) DEFAULT NULL COMMENT '（数采/检验）名，如Mpa,KPa等',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `seqencing` int DEFAULT NULL COMMENT '排序',
  `inspection_field` varchar(255) DEFAULT NULL COMMENT '对应检验单field',
  `disable_field` varchar(255) DEFAULT NULL COMMENT '关联字段（用于表示禁用，启用）',
  `flag_display` int DEFAULT '0' COMMENT '是否显示在主表',
  `page_type` varchar(255) DEFAULT NULL COMMENT '两个以上页面',
  `mandatory` int DEFAULT NULL COMMENT '是否必填',
  `auto_calculate` int DEFAULT NULL COMMENT '新建字段',
  `flag_push` int DEFAULT '0' COMMENT '是否推送lims(1 推送 0 不推送)',
  `is_display_item` int DEFAULT '1' COMMENT '是否呈现1是0否',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3953 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产记录参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_product_record_table_expansion`
--

DROP TABLE IF EXISTS `pro_product_record_table_expansion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_product_record_table_expansion` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `supplier_code` varchar(255) DEFAULT NULL COMMENT '供应商编码',
  `use_num` varchar(255) DEFAULT NULL COMMENT '使用数量(kg)',
  `batch` varchar(255) DEFAULT NULL COMMENT '库存批次',
  `record_code` varchar(255) DEFAULT NULL COMMENT '生产记录编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `supplier_name` varchar(255) DEFAULT NULL COMMENT '供应商编码',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `specification_model` varchar(255) DEFAULT NULL COMMENT '规格型号',
  `accessory_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `accessory_code` varchar(255) DEFAULT NULL COMMENT '物料编码',
  `pick_code` varchar(255) DEFAULT NULL COMMENT '领用单号',
  `quantity` varchar(255) DEFAULT NULL COMMENT '使用数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产记录表格拓展表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_record_attached`
--

DROP TABLE IF EXISTS `pro_record_attached`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_record_attached` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_code` varchar(50) DEFAULT NULL COMMENT '生产记录编号',
  `inspection_code` varchar(50) DEFAULT NULL COMMENT '检验单号',
  `samplename` varchar(30) DEFAULT NULL COMMENT '物料名称',
  `bx` varchar(20) DEFAULT NULL COMMENT '锤度',
  `ap` varchar(20) DEFAULT NULL COMMENT '纯度',
  `iu` varchar(20) DEFAULT NULL COMMENT '色值',
  `inspection_time` varchar(30) DEFAULT NULL COMMENT '检验时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产记录附属表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_record_in`
--

DROP TABLE IF EXISTS `pro_record_in`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_record_in` (
  `notes` varchar(5000) DEFAULT NULL COMMENT '备注',
  `apply_by` varchar(50) DEFAULT NULL COMMENT '申请人员',
  `apply_code` varchar(20) DEFAULT NULL COMMENT '入库编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status` varchar(255) DEFAULT NULL COMMENT '状态(1 待提交；2 待审核；3 已审核；4 已驳回)',
  `audit_by` varchar(255) DEFAULT NULL COMMENT '审核人员',
  `record_type` varchar(255) DEFAULT NULL COMMENT '入库类型',
  `table_code` varchar(255) DEFAULT NULL COMMENT '库表编码',
  `apply_time` datetime DEFAULT NULL COMMENT '申请时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `shipments_code` varchar(255) DEFAULT NULL COMMENT '储运出库单号',
  `apply_workshop` varchar(255) DEFAULT NULL COMMENT '申请工段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库_入库记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_record_in_detail`
--

DROP TABLE IF EXISTS `pro_record_in_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_record_in_detail` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `notes` varchar(5000) DEFAULT NULL COMMENT '备注',
  `supplier_code` varchar(255) DEFAULT NULL COMMENT '供应商编码',
  `quantity` varchar(255) DEFAULT NULL COMMENT '数量',
  `batch` varchar(50) DEFAULT NULL COMMENT '批次',
  `accessory_code` varchar(50) DEFAULT NULL COMMENT '物料编号',
  `apply_code` varchar(255) DEFAULT NULL COMMENT '申请记录单号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `shipments_code` varchar(255) DEFAULT NULL COMMENT '储运出库单号',
  `shipments_time` datetime DEFAULT NULL COMMENT '出库时间',
  `is_apply` varchar(255) DEFAULT NULL COMMENT '是否为申请单(1为申请单，2为入库单)',
  `shipments_by` varchar(255) DEFAULT NULL COMMENT '出库人员',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库_入库记录详情表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_record_out`
--

DROP TABLE IF EXISTS `pro_record_out`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_record_out` (
  `notes` varchar(5000) DEFAULT NULL COMMENT '备注',
  `audit_by` varchar(50) DEFAULT NULL COMMENT '审核人员',
  `status` varchar(10) DEFAULT NULL COMMENT '状态(1 待提交；2 待审核；3 已审核(待入库)；4 已驳回；5 已入库)',
  `pick_type` varchar(10) DEFAULT NULL COMMENT '领用类型(1 生产领用； 2 盘盈；3 盘亏；4 退库)',
  `pick_workshop` varchar(50) DEFAULT NULL COMMENT '领用工段',
  `team` varchar(20) DEFAULT NULL COMMENT '班组',
  `pick_by` varchar(50) DEFAULT NULL COMMENT '领用人员',
  `pick_time` datetime DEFAULT NULL COMMENT '领用时间',
  `pick_code` varchar(20) DEFAULT NULL COMMENT '领用单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `table_code` varchar(255) DEFAULT NULL COMMENT '库表编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `storage_code` varchar(255) DEFAULT NULL COMMENT '储运入库单号',
  `apply_date` varchar(20) DEFAULT NULL COMMENT '申请日期',
  `apply_time` varchar(20) DEFAULT NULL COMMENT '申请时间',
  `quantity` varchar(50) DEFAULT NULL COMMENT '入仓数量(吨)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=697 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库_出库记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_record_out_detail`
--

DROP TABLE IF EXISTS `pro_record_out_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_record_out_detail` (
  `inventory_id` varchar(50) DEFAULT NULL COMMENT '库存记录id',
  `notes` varchar(5000) DEFAULT NULL COMMENT '备注',
  `quantity` varchar(50) DEFAULT NULL COMMENT '数量',
  `batch` varchar(20) DEFAULT NULL COMMENT '批次',
  `accessory_code` varchar(20) DEFAULT NULL COMMENT '物料编号',
  `pick_code` varchar(20) DEFAULT NULL COMMENT '领用单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `prd_code` varchar(255) DEFAULT NULL COMMENT '使用的生产记录编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=558 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库_出库明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_sale_order_info`
--

DROP TABLE IF EXISTS `pro_sale_order_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_sale_order_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `order_categories` varchar(50) DEFAULT NULL COMMENT '订单大类',
  `order_code` varchar(255) DEFAULT NULL COMMENT '销售订单编号',
  `produce_code` varchar(255) DEFAULT NULL COMMENT '生产订单编号',
  `remark` varchar(2000) DEFAULT NULL COMMENT '备注',
  `order_status` int DEFAULT '1' COMMENT '订单状态',
  `csr_sap` varchar(255) DEFAULT NULL COMMENT 'SAP客户编号',
  `delivery_end` date DEFAULT NULL COMMENT '交期止',
  `delivery_start` date DEFAULT NULL COMMENT '交期起',
  `campaign` varchar(255) DEFAULT NULL COMMENT '榨季',
  `sale_organization` varchar(255) DEFAULT NULL COMMENT '销售组织',
  `affiliation` varchar(255) DEFAULT NULL COMMENT '归属部门',
  `head` varchar(255) DEFAULT NULL COMMENT '负责人',
  `business_type` varchar(255) DEFAULT NULL COMMENT '业务类型',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `order_date` date DEFAULT NULL COMMENT '下单日期',
  `csr_name` varchar(255) DEFAULT NULL COMMENT '货主名称',
  `sold_party` varchar(255) DEFAULT NULL COMMENT '售达方',
  `order_type` varchar(50) DEFAULT NULL COMMENT '订单类型',
  `outbound` varchar(255) DEFAULT '0' COMMENT '订单出库量(t)',
  `output` varchar(255) DEFAULT '0' COMMENT '产出量(t)',
  `displacement` varchar(255) DEFAULT '0' COMMENT '排产量(t)',
  `demand` varchar(255) DEFAULT '0' COMMENT '需求量(t)',
  `flag_crm` int DEFAULT '0' COMMENT '该订单是否从crm系统同步 0/1',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=162 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产订单基础信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_sale_order_product`
--

DROP TABLE IF EXISTS `pro_sale_order_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_sale_order_product` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `pro_status` int DEFAULT NULL COMMENT '订单产品状态',
  `flag_aged` int DEFAULT NULL COMMENT '是否过陈化仓 0/1/2',
  `outbound` varchar(255) DEFAULT '0' COMMENT '订单出库量(t)',
  `output` varchar(255) DEFAULT '0' COMMENT '产出量(t)',
  `displacement` varchar(255) DEFAULT '0' COMMENT '排产量(t)',
  `demand` varchar(255) DEFAULT '0' COMMENT '需求量(t)',
  `unit` varchar(50) DEFAULT NULL COMMENT '单位',
  `norm` varchar(255) DEFAULT NULL COMMENT '规格',
  `pro_code` varchar(255) DEFAULT NULL COMMENT '产品编号 产品表id',
  `pro_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `csr_name` varchar(255) DEFAULT NULL COMMENT '客户名称',
  `order_pro_line` varchar(255) DEFAULT NULL COMMENT '订单产品行号',
  `order_code` varchar(255) DEFAULT NULL COMMENT '销售订单编号',
  `prd_code` varchar(255) DEFAULT NULL COMMENT '生产订单编号 关联生产订单表',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `flag_crm` int DEFAULT '0' COMMENT '该产品是否从crm同步的 0/1',
  `sold_party` varchar(255) DEFAULT NULL COMMENT '售达方',
  `business_type` varchar(255) DEFAULT NULL COMMENT '业务类型',
  `order_date` date DEFAULT NULL COMMENT '下单日期',
  `order_type` varchar(255) DEFAULT NULL COMMENT '订单类型',
  `delivery_end` varchar(255) DEFAULT NULL COMMENT '交期止',
  `delivery_start` date DEFAULT NULL COMMENT '交期起',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `outer_norm` varchar(255) DEFAULT NULL COMMENT '外包装规格',
  `inner_norm` varchar(255) DEFAULT NULL COMMENT '内包装规格',
  `prd_date` date DEFAULT NULL COMMENT '开始生产日期(计划同步日期)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=148 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单产品表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_shift_change_config`
--

DROP TABLE IF EXISTS `pro_shift_change_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_shift_change_config` (
  `value_source` varchar(500) DEFAULT NULL COMMENT '选框选项内容',
  `check_type` int DEFAULT NULL COMMENT '检查类型(1.单选;2.输入框;3.dcs采集数字框;4.上个班采集数字框;5.结果输入框禁用)',
  `project` varchar(255) DEFAULT NULL COMMENT '检查项目名称',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段任务编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `field` varchar(255) DEFAULT NULL COMMENT '字段名',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编码用于dcs采集',
  `formula` varchar(255) DEFAULT NULL COMMENT '公式字段，用于储存公式',
  `param` varchar(255) DEFAULT NULL COMMENT '参数字段，用于公式计算',
  `sequence` int DEFAULT NULL COMMENT '排序',
  `value` varchar(255) DEFAULT NULL COMMENT '默认值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=272 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交班记录参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_shift_change_detail`
--

DROP TABLE IF EXISTS `pro_shift_change_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_shift_change_detail` (
  `value` varchar(255) DEFAULT NULL COMMENT '选项值',
  `value_source` varchar(2000) DEFAULT NULL COMMENT '单选框选项内容',
  `check_type` int DEFAULT NULL COMMENT '检查类型(1.单选;2.输入框;3.dcs采集数字框;4.上个班采集数字框;5.结果输入框禁用)',
  `project` varchar(255) DEFAULT NULL COMMENT '检查项目名称',
  `record_code` varchar(255) DEFAULT NULL COMMENT '交班记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `field` varchar(255) DEFAULT NULL COMMENT '字段名',
  `param` varchar(255) DEFAULT NULL COMMENT '参数字段，用于公式计算',
  `formula` varchar(255) DEFAULT NULL COMMENT '公式字段，用于储存公式',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编码用于dcs采集',
  `extend_value` varchar(255) DEFAULT NULL COMMENT '用于拓展值',
  `sequence` int DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=743 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交班记录参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_shift_change_record`
--

DROP TABLE IF EXISTS `pro_shift_change_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_shift_change_record` (
  `hand_description` varchar(1000) DEFAULT NULL COMMENT '交班说明',
  `hand_by` varchar(255) DEFAULT NULL COMMENT '交班人员',
  `hand_code` varchar(255) DEFAULT NULL COMMENT '交班单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `hand_time` varchar(255) DEFAULT NULL COMMENT '交班时间',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段任务编码对应工段任务表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `hand_date` date DEFAULT NULL COMMENT '交班日期',
  `record_code` varchar(255) DEFAULT NULL COMMENT '生产记录编号',
  `actually_mud_read` varchar(50) DEFAULT NULL COMMENT '本班产量',
  `last_mud_read` varchar(50) DEFAULT NULL COMMENT '上班滤泥读数',
  `mud_read` varchar(50) DEFAULT NULL COMMENT '本班滤泥读数',
  `actually_class_water` varchar(255) DEFAULT NULL COMMENT '实际打水量',
  `last_class_water` varchar(50) DEFAULT NULL COMMENT '上班打水量读数',
  `class_water` varchar(50) DEFAULT NULL COMMENT '本班打水量读数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=263 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='交班记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_special_inspect_record`
--

DROP TABLE IF EXISTS `pro_special_inspect_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_special_inspect_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_code` varchar(50) DEFAULT NULL COMMENT '记录编号',
  `record_by` varchar(50) DEFAULT NULL COMMENT '记录人员',
  `team` varchar(20) DEFAULT NULL COMMENT '班组',
  `record_date` varchar(20) DEFAULT NULL COMMENT '记录日期(检查日期)',
  `workshop_code` varchar(20) DEFAULT NULL COMMENT '工段编码 关联工段信息uu code',
  `task_code` varchar(20) DEFAULT NULL COMMENT '任务编码(所属表单编码)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='特殊巡检记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_upload_file`
--

DROP TABLE IF EXISTS `pro_upload_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_upload_file` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `file_type` int DEFAULT NULL COMMENT '文件类型: 1 图片，2 文件，3全部',
  `busi_type` int DEFAULT NULL COMMENT '业务类型: 1 吸铁器巡检记录 2 喷码内包装 3 喷码外包装4.产品数据',
  `file_id` varchar(255) DEFAULT NULL COMMENT '文件id',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `source_code` varchar(20) DEFAULT NULL COMMENT '主信息编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='上传附件生产模块';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `pro_workshop_task_config`
--

DROP TABLE IF EXISTS `pro_workshop_task_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pro_workshop_task_config` (
  `task_name` varchar(255) DEFAULT NULL COMMENT '任务名称',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务编码',
  `workshop_code` varchar(255) DEFAULT NULL COMMENT '工段编码 关联工段信息uu_code',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `fieldRecords` varchar(255) DEFAULT NULL COMMENT '数采字段配置',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=107 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工段任务配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `production_inspection_record`
--

DROP TABLE IF EXISTS `production_inspection_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `production_inspection_record` (
  `inspect_time` varchar(255) DEFAULT NULL COMMENT '巡检时间',
  `workshop_code` varchar(255) DEFAULT NULL COMMENT '工段编码 关联工段信息uu code',
  `task_code` varchar(255) DEFAULT NULL COMMENT '任务编码',
  `inspect_by` varchar(50) DEFAULT NULL COMMENT '巡检人',
  `inspect_date` varchar(20) DEFAULT NULL COMMENT '巡检日期',
  `status` int DEFAULT NULL COMMENT '任务状态(1、待执行 2、待审核 3已审核)',
  `exception_description` varchar(1000) DEFAULT NULL COMMENT '异常说明',
  `plan_code` varchar(20) DEFAULT NULL COMMENT '巡检计划编码(关联巡检计划表)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspect_code` varchar(255) DEFAULT NULL COMMENT '巡检记录编号',
  `team` varchar(10) DEFAULT NULL COMMENT '班组',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `equipment_name` varchar(255) DEFAULT NULL COMMENT '设备名称',
  `verification_results_explain` varchar(255) DEFAULT NULL COMMENT '验证结果说明',
  `verification_results` varchar(255) DEFAULT NULL COMMENT '验证结果',
  `verification_by` varchar(255) DEFAULT NULL COMMENT '验证人员',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8911 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产巡检记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `production_inspection_related`
--

DROP TABLE IF EXISTS `production_inspection_related`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `production_inspection_related` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `project` varchar(255) DEFAULT NULL COMMENT '名称',
  `inspect_code` varchar(255) DEFAULT NULL COMMENT '巡检记录编号',
  `check_type` varchar(255) DEFAULT NULL COMMENT '类别(0.空1.输入框2.按钮3.搜索框，4.禁用框，5标题6.数采)',
  `field` varchar(255) DEFAULT NULL COMMENT '（数采/检验）名，如Mpa,KPa等',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段任务编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `sort` varchar(255) DEFAULT NULL COMMENT '排序',
  `inspection_field` varchar(255) DEFAULT NULL COMMENT '对应检验单field',
  `required` int DEFAULT NULL COMMENT '是否必填',
  `page_type` varchar(255) DEFAULT NULL COMMENT 'LIMS—json',
  `disable_field` varchar(40) DEFAULT NULL COMMENT 'LIMS物料名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检关联DSC和化验结果配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `production_inspection_related_value`
--

DROP TABLE IF EXISTS `production_inspection_related_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `production_inspection_related_value` (
  `inspection_field` varchar(255) DEFAULT NULL COMMENT '对应检验单field',
  `value` varchar(255) DEFAULT NULL COMMENT '值',
  `check_type` varchar(255) DEFAULT NULL COMMENT '类别',
  `project` varchar(255) DEFAULT NULL COMMENT '巡检名称',
  `inspect_code` varchar(255) DEFAULT NULL COMMENT '巡检记录编号',
  `field` varchar(255) DEFAULT NULL COMMENT '字段名',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `required` varchar(255) DEFAULT NULL COMMENT '是否必填',
  `page_type` varchar(255) DEFAULT NULL COMMENT '相同工段不同页面',
  `flag_push` varchar(255) DEFAULT NULL COMMENT '是否推送(lims)',
  `disable_field` varchar(255) DEFAULT NULL COMMENT '新建字段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=281 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检关联DSC和化验结果参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `production_patrol_inspection_record`
--

DROP TABLE IF EXISTS `production_patrol_inspection_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `production_patrol_inspection_record` (
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `suger_bag` varchar(255) DEFAULT NULL COMMENT '袋糖范围(kg)',
  `box_weight` varchar(255) DEFAULT NULL COMMENT '整箱/袋糖核重(kg)',
  `single_weight` varchar(255) DEFAULT NULL COMMENT '单袋糖核(g)',
  `washer_quantity` varchar(255) DEFAULT NULL COMMENT '称垫片数量',
  `positioning_wire_quantity` varchar(255) DEFAULT NULL COMMENT '称卡位铁丝数量',
  `screw_quantity` varchar(255) DEFAULT NULL COMMENT '称螺丝数量',
  `packing_code` varchar(255) DEFAULT NULL COMMENT '包装记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `record_code` varchar(255) DEFAULT NULL COMMENT '小包装巡检编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='包装_生产巡检记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `production_record_expansion`
--

DROP TABLE IF EXISTS `production_record_expansion`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `production_record_expansion` (
  `reason` varchar(2000) DEFAULT NULL COMMENT '原因',
  `stop_time` varchar(255) DEFAULT NULL COMMENT '停筛时间',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_code` varchar(255) DEFAULT NULL COMMENT '生产记录编号',
  `check_type` int DEFAULT NULL COMMENT '种类',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `sfm` varchar(255) DEFAULT NULL COMMENT '十分秒',
  `ymd` varchar(255) DEFAULT NULL COMMENT '年月日',
  `end_time` varchar(255) DEFAULT NULL COMMENT '结束时间（小时分钟）',
  `start_time` varchar(255) DEFAULT NULL COMMENT '开始时间（小时分钟）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=383 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产记录拓展表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `r3_molasses_db`
--

DROP TABLE IF EXISTS `r3_molasses_db`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `r3_molasses_db` (
  `record_time` datetime DEFAULT NULL COMMENT '记录时间',
  `output` varchar(60) DEFAULT NULL COMMENT '产量',
  `team` varchar(20) DEFAULT NULL COMMENT '班组',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `department` varchar(20) DEFAULT NULL COMMENT '部门(raw_sugar_dept)原糖部门',
  `zj` varchar(10) DEFAULT NULL COMMENT '榨季',
  `total_output` varchar(100) DEFAULT NULL COMMENT '榨季累计值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='R3糖蜜统计分析表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sampling_record`
--

DROP TABLE IF EXISTS `sampling_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sampling_record` (
  `sampling_time` datetime DEFAULT NULL COMMENT '采样时间',
  `charge_by` varchar(255) DEFAULT NULL COMMENT '负责人员',
  `sampling_by` varchar(255) DEFAULT NULL COMMENT '采样人员',
  `inkjet_code` varchar(255) DEFAULT NULL COMMENT '喷码编号',
  `sampling_weight` varchar(255) DEFAULT NULL COMMENT '取样重量(kg)',
  `model` varchar(255) DEFAULT NULL COMMENT '规格',
  `sampling_quantity` varchar(255) DEFAULT NULL COMMENT '采样数量(包)',
  `batch` varchar(255) DEFAULT NULL COMMENT '原料批次',
  `packing_code` varchar(255) DEFAULT NULL COMMENT '包装记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `team` varchar(255) DEFAULT NULL COMMENT '负责人员班组',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `type` varchar(255) DEFAULT NULL COMMENT '类型(1整采，2散采)',
  `pro_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  `pro_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `record_code` varchar(50) DEFAULT NULL COMMENT '取样记录编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='取样记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `scheduled_task`
--

DROP TABLE IF EXISTS `scheduled_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scheduled_task` (
  `end_time` varchar(50) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(50) DEFAULT NULL COMMENT '开始时间',
  `next_publish_time` datetime DEFAULT NULL COMMENT '下一次任务发布时间',
  `publish_time` datetime DEFAULT NULL COMMENT '任务发布时间',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `frequency` int DEFAULT NULL COMMENT '频率(1 不重复；2 每班；3 每天；4 每周；5 每月)',
  `plan_date` varchar(50) DEFAULT NULL COMMENT '计划日期',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '计划编码，关联计划表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `inspect_type` int DEFAULT '0' COMMENT '检查类型(0 设备巡检；1 生产巡检；2 设备点检 3 润滑)',
  `cycle_unit` varchar(255) DEFAULT NULL COMMENT '周期单位',
  `cycle` varchar(255) DEFAULT NULL COMMENT '周期',
  PRIMARY KEY (`id`),
  KEY `type` (`inspect_type`)
) ENGINE=InnoDB AUTO_INCREMENT=16781 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='待执行任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `scheduled_task_detail`
--

DROP TABLE IF EXISTS `scheduled_task_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scheduled_task_detail` (
  `date` varchar(200) DEFAULT NULL COMMENT '每月第几天',
  `week` varchar(10) DEFAULT NULL COMMENT '每周第几天',
  `end_time` varchar(20) DEFAULT NULL COMMENT '结束时间',
  `start_time` varchar(20) DEFAULT NULL COMMENT '开始时间',
  `frequency` int DEFAULT NULL COMMENT '频率(1 不重复；2 每天；3 每周；4 每月)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plan_code` varchar(50) DEFAULT NULL COMMENT '巡检计划编码，关联巡检计划表',
  `shift_code` varchar(50) DEFAULT NULL COMMENT '班次编码，关联班次信息表',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=529 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='巡检计划定时任务明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sealing_record_form`
--

DROP TABLE IF EXISTS `sealing_record_form`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sealing_record_form` (
  `result3` varchar(255) DEFAULT NULL COMMENT '选项3（1、正常 2、 异常）',
  `result2` varchar(255) DEFAULT NULL COMMENT '选项2（1、正常 2、 异常）',
  `result1` varchar(255) DEFAULT NULL COMMENT '选项1（1、正常 2、 异常）',
  `model` varchar(255) DEFAULT NULL COMMENT '规格',
  `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `product_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  `remark` varchar(1000) DEFAULT NULL COMMENT '备注',
  `record_date` varchar(255) DEFAULT NULL COMMENT '记录日期',
  `team` varchar(255) DEFAULT NULL COMMENT '班组',
  `record_by` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_code` varchar(255) DEFAULT NULL COMMENT '记录编号',
  `qualified` varchar(255) DEFAULT NULL COMMENT '是否合格(1、合格 2、不合格)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `raw_material_batch` varchar(30) DEFAULT NULL COMMENT '原料批次',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='密封记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shift_material_record`
--

DROP TABLE IF EXISTS `shift_material_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shift_material_record` (
  `record_date` varchar(255) DEFAULT NULL COMMENT '记录日期',
  `work_team` varchar(255) DEFAULT NULL COMMENT '班组',
  `record_person` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `material_record_code` varchar(255) DEFAULT NULL COMMENT '物料记录编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=156 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料交接记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shift_material_record_config`
--

DROP TABLE IF EXISTS `shift_material_record_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shift_material_record_config` (
  `sequence` int DEFAULT NULL COMMENT '排序',
  `value` varchar(255) DEFAULT NULL COMMENT '值',
  `flag_empty` int DEFAULT '0' COMMENT '是否为空0.否 1.是',
  `record_name` varchar(255) DEFAULT NULL COMMENT '记录名称',
  `record_type` int DEFAULT NULL COMMENT '记录类型(1.输入；2.下拉；3.日期；4.单选+输入；5.单选+输入+选择；)',
  `value_source` varchar(255) DEFAULT NULL COMMENT '选项内容',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `typesetting` int DEFAULT NULL COMMENT '排版（1.二分之一；2.三分之一；3.四分之一)',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `field` varchar(255) DEFAULT NULL COMMENT '（数采/检验）名，如Mpa,KPa等',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `mandatory` int DEFAULT '0' COMMENT '是否必填',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料交接记录配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shift_material_record_config_parameters`
--

DROP TABLE IF EXISTS `shift_material_record_config_parameters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shift_material_record_config_parameters` (
  `handover_code` varchar(255) DEFAULT NULL COMMENT '交接编码',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段编码',
  `typesetting` int DEFAULT NULL COMMENT '排版（1.二分之一；2.三分之一；3.四分之一)',
  `device_code` varchar(255) DEFAULT NULL COMMENT '设备编码',
  `devce_field` varchar(255) DEFAULT NULL COMMENT 'DCS字段',
  `sequence` int DEFAULT NULL COMMENT '排序',
  `value` varchar(255) DEFAULT NULL COMMENT '值',
  `flag_empty` int DEFAULT NULL COMMENT '是否为空0.否 1.是',
  `record_name` varchar(255) DEFAULT NULL COMMENT '记录名称',
  `record_type` int DEFAULT NULL COMMENT '记录类型(1.输入；2.下拉；3.日期；4.单选+输入；5.单选+输入+选择；)',
  `value_source` varchar(255) DEFAULT NULL COMMENT '选项内容',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `category` varchar(255) DEFAULT NULL COMMENT '罐/箱种类',
  `status` int DEFAULT NULL COMMENT '状态',
  `input_value` varchar(255) DEFAULT NULL COMMENT '记录输入值',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `mandatory` int DEFAULT '0' COMMENT '是否必填',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3483 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料交接页面参数';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shift_material_record_opening_period`
--

DROP TABLE IF EXISTS `shift_material_record_opening_period`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shift_material_record_opening_period` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `record_time` varchar(255) DEFAULT NULL COMMENT '记录时间',
  `record_date` varchar(255) DEFAULT NULL COMMENT '记录日期',
  `material_record_opening_code` varchar(255) DEFAULT NULL COMMENT '物料记录编号(开榨期)',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `task_code` varchar(255) DEFAULT NULL COMMENT '班组编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4526 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料交接开榨期';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shift_material_record_opening_period_config`
--

DROP TABLE IF EXISTS `shift_material_record_opening_period_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shift_material_record_opening_period_config` (
  `value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `record_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段编码',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `device_id` varchar(255) DEFAULT NULL COMMENT '设备编号',
  `field` varchar(255) DEFAULT NULL COMMENT '数采/检验）名，如Mpa,KPa等',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料交接开榨期配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shift_material_record_opening_period_detail`
--

DROP TABLE IF EXISTS `shift_material_record_opening_period_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shift_material_record_opening_period_detail` (
  `value` varchar(255) DEFAULT NULL COMMENT '默认值',
  `record_value` varchar(255) DEFAULT NULL COMMENT '物料值',
  `record_name` varchar(255) DEFAULT NULL COMMENT '物料名称',
  `material_record_opening_code` varchar(255) DEFAULT NULL COMMENT '物料记录编号(开榨期)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=219616 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料交接开榨期明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shift_material_record_opening_period_status`
--

DROP TABLE IF EXISTS `shift_material_record_opening_period_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shift_material_record_opening_period_status` (
  `status` varchar(255) DEFAULT NULL COMMENT '状态',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `task_code` varchar(255) DEFAULT NULL COMMENT '班组编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='物料交接开榨期状态';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `shutdown_record`
--

DROP TABLE IF EXISTS `shutdown_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shutdown_record` (
  `reason` varchar(255) DEFAULT NULL COMMENT '停榨时间',
  `synchronous` varchar(255) DEFAULT NULL COMMENT '同步化验部门(1、同步 2、不同步)',
  `losstime` varchar(255) DEFAULT NULL COMMENT '损失时间',
  `starttime` varchar(255) DEFAULT NULL COMMENT '开机时间',
  `stoptime` varchar(255) DEFAULT NULL COMMENT '停榨时间',
  `team` varchar(255) DEFAULT NULL COMMENT '发生班组',
  `recordby` varchar(255) DEFAULT NULL COMMENT '记录人员',
  `task_code` varchar(255) DEFAULT NULL COMMENT '工段编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='停榨记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `squeeze_report_collect`
--

DROP TABLE IF EXISTS `squeeze_report_collect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `squeeze_report_collect` (
  `dcs_count` varchar(50) DEFAULT NULL COMMENT 'dcs数量',
  `weighbridge_count` varchar(50) DEFAULT NULL COMMENT '地磅数量',
  `team` varchar(50) DEFAULT NULL COMMENT '班别',
  `time` varchar(50) DEFAULT NULL COMMENT '时间',
  `date` varchar(50) DEFAULT NULL COMMENT '日期',
  `squeeze` varchar(255) DEFAULT NULL COMMENT '榨季',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日榨量报表收集表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sugar_dissolving_team_index_record`
--

DROP TABLE IF EXISTS `sugar_dissolving_team_index_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sugar_dissolving_team_index_record` (
  `dry_sugar_total` varchar(255) DEFAULT NULL COMMENT '干滤泥糖度合计',
  `dry_sugar_sum` varchar(255) DEFAULT NULL COMMENT '干滤泥糖度总数',
  `dry_sugar_count` varchar(255) DEFAULT NULL COMMENT '干滤泥糖度合格数',
  `filtered_syrup_turbidity_total` varchar(255) DEFAULT NULL COMMENT '滤清糖浆浊度合计',
  `filtered_syrup_turbidity_sum` varchar(255) DEFAULT NULL COMMENT '滤清糖浆浊度总数',
  `filtered_turbidity_count` varchar(255) DEFAULT NULL COMMENT '滤清糖浆浊度合格数',
  `concentrated_color_total` varchar(255) DEFAULT NULL COMMENT '浓缩糖浆色值合计',
  `concentrated_color_sum` varchar(255) DEFAULT NULL COMMENT '浓缩糖浆色值总数',
  `concentrated_color_count` varchar(255) DEFAULT NULL COMMENT '浓缩糖浆色值合格数',
  `decolorized_color_total` varchar(255) DEFAULT NULL COMMENT '脱色糖浆色值合计',
  `decolorized_color_sum` varchar(255) DEFAULT NULL COMMENT '脱色糖浆色值总数',
  `decolorized_color_count` varchar(255) DEFAULT NULL COMMENT '脱色糖浆色值合格数',
  `ffiltered_color_total` varchar(255) DEFAULT NULL COMMENT '滤清糖浆色值合计',
  `filtered_color_sum` varchar(255) DEFAULT NULL COMMENT '滤清糖浆色值总数',
  `filtered_color_count` varchar(255) DEFAULT NULL COMMENT '滤清糖浆色值合格数',
  `hr_color_total` varchar(255) DEFAULT NULL COMMENT '洄溶糖浆色值合计',
  `hr_color_sum` varchar(255) DEFAULT NULL COMMENT '洄溶糖浆色值总数',
  `hr_color_count` varchar(255) DEFAULT NULL COMMENT '洄溶糖浆色值合格数',
  `pos_ph_total` varchar(255) DEFAULT NULL COMMENT '饱充后糖浆PH值合计',
  `pos_ph_sum` varchar(255) DEFAULT NULL COMMENT '饱充后糖浆PH值总数',
  `pos_ph_count` varchar(255) DEFAULT NULL COMMENT '饱充后糖浆PH值合格数',
  `ps_ph_total` varchar(255) DEFAULT NULL COMMENT '饱充前糖浆PH值合计',
  `ps_ph_sum` varchar(255) DEFAULT NULL COMMENT '饱充前糖浆PH值总数',
  `ps_ph_count` varchar(255) DEFAULT NULL COMMENT '饱充前糖浆PH值合格数',
  `concentrated_syrup_total` varchar(255) DEFAULT NULL COMMENT '浓缩糖浆锤度合计',
  `concentrated_syrup_sum` varchar(255) DEFAULT NULL COMMENT '浓缩糖浆锤度总数',
  `concentrated_syrup_count` varchar(255) DEFAULT NULL COMMENT '浓缩糖浆锤度合格数',
  `filtered_syrup_total` varchar(255) DEFAULT NULL COMMENT '滤清糖浆锤度合计',
  `filtered_syrup_sum` varchar(255) DEFAULT NULL COMMENT '滤清糖浆锤度总数',
  `filtered_syrup_count` varchar(255) DEFAULT NULL COMMENT '滤清糖浆锤度合格数',
  `hr_syrup_total` varchar(255) DEFAULT NULL COMMENT '洄溶糖浆锤度合计',
  `hr_syrup_sum` varchar(255) DEFAULT NULL COMMENT '洄溶糖浆锤度总数',
  `hr_syrup_count` int DEFAULT NULL COMMENT '洄溶糖浆锤度合格数',
  `team` varchar(10) DEFAULT NULL COMMENT '班组',
  `record_date` varchar(50) NOT NULL COMMENT '记录日期',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `month` varchar(255) DEFAULT NULL COMMENT '月份',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=221 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='溶糖各班组各项指标记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sugar_refining_water_dcs`
--

DROP TABLE IF EXISTS `sugar_refining_water_dcs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sugar_refining_water_dcs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `clarify_sewage` varchar(255) DEFAULT NULL COMMENT '澄清污水箱液位 (%)',
  `clarify_ml` varchar(255) DEFAULT NULL COMMENT '澄清600㎡热水箱液位 (%)',
  `two_drum_pressure` varchar(255) DEFAULT NULL COMMENT '2#蒸发罐气鼓压力',
  `one_drum_pressure` varchar(255) DEFAULT NULL COMMENT '1#蒸发罐气鼓压力',
  `two_evaporator_press` varchar(255) DEFAULT NULL COMMENT '2#蒸发罐液室压力',
  `one_evaporator_press` varchar(255) DEFAULT NULL COMMENT '1#蒸发罐液室压力',
  `two_purified_water` varchar(255) DEFAULT NULL COMMENT '2锅炉热水箱净化水液体',
  `one_purified_water` varchar(255) DEFAULT NULL COMMENT '1锅炉热水箱净化水液体',
  `purified_water` varchar(255) DEFAULT NULL COMMENT '一部净化水流量',
  `refine_sugar_usage` varchar(255) DEFAULT NULL COMMENT '炼糖期用量累计',
  `squeeze_usage` varchar(255) DEFAULT NULL COMMENT '压榨期用量累计',
  `duty_usage_accumulate` varchar(255) DEFAULT NULL COMMENT '当天用量累计',
  `duty_usage` varchar(255) DEFAULT NULL COMMENT '当班用量',
  `record_date` varchar(255) DEFAULT NULL COMMENT '记录时间',
  `record_day` varchar(255) DEFAULT NULL COMMENT '记录日期',
  `code` varchar(255) DEFAULT NULL COMMENT '炼糖煮水编号',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `refine_sugar_usage_sum` varchar(255) DEFAULT NULL COMMENT '炼糖期用量累计之和',
  `squeeze_usage_sum` varchar(255) DEFAULT NULL COMMENT '压榨期用量累计之和',
  `duty_usage_accumulate_sum` varchar(255) DEFAULT NULL COMMENT '当天用量累计之和',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=447 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='炼糖煮水DCS';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `test`
--

DROP TABLE IF EXISTS `test`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `test` (
  `parts_name` varchar(255) DEFAULT NULL COMMENT '新建字段',
  `parent_id` varchar(255) DEFAULT NULL COMMENT '新建字段',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='测试1011';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `weighbridge_gather_info`
--

DROP TABLE IF EXISTS `weighbridge_gather_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `weighbridge_gather_info` (
  `factory` varchar(255) DEFAULT NULL COMMENT '工厂',
  `net_weight` int DEFAULT NULL COMMENT '净重(kg)',
  `master_name` varchar(255) DEFAULT NULL COMMENT '蔗主名',
  `master_code` varchar(255) DEFAULT NULL COMMENT '蔗主码',
  `driver` varchar(255) DEFAULT NULL COMMENT '司机',
  `vehicle_number` varchar(255) DEFAULT NULL COMMENT '车号',
  `code` varchar(255) DEFAULT NULL COMMENT '过磅序号 编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `squeeze` varchar(255) DEFAULT NULL COMMENT '榨季',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1213 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='地磅信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `white_sugar_packing_detail_report`
--

DROP TABLE IF EXISTS `white_sugar_packing_detail_report`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `white_sugar_packing_detail_report` (
  `packing_quantity` varchar(255) DEFAULT NULL COMMENT '数量(吨)',
  `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
  `product_code` varchar(255) DEFAULT NULL COMMENT '产品编号',
  `shift_name` varchar(255) NOT NULL COMMENT '班次',
  `packing_date` date NOT NULL COMMENT '包装日期',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='白砂糖装包工段明细报表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wms_inventory_details`
--

DROP TABLE IF EXISTS `wms_inventory_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_details` (
  `inventory_quantity` decimal(14,2) NOT NULL COMMENT '物料库存数量',
  `accessory_name` varchar(40) DEFAULT NULL COMMENT '物料名称',
  `accessory_code` varchar(60) DEFAULT NULL COMMENT '物料编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `model` varchar(40) DEFAULT NULL COMMENT '产品规格',
  `warehouse_name` varchar(40) DEFAULT NULL COMMENT '库位名称',
  `warehouse_code` varchar(40) DEFAULT NULL COMMENT '库位编码',
  `supplier_code` varchar(50) DEFAULT NULL COMMENT '供应商编码',
  `supplier_name` varchar(40) DEFAULT NULL COMMENT '供应商名称',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `batch` varchar(40) DEFAULT NULL COMMENT '库存批次',
  `product_grade` varchar(20) DEFAULT NULL COMMENT '等级',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='生产WMS库存明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wms_inventory_info`
--

DROP TABLE IF EXISTS `wms_inventory_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `whin_order_number` varchar(50) NOT NULL COMMENT '入库单号',
  `number` double(12,2) NOT NULL COMMENT '数量',
  `loc_info_code` varchar(50) NOT NULL COMMENT '库位',
  `material_code` varchar(50) NOT NULL COMMENT '物料编码',
  `flag_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `wh_info_id` int NOT NULL COMMENT '二级库id',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库库存信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wms_inventory_turnover`
--

DROP TABLE IF EXISTS `wms_inventory_turnover`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_inventory_turnover` (
  `location_code` varchar(64) NOT NULL COMMENT '库位',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `wh_info_id` int NOT NULL COMMENT '仓库信息主键',
  `flag_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `material_code` varchar(50) NOT NULL COMMENT '物料编号',
  `tur_type` varchar(10) NOT NULL COMMENT '流转类型',
  `number` int NOT NULL COMMENT '流转数量',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库库存流转';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wms_location_info`
--

DROP TABLE IF EXISTS `wms_location_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_location_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `location_type` int DEFAULT NULL COMMENT '库位类型',
  `ori_coordinate` varchar(50) DEFAULT NULL COMMENT '起始仓位',
  `occ_status` int DEFAULT '0' COMMENT '锁定状态（0-未锁定，1-已锁定）',
  `occ_number` int DEFAULT '0' COMMENT '占位数量',
  `name` varchar(50) NOT NULL COMMENT '库位名称',
  `code` varchar(20) NOT NULL COMMENT '库位编码',
  `flag_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `warehouse_id` int NOT NULL COMMENT '二级库id',
  `coordinate` varchar(50) DEFAULT NULL COMMENT '坐标',
  `sort_value` int DEFAULT NULL COMMENT '排序值',
  `threshold` double(52,2) DEFAULT NULL COMMENT '阈值',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库库位信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `wms_warehouse_info`
--

DROP TABLE IF EXISTS `wms_warehouse_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wms_warehouse_info` (
  `line_edge_library` varchar(10) DEFAULT NULL COMMENT '线边库',
  `temporary_release` varchar(10) DEFAULT NULL COMMENT '半成品暂放',
  `sample_draft` varchar(10) DEFAULT NULL COMMENT '样稿',
  `sample` varchar(10) DEFAULT NULL COMMENT '样品',
  `plate_material` varchar(10) DEFAULT NULL COMMENT '版材',
  `entrust` varchar(10) DEFAULT NULL COMMENT '委托',
  `waste` varchar(10) DEFAULT NULL COMMENT '废品',
  `finished_product` varchar(10) DEFAULT NULL COMMENT '成品',
  `half_finished_product` varchar(10) DEFAULT NULL COMMENT '半成品',
  `accessories` varchar(10) DEFAULT NULL COMMENT '辅料',
  `paper` varchar(10) DEFAULT NULL COMMENT '纸张',
  `department` varchar(64) DEFAULT NULL COMMENT '部门',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `update_by` varchar(50) NOT NULL COMMENT '修改人',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `name` varchar(50) NOT NULL COMMENT '库房名称',
  `flag_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `wh_type_id` int NOT NULL COMMENT '库房属性',
  `status` varchar(10) NOT NULL COMMENT '状态',
  `wh_admin` varchar(100) DEFAULT NULL COMMENT '所属角色',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='二级库信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `year_plan_info`
--

DROP TABLE IF EXISTS `year_plan_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `year_plan_info` (
  `description` varchar(5000) DEFAULT NULL COMMENT '项目',
  `type` varchar(255) DEFAULT NULL COMMENT '类别',
  `subType` varchar(255) DEFAULT NULL COMMENT '子类',
  `unit` varchar(255) DEFAULT NULL COMMENT '单位',
  `number` varchar(255) DEFAULT NULL COMMENT '值',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `serial` varchar(255) DEFAULT NULL COMMENT '新建字段',
  `subSubType` varchar(255) DEFAULT NULL COMMENT '孙子类',
  `record_id` varchar(255) DEFAULT NULL COMMENT '关联年计划上传记录id',
  `domain_id` varchar(50) DEFAULT NULL COMMENT '域id',
  `row4` varchar(255) DEFAULT NULL COMMENT '列4',
  PRIMARY KEY (`id`),
  KEY `plan_year` (`record_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13389 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='年计划详情展示';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-19 10:00:01
