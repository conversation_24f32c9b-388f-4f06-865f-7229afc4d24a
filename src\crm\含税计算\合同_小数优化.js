var tax = '含税'
var abs_tax_rate = Math.abs(tax_rate)
var exchange_rate = Number(exchange_rate).toFixed(6)
var product_quantity = Number(record.product_quantity)

if (tax == '含税') {
    // 含税销售单价(报价币种)  6位小数
    record['unit_price_usd'] = Number(record['unit_price_usd']).toFixed(6)
    // 含税销售金额(报价币种) = 含税销售单价(报价币种)*数量
    record['sales_amount_usd'] = (Number(record['unit_price_usd']) * product_quantity).toFixed(2)
    // 含税销售金额(RMB) = 含税销售金额(报价币种) * 汇率 
    record['sales_amount'] = (Number(record['sales_amount_usd']) * exchange_rate).toFixed(2)
    // 含税销售单价(RMB) = 含税销售金额(RMB)/数量   6位小数
    record['unit_price'] = (Number(record['sales_amount']) / product_quantity).toFixed(6)
    // 不含税销售金额(RMB) = 含税销售金额(RMB) / (1 + 税率)
    record['sales_amount_excluding'] = (Number(record['sales_amount']) / (1 + abs_tax_rate / 100)).toFixed(2)
    //不含税销售金额(报价币种) = 不含税销售金额(RMB) / 汇率
    record['sales_amount_excluding_usd'] = (Number(record['sales_amount_excluding']) / exchange_rate).toFixed(2)
    //不含税销售单价(报价币种)=不含税销售金额(报价币种)/数量 保留6位小数
    record['unit_price_excluding_usd'] = (Number(record['sales_amount_excluding_usd']) / product_quantity).toFixed(6)
    // 不含税销售单价(RMB) = 不含税销售金额(RMB) / 数量      保留6位小数
    record['unit_price_excluding'] = (Number(record['sales_amount_excluding']) / product_quantity).toFixed(6)
    //税额=含税销售金额(RMB)-不含税销售金额(RMB)
    record['tax_diff'] = (Number(record['sales_amount']) - Number(record['sales_amount_excluding'])).toFixed(2)
} else {
    //不含税销售单价(报价币种) 保留6位小数
    record['unit_price_excluding_usd'] = Number(record['unit_price_excluding_usd']).toFixed(6)
    // 不含税销售金额(报价币种) =  不含税销售单价(报价币种)*数量
    record['sales_amount_excluding_usd'] = (Number(record['unit_price_excluding_usd']) * product_quantity).toFixed(2)
    // 不含税销售金额(RMB) = 不含税销售金额(报价币种) * 汇率
    record['sales_amount_excluding'] = (Number(record['sales_amount_excluding_usd']) * exchange_rate).toFixed(2)
    // 不含税销售单价(RMB) = 不含税销售金额(RMB) / 数量      保留6位小数
    record['unit_price_excluding'] = (Number(record['sales_amount_excluding']) / product_quantity).toFixed(6)
    //税额=不含税销售金额(RMB)*税率
    record['tax_diff'] = (Number(record['sales_amount_excluding']) * abs_tax_rate / 100).toFixed(2)
    // 含税销售金额(RMB) = 不含税销售金额(RMB) + 税额
    record['sales_amount'] = (Number(record['sales_amount_excluding']) + Number(record['tax_diff'])).toFixed(2)
    // 含税销售金额(报价币种) = 含税销售金额(RMB) / 汇率
    record['sales_amount_usd'] = (Number(record['sales_amount']) / exchange_rate).toFixed(2)
    // 含税销售单价(报价币种) = 含税销售金额(报价币种)/数量  6位小数
    record['unit_price_usd'] = (Number(record['sales_amount_usd']) / product_quantity).toFixed(6)
    // 含税销售单价(RMB) = 含税销售金额(RMB)/数量  6位小数
    record['unit_price'] = (Number(record['sales_amount']) / product_quantity).toFixed(6)
}

console.log("record", JSON.stringify(record))
return record

