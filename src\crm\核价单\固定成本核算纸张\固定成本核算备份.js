var materialResult = JSON.parse(JSON.stringify(obj.materialResult))
var paperlArr = []
var craftArr = JSON.parse(JSON.stringify(obj.craftArr)) // 传进来的参数工艺路线
var inCarftArr = [] //需要存核价单的工艺路线
var bjArr = []

//处理纸张成本
var parent_code = ""
for (var x = 0; x < materialResult.length; x++) {
  if (materialResult[x].categroy == 1) {
    //处理纸张物价
    for (var y = 0; y < crm_price_manager.length; y++) {
      if (materialResult[x].material_code == crm_price_manager[y].material_code) {
        materialResult[x]['selling_price'] = (crm_price_manager[y].selling_price*1).toFixed(4)
        break
      }
    }
    if (!materialResult[x].selling_price) {
      materialResult[x]['selling_price'] = 0
    }
    //处理分切尺寸
    if (materialResult[x].cut_size) {
    var cutArr = materialResult[x].cut_size.split("*")
    materialResult[x]['fqcc'] = cutArr[0]*1*cutArr[1]*1
  }else{
    materialResult[x]['fqcc'] = 0
  }
    //处理开数
    if (materialResult[x].pushing) {
      var pushing = materialResult[x].pushing
      var kaishu = null
      if (pushing.indexOf(",") != -1) {
        var arr = pushing.split(",")
        for (var y = 0; y < arr.length; y++) {
          var index = arr[y].indexOf("(")
          var index2 = arr[y].indexOf(")")
           kaishu += arr[y].substring(index+1,index2)*1
        }
      }else{
          var index = pushing.indexOf("(")
          var index2 = pushing.indexOf(")")
          kaishu = pushing.substring(index+1,index2)*1
      }
      materialResult[x]['kaishu'] = kaishu
    }else{
      materialResult[x]['kaishu'] = 1
    }
    if (materialResult[x].parent_code != parent_code) {
      paperlArr.push(materialResult[x])
    }
    parent_code = materialResult[x].parent_code
  }
  if (materialResult[x].categroy == 3 || materialResult[x].categroy == 0 || materialResult[x].categroy == 7) {
    bjArr.push(materialResult[x])
  }
} 
var first_out_of_book = 0
var max_out_of_book = 0
var multi_product = {};
for (var i = 0; i < craftArr.length; i++) {
  if(craftArr[i].sub_code){
    if (craftArr[i].process_type == "1") {
      if(!multi_product[craftArr[i].sub_code]){
        multi_product[craftArr[i].sub_code]=0
      }
    if (craftArr[i].out_of_book) {
      if (Number(craftArr[i].out_of_book) > multi_product[craftArr[i].sub_code]) {
        multi_product[craftArr[i].sub_code] = Number(craftArr[i].out_of_book)
      }
    }
  }
  }else{
    if (craftArr[i].process_type == "1") {
    if (craftArr[i].out_of_book) {
      if (Number(craftArr[i].out_of_book) > max_out_of_book) {
        max_out_of_book = Number(craftArr[i].out_of_book)
      }
    }
  }
  }
  
}
for (var x = 0; x < craftArr.length ; x++) {
  var obj = {}
  obj['parent_code'] = craftArr[x].parent_code  //parent_code
  obj['categroy'] = "10"  //categroy
  obj['craft_name'] = craftArr[x].craft_name  //工艺 
  obj['resource'] = craftArr[x].work_code  //资源
  obj['work_unit'] = craftArr[x].operation_unit  //作业单位
  obj['minimum_operation_cost'] = craftArr[x].minimum_consumption  //工序最低消耗
  obj['fixed_difficulty_cost'] = craftArr[x].fixed_consumption  //工序作业难度消耗
  obj['total_fixed_cost'] = obj['minimum_operation_cost']*1 + obj['fixed_difficulty_cost']*1  //总固定消耗
  if (x == 0) {
    first_out_of_book = craftArr[x].out_of_book //出本
  }
  if (craftArr[x].process_type == "1") {   //大张工序
    if(craftArr[x].sub_code){
       obj['operation_joint_number'] = multi_product[craftArr[x].sub_code];
    }else{
      obj['operation_joint_number'] = max_out_of_book //出本
    }
    for (var y = 0; y < paperlArr.length; y++) {
      if (craftArr[x].parent_code == paperlArr[y].parent_code) {
        if(paperlArr[y].main_class == "卷筒"){
          obj['folded_paper_quantity'] = ((Number(paperlArr[y].fqcc)/1000000)*Number(paperlArr[y].gram_weight)/1000)/Number(paperlArr[y].kaishu) * Number(obj.total_fixed_cost)
        }else if (paperlArr[y].main_class == "平张") {
          obj['folded_paper_quantity'] = Number(obj.total_fixed_cost)/Number(paperlArr[y].kaishu)
        }
        obj['folded_paper_quantity'] = obj['folded_paper_quantity'].toFixed(11)
        obj['material_price'] = (paperlArr[y].selling_price*1).toFixed(4)
        obj['paper_cutting_number'] = paperlArr[y].kaishu
        break
      } 
    }
  }else{ 
    obj['operation_joint_number'] = craftArr[x].out_of_book //小张工序
    if(craftArr[x].sub_code){
       max_out_of_book = multi_product[craftArr[x].sub_code];
    }
    for (var y = 0; y < paperlArr.length; y++) {
      if (craftArr[x].parent_code == paperlArr[y].parent_code) {
        if(paperlArr[y].main_class == "卷筒"){
          obj['folded_paper_quantity'] = ((Number(paperlArr[y].fqcc)/1000000) *Number(paperlArr[y].gram_weight)/1000)/Number(paperlArr[y].kaishu)/max_out_of_book * Number(obj.total_fixed_cost)
          //obj['folded_paper_quantity'] = ((paperlArr[y].fqcc/1000000) *paperlArr[y].gram_weight/1000)/paperlArr[y].kaishu/first_out_of_book * obj.total_fixed_cost
        }else if (paperlArr[y].main_class == "平张") {
          obj['folded_paper_quantity'] = Number(obj.total_fixed_cost)/Number(paperlArr[y].kaishu)/max_out_of_book
          //obj['folded_paper_quantity'] = obj.total_fixed_cost/paperlArr[y].kaishu/first_out_of_book
        }
        obj['folded_paper_quantity'] = obj['folded_paper_quantity'].toFixed(11)
        obj['material_price'] = (paperlArr[y].selling_price*1).toFixed(4)
         obj['paper_cutting_number'] = paperlArr[y].kaishu
        break
      }
    }
  }
  obj['amount'] = (obj.folded_paper_quantity * 1) * (obj.material_price*1)
  obj['amount'] = (obj.amount*1).toFixed(12)
  obj['process_type'] = craftArr[x].process_type
  inCarftArr.push(obj)
}
var inArr = []
inArr = inArr.concat(paperlArr)
inArr = inArr.concat(inCarftArr)
  inCarftArr.push(obj)
inArr = inArr.concat(bjArr) 
for(var k=0;k<inArr.length;k++){
  inArr[k].quotation_code = quotation_code;
  inArr[k].new_version = new_version;
  inArr[k].version = "1.0";
}
return inArr