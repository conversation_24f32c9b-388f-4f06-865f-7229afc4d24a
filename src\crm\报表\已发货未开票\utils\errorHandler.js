class ErrorHandler {
    static async wrapAsync(fn) {
        try {
            await fn();
        } catch (error) {
            console.error('Error occurred:', error);
            throw error;
        }
    }

    static logError(error, context = '') {
        const timestamp = new Date().toISOString();
        const errorMessage = `[${timestamp}] ${context}: ${error.message}\n${error.stack}`;
        console.error(errorMessage);
        // 这里可以添加写入日志文件的逻辑
    }
}

module.exports = ErrorHandler; 