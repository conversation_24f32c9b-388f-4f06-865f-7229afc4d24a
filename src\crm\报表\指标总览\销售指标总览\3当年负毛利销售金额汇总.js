var data_arr = [
    { year: 2024, month: '01', total_profit_amount: Math.floor(Math.random() * -500000) },
    { year: 2024, month: '02', total_profit_amount: Math.floor(Math.random() * -450000) },
    { year: 2024, month: '03', total_profit_amount: Math.floor(Math.random() * -400000) },
    { year: 2024, month: '04', total_profit_amount: Math.floor(Math.random() * -350000) },
    { year: 2024, month: '05', total_profit_amount: Math.floor(Math.random() * -300000) },
    { year: 2024, month: '06', total_profit_amount: Math.floor(Math.random() * -250000) },
    { year: 2024, month: '07', total_profit_amount: Math.floor(Math.random() * -200000) },
    { year: 2024, month: '08', total_profit_amount: Math.floor(Math.random() * -150000) },
    { year: 2024, month: '09', total_profit_amount: Math.floor(Math.random() * -100000) },
    { year: 2024, month: '10', total_profit_amount: Math.floor(Math.random() * -50000) },
    { year: 2024, month: '11', total_profit_amount: Math.floor(Math.random() * -40000) },
    { year: 2024, month: '12', total_profit_amount: Math.floor(Math.random() * -30000) },
    { year: 2025, month: '01', total_profit_amount: Math.floor(Math.random() * -20000) },
    { year: 2025, month: '02', total_profit_amount: Math.floor(Math.random() * -10000) },
    { year: 2025, month: '03', total_profit_amount: -225387.69 },
    { year: 2025, month: '04', total_profit_amount: Math.floor(Math.random() * -15000) },
    { year: 2025, month: '05', total_profit_amount: Math.floor(Math.random() * -25000) },
    { year: 2025, month: '06', total_profit_amount: Math.floor(Math.random() * -35000) },
    { year: 2025, month: '07', total_profit_amount: Math.floor(Math.random() * -45000) },
    { year: 2025, month: '08', total_profit_amount: Math.floor(Math.random() * -55000) },
    { year: 2025, month: '09', total_profit_amount: Math.floor(Math.random() * -65000) },
    { year: 2025, month: '10', total_profit_amount: Math.floor(Math.random() * -75000) },
    { year: 2025, month: '11', total_profit_amount: Math.floor(Math.random() * -85000) },
    { year: 2025, month: '12', total_profit_amount: Math.floor(Math.random() * -95000) }
]

var years = data_arr.map(function(item) {
    return item.year;
}).filter(function(value, index, self) {
    return self.indexOf(value) === index;
}).sort();

var option = {
    title: {
        text: "当年负毛利销售金额汇总"
    },
    tooltip: {
        trigger: "axis"
    },
    legend: {
        data: years.map(function(year) {
            return year + '年';
        })
    },
    xAxis: {
        type: "category",
        data: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]
    },
    yAxis: {
        type: "value",
        name: "金额",
        inverse: true,
        axisLabel: {
            formatter: "{value}"
        }
    },
    series: years.map(function(year, index) {
        return {
            name: year + '年',
            type: "bar",
            data: data_arr.filter(function(item) {
                return item.year === year;
            }).map(function(item) {
                return item.total_profit_amount;
            }),
            itemStyle: {
                color: index === 0 ? '#5470c6' : '#66ccbb'
            }
        };
    })
};

return option