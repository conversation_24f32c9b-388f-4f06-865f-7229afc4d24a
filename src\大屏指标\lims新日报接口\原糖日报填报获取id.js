var temp_data={
    "Fields": [
        {
            "Name": "ID",
            "Type": 3
        },
        {
            "Name": "NIANDUDM",
            "Type": 1
        },
        {
            "Name": "GongSiDM",
            "Type": 3
        },
        {
            "Name": "RQ",
            "Type": 3
        },
        {
            "Name": "XIAOQI",
            "Type": 1
        },
        {
            "Name": "HAO",
            "Type": 1
        }
    ],
    "Data": "[[\"7bc5032f-0359-11f0-9caf-00163e0ac7c1\",2025,\"FNR\",\"2025-03-17\",3,21],[\"7bc5032f-0359-11f0-9caf-00163e0ac7c1\",2025,\"FNR\",\"2025-03-17\",3,21]]",
    "TimeCost": 33,
    "DataCnt": 2,
    "ErrorCode": "",
    "RenderedSql": "",
    "BaseResp": {
        "StatusMessage": "",
        "StatusCode": 0
    }
}
var date_str='2025-03-17'
function transformData(data) {
    // 解析 Data 字符串为 JavaScript 数组
    var parsedData = JSON.parse(data.Data);
    
    // 将每行数据转换为对象数组，每个对象包含多个key-value对
    return parsedData.map(function(row) {
        // 将每行转换为多个key-value对象
        return data.Fields.map(function(field, index) {
            return {
                key: field.Name,
                value: row[index]
            };
        });
    });
}
var trans_data=transformData(temp_data);
//遍历trans_data,获取RQ=date_str的对象,并获取ID
var target_obj = [];
for(var i = 0; i < trans_data.length; i++) {
    var obj = trans_data[i];
    var found = false;
    for(var j = 0; j < obj.length; j++) {
        var item = obj[j];
        if(item.key == 'RQ' && item.value == date_str) {
            found = true;
            break;
        }
    }
    if(found) {
        target_obj = obj;
        break;
    }
}

var id_obj = {};
for(var k = 0; k < target_obj.length; k++) {
    var item = target_obj[k];
    if(item.key == 'ID') {
        id_obj = item;
        break;
    }
}

var id = id_obj.value || '';
console.log(JSON.stringify(id));
//获取指定日期id
// 现在你可以使用 trans_data 数组来进行后续的操作，例如渲染到页面或进行其他计算。