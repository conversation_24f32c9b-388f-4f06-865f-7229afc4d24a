var crm_fixed_cost_auxiliary_ctp=[{"edition_price":"0","amount":"0","craft_name":"印刷","create_time":"2025-03-22 14:41:40","categroy":"9","edition_name":"搭印产品共用版","version":"1.0","edition_categroy":"CTP版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","edition_code":"XGP-搭印产品共用版-BBV1","edition_quantity":"0","edition_type":"印版","parent_code":"ZDQX0001278","quotation_version":"3","id":90346,"update_by":"1867047194732875781","flag_deleted":0},{"edition_price":"0","amount":"0","craft_name":"印刷","create_time":"2025-03-22 14:41:40","categroy":"9","edition_name":"搭印产品共用版","version":"1.0","edition_categroy":"CTP版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","edition_code":"XGP-搭印产品共用版-BZV1","edition_quantity":"0","edition_type":"印版","parent_code":"ZDQX0001278","quotation_version":"3","id":90347,"update_by":"1867047194732875781","flag_deleted":0},{"edition_price":"26.1416","amount":"130.7080","craft_name":"印刷","create_time":"2025-03-22 14:41:40","categroy":"9","edition_name":"X盒-SL-241024RC38-OBALEN-星河入眸绿CTP版","version":"1.0","edition_categroy":"CTP版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","edition_code":"核价-ZDQX0001276-BZV1","edition_quantity":"5","edition_type":"印版","parent_code":"ZDQX0001278","quotation_version":"3","id":90348,"update_by":"1867047194732875781","flag_deleted":0},{"edition_price":"26.1416","amount":"26.1416","craft_name":"印刷","create_time":"2025-03-22 14:41:40","categroy":"9","edition_name":"X盒-SL-241024RC38-OBALEN-星河入眸绿CTP版","version":"1.0","edition_categroy":"CTP版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","edition_code":"核价-ZDQX0001276-BBV1","edition_quantity":"1","edition_type":"印版","parent_code":"ZDQX0001278","quotation_version":"3","id":90349,"update_by":"1867047194732875781","flag_deleted":0},{"create_time":"2025-03-22 14:41:40","categroy":"3","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","parent_code":"0","quotation_version":"3","id":90350,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","material_code":"ZDQX0001278","flag_deleted":0}]
var crm_fixed_cost_labor=[{"total_preparation_time":"0.5","craft_name":"原料分切","repair_charge":"11.25","depreciation_expense_other":"15","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"0.5","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":282647,"update_by":"1867047194732875781","depreciation_expense_buildings":"15","executive_salary":"15","electric_charge":"7.5","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPFQZX","storage_charge":"3.75","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"80","version":"1.0","fixed_manufacturing_cost":"75","water_rate":"7.5","fixed_labor":"40","resource_center_fixed_manufacturing_cost":"150"},{"total_preparation_time":"1.5","craft_name":"印刷","repair_charge":"180","depreciation_expense_other":"240","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"1.5","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":282648,"update_by":"1867047194732875781","depreciation_expense_buildings":"240","executive_salary":"240","electric_charge":"120","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPYSWSYSZX","storage_charge":"60","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"200","version":"1.0","fixed_manufacturing_cost":"1200","water_rate":"120","fixed_labor":"300","resource_center_fixed_manufacturing_cost":"800"},{"total_preparation_time":"0.5","craft_name":"印刷","repair_charge":"52.5","depreciation_expense_other":"70","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"1.5","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":282649,"update_by":"1867047194732875781","depreciation_expense_buildings":"70","executive_salary":"70","electric_charge":"35","flag_deleted":0,"operation_difficulty_fixed_time":"-1","production_resource":"GPYS4SZX","storage_charge":"17.5","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"200","version":"1.0","fixed_manufacturing_cost":"350","water_rate":"35","fixed_labor":"100","resource_center_fixed_manufacturing_cost":"700"},{"total_preparation_time":"1","craft_name":"覆膜","repair_charge":"24","depreciation_expense_other":"32","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"1","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":282650,"update_by":"1867047194732875781","depreciation_expense_buildings":"32","executive_salary":"32","electric_charge":"16","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPYSFMZX","storage_charge":"8","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"50","version":"1.0","fixed_manufacturing_cost":"160","water_rate":"16","fixed_labor":"50","resource_center_fixed_manufacturing_cost":"160"},{"total_preparation_time":"3","craft_name":"丝印","repair_charge":"120","depreciation_expense_other":"120","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"3","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":282651,"update_by":"1867047194732875781","depreciation_expense_buildings":"120","executive_salary":"120","electric_charge":"30","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPYSSYZX","storage_charge":"30","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"60","version":"1.0","fixed_manufacturing_cost":"600","water_rate":"60","fixed_labor":"180","resource_center_fixed_manufacturing_cost":"200"},{"total_preparation_time":"2","craft_name":"烫印","repair_charge":"80","depreciation_expense_other":"80","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"2","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":282652,"update_by":"1867047194732875781","depreciation_expense_buildings":"80","executive_salary":"80","electric_charge":"20","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPZHTYZX","storage_charge":"20","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"80","version":"1.0","fixed_manufacturing_cost":"400","water_rate":"40","fixed_labor":"160","resource_center_fixed_manufacturing_cost":"200"},{"total_preparation_time":"2","craft_name":"击凸（凹）","repair_charge":"60","depreciation_expense_other":"80","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"2","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":282653,"update_by":"1867047194732875781","depreciation_expense_buildings":"80","executive_salary":"80","electric_charge":"40","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPZHMQZX","storage_charge":"20","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"80","version":"1.0","fixed_manufacturing_cost":"400","water_rate":"40","fixed_labor":"160","resource_center_fixed_manufacturing_cost":"200"},{"total_preparation_time":"1","craft_name":"模切","repair_charge":"30","depreciation_expense_other":"40","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"1","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":282654,"update_by":"1867047194732875781","depreciation_expense_buildings":"40","executive_salary":"40","electric_charge":"20","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPZHMQZX","storage_charge":"10","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"80","version":"1.0","fixed_manufacturing_cost":"200","water_rate":"20","fixed_labor":"80","resource_center_fixed_manufacturing_cost":"200"},{"total_preparation_time":"0","craft_name":"打纸","repair_charge":"0","depreciation_expense_other":"0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"0","parent_code":"ZDQX0001278","process_type":"2","quotation_version":"3","id":282655,"update_by":"1867047194732875781","depreciation_expense_buildings":"0","executive_salary":"0","electric_charge":"0","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPZHQFZX","storage_charge":"0","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"40","version":"1.0","fixed_manufacturing_cost":"0","water_rate":"0","fixed_labor":"0","resource_center_fixed_manufacturing_cost":"100"},{"total_preparation_time":"0","craft_name":"纸盒品检机选剔","repair_charge":"0","depreciation_expense_other":"0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"0.00","parent_code":"ZDQX0001278","process_type":"2","quotation_version":"3","id":282656,"update_by":"1867047194732875781","depreciation_expense_buildings":"0","executive_salary":"0","electric_charge":"0","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPWXSCZX","storage_charge":"0","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"0.00","version":"1.0","fixed_manufacturing_cost":"0","water_rate":"0","fixed_labor":"0","resource_center_fixed_manufacturing_cost":"0.00"},{"total_preparation_time":"1","craft_name":"粘盒","repair_charge":"37.5","depreciation_expense_other":"50","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","production_preparation_time":"1","parent_code":"ZDQX0001278","process_type":"2","quotation_version":"3","id":282657,"update_by":"1867047194732875781","depreciation_expense_buildings":"50","executive_salary":"50","electric_charge":"25","flag_deleted":0,"operation_difficulty_fixed_time":"0","production_resource":"GPZHPJNHZX","storage_charge":"12.5","create_time":"2025-03-22 14:41:40","categroy":"10","resource_center_standard_hourly_wage":"150","version":"1.0","fixed_manufacturing_cost":"250","water_rate":"25","fixed_labor":"150","resource_center_fixed_manufacturing_cost":"250"},{"create_time":"2025-03-22 14:41:40","categroy":"3","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","parent_code":"0","quotation_version":"3","id":282658,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","flag_deleted":0,"material_code":"ZDQX0001278"}]
var custResult={"msg":"success","code":200,"data":{"product_version":"2.3","create_time":"2025-03-22 14:41:31","product_weight":0.0056,"mnecode":"C001073","product_code":"ZDQX0001278","unit_cn":"只","object_id":"","version":"1.0","is_used":1,"create_by":"1867047194732875781","unit":6591,"update_time":"2025-03-22 14:41:31","default_factory":"凌峰","id":103156,"update_by":"1867047194732875781","grade_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","client_name":"西安科诗美光学科技有限公司","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","client_code":"C000790","flag_deleted":0}}
var custObj=custResult.data
var crm_variable_cost_accounting=[{"create_time":"2025-03-22 14:41:40","categroy":"1","paper_number":"****************","paper_specification":"787","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","quotation_version":"3","id":109846,"update_by":"1867047194732875781","material_name":"787 300g 宁波酋长白卡","material_code":"****************","flag_deleted":0},{"amount":"0.************","craft_name":"原料分切","create_time":"2025-03-22 14:41:40","categroy":"10","single_product_unit_usage":"0.************","paper_number":"****************","paper_specification":"787","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","out_of_book":"21","parent_code":"ZDQX0001278","material_unit_price":"4.1279","process_type":"1","quotation_version":"3","id":109847,"update_by":"1867047194732875781","flag_deleted":0},{"create_time":"2025-03-22 14:41:40","categroy":"3","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","parent_code":"0","quotation_version":"3","id":109848,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","material_code":"ZDQX0001278","flag_deleted":0}]
var arr=[{"crm_one_time_cost_sumAmount":"4200.00","gd_depreciation_expense_other":50,"product_weight":"0.0056","bd_depreciation_expense_buildings":0.0013875,"gd_depreciation_expense_buildings":50,"main_class":"折叠纸盒类","create_by_name":"何健","create_by":"1867047194732875781","update_time":"2025-03-22 13:57:30","quotation_code":"HJ253001118","makeup_product":"0","fixed_paper_sumAmount":17.************,"cost_accounting_sumAmount":0.************,"dw_sum_amount":0.************,"variable_auxiliary_materials_sumAmount":"0.************","mnemonic_code":"ZDQX001059","net_size":"*","quotation_version":"4","id":"ZDQX0001275_2.4_[C000790]","bd_depreciation_expense_other":0.0013875,"update_by":"1867047194732875781","labor_rg_sumAmount":150,"material_name":"X盒-SL-241031RC38-OBALEN-珍珠微光蓝","labor_bd_sumAmount":150,"flag_deleted":0,"product_version":"2.4","update_by_name":"何健","create_time":"2025-03-22 13:56:07","gd_sum_amount":417.************,"product_size":"75.0*16.0*75.0","version":"2.0","object_id":"","cust_code":"C000790","reason_approval":"","sub_class":"医疗器械类","out_of_book":"21","zz_unit_variable_labor_cost_sumAmount":0.0041625,"auxiliary_materials_sumAmount":0,"auxiliary_ctp_sumAmount":0,"standard_unit":"只","rg_unit_variable_labor_cost_sumAmount":"0.************","grade_name":"X盒-SL-241031RC38-OBALEN-珍珠微光蓝","status":"4","material_code":"ZDQX0001275"}]
var tempProductWeight=67.2
var crm_one_time_cost=[{"amount":"0","create_time":"2025-03-22 14:41:41","categroy":"9","serviceability_ratio":"700000","edition_name":"X盒-SD-240112RC38-Meetfree-以爱之名版","version":"1.0","edition_categroy":"模切版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","material_price":"0","quotation_code":"HJ253001125","edition_code":"ZDQX000040-MV1","edition_type":"模切版","parent_code":"ZDQX0001278","quotation_version":"3","id":91882,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"0","create_time":"2025-03-22 14:41:41","categroy":"9","serviceability_ratio":"300000","edition_name":"X盒-SD-240112RC38-Meetfree-以爱之名版","version":"1.0","edition_categroy":"光油版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","material_price":"0","quotation_code":"HJ253001125","edition_code":"ZDQX000040-GBV1","edition_type":"印版","parent_code":"ZDQX0001278","quotation_version":"3","id":91883,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"0.0000","create_time":"2025-03-22 14:41:41","categroy":"9","serviceability_ratio":"200000","edition_name":"X盒-SL-241024RC38-OBALEN-星河入眸绿版","version":"1.0","edition_categroy":"丝印版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","material_price":"0.0000","quotation_code":"HJ253001125","edition_code":"ZDQX001058-SV1","edition_type":"印版","parent_code":"ZDQX0001278","quotation_version":"3","id":91884,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"2100","create_time":"2025-03-22 14:41:41","categroy":"9","serviceability_ratio":"700000","edition_name":"X盒-SL-241024RC38-OBALEN-星河入眸绿版","version":"1.0","edition_categroy":"烫印版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","material_price":"100.0000","quotation_code":"HJ253001125","edition_code":"ZDQX001058-TV1","edition_type":"模切版","parent_code":"ZDQX0001278","quotation_version":"3","id":91885,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"2100","create_time":"2025-03-22 14:41:41","categroy":"9","serviceability_ratio":"700000","edition_name":"X盒-SL-241024RC38-OBALEN-星河入眸绿版","version":"1.0","edition_categroy":"凹凸版","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","material_price":"100.0000","quotation_code":"HJ253001125","edition_code":"ZDQX001058-AV1","edition_type":"模切版","parent_code":"ZDQX0001278","quotation_version":"3","id":91886,"update_by":"1867047194732875781","flag_deleted":0},{"create_time":"2025-03-22 14:41:41","categroy":"3","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"0","quotation_version":"3","id":91887,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","material_code":"ZDQX0001278","flag_deleted":0}]
var crm_fixed_cost_paper=[{"create_time":"2025-03-22 14:41:40","categroy":"1","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","quotation_version":"3","id":357134,"update_by":"1867047194732875781","material_name":"787 300g 宁波酋长白卡","flag_deleted":0,"material_code":"****************"},{"amount":"12.085005156000","craft_name":"原料分切","create_time":"2025-03-22 14:41:40","operation_joint_number":"21","resource":"GPFQZX","categroy":"10","total_fixed_cost":"20.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"张","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"2.92764000000","minimum_operation_cost":"20","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":357135,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"90.637538670000","craft_name":"印刷","create_time":"2025-03-22 14:41:40","operation_joint_number":"21","resource":"GPYSWSYSZX","categroy":"10","total_fixed_cost":"150.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"张","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"21.95730000000","minimum_operation_cost":"150","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":357136,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"90.637538670000","craft_name":"印刷","create_time":"2025-03-22 14:41:40","operation_joint_number":"21","resource":"GPYS4SZX","categroy":"10","total_fixed_cost":"150.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"张","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"21.95730000000","minimum_operation_cost":"150","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":357137,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"21.148759023000","craft_name":"覆膜","create_time":"2025-03-22 14:41:40","operation_joint_number":"21","resource":"GPYSFMZX","categroy":"10","total_fixed_cost":"35.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"张","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"5.12337000000","minimum_operation_cost":"35","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":357138,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"18.127507734000","craft_name":"丝印","create_time":"2025-03-22 14:41:40","operation_joint_number":"21","resource":"GPYSSYZX","categroy":"10","total_fixed_cost":"30.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"张","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"4.39146000000","minimum_operation_cost":"30","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":357139,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"15.106256445000","craft_name":"烫印","create_time":"2025-03-22 14:41:40","operation_joint_number":"21","resource":"GPZHTYZX","categroy":"10","total_fixed_cost":"25.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"张","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"3.65955000000","minimum_operation_cost":"25","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":357140,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"18.127507734000","craft_name":"击凸（凹）","create_time":"2025-03-22 14:41:40","operation_joint_number":"21","resource":"GPZHMQZX","categroy":"10","total_fixed_cost":"30.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"张","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"4.39146000000","minimum_operation_cost":"30","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":357141,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"21.148759023000","craft_name":"模切","create_time":"2025-03-22 14:41:40","operation_joint_number":"21","resource":"GPZHMQZX","categroy":"10","total_fixed_cost":"35.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"张","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"5.12337000000","minimum_operation_cost":"35","parent_code":"ZDQX0001278","process_type":"1","quotation_version":"3","id":357142,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"1.438691090006","craft_name":"打纸","create_time":"2025-03-22 14:41:40","operation_joint_number":"1","resource":"GPZHQFZX","categroy":"10","total_fixed_cost":"50.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"只","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"0.34852857143","minimum_operation_cost":"50","parent_code":"ZDQX0001278","process_type":"2","quotation_version":"3","id":357143,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"1.870298417012","craft_name":"纸盒品检机选剔","create_time":"2025-03-22 14:41:40","operation_joint_number":"1","resource":"GPWXSCZX","categroy":"10","total_fixed_cost":"65.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"只","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"0.45308714286","minimum_operation_cost":"65","parent_code":"ZDQX0001278","process_type":"2","quotation_version":"3","id":357144,"update_by":"1867047194732875781","flag_deleted":0},{"amount":"14.386910900018","craft_name":"粘盒","create_time":"2025-03-22 14:41:40","operation_joint_number":"1","resource":"GPZHPJNHZX","categroy":"10","total_fixed_cost":"500.0000","version":"1.0","paper_cutting_number":"1","create_by":"1867047194732875781","fixed_difficulty_cost":"0","update_time":"2025-03-22 14:41:40","work_unit":"只","quotation_code":"HJ253001125","material_price":"4.1279","folded_paper_quantity":"3.48528571429","minimum_operation_cost":"500","parent_code":"ZDQX0001278","process_type":"2","quotation_version":"3","id":357145,"update_by":"1867047194732875781","flag_deleted":0},{"create_time":"2025-03-22 14:41:40","categroy":"3","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","parent_code":"0","quotation_version":"3","id":357146,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","flag_deleted":0,"material_code":"ZDQX0001278"}]
var obj={"main_class":"折叠纸盒类","create_by_name":"何健","create_by":"1867047194732875781","update_time":"2025-03-22 14:44:38","quotation_code":"HJ253001125","mnemonic_code":"ZDQX001063","net_size":"*","cust_name":"[西安科诗美光学科技有限公司]","quotation_version":"3","id":38628,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","flag_deleted":0,"product_version":"2.3","update_by_name":"何健","create_time":"2025-03-22 14:41:40","product_size":"75.0*16.0*75.0","version":"2.0","cust_code":"[C000790]","reason_approval":"","sub_class":"医疗器械类","out_of_book":"21","standard_unit":"只","status":"4","material_code":"ZDQX0001278"}
var crm_fixed_cost_auxiliary_materials=[{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"1kg*12罐/箱","main_class":"油墨类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"专色油墨","parent_code":"ZDQX0001278","material_unit_price":"44.2480","quotation_version":"3","id":375735,"update_by":"1867047194732875781","material_name":"公司自调普通专色油墨(1kg*12罐/箱)","material_code":"1010201030000034","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"1kg*12罐/箱","main_class":"油墨类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","parent_code":"ZDQX0001278","material_unit_price":"47.3892","quotation_version":"3","id":375736,"update_by":"1867047194732875781","material_name":"新金冠蓝（高光）(1kg*12罐/箱 南通迪爱生)","material_code":"1010201010000010","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"1kg*12罐/箱","main_class":"油墨类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","parent_code":"ZDQX0001278","material_unit_price":"23.6988","quotation_version":"3","id":375737,"update_by":"1867047194732875781","material_name":"油墨PackSafe亮光快干环保油墨（黑）(1kg*12罐/箱)","material_code":"1010201010000077","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"1kg*12罐/箱","main_class":"油墨类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","parent_code":"ZDQX0001278","material_unit_price":"44.8850","quotation_version":"3","id":375738,"update_by":"1867047194732875781","material_name":"新金冠红（高光）(1kg*12罐/箱 南通迪爱生)","material_code":"1010201010000053","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"1kg*12罐/箱","main_class":"油墨类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","parent_code":"ZDQX0001278","material_unit_price":"40.8850","quotation_version":"3","id":375739,"update_by":"1867047194732875781","material_name":"新金冠黄（高光）(1kg*12罐/箱 南通迪爱生)","material_code":"1010201010000009","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"20kg/桶","main_class":"表面处理用油类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"水性光油","parent_code":"ZDQX0001278","material_unit_price":"17.4336","quotation_version":"3","id":375740,"update_by":"1867047194732875781","material_name":"GB-800B水性底油（改善型）(20kg/桶 深圳高帮伟业科技有限公司)","material_code":"1010202010000016","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"1kg*12罐/箱","main_class":"油墨类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","parent_code":"ZDQX0001278","material_unit_price":"23.6988","quotation_version":"3","id":375741,"update_by":"1867047194732875781","material_name":"油墨PackSafe亮光快干环保油墨（黑）(1kg*12罐/箱)","material_code":"1010201010000077","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"50kg/桶","main_class":"胶水类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"覆膜胶","parent_code":"ZDQX0001278","material_unit_price":"9.7345","quotation_version":"3","id":375742,"update_by":"1867047194732875781","material_name":"水性覆膜胶3093(50kg/桶 上海奇想青晨)","material_code":"1010206030000001","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"5KG/桶","main_class":"表面处理用油类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"丝网用油","parent_code":"ZDQX0001278","material_unit_price":"66.3717","quotation_version":"3","id":375743,"update_by":"1867047194732875781","material_name":"中益UVH-15022哑膜光油（冬季型）(5KG/桶 中山中益)","material_code":"1010202050000001","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"0.63*240m/卷","main_class":"烫金材料","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"卷","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"烫印电化铝","parent_code":"ZDQX0001278","material_unit_price":"502.6554","quotation_version":"3","id":375744,"update_by":"1867047194732875781","material_name":"透明介质素面烫印电化铝HSA-S04-TM-AD06(可UV上烫）(0.63*240m/卷 佛山市)","material_code":"1010204050000002","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"50kg/桶","main_class":"胶水类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"水性糊盒胶","parent_code":"ZDQX0001278","material_unit_price":"29.3097","quotation_version":"3","id":375745,"update_by":"1867047194732875781","material_name":"冠力糊盒胶MA-768(50kg/桶)","material_code":"1010206010000001","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"125kg/桶","main_class":"表面处理用油类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"kg","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"水性光油","parent_code":"ZDQX0001278","material_unit_price":"13.8495","quotation_version":"3","id":375746,"update_by":"1867047194732875781","material_name":"Terra ATP966 水性光油(125kg/桶 阿塔卡化工（佛山）有限公司)","material_code":"1010202010000002","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"2","specification":"780mm*6500米/卷","main_class":"膜类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"卷","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"BOPP即涂哑膜","parent_code":"ZDQX0001278","material_unit_price":"983.8278","quotation_version":"3","id":375747,"update_by":"1867047194732875781","material_name":"双电晕哑膜(780mm*6500米/卷 重庆金田)","material_code":"1010205030000006","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"6","specification":"粘箱","main_class":"纸箱类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"套","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"五层纸箱","parent_code":"ZDQX0001278","material_unit_price":"4.6076","quotation_version":"3","id":375748,"update_by":"1867047194732875781","material_name":"纸箱482829","material_code":"1010301010000051","flag_deleted":0},{"amount":"0.00","create_time":"2025-03-22 14:41:40","categroy":"6","specification":"/","main_class":"垫板隔档类","fixed_usage":"0","version":"1.0","create_by":"1867047194732875781","unit":"张","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"三层隔档","parent_code":"ZDQX0001278","material_unit_price":"0.3091","quotation_version":"3","id":375749,"update_by":"1867047194732875781","material_name":"隔挡4828","material_code":"1010302020000008","flag_deleted":0},{"create_time":"2025-03-22 14:41:40","categroy":"3","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","parent_code":"0","quotation_version":"3","id":375750,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","material_code":"ZDQX0001278","flag_deleted":0}]
var crm_variable_cost_auxiliary_materials=[{"unit_usage_unit":"kg/万","amount":"0.000275934953","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"1kg*12罐/箱","main_class":"油墨类","version":"1.0","unit_usage":"0.000006236100","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"专色油墨","material_unit_price":"44.2480","quotation_version":"3","id":337714,"update_by":"1867047194732875781","material_name":"公司自调普通专色油墨(1kg*12罐/箱)","material_code":"1010201030000034","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.000295523790","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"1kg*12罐/箱","main_class":"油墨类","version":"1.0","unit_usage":"0.000006236100","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","material_unit_price":"47.3892","quotation_version":"3","id":337715,"update_by":"1867047194732875781","material_name":"新金冠蓝（高光）(1kg*12罐/箱 南通迪爱生)","material_code":"1010201010000010","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.000147788087","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"1kg*12罐/箱","main_class":"油墨类","version":"1.0","unit_usage":"0.000006236100","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","material_unit_price":"23.6988","quotation_version":"3","id":337716,"update_by":"1867047194732875781","material_name":"油墨PackSafe亮光快干环保油墨（黑）(1kg*12罐/箱)","material_code":"1010201010000077","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.000279907348","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"1kg*12罐/箱","main_class":"油墨类","version":"1.0","unit_usage":"0.000006236100","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","material_unit_price":"44.8850","quotation_version":"3","id":337717,"update_by":"1867047194732875781","material_name":"新金冠红（高光）(1kg*12罐/箱 南通迪爱生)","material_code":"1010201010000053","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.000254962949","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"1kg*12罐/箱","main_class":"油墨类","version":"1.0","unit_usage":"0.000006236100","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","material_unit_price":"40.8850","quotation_version":"3","id":337718,"update_by":"1867047194732875781","material_name":"新金冠黄（高光）(1kg*12罐/箱 南通迪爱生)","material_code":"1010201010000009","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.001087176730","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"20kg/桶","main_class":"表面处理用油类","version":"1.0","unit_usage":"0.000062361000","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"水性光油","material_unit_price":"17.4336","quotation_version":"3","id":337719,"update_by":"1867047194732875781","material_name":"GB-800B水性底油（改善型）(20kg/桶 深圳高帮伟业科技有限公司)","material_code":"1010202010000016","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.000147788087","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"1kg*12罐/箱","main_class":"油墨类","version":"1.0","unit_usage":"0.000006236100","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"普通油墨","material_unit_price":"23.6988","quotation_version":"3","id":337720,"update_by":"1867047194732875781","material_name":"油墨PackSafe亮光快干环保油墨（黑）(1kg*12罐/箱)","material_code":"1010201010000077","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.003642304325","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"50kg/桶","main_class":"胶水类","version":"1.0","unit_usage":"0.000374164500","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"覆膜胶","material_unit_price":"9.7345","quotation_version":"3","id":337721,"update_by":"1867047194732875781","material_name":"水性覆膜胶3093(50kg/桶 上海奇想青晨)","material_code":"1010206030000001","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.002759323781","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"5KG/桶","main_class":"表面处理用油类","version":"1.0","unit_usage":"0.000041573800","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"丝网用油","material_unit_price":"66.3717","quotation_version":"3","id":337722,"update_by":"1867047194732875781","material_name":"中益UVH-15022哑膜光油（冬季型）(5KG/桶 中山中益)","material_code":"1010202050000001","flag_deleted":0},{"unit_usage_unit":"卷/万","amount":"0.031667290200","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"0.63*240m/卷","main_class":"烫金材料","version":"1.0","unit_usage":"0.000063000000","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"烫印电化铝","material_unit_price":"502.6554","quotation_version":"3","id":337723,"update_by":"1867047194732875781","material_name":"透明介质素面烫印电化铝HSA-S04-TM-AD06(可UV上烫）(0.63*240m/卷 佛山市)","material_code":"1010204050000002","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.000879291000","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"50kg/桶","main_class":"胶水类","version":"1.0","unit_usage":"0.000030000000","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"水性糊盒胶","material_unit_price":"29.3097","quotation_version":"3","id":337724,"update_by":"1867047194732875781","material_name":"冠力糊盒胶MA-768(50kg/桶)","material_code":"1010206010000001","flag_deleted":0},{"unit_usage_unit":"kg/万","amount":"0.000863668669","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"125kg/桶","main_class":"表面处理用油类","version":"1.0","unit_usage":"0.000062361000","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"水性光油","material_unit_price":"13.8495","quotation_version":"3","id":337725,"update_by":"1867047194732875781","material_name":"Terra ATP966 水性光油(125kg/桶 阿塔卡化工（佛山）有限公司)","material_code":"1010202010000002","flag_deleted":0},{"unit_usage_unit":"卷/万","amount":"0.003935311200","create_time":"2025-03-22 14:41:40","categroy":"2","auxiliary_material_number":"ZDQX0001278","specification":"780mm*6500米/卷","main_class":"膜类","version":"1.0","unit_usage":"0.000004000000","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"BOPP即涂哑膜","material_unit_price":"983.8278","quotation_version":"3","id":337726,"update_by":"1867047194732875781","material_name":"双电晕哑膜(780mm*6500米/卷 重庆金田)","material_code":"1010205030000006","flag_deleted":0},{"amount":"0.002477204261","create_time":"2025-03-22 14:41:40","categroy":"6","auxiliary_material_number":"ZDQX0001278","specification":"粘箱","main_class":"纸箱类","version":"1.0","unit_usage":"0.000537634400","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"五层纸箱","material_unit_price":"4.6076","quotation_version":"3","id":337727,"update_by":"1867047194732875781","material_name":"纸箱482829","material_code":"1010301010000051","flag_deleted":0},{"amount":"0.000166182793","create_time":"2025-03-22 14:41:40","categroy":"6","auxiliary_material_number":"ZDQX0001278","specification":"/","main_class":"垫板隔档类","version":"1.0","unit_usage":"0.000537634400","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","sub_class":"三层隔档","material_unit_price":"0.3091","quotation_version":"3","id":337728,"update_by":"1867047194732875781","material_name":"隔挡4828","material_code":"1010302020000008","flag_deleted":0},{"create_time":"2025-03-22 14:41:40","categroy":"3","auxiliary_material_number":"0","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:40","quotation_code":"HJ253001125","quotation_version":"3","id":337729,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","material_code":"ZDQX0001278","flag_deleted":0}]
var entity={}
var crm_unit_variable_labor_cost=[{"craft_name":"原料分切","repair_charge":"0.000107142862","depreciation_expense_other":"0.00014285715","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"1","component_count":"1","quotation_version":"3","id":268531,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.00014285715","executive_salary":"0.00014285715","electric_charge":"0.000071428575","flag_deleted":0,"production_resource":"GPFQZX","storage_charge":"0.000035714288","process_operation_difficulty_factor":"0","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.000714285750","resource_quota":"10000","resource_center_standard_hourly_wage":"80","version":"1.0","variable_labor_cost":"0.000380952400","water_rate":"0.000071428575","resource_center_fixed_manufacturing_cost":"150","out_of_book":"21","unit_product_operation_time":"0.000004761905"},{"craft_name":"印刷","repair_charge":"0.00047619048","depreciation_expense_other":"0.00063492064","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"1","component_count":"1","quotation_version":"3","id":268532,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.00063492064","executive_salary":"0.00063492064","electric_charge":"0.00031746032","flag_deleted":0,"production_resource":"GPYSWSYSZX","storage_charge":"0.00015873016","process_operation_difficulty_factor":"0","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.003174603200","resource_quota":"12000","resource_center_standard_hourly_wage":"200","version":"1.0","variable_labor_cost":"0.000793650800","water_rate":"0.00031746032","resource_center_fixed_manufacturing_cost":"800","out_of_book":"21","unit_product_operation_time":"0.000003968254"},{"craft_name":"印刷","repair_charge":"0.00047916666","depreciation_expense_other":"0.00063888888","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"1","component_count":"1","quotation_version":"3","id":268533,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.00063888888","executive_salary":"0.00063888888","electric_charge":"0.00031944444","flag_deleted":0,"production_resource":"GPYS4SZX","storage_charge":"0.00015972222","process_operation_difficulty_factor":"0.15","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.003194444400","resource_quota":"12000","resource_center_standard_hourly_wage":"200","version":"1.0","variable_labor_cost":"0.000912698400","water_rate":"0.00031944444","resource_center_fixed_manufacturing_cost":"700","out_of_book":"21","unit_product_operation_time":"0.000004563492"},{"craft_name":"覆膜","repair_charge":"0.000380952384","depreciation_expense_other":"0.000507936512","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"1","component_count":"1","quotation_version":"3","id":268534,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.000507936512","executive_salary":"0.000507936512","electric_charge":"0.000253968256","flag_deleted":0,"production_resource":"GPYSFMZX","storage_charge":"0.000126984128","process_operation_difficulty_factor":"0.5","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.002539682560","resource_quota":"4500","resource_center_standard_hourly_wage":"50","version":"1.0","variable_labor_cost":"0.000793650800","water_rate":"0.000253968256","resource_center_fixed_manufacturing_cost":"160","out_of_book":"21","unit_product_operation_time":"0.000015873016"},{"craft_name":"丝印","repair_charge":"0.00107142856","depreciation_expense_other":"0.00107142856","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"1","component_count":"1","quotation_version":"3","id":268535,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.00107142856","executive_salary":"0.00107142856","electric_charge":"0.00026785714","flag_deleted":0,"production_resource":"GPYSSYZX","storage_charge":"0.00026785714","process_operation_difficulty_factor":"0.8","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.005357142800","resource_quota":"3200","resource_center_standard_hourly_wage":"60","version":"1.0","variable_labor_cost":"0.001607142840","water_rate":"0.00053571428","resource_center_fixed_manufacturing_cost":"200","out_of_book":"21","unit_product_operation_time":"0.000026785714"},{"craft_name":"烫印","repair_charge":"0.00049523808","depreciation_expense_other":"0.00049523808","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"1","component_count":"1","quotation_version":"3","id":268536,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.00049523808","executive_salary":"0.00049523808","electric_charge":"0.00012380952","flag_deleted":0,"production_resource":"GPZHTYZX","storage_charge":"0.00012380952","process_operation_difficulty_factor":"0.3","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.002476190400","resource_quota":"5000","resource_center_standard_hourly_wage":"80","version":"1.0","variable_labor_cost":"0.000990476160","water_rate":"0.00024761904","resource_center_fixed_manufacturing_cost":"200","out_of_book":"21","unit_product_operation_time":"0.000012380952"},{"craft_name":"击凸（凹）","repair_charge":"0.0002857143","depreciation_expense_other":"0.0003809524","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"1","component_count":"1","quotation_version":"3","id":268537,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.0003809524","executive_salary":"0.0003809524","electric_charge":"0.0001904762","flag_deleted":0,"production_resource":"GPZHMQZX","storage_charge":"0.0000952381","process_operation_difficulty_factor":"0","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.001904762000","resource_quota":"5000","resource_center_standard_hourly_wage":"80","version":"1.0","variable_labor_cost":"0.000761904800","water_rate":"0.0001904762","resource_center_fixed_manufacturing_cost":"200","out_of_book":"21","unit_product_operation_time":"0.000009523810"},{"craft_name":"模切","repair_charge":"0.00023809524","depreciation_expense_other":"0.00031746032","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"1","component_count":"1","quotation_version":"3","id":268538,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.00031746032","executive_salary":"0.00031746032","electric_charge":"0.00015873016","flag_deleted":0,"production_resource":"GPZHMQZX","storage_charge":"0.00007936508","process_operation_difficulty_factor":"0","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.001587301600","resource_quota":"6000","resource_center_standard_hourly_wage":"80","version":"1.0","variable_labor_cost":"0.000634920640","water_rate":"0.00015873016","resource_center_fixed_manufacturing_cost":"200","out_of_book":"21","unit_product_operation_time":"0.000007936508"},{"craft_name":"打纸","repair_charge":"0.00015","depreciation_expense_other":"0.0002","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"2","component_count":"1","quotation_version":"3","id":268539,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.0002","executive_salary":"0.0002","electric_charge":"0.0001","flag_deleted":0,"production_resource":"GPZHQFZX","storage_charge":"0.00005","process_operation_difficulty_factor":"0","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.001000000000","resource_quota":"100000","resource_center_standard_hourly_wage":"40","version":"1.0","variable_labor_cost":"0.000400000000","water_rate":"0.0001","resource_center_fixed_manufacturing_cost":"100","out_of_book":"1","unit_product_operation_time":"0.000010000000"},{"craft_name":"纸盒品检机选剔","repair_charge":"0","depreciation_expense_other":"0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"2","component_count":"1","quotation_version":"3","id":268540,"update_by":"1867047194732875781","depreciation_expense_buildings":"0","executive_salary":"0","electric_charge":"0","flag_deleted":0,"production_resource":"GPWXSCZX","storage_charge":"0","process_operation_difficulty_factor":"1.667","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.000000000000","resource_quota":"1","resource_center_standard_hourly_wage":"0.00","version":"1.0","variable_labor_cost":"0.000000000000","water_rate":"0","resource_center_fixed_manufacturing_cost":"0.00","out_of_book":"1","unit_product_operation_time":"2.667000000000"},{"craft_name":"粘盒","repair_charge":"0.000890625","depreciation_expense_other":"0.0011875","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"ZDQX0001278","process_type":"2","component_count":"1","quotation_version":"3","id":268541,"update_by":"1867047194732875781","depreciation_expense_buildings":"0.0011875","executive_salary":"0.0011875","electric_charge":"0.00059375","flag_deleted":0,"production_resource":"GPZHPJNHZX","storage_charge":"0.000296875","process_operation_difficulty_factor":"0.9","create_time":"2025-03-22 14:41:41","categroy":"10","variable_manufacturing_cost":"0.005937500000","resource_quota":"80000","resource_center_standard_hourly_wage":"150","version":"1.0","variable_labor_cost":"0.003562500000","water_rate":"0.00059375","resource_center_fixed_manufacturing_cost":"250","out_of_book":"1","unit_product_operation_time":"0.000023750000"},{"create_time":"2025-03-22 14:41:41","categroy":"3","version":"1.0","create_by":"1867047194732875781","update_time":"2025-03-22 14:41:41","quotation_code":"HJ253001125","parent_code":"0","component_count":"1","quotation_version":"3","id":268542,"update_by":"1867047194732875781","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","flag_deleted":0,"material_code":"ZDQX0001278"}]
var entity2={"product_weight":"0.0056","settlement_currency_amount_exclusive":"4141.60","settlement_currency_price_exclusive":0.345133,"quantity_fluctuation":"+3","main_class":"折叠纸盒类","tax_rate":"13","product_quantity":"12000","quotation_product_id":"3770","create_by":"null","settlement_currency_amount_inclusive":"4680.00","update_time":"2025-03-25 10:34:28","makeup_product":"0","contract_product_line_number":"2","commission_print_number":"KSM-CG-BC-*********","mnemonic_code":"ZDQX001063","id":1739,"update_by":"null","material_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","flag_deleted":0,"product_version":"2.3","amount_exclusive_tax":"4141.60","quotation_factory":"凌峰","exchange_rate":1,"contract_management_code":"25030483","create_time":"2025-03-25 10:34:28","administrative_code":"610404000000","amount_tax_inclusive":"4680.00","unit_price":0.39,"object_id":"","settlement_currency":"人民币","cust_code":"C000790","plate_area":"3","delivery_date":"2025-03-31","sub_class":"医疗器械类","out_of_book":21,"current_issue_quantity":12000,"issued_quantity":"0","standard_unit":"只","grade_name":"X盒-SL-241024RC38-OBALEN-星河入眸紫","product_big_version":"2","settlement_currency_price_inclusive":0.39,"unit_price_exclusive":0.345133,"material_code":"ZDQX0001278"}
var plate_area=entity2.plate_area
var print_count=entity2.print_count
var feign_cost
var handling_expense
var delivery_expense

var fixed_paper_sumAmount = 0
var auxiliary_materials_sumAmount = 0
var auxiliary_ctp_sumAmount = 0
var labor_rg_sumAmount = 0
var labor_bd_sumAmount = 0
var cost_accounting_sumAmount = 0
var variable_auxiliary_materials_sumAmount = 0
var rg_unit_variable_labor_cost_sumAmount = 0
var zz_unit_variable_labor_cost_sumAmount = 0
var crm_one_time_cost_sumAmount = 0
var gd_depreciation_expense_other = 0
var gd_depreciation_expense_buildings = 0
var bd_depreciation_expense_buildings = 0
var bd_depreciation_expense_other = 0
var count1 = 0 //管理人员工资+水费+电气费+仓储费+修理费
var count2 = 0 //管理人员工资+水费+电气费+仓储费+修理费
var gdArr = entity.gdArr
var gdrgArr = entity.gdrgArr
var bdArr = entity.bdArr
var bdrgArr = entity.bdrgArr
var auxiliary_ctp = entity.auxiliary_ctp

// 统计主产品大张工序固定纸张值
if(Array.isArray(gdArr) && gdArr.length > 0){
  for (var y = 0; y < gdArr.length; y++) {
    if (gdArr[y].craft_name == "印刷") {
      fixed_paper_sumAmount += gdArr[y].amount*1*(plate_area/10)*print_count
    }else{
      fixed_paper_sumAmount += gdArr[y].amount*1*(plate_area/10)
    }
  }
}
// 计算小张工序的值
if(Array.isArray(crm_fixed_cost_paper) && crm_fixed_cost_paper.length > 0){
  for (var x = 0; x < crm_fixed_cost_paper.length; x++) {
    if (crm_fixed_cost_paper[x].categroy == 10 && crm_fixed_cost_paper[x].process_type !== "1") {
      fixed_paper_sumAmount += crm_fixed_cost_paper[x].amount*1
    }
  }
}
/*
if(Array.isArray(crm_fixed_cost_paper) && crm_fixed_cost_paper.length > 0){
  for (var x = 0; x < crm_fixed_cost_paper.length; x++) {
    if (crm_fixed_cost_paper[x].categroy == 10) {
      if(Array.isArray(gdArr) && gdArr.length > 0){
        for (var y = 0; y < gdArr.length; y++) {
          if (crm_fixed_cost_paper[x].craft_name == gdArr[y].craft_name) {
            crm_fixed_cost_paper[x].amount = gdArr[y].amount
          }
        }
      }
      if (crm_fixed_cost_paper[x].process_type == 1) {//大张工序
        if (crm_fixed_cost_paper[x].craft_name == "印刷") {
          fixed_paper_sumAmount += crm_fixed_cost_paper[x].amount*1*(plate_area/10)*print_count
        }else{
          fixed_paper_sumAmount += crm_fixed_cost_paper[x].amount*1*(plate_area/10)
        }
      }else{
        fixed_paper_sumAmount += crm_fixed_cost_paper[x].amount*1
      }
    }
  }
}
*/

if (Array.isArray(crm_fixed_cost_auxiliary_materials) && crm_fixed_cost_auxiliary_materials.length > 0){
for (var x = 0; x < crm_fixed_cost_auxiliary_materials.length; x++) {
  if (crm_fixed_cost_auxiliary_materials[x].categroy == 2) {
    auxiliary_materials_sumAmount += crm_fixed_cost_auxiliary_materials[x].amount*1
  }
}
}

// ctp采用主产品的
if(Array.isArray(auxiliary_ctp) && auxiliary_ctp.length > 0){
  for (var x = 0; x < auxiliary_ctp.length; x++){
    auxiliary_ctp_sumAmount += auxiliary_ctp[x].amount*1
  }
}
/*
if (Array.isArray(crm_fixed_cost_auxiliary_ctp) && crm_fixed_cost_auxiliary_ctp.length > 0){
for (var x = 0; x < crm_fixed_cost_auxiliary_ctp.length; x++) {
  if (crm_fixed_cost_auxiliary_ctp[x].categroy == 9) {
    auxiliary_ctp_sumAmount += crm_fixed_cost_auxiliary_ctp[x].amount*1
  }
}
}
*/

// 通知主产品大张工序的固定人工制造费
if (Array.isArray(gdrgArr) && gdrgArr.length > 0){
     for (var y = 0; y < gdrgArr.length; y++) {
       if (gdrgArr[y].craft_name == "印刷") {
         count1 += gdrgArr[y].count1*print_count*(plate_area/10)
         labor_bd_sumAmount += gdrgArr[y].fixed_manufacturing_cost*1*print_count*(plate_area/10)
         labor_rg_sumAmount += gdrgArr[y].fixed_labor*1*print_count*(plate_area/10)
         gd_depreciation_expense_other += gdrgArr[y].depreciation_expense_other*1*print_count*(plate_area/10)
         gd_depreciation_expense_buildings += gdrgArr[y].depreciation_expense_buildings*1*print_count*(plate_area/10)
       }else{
         count1 += gdrgArr[y].count1*(plate_area/10)
         labor_bd_sumAmount += gdrgArr[y].fixed_manufacturing_cost*1*(plate_area/10)
         labor_rg_sumAmount += gdrgArr[y].fixed_labor*1*(plate_area/10)
         gd_depreciation_expense_other += gdrgArr[y].depreciation_expense_other*1*(plate_area/10)
         gd_depreciation_expense_buildings += gdrgArr[y].depreciation_expense_buildings*1*(plate_area/10)
       }
     }
}
// 计算小张工序的值
if (Array.isArray(crm_fixed_cost_labor) && crm_fixed_cost_labor.length > 0){
  for (var x = 0; x < crm_fixed_cost_labor.length; x++) {
    if (crm_fixed_cost_labor[x].categroy == "10" && crm_fixed_cost_labor[x].process_type !== "1"){
      count1 += crm_fixed_cost_labor[x].executive_salary*1+ crm_fixed_cost_labor[x].repair_charge*1+crm_fixed_cost_labor[x].water_rate*1+crm_fixed_cost_labor[x].electric_charge*1+crm_fixed_cost_labor[x].storage_charge*1
      labor_bd_sumAmount += crm_fixed_cost_labor[x].fixed_manufacturing_cost*1
      labor_rg_sumAmount += crm_fixed_cost_labor[x].fixed_labor*1
      gd_depreciation_expense_other +=  crm_fixed_cost_labor[x].depreciation_expense_other*1          //折旧费（设备及其他类）
      gd_depreciation_expense_buildings +=  crm_fixed_cost_labor[x].depreciation_expense_buildings*1          //折旧费（房屋建筑物类）
    }
  }
}

/*
if (Array.isArray(crm_fixed_cost_labor) && crm_fixed_cost_labor.length > 0){
for (var x = 0; x < crm_fixed_cost_labor.length; x++) {
   if (crm_fixed_cost_labor[x].categroy == "10") {
     if (Array.isArray(gdrgArr) && gdrgArr.length > 0){
     for (var y = 0; y < gdrgArr.length; y++) {
       if (crm_fixed_cost_labor[x].craft_name == gdrgArr[y].craft_name) {
         crm_fixed_cost_labor[x]['amount'] = gdrgArr[y].count1
         crm_fixed_cost_labor[x].fixed_manufacturing_cost = gdrgArr[y].fixed_manufacturing_cost
         crm_fixed_cost_labor[x].fixed_labor = gdrgArr[y].fixed_labor
         crm_fixed_cost_labor[x].depreciation_expense_other = gdrgArr[y].depreciation_expense_other
         crm_fixed_cost_labor[x].depreciation_expense_buildings = gdrgArr[y].depreciation_expense_buildings
       }
     }
     }
     
    if (crm_fixed_cost_labor[x].process_type == 1) {
      if (crm_fixed_cost_labor[x].craft_name == "印刷") {
       count1 += crm_fixed_cost_labor[x].amount*print_count*(plate_area/10)
       labor_bd_sumAmount += crm_fixed_cost_labor[x].fixed_manufacturing_cost*1*print_count*(plate_area/10)
       labor_rg_sumAmount += crm_fixed_cost_labor[x].fixed_labor*1*print_count*(plate_area/10)
        gd_depreciation_expense_other +=  crm_fixed_cost_labor[x].depreciation_expense_other*1*print_count*(plate_area/10)
        gd_depreciation_expense_buildings +=  crm_fixed_cost_labor[x].depreciation_expense_buildings*1*print_count*(plate_area/10)
      }else{
       count1 += (crm_fixed_cost_labor[x].executive_salary*1+ crm_fixed_cost_labor[x].repair_charge*1+crm_fixed_cost_labor[x].water_rate*1+crm_fixed_cost_labor[x].electric_charge*1+crm_fixed_cost_labor[x].storage_charge*1)*(plate_area/10)
       labor_bd_sumAmount += crm_fixed_cost_labor[x].fixed_manufacturing_cost*1*(plate_area/10)
       labor_rg_sumAmount += crm_fixed_cost_labor[x].fixed_labor*1*(plate_area/10)
        gd_depreciation_expense_other +=  crm_fixed_cost_labor[x].depreciation_expense_other*1*(plate_area/10)
        gd_depreciation_expense_buildings +=  crm_fixed_cost_labor[x].depreciation_expense_buildings*1*(plate_area/10)
      }
    }else{
      //count1 += crm_fixed_cost_labor[x].amount
      count1 += crm_fixed_cost_labor[x].executive_salary*1+ crm_fixed_cost_labor[x].repair_charge*1+crm_fixed_cost_labor[x].water_rate*1+crm_fixed_cost_labor[x].electric_charge*1+crm_fixed_cost_labor[x].storage_charge*1
      labor_bd_sumAmount += crm_fixed_cost_labor[x].fixed_manufacturing_cost*1
      labor_rg_sumAmount += crm_fixed_cost_labor[x].fixed_labor*1
      gd_depreciation_expense_other +=  crm_fixed_cost_labor[x].depreciation_expense_other*1          //折旧费（设备及其他类）
      gd_depreciation_expense_buildings +=  crm_fixed_cost_labor[x].depreciation_expense_buildings*1          //折旧费（房屋建筑物类）
    }
  }
}
}
*/

// 变动纸张不考虑主产品
if(Array.isArray(crm_variable_cost_accounting) && crm_variable_cost_accounting.length > 0){
for (var x = 0; x < crm_variable_cost_accounting.length; x++) {
  if (crm_variable_cost_accounting[x].categroy == "10") {
    /*if(Array.isArray(bdArr) && bdArr.length > 0){
    for (var y = 0; y < bdArr.length; y++) {
      if (crm_fixed_cost_paper[x].craft_name == bdArr[y].craft_name) {
        crm_fixed_cost_paper[x].amount = bdArr[y].amount
      }
    }
    }*/
     if (crm_variable_cost_accounting[x].process_type == 1) {//大张工序
      if (crm_variable_cost_accounting[x].craft_name == "印刷") {
        // 变动成本不需要计算拼版面积比
        cost_accounting_sumAmount += crm_variable_cost_accounting[x].amount*1*print_count//*(plate_area/10)
      }else{
        // 变动成本不需要计算拼版面积比
        cost_accounting_sumAmount += crm_variable_cost_accounting[x].amount*1
        // cost_accounting_sumAmount += crm_variable_cost_accounting[x].amount*1*(plate_area/10)
      }
    }else{
      cost_accounting_sumAmount += crm_variable_cost_accounting[x].amount*1
    }
  }
}
}


if(Array.isArray(crm_variable_cost_auxiliary_materials) && crm_variable_cost_auxiliary_materials.length > 0){
for (var x = 0; x < crm_variable_cost_auxiliary_materials.length; x++) {
  if (crm_variable_cost_auxiliary_materials[x].categroy == 2) {
    variable_auxiliary_materials_sumAmount += crm_variable_cost_auxiliary_materials[x].amount*1
  }
}
}

// 计算主产品的大张工序变动成本
if(Array.isArray(bdrgArr) && bdrgArr.length > 0){
     for (var y = 0; y < bdrgArr.length; y++) {
       if (bdrgArr[y].craft_name == "印刷") {
         count2 += bdrgArr[y].count2*print_count
         zz_unit_variable_labor_cost_sumAmount += bdrgArr[y].variable_manufacturing_cost*print_count
         rg_unit_variable_labor_cost_sumAmount += bdrgArr[y].variable_labor_cost*print_count
         bd_depreciation_expense_other += bdrgArr[y].depreciation_expense_other*print_count
         bd_depreciation_expense_buildings +=  bdrgArr[y].depreciation_expense_buildings*print_count
       }else{
         count2 += bdrgArr[y].count2*1
         zz_unit_variable_labor_cost_sumAmount += bdrgArr[y].variable_manufacturing_cost*1
         rg_unit_variable_labor_cost_sumAmount += bdrgArr[y].variable_labor_cost*1
         bd_depreciation_expense_other += bdrgArr[y].depreciation_expense_other*1
         bd_depreciation_expense_buildings +=  bdrgArr[y].depreciation_expense_buildings*1
       }
     }
}
// 子产品小张工序
if(Array.isArray(crm_unit_variable_labor_cost) && crm_unit_variable_labor_cost.length > 0){
  for (var x = 0; x < crm_unit_variable_labor_cost.length; x++) {
    if (crm_unit_variable_labor_cost[x].categroy == "10" && crm_unit_variable_labor_cost[x].process_type !== "1"){
       count2 += crm_unit_variable_labor_cost[x].executive_salary*1+ crm_unit_variable_labor_cost[x].repair_charge*1+crm_unit_variable_labor_cost[x].water_rate*1+crm_unit_variable_labor_cost[x].electric_charge*1+crm_unit_variable_labor_cost[x].storage_charge*1
       zz_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_manufacturing_cost*1
       rg_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_labor_cost*1
       bd_depreciation_expense_other +=  crm_unit_variable_labor_cost[x].depreciation_expense_other*1          //折旧费（设备及其他类）
       bd_depreciation_expense_buildings +=  crm_unit_variable_labor_cost[x].depreciation_expense_buildings*1          //折旧费（房屋建筑物类）
    }
  }
}
/*
if(Array.isArray(crm_unit_variable_labor_cost) && crm_unit_variable_labor_cost.length > 0){
for (var x = 0; x < crm_unit_variable_labor_cost.length; x++) {
  if (crm_unit_variable_labor_cost[x].categroy == "10") {
    if(Array.isArray(bdrgArr) && bdrgArr.length > 0){
     for (var y = 0; y < bdrgArr.length; y++) {
       if (crm_unit_variable_labor_cost[x].craft_name == bdrgArr[y].craft_name) {
         crm_unit_variable_labor_cost[x]['amount'] = bdrgArr[y].count2
         crm_unit_variable_labor_cost[x].variable_manufacturing_cost = bdrgArr[y].variable_manufacturing_cost
         crm_unit_variable_labor_cost[x].variable_labor_cost = bdrgArr[y].variable_labor_cost
         crm_unit_variable_labor_cost[x].depreciation_expense_other = bdrgArr[y].depreciation_expense_other
         crm_unit_variable_labor_cost[x].depreciation_expense_buildings = bdrgArr[y].depreciation_expense_buildings
       }
     }
    }
    if (crm_unit_variable_labor_cost[x].process_type == 1) {
      if (crm_unit_variable_labor_cost[x].craft_name == "印刷") {
        // 变动成本不需要成拼版面积比
       count2 += crm_unit_variable_labor_cost[x].amount*print_count//*(plate_area/10)
       zz_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_manufacturing_cost*1*print_count//*(plate_area/10)
       rg_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_labor_cost*1*print_count//*(plate_area/10)
        bd_depreciation_expense_other +=  crm_unit_variable_labor_cost[x].depreciation_expense_other*1*print_count//*(plate_area/10)
        bd_depreciation_expense_buildings +=  crm_unit_variable_labor_cost[x].depreciation_expense_buildings*1  *print_count//*(plate_area/10)
      }else{
        // 变动成本不需要计算拼版面积比
        count2 += (crm_unit_variable_labor_cost[x].amount ? crm_unit_variable_labor_cost[x].amount : 0)
        zz_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_manufacturing_cost*1
        rg_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_labor_cost*1
        bd_depreciation_expense_other +=  crm_unit_variable_labor_cost[x].depreciation_expense_other*1          //折旧费（设备及其他类）
        bd_depreciation_expense_buildings +=  crm_unit_variable_labor_cost[x].depreciation_expense_buildings*1          //折旧费（房屋建筑物类）
       /*count2 += crm_unit_variable_labor_cost[x].amount*(plate_area/10)
       zz_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_manufacturing_cost*1*1*(plate_area/10)
       rg_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_labor_cost*1*(plate_area/10)
        bd_depreciation_expense_other +=  crm_unit_variable_labor_cost[x].depreciation_expense_other*1*(plate_area/10)
        bd_depreciation_expense_buildings +=  crm_unit_variable_labor_cost[x].depreciation_expense_buildings*1  *(plate_area/10)*/
     /* }
    }else{
     //count2 += crm_unit_variable_labor_cost[x].amount
     count2 += crm_unit_variable_labor_cost[x].executive_salary*1+ crm_unit_variable_labor_cost[x].repair_charge*1+crm_unit_variable_labor_cost[x].water_rate*1+crm_unit_variable_labor_cost[x].electric_charge*1+crm_unit_variable_labor_cost[x].storage_charge*1
     zz_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_manufacturing_cost*1
     rg_unit_variable_labor_cost_sumAmount += crm_unit_variable_labor_cost[x].variable_labor_cost*1
     bd_depreciation_expense_other +=  crm_unit_variable_labor_cost[x].depreciation_expense_other*1          //折旧费（设备及其他类）
     bd_depreciation_expense_buildings +=  crm_unit_variable_labor_cost[x].depreciation_expense_buildings*1          //折旧费（房屋建筑物类）
    }
  }
}
}
*/

if(Array.isArray(crm_one_time_cost) && crm_one_time_cost.length > 0){
for (var x = 0; x < crm_one_time_cost.length; x++) {
  if (crm_one_time_cost[x].categroy == "9") {
    crm_one_time_cost_sumAmount += crm_one_time_cost[x].amount*1
  }
}
}
obj['gd_depreciation_expense_other'] = isNaN(gd_depreciation_expense_other)?0:gd_depreciation_expense_other//.toFixed(2); 
obj['gd_depreciation_expense_buildings'] = isNaN(gd_depreciation_expense_buildings)?0:gd_depreciation_expense_buildings//.toFixed(2);
obj['bd_depreciation_expense_buildings'] = isNaN(bd_depreciation_expense_buildings)?0:bd_depreciation_expense_buildings//.toFixed(2);
obj['bd_depreciation_expense_other'] = isNaN(bd_depreciation_expense_other)?0:bd_depreciation_expense_other//.toFixed(2);
obj['fixed_paper_sumAmount'] = isNaN(fixed_paper_sumAmount)?0:fixed_paper_sumAmount//.toFixed(2); 
obj['auxiliary_materials_sumAmount'] = isNaN(auxiliary_materials_sumAmount)?0:auxiliary_materials_sumAmount//.toFixed(2);
obj['auxiliary_ctp_sumAmount'] = isNaN(auxiliary_ctp_sumAmount)?0:auxiliary_ctp_sumAmount//.toFixed(2);
obj['labor_rg_sumAmount'] = isNaN(labor_rg_sumAmount)?0:labor_rg_sumAmount//.toFixed(2);
obj['labor_bd_sumAmount'] = isNaN(labor_bd_sumAmount)?0:labor_bd_sumAmount//.toFixed(2);
obj['cost_accounting_sumAmount'] = isNaN(cost_accounting_sumAmount)?0:cost_accounting_sumAmount;
obj['variable_auxiliary_materials_sumAmount'] = isNaN(variable_auxiliary_materials_sumAmount)?0:variable_auxiliary_materials_sumAmount.toFixed(12);
obj['rg_unit_variable_labor_cost_sumAmount'] = isNaN(rg_unit_variable_labor_cost_sumAmount)?0:rg_unit_variable_labor_cost_sumAmount.toFixed(12);
obj['zz_unit_variable_labor_cost_sumAmount'] = isNaN(zz_unit_variable_labor_cost_sumAmount)?0:zz_unit_variable_labor_cost_sumAmount.toFixed(12);
obj['crm_one_time_cost_sumAmount'] = isNaN(crm_one_time_cost_sumAmount)?0:crm_one_time_cost_sumAmount.toFixed(2);
obj['gd_sum_amount'] = obj.fixed_paper_sumAmount*1 + obj.auxiliary_materials_sumAmount*1 + obj.auxiliary_ctp_sumAmount*1 + obj.labor_bd_sumAmount*1 +obj.labor_rg_sumAmount*1
obj['dw_sum_amount'] = obj.cost_accounting_sumAmount*1 + obj.variable_auxiliary_materials_sumAmount*1 + obj.zz_unit_variable_labor_cost_sumAmount*1 + obj.rg_unit_variable_labor_cost_sumAmount*1
obj.labor_bd_sumAmount = count1
obj.zz_unit_variable_labor_cost_sumAmount = count2
obj.id = obj.material_code+"_"+obj.product_version+"_"+obj.cust_code
obj.cust_code = entity2.cust_code
obj.cust_name = entity2.cust_name
obj.makeup_product = entity2.makeup_product
obj.product_weight = entity2.product_weight

obj['object_id'] = custObj.object_id
obj['grade_name'] = custObj.grade_name
// 承运商运费
obj['carrier_feign_cost'] = feign_cost
// 装卸费
obj['handling_expense'] = handling_expense
// 送货费
obj['delivery_expense'] = delivery_expense
if (arr == null) {
  arr = []
}

arr.push(obj)
console.log(arr)
return arr



