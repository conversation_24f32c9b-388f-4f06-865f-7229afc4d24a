SELECT
    CONCAT(dssm.update_year, '-', IF(dssm.month_of_year < 10, concat('0', dssm.month_of_year), dssm.month_of_year)) update_month,
    CASE
        WHEN COALESCE(rd.count_shipment_batches, 0) = 0 THEN 0
        ELSE ROUND(100 * rd.count_shipment_batches / dssm.planned_shipment_batches, 2)
        END achievement_rate,
    0 achievement_rate_yoy,
    0 achievement_rate_mom
FROM
    cockpit.dwd_shipment_summary_month dssm
        LEFT JOIN
    (
        SELECT
            update_year,
            month_of_year,
            COUNT(*) count_shipment_batches
        FROM
            cockpit.dwd_shipment_detail_month dsdm2
        WHERE
            dsdm2.update_year = YEAR(CURDATE())
    AND dsdm2.detail_type = '2'
		AND dsdm2.actual_shipment_date < DATE_ADD(planned_shipment_date, INTERVAL IFNULL((SELECT dict_value FROM ods_sys_dict_biz osdb WHERE osdb.flag_deleted = 0 AND osdb.dict_code = 'summary_set_day'
LIMIT 1), 0)  DAY)
GROUP BY
    dsdm2.update_year,
    dsdm2.month_of_year
    ) rd ON
    rd.update_year = dssm.update_year
    AND rd.month_of_year = dssm.month_of_year
WHERE
    dssm.update_year = YEAR(CURDATE())
ORDER BY
    dssm.month_of_year;

select * from dwd_shipment_achievement_rate_month dsarm
where substring(dsarm.update_month, 1, 4)=year(now())
;
SELECT
    cast(RIGHT(update_month, 2) as signed ) time,
    achievement_rate value
FROM
    dwd_shipment_achievement_rate_month dsarm
WHERE
    LEFT(update_month, 4) = YEAR(CURDATE())
ORDER BY
    RIGHT(update_month, 2);
select * from dwd_deliver_reach_rate ddrr;

