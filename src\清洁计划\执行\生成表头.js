var template={
    "title": "1号",
    "dataIndex": "day_1",
    "key": "key-1irrg610h",
    "resizable": true,
    "width": 70,
    "ellipsis": true,
    "config": {
        "value": {
            "title": "列配置",
            "content": [
                {
                    "title": "字体配置",
                    "key": null,
                    "type": "font-config-simple",
                    "value": null,
                    "props": {
                        "color": "",
                        "size": 14,
                        "align": "left"
                    }
                },
                {
                    "title": "列头配置",
                    "key": null,
                    "type": "slot-drag",
                    "detail": "column",
                    "enable": false,
                    "slot": [
                        [],
                        []
                    ],
                    "cascaderdata": {
                        "type": "column",
                        "key": ""
                    },
                    "layout": {
                        "rows": 1,
                        "cols": 2
                    }
                },
                {
                    "title": "整列配置",
                    "key": null,
                    "type": "slot-drag",
                    "detail": "record",
                    "enable": true,
                    "whole": true,
                    "slot": [
                        [
                            {
                                "name": "按钮",
                                "id": "button",
                                "color": "#7a6fff",
                                "keyid": "1748327293217kfpnwrxo",
                                "whole": true,
                                "config": {
                                    "title": "元素配置",
                                    "content": [
                                        {
                                            "title": "按钮位置",
                                            "key": null,
                                            "type": "radios-select",
                                            "value": "left",
                                            "options": [
                                                {
                                                    "label": "靠左",
                                                    "value": "left"
                                                },
                                                {
                                                    "label": "居中",
                                                    "value": "center"
                                                },
                                                {
                                                    "label": "靠右",
                                                    "value": "right"
                                                }
                                            ]
                                        },
                                        {
                                            "title": "文本配置",
                                            "key": null,
                                            "type": "font-config-slot",
                                            "range": [
                                                {
                                                    "value": null,
                                                    "text": "按钮",
                                                    "props": {
                                                        "color": "",
                                                        "size": 14,
                                                        "align": "center"
                                                    },
                                                    "usefather": {
                                                        "enable": true,
                                                        "key": "dataIndex"
                                                    }
                                                }
                                            ],
                                            "whole": true
                                        },
                                        {
                                            "key": "尺寸",
                                            "type": "radios-select",
                                            "value": "small",
                                            "options": [
                                                {
                                                    "label": "大",
                                                    "value": "large"
                                                },
                                                {
                                                    "label": "中",
                                                    "value": "middle"
                                                },
                                                {
                                                    "label": "小",
                                                    "value": "small"
                                                }
                                            ]
                                        },
                                        {
                                            "title": "圆角配置",
                                            "key": null,
                                            "type": "radios-select",
                                            "value": "none",
                                            "options": [
                                                {
                                                    "label": "无",
                                                    "value": "none"
                                                },
                                                {
                                                    "label": "弱",
                                                    "value": "sm"
                                                },
                                                {
                                                    "label": "中",
                                                    "value": "md"
                                                },
                                                {
                                                    "label": "大",
                                                    "value": "lg"
                                                },
                                                {
                                                    "label": "极",
                                                    "value": "xl"
                                                },
                                                {
                                                    "label": "圆",
                                                    "value": "circle"
                                                }
                                            ]
                                        },
                                        {
                                            "title": "按钮类型",
                                            "key": null,
                                            "type": "radios-select",
                                            "value": "default",
                                            "options": [
                                                {
                                                    "label": "基本",
                                                    "value": "primary"
                                                },
                                                {
                                                    "label": "虚线",
                                                    "value": "dashed"
                                                },
                                                {
                                                    "label": "超链接",
                                                    "value": "link"
                                                },
                                                {
                                                    "label": "文本",
                                                    "value": "text"
                                                },
                                                {
                                                    "label": "默认",
                                                    "value": "default"
                                                }
                                            ]
                                        },
                                        {
                                            "title": "额外配置",
                                            "key": null,
                                            "type": "checks-select",
                                            "value": [],
                                            "options": [
                                                {
                                                    "label": "幽灵",
                                                    "value": "ghost"
                                                },
                                                {
                                                    "label": "警告",
                                                    "value": "danger"
                                                },
                                                {
                                                    "label": "禁用",
                                                    "value": "disabled"
                                                }
                                            ]
                                        },
                                        {
                                            "title": "动作配置",
                                            "key": null,
                                            "type": "action-config",
                                            "actions": [
                                                {
                                                    "label": "点击",
                                                    "key": "click",
                                                    "content": {
                                                        "eventType": "advance",
                                                        "inputParams": [],
                                                        "callbackList": [],
                                                        "logicSource": {
                                                            "scriptContent": "var outputContext = {\n    \n  } ; const f_test =  (record ) => \n  {var L_1748331226328utodspwm = pageParams.edit_type; var R_1748331226328utodspwm = '详情'; if(L_1748331226328utodspwm === R_1748331226328utodspwm ){} else {var P_1748331226328irsogugd_record = record; var P_1748331226328irsogugd_origial_arr = getCommonVariableValue('1748328614385ckwadqzp'); const f_1748331226328hmncyrgw =  (record,origial_arr) => {var original_value=''\nfor (let index = 0; index < origial_arr.length; index++) {\n  if(origial_arr[index].id==record.id){\n    original_value=origial_arr[index].day_1\n    break\n  }\n}\nconsole.log(\"origial_arr\",JSON.stringify(origial_arr))\nconsole.log(\"original_value\",original_value)\nconsole.log(\"record\",JSON.stringify(record))\nif(record.day_1=='计划'||record.day_1=='--'){\n  record.day_1='完成'\n}else if(record.day_1=='完成'){\n  if(original_value=='计划'){\n    record.day_1='计划'\n  }else{\n    record.day_1='--'\n  }\n}\nreturn record\n};record =  f_1748331226328hmncyrgw(P_1748331226328irsogugd_record,P_1748331226328irsogugd_origial_arr);}return outputContext; }; return f_test (inputParams.record  )",
                                                            "pageContent": "{\"widgetList\":[{\"name\":\"条件判断\",\"class\":\"h3cicon h3c-condition\",\"actived\":true,\"key\":\"1748331210438zlnmsvqu\",\"renderItem\":\"H3cJudgement\",\"type\":\"H3cJudgement\",\"hasChildren\":true,\"error\":{\"code\":200,\"msgs\":\"\"},\"meta\":{\"conditionType\":\"===\",\"leftContent\":{\"valueType\":\"pageElement\",\"value\":\"PAGE_PARAMS$#^#$edit_type\"},\"rightContent\":{\"valueType\":\"string\",\"value\":\"详情\"},\"label\":\"条件判断\"},\"successList\":[],\"failureList\":[{\"name\":\"自定义代码\",\"class\":\"h3cicon h3c-custom-code\",\"actived\":true,\"key\":\"1748328708321otpevank\",\"type\":\"H3cCustomcode\",\"error\":{\"code\":200,\"msgs\":\"\"},\"meta\":{\"id\":0.8141117522790337,\"propName\":[\"record\"],\"propValue\":{\"input\":[{\"name\":\"record\"},{\"name\":\"origial_arr\"}],\"output\":[{\"name\":\"\"}],\"content\":\"var original_value=''\\nfor (let index = 0; index < origial_arr.length; index++) {\\n  if(origial_arr[index].id==record.id){\\n    original_value=origial_arr[index].day_1\\n    break\\n  }\\n}\\nconsole.log(\\\"origial_arr\\\",JSON.stringify(origial_arr))\\nconsole.log(\\\"original_value\\\",original_value)\\nconsole.log(\\\"record\\\",JSON.stringify(record))\\nif(record.day_1=='计划'||record.day_1=='--'){\\n  record.day_1='完成'\\n}else if(record.day_1=='完成'){\\n  if(original_value=='计划'){\\n    record.day_1='计划'\\n  }else{\\n    record.day_1='--'\\n  }\\n}\\nreturn record\\n\"},\"propType\":\"fixed\",\"label\":\"自定义代码\",\"isAsync\":false,\"isAwait\":false,\"assignmentList\":[{\"name\":\"record\",\"value\":[\"record\"],\"valueType\":\"variable\"},{\"name\":\"origial_arr\",\"value\":\"PAGE_VARIABLES$#^#$1748328614385ckwadqzp\",\"valueType\":\"pageElement\"}]},\"widgetKey\":\"js-editor\"}],\"widgetKey\":\"condition\"}],\"globalContext\":{\"variables\":[],\"inputParams\":[{\"name\":\"record\",\"type\":\"object\",\"key\":\"1748328446220rhidguwk\",\"children\":[],\"extendable\":true}],\"outputs\":[],\"options\":{},\"allVariables\":[{\"name\":\"record\",\"type\":\"object\",\"key\":\"1748328446220rhidguwk\",\"children\":[],\"extendable\":true},{\"name\":\"outputContext\",\"key\":\"outputContext\",\"type\":\"object\",\"children\":[]}]}}",
                                                            "params": {
                                                                "input": [
                                                                    {
                                                                        "name": "record",
                                                                        "type": "object",
                                                                        "key": "1748328446220rhidguwk",
                                                                        "children": [],
                                                                        "extendable": true
                                                                    }
                                                                ],
                                                                "output": []
                                                            },
                                                            "fromNodes": []
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            "title": "下拉按钮",
                                            "key": null,
                                            "type": "dropdown-button-config",
                                            "enable": false,
                                            "buttons": []
                                        },
                                        {
                                            "title": "属性变更",
                                            "key": null,
                                            "type": "logic-state-config-speed",
                                            "actions": [
                                                {
                                                    "label": "属性变更",
                                                    "key": "stateChange",
                                                    "content": {
                                                        "eventType": "logic",
                                                        "inputParams": [],
                                                        "callbackList": []
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            "title": "权限控制",
                                            "key": null,
                                            "type": "button-permission-config",
                                            "value": false
                                        }
                                    ]
                                },
                                "positionstyle": {
                                    "margin-right": "auto",
                                    "margin-left": "0"
                                },
                                "range": [
                                    {
                                        "style": {
                                            "color": "",
                                            "font-size": "14",
                                            "text-align": "center"
                                        },
                                        "usefather": {
                                            "enable": true,
                                            "key": "dataIndex"
                                        },
                                        "text": "dataIndex"
                                    }
                                ],
                                "slotSize": "small",
                                "danger": false,
                                "ghost": false,
                                "type": "default",
                                "shape": "",
                                "class": "rounded-none",
                                "disabled": false,
                                "show": true,
                                "handleLink": "e=>{var l;for(const t of e)!logicshow.args&&(logicshow.args=[]),!((l=logicshow.args)!=null&&l.some(a=>a.title==t.title&&a.flag))&&logicshow.args.push(t);console.log(logicshow.args)}",
                                "buttons": [],
                                "dropdown": false
                            }
                        ]
                    ],
                    "stateChange": {
                        "label": "属性变更",
                        "type": "state-config-speed",
                        "key": null,
                        "content": {
                            "eventType": "logic",
                            "inputParams": [],
                            "callbackList": [],
                            "logicSource": {
                                "scriptContent": null,
                                "pageContent": null,
                                "params": {
                                    "input": null,
                                    "output": null
                                }
                            }
                        }
                    },
                    "cascaderdata": {
                        "type": "record",
                        "key": ""
                    },
                    "layout": {
                        "rows": 1,
                        "cols": 1
                    }
                }
            ]
        }
    }
}
var template_str=JSON.stringify(template)
var tableColumnL=[]
for (let i = 1; i <=31; i++) {
    var b=template_str.replaceAll("day_1","day_"+i)
    var obj=JSON.parse(b)
    obj.title=i+"号"
    tableColumnL.push(obj)
}
console.log(JSON.stringify(tableColumnL))