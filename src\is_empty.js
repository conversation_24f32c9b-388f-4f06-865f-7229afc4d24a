var apiResNewL = JSON.parse(apiResL);
if (!(apiResNewL.code == 200 && apiResNewL.data && apiResNewL.data.code == 200)) {
    return resDataL;
}

var apiDataL = apiResNewL.data.data;
var processPriority = ['inkjet_code', 'print', 'cutting', 'die_cutting', 'gluing_box', 'making_box', 'finish'];

// 预处理：按 factory_details_id 分组
var apiDataMap = {};
for (var i = 0; i < apiDataL.length; i++) {
    var key = apiDataL[i].factory_details_id;
    apiDataMap[key] = apiDataMap[key] || [];
    apiDataMap[key].push(apiDataL[i]);
}

// 主处理逻辑
for (var j = 0; j < resDataL.length; j++) {
    var currentId = resDataL[j].csaleorderbid;
    var matchedData = apiDataMap[currentId];
    if (!matchedData) continue;

    var finalData = {
        stock_quantity: 0,
        factory_plan_delivery_date: '',
        inkjet_code: '',
        print: '',
        cutting: '',
        die_cutting: '',
        gluing_box: '',
        making_box: '',
        finish: ''
    };

    // 单条数据优化路径
    if (matchedData.length === 1) {
        var dataItem = matchedData[0];
        // 直接全量赋值
        finalData.stock_quantity = dataItem.stock_quantity || 0;
        finalData.factory_plan_delivery_date = dataItem.delivery_date || '';
        finalData.production_batch_number = dataItem.production_batch_number || '';
        for (var p = 0; p < processPriority.length; p++) {
            var process = processPriority[p];
            finalData[process] = dataItem[process] || '';
        }
    } 
    // 多条数据处理
    else {
        var highestPriorityIndex = -1;
        var bestMatch = null;
        
        // 找出最高优先级记录
        for (var k = 0; k < matchedData.length; k++) {
            var dataItem = matchedData[k];
            for (var p = processPriority.length - 1; p >= 0; p--) {
                if (dataItem[processPriority[p]] && p > highestPriorityIndex) {
                    highestPriorityIndex = p;
                    bestMatch = dataItem;
                    break;
                }
            }
            if (highestPriorityIndex === processPriority.length - 1) break;
        }

        if (bestMatch) {
            // 按工序优先级赋值
            finalData.stock_quantity = bestMatch.stock_quantity || 0;
            finalData.factory_plan_delivery_date = bestMatch.delivery_date || '';
            finalData.production_batch_number = bestMatch.production_batch_number || '';
            if (finalData.production_batch_number != '')
                finalData.production_batch_number = getValue(finalData.production_batch_number);
            for (var p = 0; p <= highestPriorityIndex; p++) {
                var process = processPriority[p];
                finalData[process] = bestMatch[process] || '';
            }
        }
    }

    // 赋值
    resDataL[j].stock_quantity = finalData.stock_quantity;
    resDataL[j].factory_plan_delivery_date = finalData.factory_plan_delivery_date;
    resDataL[j].inkjet_code = finalData.inkjet_code;
    resDataL[j].print = finalData.print;
    resDataL[j].cutting = finalData.cutting;
    resDataL[j].die_cutting = finalData.die_cutting;
    resDataL[j].gluing_box = finalData.gluing_box;
    resDataL[j].making_box = finalData.making_box;
    resDataL[j].finish = finalData.finish;
}

return resDataL;

function getValue(str) {
  return str.replace(/.{2}$/, '');
}