-- 当年,当期,当月回款金额
SELECT
    cust_code,
    SUM(CASE
            WHEN billdate >= DATE_FORMAT(NOW(), '%Y-01-01') THEN money
            ELSE 0
        END) AS year_payment_amount,
    SUM(CASE
            WHEN billdate >= DATE_SUB(DATE_FORMAT(NOW(), '%Y-%m-01'),
                                      INTERVAL (MONTH(NOW()) - 1) % 3 MONTH)
                THEN money
            ELSE 0
        END) AS quarter_payment_amount,
    SUM(CASE
            WHEN billdate >= DATE_FORMAT(NOW(), '%Y-%m-01') THEN money
            ELSE 0
        END) AS month_payment_amount
FROM crm_sales_received_payments
WHERE billdate >= DATE_FORMAT(NOW(), '%Y-01-01')
  AND flag_deleted = 0
GROUP BY cust_code;

-- 期初,期末欠款
SELECT cust_code,
       SUM(CASE
               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(
                       DATE_SUB(
                               DATE_FORMAT(NOW(), '%Y-%m-01'),
                               INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
                       ),
                       '%Y-%m'
                                                        ) THEN qkbbbye
               ELSE 0
           END) AS begin_outstanding_amount,
       SUM(CASE
               WHEN date_format(create_time, '%Y-%m') = date_format(now(), '%Y-%m')
                   THEN qkbbbye
               ELSE 0
           END) AS end_outstanding_amount
FROM crm_account_receivable_age
WHERE date_format(create_time, '%Y-%m') IN (
                                            DATE_FORMAT(
                                                    DATE_SUB(
                                                            DATE_FORMAT(NOW(), '%Y-%m-01'),
                                                            INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
                                                    ),
                                                    '%Y-%m'
                                            ),
                                            date_format(now(), '%Y-%m')
    )
  AND flag_deleted = 0
GROUP BY cust_code;

-- 当期销售开票金额
SELECT
    customer_no cust_code,
    ROUND(SUM(invoiced_amount_include), 2) AS invoiced_amount_include_current
FROM bip_outbound_order_detail bood
         LEFT JOIN (
    SELECT
        csrcid,
        csrcbid,
        SUM(norigtaxmny) invoiced_amount_include,
        DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
    FROM crm_sales_invoice_details
    WHERE flag_deleted = 0
    GROUP BY csrcid, csrcbid, dbilldate
) csid ON bood.outbound_header = csid.csrcid
    AND bood.outbound_line_id = csid.csrcbid
WHERE bood.flag_deleted = 0
  AND invoiced_date >= DATE_FORMAT(
        DATE_SUB(
                DATE_FORMAT(NOW(), '%Y-%m-01'),
                INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
        ),
        '%Y-%m-01'
                       )
  AND invoiced_date < DATE_FORMAT(now(),'%Y-%m-%d')
GROUP BY customer_no
;


-- 4个季度度欠款金额
SELECT
    cust_code,
    ROUND(SUM(CASE
                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-01')
                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-03')
                      THEN qkbbbye
                  ELSE 0
        END), 2) AS outstanding_amount_1,
    ROUND(SUM(CASE
                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-04')
                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-06')
                      THEN qkbbbye
                  ELSE 0
        END), 2) AS outstanding_amount_2,
    ROUND(SUM(CASE
                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-07')
                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-09')
                      THEN qkbbbye
                  ELSE 0
        END), 2) AS outstanding_amount_3,
    ROUND(SUM(CASE
                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-10')
                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-12')
                      THEN qkbbbye
                  ELSE 0
        END), 2) AS outstanding_amount_4
FROM crm_account_receivable_age
WHERE flag_deleted = 0
  AND date_format(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
GROUP BY cust_code
;
-- 4个季度开票金额
SELECT
    customer_no cust_code,
    ROUND(SUM(CASE
                  WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                      AND invoiced_date < DATE_FORMAT(NOW(), '%Y-04-01')
                      THEN invoiced_amount_include
                  ELSE 0
        END), 2) AS invoiced_amount_include_1,
    ROUND(SUM(CASE
                  WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-04-01')
                      AND invoiced_date < DATE_FORMAT(NOW(), '%Y-07-01')
                      THEN invoiced_amount_include
                  ELSE 0
        END), 2) AS invoiced_amount_include_2,
    ROUND(SUM(CASE
                  WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-07-01')
                      AND invoiced_date < DATE_FORMAT(NOW(), '%Y-10-01')
                      THEN invoiced_amount_include
                  ELSE 0
        END), 2) AS invoiced_amount_include_3,
    ROUND(SUM(CASE
                  WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-10-01')
                      AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                      THEN invoiced_amount_include
                  ELSE 0
        END), 2) AS invoiced_amount_include_4
FROM bip_outbound_order_detail bood
         LEFT JOIN (
    SELECT
        csrcid,
        csrcbid,
        SUM(norigtaxmny) invoiced_amount_include,
        DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
    FROM crm_sales_invoice_details
    WHERE flag_deleted = 0
    GROUP BY csrcid, csrcbid, dbilldate
) csid ON bood.outbound_header = csid.csrcid
    AND bood.outbound_line_id = csid.csrcbid
WHERE bood.flag_deleted = 0
  AND invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
  AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
GROUP BY customer_no
;

-- 4个季度的回款周期
WITH quarterly_outstanding AS (
    -- 获取每个季度的欠款数据
    SELECT
        cust_code,
        outstanding_amount_1,
        outstanding_amount_2,
        outstanding_amount_3,
        outstanding_amount_4
    FROM (
             SELECT
                 cust_code,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-01')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-03')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_1,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-04')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-06')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_2,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-07')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-09')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_3,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-10')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-12')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_4
             FROM crm_account_receivable_age
             WHERE flag_deleted = 0
               AND date_format(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
             GROUP BY cust_code
         ) t1
),
     quarterly_invoice AS (
         -- 获取每个季度的开票金额
         SELECT
             customer_no AS cust_code,
             invoiced_amount_include_1,
             invoiced_amount_include_2,
             invoiced_amount_include_3,
             invoiced_amount_include_4
         FROM (
                  SELECT
                      customer_no,
                      ROUND(SUM(CASE
                                    WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                                        AND invoiced_date < DATE_FORMAT(NOW(), '%Y-04-01')
                                        THEN invoiced_amount_include
                                    ELSE 0
                          END), 2) AS invoiced_amount_include_1,
                      ROUND(SUM(CASE
                                    WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-04-01')
                                        AND invoiced_date < DATE_FORMAT(NOW(), '%Y-07-01')
                                        THEN invoiced_amount_include
                                    ELSE 0
                          END), 2) AS invoiced_amount_include_2,
                      ROUND(SUM(CASE
                                    WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-07-01')
                                        AND invoiced_date < DATE_FORMAT(NOW(), '%Y-10-01')
                                        THEN invoiced_amount_include
                                    ELSE 0
                          END), 2) AS invoiced_amount_include_3,
                      ROUND(SUM(CASE
                                    WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-10-01')
                                        AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                                        THEN invoiced_amount_include
                                    ELSE 0
                          END), 2) AS invoiced_amount_include_4
                  FROM bip_outbound_order_detail bood
                           LEFT JOIN (
                      SELECT
                          csrcid,
                          csrcbid,
                          SUM(norigtaxmny) invoiced_amount_include,
                          DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
                      FROM crm_sales_invoice_details
                      WHERE flag_deleted = 0
                      GROUP BY csrcid, csrcbid, dbilldate
                  ) csid ON bood.outbound_header = csid.csrcid
                      AND bood.outbound_line_id = csid.csrcbid
                  WHERE bood.flag_deleted = 0
                    AND invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                    AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                  GROUP BY customer_no
              ) t2
     )

-- 计算每个季度的回款周期指标
SELECT
    o.cust_code,
    -- 第一季度回款周期
    CASE
        WHEN i.invoiced_amount_include_1 = 0 THEN NULL
        ELSE ROUND((o.outstanding_amount_1 / 180) / NULLIF(i.invoiced_amount_include_1, 0), 4)
        END AS payment_cycle_q1,
    -- 第二季度回款周期
    CASE
        WHEN i.invoiced_amount_include_2 = 0 THEN NULL
        ELSE ROUND((o.outstanding_amount_2 / 180) / NULLIF(i.invoiced_amount_include_2, 0), 4)
        END AS payment_cycle_q2,
    -- 第三季度回款周期
    CASE
        WHEN i.invoiced_amount_include_3 = 0 THEN NULL
        ELSE ROUND((o.outstanding_amount_3 / 180) / NULLIF(i.invoiced_amount_include_3, 0), 4)
        END AS payment_cycle_q3,
    -- 第四季度回款周期
    CASE
        WHEN i.invoiced_amount_include_4 = 0 THEN NULL
        ELSE ROUND((o.outstanding_amount_4 / 180) / NULLIF(i.invoiced_amount_include_4, 0), 4)
        END AS payment_cycle_q4
FROM quarterly_outstanding o
         LEFT JOIN quarterly_invoice i ON o.cust_code = i.cust_code
WHERE (i.invoiced_amount_include_1 > 0
    OR i.invoiced_amount_include_2 > 0
    OR i.invoiced_amount_include_3 > 0
    OR i.invoiced_amount_include_4 > 0)
ORDER BY o.cust_code;


-- 年度度欠款金额
SELECT
    cust_code,
    ROUND(SUM(CASE
                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-01')
                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-12')
                      THEN qkbbbye
                  ELSE 0
        END), 2) AS outstanding_amount_year
FROM crm_account_receivable_age
WHERE flag_deleted = 0
  AND date_format(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
GROUP BY cust_code
;
-- 年度开票金额
SELECT
    customer_no cust_code,
    ROUND(SUM(invoiced_amount_include), 2) AS invoiced_amount_include_year
FROM bip_outbound_order_detail bood
         LEFT JOIN (
    SELECT
        csrcid,
        csrcbid,
        SUM(norigtaxmny) invoiced_amount_include,
        DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
    FROM crm_sales_invoice_details
    WHERE flag_deleted = 0
    GROUP BY csrcid, csrcbid, dbilldate
) csid ON bood.outbound_header = csid.csrcid
    AND bood.outbound_line_id = csid.csrcbid
WHERE bood.flag_deleted = 0
  AND invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
  AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
GROUP BY customer_no;

-- 年度回款周期
WITH yearly_outstanding AS (
    -- 年度欠款金额
    SELECT
        cust_code,
        ROUND(SUM(CASE
                      WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-01')
                          OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-12')
                          THEN qkbbbye
                      ELSE 0
            END), 2) AS outstanding_amount_year
    FROM crm_account_receivable_age
    WHERE flag_deleted = 0
      AND date_format(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
    GROUP BY cust_code
),
     yearly_invoice AS (
         -- 年度开票金额
         SELECT
             customer_no cust_code,
             ROUND(SUM(invoiced_amount_include), 2) AS invoiced_amount_include_year
         FROM bip_outbound_order_detail bood
                  LEFT JOIN (
             SELECT
                 csrcid,
                 csrcbid,
                 SUM(norigtaxmny) invoiced_amount_include,
                 DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
             FROM crm_sales_invoice_details
             WHERE flag_deleted = 0
             GROUP BY csrcid, csrcbid, dbilldate
         ) csid ON bood.outbound_header = csid.csrcid
             AND bood.outbound_line_id = csid.csrcbid
         WHERE bood.flag_deleted = 0
           AND invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
           AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
         GROUP BY customer_no
     )

-- 计算年度回款周期指标
SELECT
    o.cust_code,
    CASE
        WHEN i.invoiced_amount_include_year = 0 THEN NULL
        ELSE ROUND((o.outstanding_amount_year / 720) / NULLIF(i.invoiced_amount_include_year, 0), 4)
        END AS payment_cycle_year
FROM yearly_outstanding o
         LEFT JOIN yearly_invoice i ON o.cust_code = i.cust_code
WHERE i.invoiced_amount_include_year > 0
ORDER BY o.cust_code;

-- 客户信息及考核指标
select
    '西安环球'                                                                                           sale_company,
    t1.deparment_code,
    t1.deparment_name,
    t1.department_region region,
    t1.cust_code,
    t1.cust_name,
    t1.cust_manager_code,
    t1.cust_manager_name,
    t1.cust_type,
    CASE
        WHEN t1.cust_type = 0 THEN '新建客户'
        WHEN t1.cust_type = 1 THEN '公海客户'
        WHEN t1.cust_type = 2 THEN '合作客户'
        WHEN t1.cust_type = 3 THEN '开发中客户'
        WHEN t1.cust_type = 4 THEN '受限客户'
        END                                                                                              cust_type_name,
    t2.payment_cycle_target  -- 考核指标
from
(WITH RankedCustomers AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
    FROM crm_cust_basic where flag_deleted=0 and cust_type not in (0)
)
SELECT
    *
FROM RankedCustomers
WHERE rn = 1)t1 left join metric_person t2 on t1.cust_manager_code=t2.cust_manager_code and t2.flag_deleted=0
where t2.metric_year=year(now())
;


