var temp_data=[
    {
        "ZHEYE_RSL": null,
        "HAO": 28,
        "BANBIEDM": "01",
        "NIANDUDM": 2025,
        "XIAOQI": 5,
        "ID": "ede4653b-f637-11ef-9caf-00163e0ac7c1",
        "GongSiDM": "FNR",
        "RQ": "2025-03-01"
    },
    {
        "ZHEYE_RSL": null,
        "HAO": 29,
        "BANBIEDM": "02",
        "NIANDUDM": 2025,
        "XIAOQI": 5,
        "ID": "0b9f0bc9-f67d-11ef-9caf-00163e0ac7c1",
        "GongSiDM": "FNR",
        "RQ": "2025-03-01"
    },
    {
        "ZHEYE_RSL": 205.62,
        "HAO": 30,
        "BANBIEDM": "03",
        "NIANDUDM": 2025,
        "XIAOQI": 5,
        "ID": "8d4ac236-f6bb-11ef-9caf-00163e0ac7c1",
        "GongSiDM": "FNR",
        "RQ": "2025-03-01"
    },
    {
        "ZHEYE_RSL": null,
        "HAO": 28,
        "BANBIEDM": "01",
        "NIANDUDM": 2025,
        "XIAOQI": 5,
        "ID": "ede4653b-f637-11ef-9caf-00163e0ac7c1",
        "GongSiDM": "FNR",
        "RQ": "2025-03-01"
    },
    {
        "ZHEYE_RSL": null,
        "HAO": 29,
        "BANBIEDM": "02",
        "NIANDUDM": 2025,
        "XIAOQI": 5,
        "ID": "0b9f0bc9-f67d-11ef-9caf-00163e0ac7c1",
        "GongSiDM": "FNR",
        "RQ": "2025-03-01"
    },
    {
        "ZHEYE_RSL": 205.62,
        "HAO": 30,
        "BANBIEDM": "03",
        "NIANDUDM": 2025,
        "XIAOQI": 5,
        "ID": "8d4ac236-f6bb-11ef-9caf-00163e0ac7c1",
        "GongSiDM": "FNR",
        "RQ": "2025-03-01"
    }
]
// 使用对象作为哈希表来去重ID
var id_hash = {};
for (var i = 0; i < temp_data.length; i++) {
    if (temp_data[i].ID) {
        id_hash[temp_data[i].ID] = true;
    }
}

// 获取所有唯一的ID
var unique_ids = [];
for (var id in id_hash) {
    if (id_hash.hasOwnProperty(id)) {
        unique_ids.push(id);
    }
}

// 输出结果
console.log(unique_ids);