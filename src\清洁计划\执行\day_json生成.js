var obj= 
    {
        "create_time": "2025-05-27 16:21:48",
        "responsible_position": "23",
        "responsible_person": "234",
        "section_name": "精糖干燥工段",
        "task_code": "Clean0010",
        "task_desc": "23",
        "cycle_type": "末端循环",
        "frequency_value": "[1号,3号]",
        "create_by": "顾俊翔",
        "update_time": "2025-05-27 16:21:48",
        "section_code": "A07",
        "frequency_type": "固定日期",
        "id": 13,
        "update_by": "顾俊翔",
        "flag_deleted": 0
    }
//如果frequency_type=='固定日期',则frequency_value为日期,枚举值 '1号'~'31号' 如:frequency_value=='1号' 对应day_json.day_1='计划' 
//如果frequency_type=='固定天数',则frequency_value为间隔天数,枚举值:'每1天'~'每7天',如:则frequency_value=='每7天',同时判断cycle_type,如果是首端循环,则frequency_value=='每7天',对应day_json.day_1='计划',day_json.day_8='计划',day_json.day_15='计划',day_json.day_22='计划',day_json.day_29='计划'
//如果cycle_type=='末端循环',则frequency_value=='每7天',对应day_json.day_7='计划',day_json.day_14='计划',day_json.day_21='计划',day_json.day_28='计划'
var day_json = {};
// 通过循环初始化31天的数据
for(var i = 1; i <= 31; i++) {
    day_json['day_' + i] = '--';
}
var frequency_value_arr=obj.frequency_value.replace ("[","").replace ("]","").split(",")
// 处理固定日期类型
if (obj.frequency_type === '固定日期') {
    for(var i=0;i<frequency_value_arr.length;i++){
        var new_frequency_value=frequency_value_arr[i].trim();
        var day_number = parseInt(new_frequency_value.replace('号', ''));
        day_json['day_' + day_number] = '计划';
    }
}

// 处理固定天数类型
if (obj.frequency_type === '固定天数') {
    for(var i=0;i<frequency_value_arr.length;i++){
        var new_frequency_value=frequency_value_arr[i].trim();
        var interval = parseInt(new_frequency_value.replace('每', '').replace('天', ''));
        if (obj.cycle_type === '末端循环') {
            // 从interval开始，每隔interval天设置一次计划
            for (var i = interval; i <= 31; i += interval) {
                day_json['day_' + i] = '计划';
            }
        } else {
            // 首端循环从第1天开始计算
            for (var i = 1; i <= 31; i += interval) {
                day_json['day_' + i] = '计划';
            }
        }
    }
}

console.log(JSON.stringify(day_json));