-- MySQL dump 10.13  Distrib 8.0.19, for Win64 (x86_64)
--
-- Host: **************    Database: hq_mdm_b
-- ------------------------------------------------------
-- Server version	8.0.29

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `check_item`
--

DROP TABLE IF EXISTS `check_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `check_item` (
  `checking_id` int DEFAULT NULL COMMENT '点检计划id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备编码',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备名称',
  `classification` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资产分类',
  `department` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '使用部门',
  `states` int DEFAULT NULL COMMENT '使用状态',
  `manager` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机台负责人',
  `executor` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '点检执行人',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='点检项目';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `comparison_table`
--

DROP TABLE IF EXISTS `comparison_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comparison_table` (
  `material_classification` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物料分类编码',
  `material_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物料类型',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='物料分类对照表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `configure_middle_table`
--

DROP TABLE IF EXISTS `configure_middle_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `configure_middle_table` (
  `status` int NOT NULL COMMENT '状态',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `scheme_id` int NOT NULL COMMENT '验证方案id',
  `config_id` int NOT NULL COMMENT '配置设备id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='配置管理关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cpda_cs`
--

DROP TABLE IF EXISTS `cpda_cs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cpda_cs` (
  `nuit` int NOT NULL COMMENT '单位',
  `product_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '产品名称',
  `product_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '产品编码',
  `min` int NOT NULL COMMENT '小类',
  `big` int NOT NULL COMMENT '大类',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品档案测试';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_accessory_equipment`
--

DROP TABLE IF EXISTS `em_accessory_equipment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_accessory_equipment` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `equipment_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附属设备名称',
  `equipment_specification` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附属设备规格',
  `unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位',
  `count` int DEFAULT NULL COMMENT '数量',
  `price` decimal(20,10) DEFAULT NULL COMMENT '金额/元',
  `remake` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `device_id` int DEFAULT NULL COMMENT '设备id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备固定资产附属设备';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_association_between`
--

DROP TABLE IF EXISTS `em_association_between`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_association_between` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `maintain_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '维修单号',
  `failure_location` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '故障名称',
  `repair_report_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '报修人',
  `maintain_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '维修人',
  `failure_description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '故障描述',
  `failure_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '故障类型',
  `failure_deveice_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '故障设备',
  `maintenance_id` int DEFAULT NULL COMMENT '维修知识库ID',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='维修知识库关联案例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_check_sheet`
--

DROP TABLE IF EXISTS `em_check_sheet`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_check_sheet` (
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
  `plan_id` int NOT NULL COMMENT '点检计划id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `period` date NOT NULL COMMENT '周期',
  `equipment` int NOT NULL COMMENT '点检结果',
  `check_items` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '点检项目/部位',
  `check_content` int NOT NULL COMMENT '点检内容',
  `check_condition` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '点检状态',
  `reference_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参考值',
  `actual_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '实际值',
  `inspection_executor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '点检执行人',
  `execution_time` date NOT NULL COMMENT '执行时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='点击计划和点检记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_checking_plan`
--

DROP TABLE IF EXISTS `em_checking_plan`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_checking_plan` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检工单ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '点检工单名称',
  `inspection` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检方案',
  `checked` int DEFAULT NULL COMMENT '点检设备数',
  `states` int NOT NULL DEFAULT '0' COMMENT '状态',
  `device_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备名称',
  `device_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
  `project_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计划名称',
  `plan_description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '计划描述',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `classification` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检设备id',
  `checking` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检设备数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='点检计划';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_device_coordination`
--

DROP TABLE IF EXISTS `em_device_coordination`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_device_coordination` (
  `reality_date` date DEFAULT NULL COMMENT '实际交付日期',
  `notes` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `status_time` datetime DEFAULT NULL COMMENT '状态更新时间',
  `score` int DEFAULT NULL COMMENT '评分',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `oa_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT '—' COMMENT 'OA申请单号',
  `maintain_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT '——' COMMENT '设备维修申请单号',
  `client` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '委托方',
  `coordination_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '外协类型1.外协加工2.外协维修',
  `applicant` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请人',
  `department` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门',
  `process_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '加工件名称',
  `process_date` date DEFAULT NULL COMMENT '加工日期',
  `process_num` int DEFAULT NULL COMMENT '加工数量',
  `estimate_cost` decimal(12,5) DEFAULT NULL COMMENT '预估费用',
  `specifications` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规格',
  `priority` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '优先级1.一般2.紧急',
  `unit` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位',
  `deliver_date` date DEFAULT NULL COMMENT '要求交付日期',
  `technology_illustrate` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '技术要求说明',
  `use_to` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT '——' COMMENT '用途',
  `status` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '状态1.未提交2.审批中3.审批通过4.审批不通过5.验收完成',
  `reality_cost` decimal(12,5) DEFAULT NULL COMMENT '实际费用',
  `maintenance_reason` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '外协维修原因',
  `maintenance_content` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '外协维修内容',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `oa_time` datetime DEFAULT NULL COMMENT 'oa审批时间',
  `accept_time` datetime DEFAULT NULL COMMENT '验收时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备外协';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_device_maintain_file`
--

DROP TABLE IF EXISTS `em_device_maintain_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_device_maintain_file` (
  `file_name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '文件名称',
  `file_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件id',
  `maintain_id` int DEFAULT NULL COMMENT '维修单id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='设备报修图片';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_equipment_step`
--

DROP TABLE IF EXISTS `em_equipment_step`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_equipment_step` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `OA_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA申请编码',
  `OA_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'OA申请名称',
  `status` int DEFAULT NULL COMMENT '状态(1未提交，2审批中，3审批通过，4审批不通过)',
  `approver` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '审批人',
  `device_id` int DEFAULT NULL COMMENT '设备id',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `type` int DEFAULT NULL COMMENT '设备固定资产步骤',
  `OA_approval_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '审批通过时间',
  `submission_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '提交时间',
  `status_update_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态修改时间',
  `proof_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '验证原由',
  `create_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '制造日期',
  `proof_date_one` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '验证日期1(新机验证)',
  `proof_date_two` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '验证日期2(新机验证)',
  `mkid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '审批流id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='设备固定资产步骤处理列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_fixed_assets`
--

DROP TABLE IF EXISTS `em_fixed_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_fixed_assets` (
  `validation_report_status` int DEFAULT '0' COMMENT '验证报告是否提交草稿1、点击过保存草稿',
  `isposal_recorder` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处置记录人',
  `write_time` datetime DEFAULT NULL COMMENT '处置记录时间',
  `valuation_value_disposal` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处置评估现值',
  `transaction_amount` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '处置成交金额（元）',
  `trading_party` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '交易方',
  `disposal_date` date DEFAULT NULL COMMENT '处置日期',
  `original_value_of_assets` decimal(20,10) DEFAULT NULL COMMENT '资产原值（元）',
  `net_asset_value` decimal(20,10) DEFAULT NULL COMMENT '资产净值（元）',
  `purchase_date` date DEFAULT NULL COMMENT '采购日期',
  `date_of_retirement` date DEFAULT NULL COMMENT '报废日期',
  `discard_reason` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '报废原因',
  `tc_applicant` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '投产申请人',
  `tc_application_date` date DEFAULT NULL COMMENT '投产申请日期',
  `tzRemake` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '投产备注',
  `application_date` date DEFAULT NULL COMMENT '申请日期',
  `applicant` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '申请人',
  `remake` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '型号',
  `specification` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '规格',
  `call_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '调入来源',
  `price` decimal(20,10) DEFAULT NULL COMMENT '价格/元',
  `manufacturer` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '制造商',
  `supplier` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商',
  `producing_country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产国',
  `factory_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '生产厂名',
  `date_of_production` date DEFAULT NULL COMMENT '出厂日期',
  `factory_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '出厂编号',
  `vendor_contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '厂商联系人',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '厂商联系电话',
  `detailed_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '详细地址',
  `postal_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '邮政编码',
  `date_of_acceptance` date DEFAULT NULL COMMENT '验收日期',
  `commissioning_date` date DEFAULT NULL COMMENT '投产日期',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除(0是未删除，1是已删除)',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plant_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '厂内编号',
  `equipment_coding` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
  `devicename` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备名称',
  `device_id` int DEFAULT NULL COMMENT '设备类型管理ID',
  `classification_assets` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资产分类（资产卡片来源）',
  `device_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备类型',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '使用部门',
  `status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态',
  `machine_manager` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机台负责人',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `device_id_identification` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备id',
  `maintenance_cost` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '维保成本',
  `relevant_department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '归口部门',
  `currency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '币种',
  `bf_relevant_department` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '申请报废归口部门',
  `bf_applicant` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '报废申请人',
  `bf_application_date` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '报废申请时间',
  `code_of_fixed_assets` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '固定资产编码',
  `requirement_application_time` date DEFAULT NULL COMMENT '需求申请通过',
  `purchase_application_time` date DEFAULT NULL COMMENT '采购申请通过',
  `installation_complete_time` date DEFAULT NULL COMMENT '安装完成',
  `verification_completed_time` date DEFAULT NULL COMMENT '验证完成',
  `bf_remake` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '报废备注',
  `classification_assets_tc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '资产分类（申请验收）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='设备固定资产管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_fixed_assets_technical`
--

DROP TABLE IF EXISTS `em_fixed_assets_technical`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_fixed_assets_technical` (
  `type` int DEFAULT NULL COMMENT '1技术资料2设备调研3.验证方案4.设备维修',
  `file_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件id',
  `equipment_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `file_name` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '文件名称',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=97 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备技术资料';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_installation_record`
--

DROP TABLE IF EXISTS `em_installation_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_installation_record` (
  `record_date` date DEFAULT NULL COMMENT '日期',
  `job_content` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工作内容',
  `open_question` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '存在的问题',
  `installer` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '安装人',
  `notekeeper` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '记录人',
  `equipment_id` int DEFAULT NULL COMMENT '设备id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备资产安装记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintain_spare_parts`
--

DROP TABLE IF EXISTS `em_maintain_spare_parts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintain_spare_parts` (
  `outbound_quantity` int DEFAULT NULL COMMENT '出库数量',
  `registered_usage_quantity` int DEFAULT NULL COMMENT '登记使用数量',
  `unit_price` double(20,10) DEFAULT NULL COMMENT '单价',
  `total_price` double(20,10) DEFAULT NULL COMMENT '总价',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `spare_parts_code` char(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备品备件编号(物料编号)',
  `spare_partes_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备品备件名称(物料名称)',
  `maintain_id` int DEFAULT NULL COMMENT '维修单id',
  `specification` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规格型号',
  `current_usage_quantity` int DEFAULT NULL COMMENT '当前使用数量',
  `unit` char(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备维修_备品备件使用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintain_status`
--

DROP TABLE IF EXISTS `em_maintain_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintain_status` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `status_code` int NOT NULL COMMENT '状态',
  `status_desc` char(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '状态描述',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备维修状态';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_maintenance_knowledge`
--

DROP TABLE IF EXISTS `em_maintenance_knowledge`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_maintenance_knowledge` (
  `maintain_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '维修单号',
  `failure_deveice_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '故障设备',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '知识编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '知识名称',
  `fault_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '故障类型',
  `related_case` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '关联案例',
  `fault_phenomenon` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '故障现象',
  `cause_analysis` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '故障原因分析',
  `measures` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '维修措施',
  `details` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '知识详情',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='维修知识库';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_proof_scheme`
--

DROP TABLE IF EXISTS `em_proof_scheme`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_proof_scheme` (
  `maintain_id` int DEFAULT NULL COMMENT '维修单id',
  `proof_date` date DEFAULT NULL COMMENT '验证日期',
  `proof_reason` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '验证原由',
  `category` int DEFAULT NULL COMMENT '类别:1,方案;2.报告',
  `file_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT '——' COMMENT '文件名称',
  `facility_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `oa_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT '——' COMMENT 'OA申请编码',
  `oa_state` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '状态：1.未提交2.审批中3.审批通过4.审批不通过',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `type` int DEFAULT NULL COMMENT '验证类别',
  `key_parts_description` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备维修:变更验证(关键部位描述)',
  `create_date` datetime DEFAULT NULL COMMENT '制造日期',
  `proof_date_two` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '验证日期2(新机验证)',
  `proof_date_one` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '验证日期1(新机验证)',
  `device_id` int DEFAULT NULL COMMENT '设备类别id',
  `equipment_coding` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备编码',
  `proof_number` int DEFAULT NULL COMMENT '验证次数',
  `manufacturer` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '制造商',
  `model` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '型号',
  `application_dept` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门名称',
  `application_department` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请部门',
  `applicant_time` datetime DEFAULT NULL COMMENT '申请时间',
  `applicant` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请人',
  `update_date` datetime DEFAULT NULL COMMENT 'OA状态更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='验证方案管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_spot_check_items`
--

DROP TABLE IF EXISTS `em_spot_check_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_spot_check_items` (
  `spot_check_name` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检项名称',
  `spot_check_status` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '1,启用，2禁用',
  `spot_check_cycle` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检周期',
  `spot_check_type` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '1，单值勾选；2数值输入',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `spot_check_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检项编码',
  `spot_check_site` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检部位',
  `spot_check_content` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检内容',
  `spot_check_method` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检方法',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='点检项管理弃置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_spot_check_items_picture`
--

DROP TABLE IF EXISTS `em_spot_check_items_picture`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_spot_check_items_picture` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `check_Id` int DEFAULT NULL COMMENT '点检项id',
  `file_name` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件名称',
  `file_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件上传后返回的id',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='点检项参考图片';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_spot_check_items_type`
--

DROP TABLE IF EXISTS `em_spot_check_items_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_spot_check_items_type` (
  `reference_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单位',
  `reference_value_2` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参考值2',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `spot_check_id` int DEFAULT NULL COMMENT '点检项id',
  `spot_check_value` varchar(225) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '勾选值',
  `reference_value_1` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '参考值1',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='点检项类型';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `em_verify_file`
--

DROP TABLE IF EXISTS `em_verify_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `em_verify_file` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `manage_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '管理编码',
  `file_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件id',
  `file_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='验证方案管理文件上传';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ems_device_type`
--

DROP TABLE IF EXISTS `ems_device_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ems_device_type` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `device_code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备类型编码',
  `device_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备类型名称',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='ems_device_type设备类型管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ems_fixed_assets`
--

DROP TABLE IF EXISTS `ems_fixed_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ems_fixed_assets` (
  `application_date` date DEFAULT NULL COMMENT '申请日期',
  `applicant` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请人',
  `remake` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `model` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '型号',
  `specification` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规格',
  `call_source` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '调入来源',
  `price` decimal(20,10) DEFAULT NULL COMMENT '价格/元',
  `manufacturer` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '制造商',
  `supplier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商',
  `producing_country` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '生产国',
  `factory_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '生产厂名',
  `date_of_production` date DEFAULT NULL COMMENT '出厂日期',
  `factory_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '出厂编号',
  `vendor_contact` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '厂商联系人',
  `contact_phone` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '厂商联系电话',
  `detailed_address` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '详细地址',
  `postal_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '邮政编码',
  `date_of_acceptance` date DEFAULT NULL COMMENT '验收日期',
  `commissioning_date` date DEFAULT NULL COMMENT '投产日期',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除(0是未删除，1是已删除)',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `plant_number` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '厂内编号',
  `equipment_coding` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备编码',
  `devicename` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备名称',
  `device_id` int DEFAULT NULL COMMENT '设备类型管理ID',
  `classification_assets` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资产分类',
  `device_type` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备类型',
  `department` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '使用部门',
  `status` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '状态',
  `machine_manager` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机台负责人',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `demand_application_times` date DEFAULT NULL COMMENT '需求申请通过时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备固定资产管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ems_process_consumption_ratio`
--

DROP TABLE IF EXISTS `ems_process_consumption_ratio`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ems_process_consumption_ratio` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `process_id` int NOT NULL COMMENT '工序id',
  `start_value` int NOT NULL COMMENT '起始数字',
  `end_value` int NOT NULL COMMENT '结束数字',
  `consumption_rate` decimal(10,5) DEFAULT NULL COMMENT '消耗率',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序运转消耗比例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ems_process_operation_unit`
--

DROP TABLE IF EXISTS `ems_process_operation_unit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ems_process_operation_unit` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `process_id` int NOT NULL COMMENT '工序id',
  `unit_name` char(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '单位名称',
  `is_default` int DEFAULT NULL COMMENT '是否默认',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序作业单位';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `ems_process_plate_type`
--

DROP TABLE IF EXISTS `ems_process_plate_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ems_process_plate_type` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `process_id` int NOT NULL COMMENT '工序id',
  `parameter_id` int DEFAULT NULL COMMENT '参数id',
  `parameter_value` char(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '参数名称',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序默认用版类型';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `machine_calendar`
--

DROP TABLE IF EXISTS `machine_calendar`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `machine_calendar` (
  `strat_day` date NOT NULL COMMENT '开始日期',
  `end_time` date NOT NULL COMMENT '结束日期',
  `week_day` date NOT NULL COMMENT '上班日期',
  `work_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '资源中心id',
  `machine_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '机台id',
  `shift_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '班次集合id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=41756 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mom_机台日历表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `machine_file`
--

DROP TABLE IF EXISTS `machine_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `machine_file` (
  `unitt` int DEFAULT NULL COMMENT '单位(1min，1张)',
  `quality_dept_auto` int DEFAULT NULL COMMENT '其他配置首件检验',
  `quality_dept_task_gener_mode` int DEFAULT '2' COMMENT '质量部过程检验任务生成方式（1按时间，2按纸张，3按检验抽样标准）',
  `quality_dept_interval` int DEFAULT '2000' COMMENT '质量部过程检验间隔',
  `the_machine_first_check` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机台首检',
  `parturition_task_gener_mode` int DEFAULT NULL COMMENT '任务生成方式（1按时间，2按张数，3检验抽样标准）',
  `parturition_interval` int DEFAULT NULL COMMENT '生产过程检验间隔',
  `parturition_min` int DEFAULT NULL COMMENT '生产过程检验min',
  `isdelete` int DEFAULT '0' COMMENT '是否删除',
  `model_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机型类型',
  `falg_start` int DEFAULT '0' COMMENT '启用禁用状态',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `machine_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机台名称',
  `code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机台编号',
  `model` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机型名称',
  `workshop` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属车间',
  `workcenter` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工作中心',
  `annotation` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `resource_center_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源中心编码',
  `machine_ip` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机台ip',
  `workshop_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '车间编号',
  `money_every_hour` decimal(20,2) DEFAULT NULL COMMENT '标准工时(元/小时)',
  `inspection_quantity_requiremen` int DEFAULT '0' COMMENT '巡检要求数量',
  `inspection_alarm_time` int DEFAULT NULL COMMENT '巡检报警时间',
  `is_lock` varchar(255) DEFAULT NULL COMMENT '锁定状态:0:正常;1:锁定',
  `tooth_devices` int DEFAULT NULL COMMENT '牙排设备0禁用1启用',
  `floor_space` varchar(255) DEFAULT NULL COMMENT '占地面积（平方米）',
  `equipment_power` varchar(255) DEFAULT NULL COMMENT '设备功率（千瓦/时）',
  `number_crew` int DEFAULT NULL COMMENT '班组人数（个）',
  `equipment_value` varchar(255) DEFAULT NULL COMMENT '设备价值(万元)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=155 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mes_机台档案表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_mom_afferent`
--

DROP TABLE IF EXISTS `mdm_mom_afferent`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_mom_afferent` (
  `module_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '菜单名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `document_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单据号',
  `data_sources` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数据来源（FZ---方正,ERP--企业ERP）',
  `data` longtext COMMENT '数据（json格式）',
  `state` int DEFAULT '0' COMMENT '状态（0未消费  1已消费)',
  `link` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '链路',
  `cost_time` datetime DEFAULT NULL COMMENT '消费时间',
  `msg` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '失败提示',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `data_sources` (`data_sources`) USING BTREE,
  KEY `document_number` (`document_number`) USING BTREE,
  KEY `module_name` (`module_name`) USING BTREE,
  KEY `state` (`state`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=633820 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='传入中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_mom_afferent_copy1`
--

DROP TABLE IF EXISTS `mdm_mom_afferent_copy1`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_mom_afferent_copy1` (
  `module_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `document_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单据号',
  `data_sources` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数据来源（FZ---方正,ERP--企业ERP）',
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '数据（json格式）',
  `state` int NOT NULL DEFAULT '0' COMMENT '状态（0未消费  1已消费)',
  `link` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '链路',
  `cost_time` datetime DEFAULT NULL COMMENT '消费时间',
  `msg` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '失败提示',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='传入中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mdm_mom_outgoing`
--

DROP TABLE IF EXISTS `mdm_mom_outgoing`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mdm_mom_outgoing` (
  `operope_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '删除修改新增',
  `api_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '路径',
  `data_To` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数据去向（FZ---方正，ERP---企业ERP）',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `document_Number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单据号',
  `data_Sources` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数据来源(来源本系统具体功能)',
  `data` longtext,
  `state` int DEFAULT '0' COMMENT '状态（0未消费  1已消费)',
  `link` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '链路',
  `cost_time` datetime DEFAULT NULL COMMENT '消费时间',
  `msg` varchar(9999) DEFAULT NULL,
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `data_Sources` (`data_Sources`) USING BTREE,
  KEY `data_To` (`data_To`) USING BTREE,
  KEY `document_number` (`document_Number`) USING BTREE,
  KEY `state` (`state`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=217884 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='传出中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_accessories`
--

DROP TABLE IF EXISTS `mes_accessories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_accessories` (
  `id_jianyanmuban` int DEFAULT NULL COMMENT '关联检验模板id',
  `place` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '产地',
  `flag_status` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '数据状态：1:草稿，2:生效',
  `file_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '图片文件id',
  `accessories_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '辅料编号',
  `accessories_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '辅料名称',
  `large_class` int NOT NULL COMMENT '大类',
  `little_class` int NOT NULL COMMENT '小类',
  `specification` int NOT NULL COMMENT '规格',
  `standard_unit` int NOT NULL COMMENT '标准单位',
  `brand` int NOT NULL COMMENT '品牌',
  `safe_stock` int NOT NULL DEFAULT '0' COMMENT '安全库存物料',
  `max_stock` decimal(16,8) DEFAULT NULL COMMENT '最大库存数量',
  `min_stock` decimal(16,8) DEFAULT NULL COMMENT '最小库存数量',
  `describes` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '描述',
  `purchase_price` decimal(14,6) DEFAULT NULL COMMENT '标准采购价',
  `purchase_unit` int NOT NULL COMMENT '采购单位',
  `min_purchase` decimal(14,6) DEFAULT '1.000000' COMMENT '最小采购量',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='辅料档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_accessories_file`
--

DROP TABLE IF EXISTS `mes_accessories_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_accessories_file` (
  `paper_Id` int NOT NULL COMMENT '辅料档案id',
  `file_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '上传后返回id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `picture` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '物料文件',
  `file_type` int NOT NULL COMMENT '文件类型',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='辅料档案图片';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_accessories_subclass`
--

DROP TABLE IF EXISTS `mes_accessories_subclass`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_accessories_subclass` (
  `area_unit` int DEFAULT NULL COMMENT '1.平方米2.平方厘米3.平方毫米4.个',
  `big_class` int NOT NULL COMMENT '大类',
  `small_class` int NOT NULL COMMENT '小类',
  `status` int NOT NULL DEFAULT '2' COMMENT '状态；1.启用2.禁用',
  `units` int DEFAULT NULL COMMENT '单位',
  `count_mode` int DEFAULT NULL COMMENT '计算方式：1.按版面积2.按产品个数',
  `product_num` decimal(10,2) DEFAULT NULL COMMENT '产品数量',
  `material_num` decimal(14,6) DEFAULT NULL COMMENT '物料数据',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=901 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='辅料小类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_accessories_template`
--

DROP TABLE IF EXISTS `mes_accessories_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_accessories_template` (
  `material_id` int DEFAULT NULL COMMENT '辅料id',
  `template_id` int DEFAULT NULL COMMENT '关联检验模板id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1521 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='物料检验模板配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_area_mode`
--

DROP TABLE IF EXISTS `mes_area_mode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_area_mode` (
  `type_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `mode_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `mode_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `parent_mode_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `sort_no` int DEFAULT NULL,
  `status_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=45104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_arrival_notice`
--

DROP TABLE IF EXISTS `mes_arrival_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_arrival_notice` (
  `arrival_header_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货单表头主键',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `arrival_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货单号',
  `arrival_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货类型',
  `arrival_date` datetime DEFAULT NULL COMMENT '到货日期',
  `supplier_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码',
  `purchaser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购员',
  `purchaser_department` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购部门',
  `purchaser_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单号',
  `submitted_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '送检人',
  `submitted_time` datetime DEFAULT NULL COMMENT '送检时间',
  `data_sources` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数据来源（WMS--WMS）',
  `document_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单据号.',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1063 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='到货单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_arrival_notice_particulars`
--

DROP TABLE IF EXISTS `mes_arrival_notice_particulars`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_arrival_notice_particulars` (
  `arrival_header_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货单表头主键',
  `treatment_measures` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '处理措施（0接收，1让步接收，2拒收）',
  `supplier_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商名称',
  `material_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料类型（1纸张，2辅料，3包材，4外协）',
  `inspection_type_status` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '任务类型组合(1,12,123)1物流检验，2QC检验，3实验室检验',
  `remake` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `planned_delivery_date` datetime DEFAULT NULL COMMENT '计划收货日期',
  `gift` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '赠品',
  `conversion_rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '换算率',
  `standard_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主单位',
  `order_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主数量',
  `arrival_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数量',
  `procure_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位',
  `material_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料名称',
  `line_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行号',
  `arrival_body_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货单表体主键',
  `results` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '结果 ,0合格，1不合格',
  `status` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '整体状态  ,0未完成，1已完成',
  `supplier_batch` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商批次',
  `schedule` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '进度',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `production_batch_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '生产批次号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='到货详情表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_categroy_template`
--

DROP TABLE IF EXISTS `mes_categroy_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_categroy_template` (
  `jybd_id` int DEFAULT NULL COMMENT '检验表单id',
  `template_id` int DEFAULT NULL COMMENT '模板id',
  `small_id` int DEFAULT NULL COMMENT '小类id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=108 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='大小类配置检验模板';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_craft`
--

DROP TABLE IF EXISTS `mes_craft`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_craft` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工艺参数设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_craft_install`
--

DROP TABLE IF EXISTS `mes_craft_install`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_craft_install` (
  `process_id` int NOT NULL COMMENT '工序设置主表id',
  `craft_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工艺参数编号',
  `craft_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工艺参数名称',
  `status` int NOT NULL COMMENT '状态：1.启用2.禁用',
  `requireds` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '必填值:默认true',
  `edit_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编辑框类型：1.下拉框2.文本框3.数字框4.勾选框',
  `enum_value` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '枚举值/小数位',
  `defaults` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '默认值',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工艺参数设置_产品档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_craft_material`
--

DROP TABLE IF EXISTS `mes_craft_material`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_craft_material` (
  `product_codes` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码(唯一标识)',
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `craft_id` int DEFAULT NULL COMMENT '工艺路线id',
  `material_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料code',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工艺路线明细与BOM物料';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_currency_file`
--

DROP TABLE IF EXISTS `mes_currency_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_currency_file` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
  `name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `currtypesign` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '币种币符',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='币种档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_customer_archives`
--

DROP TABLE IF EXISTS `mes_customer_archives`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_customer_archives` (
  `customer_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '企业地址',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户名称',
  `mnecode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '助记码',
  `pk_custclass_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户基本分类名称',
  `customer_english_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户英文名称',
  `customer_abbreviation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户简称',
  `supplier` int DEFAULT NULL COMMENT '是否供应商0是1否',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `vip_customer` int DEFAULT NULL COMMENT '是否客户VIP（0是1否 ）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8831 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='客户档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_customer_contact`
--

DROP TABLE IF EXISTS `mes_customer_contact`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_customer_contact` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户编码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系人姓名',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系人电话',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `customer_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户地址',
  `customer_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户名称',
  `address_enable` varchar(255) DEFAULT NULL COMMENT '客户联系人状态，2：启用，3：停用',
  `xzqh_code` varchar(100) DEFAULT NULL COMMENT '行政区域码',
  `xzqh_name` varchar(100) DEFAULT NULL COMMENT '行政区域码名称',
  `pk_address` varchar(100) DEFAULT NULL COMMENT '客户地址主键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=18406 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='客户联系人';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_defect`
--

DROP TABLE IF EXISTS `mes_defect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_defect` (
  `type` int NOT NULL DEFAULT '0' COMMENT '层级类型0：顶层1.父级2.子级',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `parent_id` int DEFAULT NULL COMMENT '父类参数值',
  `data_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '大类名称',
  `describes` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `data_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缺陷分类属性分类关联字典code',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=184 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='缺陷分类';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_device_maintain`
--

DROP TABLE IF EXISTS `mes_device_maintain`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_device_maintain` (
  `assigned_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '分配人',
  `device_current_status` int DEFAULT NULL COMMENT '设备当前状态',
  `maintain_code` char(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '维修单号',
  `failure_deveice_id` int DEFAULT NULL COMMENT '故障设备id',
  `failure_location` int DEFAULT NULL COMMENT '故障部位',
  `failure_type` int DEFAULT NULL COMMENT '故障类型',
  `key_parts` int DEFAULT NULL COMMENT '是否关键部位;1是,0不是',
  `failure_level` int DEFAULT NULL COMMENT '故障等级',
  `failure_description` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '故障描述',
  `repair_report_time` datetime DEFAULT NULL COMMENT '报修时间',
  `status` int DEFAULT NULL COMMENT '状态',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `assigned_time` datetime DEFAULT NULL COMMENT '分配时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备报修';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_device_maintain_properties`
--

DROP TABLE IF EXISTS `mes_device_maintain_properties`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_device_maintain_properties` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `category` int NOT NULL COMMENT '类别:1:优先级;2:故障类型',
  `category_item` char(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '类别具体项',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备维修配置参数';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_device_stats_config`
--

DROP TABLE IF EXISTS `mes_device_stats_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_device_stats_config` (
  `iot_stats_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IoT属性名称',
  `iot_stats_identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'IoT属性标识符',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `device_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=74 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='设备属性配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_edition_file`
--

DROP TABLE IF EXISTS `mes_edition_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_edition_file` (
  `perimeter` int DEFAULT NULL COMMENT '版辊周长',
  `edition_cleanliness` char(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版净度',
  `remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `edition_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版编号',
  `edition_type` int DEFAULT NULL COMMENT '版类型',
  `edition_categroy` int DEFAULT NULL COMMENT '版类别',
  `edition_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版名称',
  `status` int DEFAULT NULL COMMENT '状态',
  `source` int DEFAULT NULL COMMENT '来源',
  `share` int DEFAULT NULL COMMENT '共用（0否，1是）',
  `chromatic_degree` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色次',
  `color_sequence` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色序',
  `direction_mouth` int DEFAULT NULL COMMENT '叼口方向',
  `serviceability_ratio` decimal(20,10) DEFAULT NULL COMMENT '耐用率',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `is_synchronous` varchar(255) DEFAULT '0' COMMENT '是否同步',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=66241 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='版档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_edition_file_annex`
--

DROP TABLE IF EXISTS `mes_edition_file_annex`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_edition_file_annex` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `edition_id` int NOT NULL COMMENT '版档案ID',
  `annex_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件名称',
  `annex_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='版文件信息附件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_equipment_put_operation`
--

DROP TABLE IF EXISTS `mes_equipment_put_operation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_equipment_put_operation` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `OA_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA申请编码',
  `OA_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA申请名称',
  `status` int DEFAULT NULL COMMENT '状态(1未提交，2审批中，3已完成)',
  `approver` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审批人',
  `device_id` int DEFAULT NULL COMMENT '设备id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备固定资产设备投产';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_equipment_scrap`
--

DROP TABLE IF EXISTS `mes_equipment_scrap`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_equipment_scrap` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `OA_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA申请编码',
  `OA_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA申请名称',
  `status` int DEFAULT NULL COMMENT '状态(1未提交，2审批中，3已完成)',
  `approver` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审批人',
  `device_id` int DEFAULT NULL COMMENT '设备id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备固定资产设备报废';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_equipment_verification`
--

DROP TABLE IF EXISTS `mes_equipment_verification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_equipment_verification` (
  `type` int DEFAULT NULL COMMENT '类型：1、设备验证',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `OA_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA申请编码',
  `OA_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA申请名称',
  `status` int DEFAULT NULL COMMENT '状态(1未提交，2审批中，3已完成)',
  `approver` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审批人',
  `device_id` int DEFAULT NULL COMMENT '设备id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备固定资产设备验证';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_extraction_scheme`
--

DROP TABLE IF EXISTS `mes_extraction_scheme`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_extraction_scheme` (
  `sampling_id` int NOT NULL COMMENT '抽样标准id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `min_lot_size` int NOT NULL COMMENT '最小批量',
  `maximum_quantity` int NOT NULL COMMENT '最大批量',
  `character_code` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '字码',
  `sampling_amount` int NOT NULL COMMENT '抽样数量',
  `AQL` decimal(10,5) NOT NULL COMMENT 'AQL',
  `AC` int NOT NULL COMMENT 'AC',
  `RE` int NOT NULL COMMENT 'RE',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=255 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='样本抽取方案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_finished_product_check`
--

DROP TABLE IF EXISTS `mes_finished_product_check`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_finished_product_check` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `sheet_sample_choice` int DEFAULT NULL COMMENT '抽样单单选',
  `finished_product_task_choice` int DEFAULT NULL COMMENT '成品检验任务单选',
  `down_box` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '下拉框',
  `min` int DEFAULT NULL COMMENT '时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='其他配置成品检验';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_gc_file_upload`
--

DROP TABLE IF EXISTS `mes_gc_file_upload`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_gc_file_upload` (
  `categroy` int NOT NULL COMMENT '1qc检验2实验室检验',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `file_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件id',
  `file_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件名称',
  `gc_id` int NOT NULL COMMENT '过程检验id',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='过程检验上传文件表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_gc_new_template_project`
--

DROP TABLE IF EXISTS `mes_gc_new_template_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_gc_new_template_project` (
  `inspection_result` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验结果',
  `qualified` int DEFAULT NULL COMMENT '是否合格',
  `type` int DEFAULT NULL COMMENT '类型1QC检验2实验室检验',
  `project_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验项目',
  `inspection_standard` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验标准',
  `inspection_tool` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验工具',
  `user_group` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验用户',
  `arithmetic` int DEFAULT NULL COMMENT '算法(1乘，2加)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `template_id` int NOT NULL COMMENT '关联检验模板id',
  `detail_property` int DEFAULT NULL COMMENT '项目属性(1定性，2定量)',
  `standard_type` int DEFAULT NULL COMMENT '标准类型(1固定值，2变动值)',
  `source_of_indicators` int DEFAULT NULL COMMENT '指标来源(1长，2宽，3高)',
  `operator` int DEFAULT NULL COMMENT '操作符(1人工判定，2大于，3大于等于，4等于，5小于，6小于等于，7介于)',
  `min_value` decimal(20,10) DEFAULT NULL COMMENT '最小值',
  `max_value` decimal(20,10) DEFAULT NULL COMMENT '最大值',
  `standard_values` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准值',
  `unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位',
  `editing_type` int DEFAULT NULL COMMENT '编辑类型(1数值，2文本，3下拉框)',
  `data_accuracy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数据精度',
  `enumeration_values` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '枚举值',
  `defect_levels` int DEFAULT NULL COMMENT '缺陷等级(1一般缺陷，2严重缺陷，3致命缺陷)',
  `remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `checking_roles` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验角色(1QC,2实验室)',
  `first_inspection_machin` int DEFAULT NULL COMMENT '机台首检(1是，2否)',
  `machine_self_test` int DEFAULT NULL COMMENT '机台自检(1是，2否)',
  `qc_first_inspection` int DEFAULT NULL COMMENT 'QC首检(1是，2否)',
  `routing_inspection` int DEFAULT NULL COMMENT '巡检(1是，2否)',
  `tailed_test` int DEFAULT NULL COMMENT '尾检(1是，2否)',
  `spc_control` int DEFAULT NULL COMMENT 'SPC控制(1是，2否)',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `gc_id` int DEFAULT NULL COMMENT '过程检验id',
  `wj_remake` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尾检备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1560 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='过程检验保存检验模板绑定项目';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_incoming_inspection`
--

DROP TABLE IF EXISTS `mes_incoming_inspection`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_incoming_inspection` (
  `surveyor` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验人',
  `supplier_attachments` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商附件',
  `sampling_or_not` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否留样（0否，1是）',
  `mes_arrival_notice_particulars` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '到货详情表id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `result` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验结果（0不合格，1合格）',
  `reason` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '不合格原因',
  `inspection_opinions` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验意见',
  `upload_attachments` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '上传附件',
  `status` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '状态（0未提交，1已提交）',
  `inspection_time` datetime DEFAULT NULL,
  `inspection_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检验单据编号',
  `sale_order` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '销售订单号',
  `production_batch_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '生产批次号',
  `product_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码',
  `product_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品名称',
  `inspection_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检验类型（1库房检验，2QC检验，3实验室检验）',
  `source` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '来源',
  `remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `inspection_template_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验模板id',
  `template_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验模板名',
  `sampling_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '抽样数',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_incoming_inspection_config`
--

DROP TABLE IF EXISTS `mes_incoming_inspection_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_incoming_inspection_config` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `autogeneration` int DEFAULT NULL COMMENT '确认收货自动生成(1确认)',
  `paper_strategy` int DEFAULT NULL COMMENT '纸张策略(1每批检验)',
  `ingredients_strategy` int DEFAULT NULL COMMENT '辅料检验(1每批检验)',
  `else_strategy` int DEFAULT NULL COMMENT '其他策略(1每批检验)',
  `packaging_material_inspection` int DEFAULT NULL COMMENT '包材检验(1每批检验,2每月按供应商检验)',
  `json_list` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '时间列表',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验其他配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_incoming_inspection_detail`
--

DROP TABLE IF EXISTS `mes_incoming_inspection_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_incoming_inspection_detail` (
  `inspection_result` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验结果',
  `inspection_tool` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验工具',
  `incoming_inspection_id` int NOT NULL COMMENT '来料检验主表的id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `detail_property` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验属性',
  `project_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验项目',
  `inspection_standard` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验标准',
  `operator` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作符',
  `standard_values` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准值',
  `min_value` int DEFAULT NULL COMMENT '最小值',
  `max_value` int DEFAULT NULL COMMENT '最大值',
  `unit` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准单位',
  `defect_levels` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缺陷等级',
  `remake` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `result` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否合格（0不合格，1合格）',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=57 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_inspected_records`
--

DROP TABLE IF EXISTS `mes_inspected_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_inspected_records` (
  `status` int DEFAULT NULL COMMENT '状态',
  `check_accept_result` int DEFAULT NULL COMMENT '验收结果',
  `maintain_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '故障维修停机时间',
  `score` double DEFAULT NULL COMMENT '验收评分',
  `description` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `maintain_id` int DEFAULT NULL COMMENT '维修单id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备维修_验收记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_inspection_items`
--

DROP TABLE IF EXISTS `mes_inspection_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_inspection_items` (
  `user_group` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '适用用户',
  `inspection_tool` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验工具',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspection_standard` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验标准',
  `project_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检验项目',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=784 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='检验项目mes_inspection_items';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_inspection_template`
--

DROP TABLE IF EXISTS `mes_inspection_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_inspection_template` (
  `parent_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父id',
  `type_of_test` int DEFAULT NULL COMMENT '检验类型(1来料检验，2过程检验，3成品检验，4出库检验)',
  `relevance_sampling_standard` int DEFAULT NULL COMMENT '关联抽样标准',
  `vindicate_remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验模板维护备注',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `template_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检验模板名称',
  `remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `common_validation_template` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公共检验模板ids',
  `reasons_for_change` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '变更原因',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='检验模板新';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_inspection_templatem`
--

DROP TABLE IF EXISTS `mes_inspection_templatem`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_inspection_templatem` (
  `parent_id` int NOT NULL COMMENT '上级模板id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `template_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检验模板名称',
  `check_type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检验类型（1来料检验（纸张），2来料检验（辅料），3来料检验（包材），4过程检验，5成品检验）',
  `sampling_standard` int NOT NULL COMMENT '抽样标准',
  `inspection_level` int NOT NULL COMMENT '质检等级',
  `QC_inspection` int NOT NULL COMMENT '是否QC检验(1是，2否)',
  `laboratory_test_or_not` int NOT NULL COMMENT '是否实验室检验(1是，2否)',
  `remake` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `status` int DEFAULT NULL COMMENT '审核状态(1待审批，2审批中，3审核通过，4驳回)',
  `opinion` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审批意见',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `mkid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'mkid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='检验模板';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_inspection_templatem_file`
--

DROP TABLE IF EXISTS `mes_inspection_templatem_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_inspection_templatem_file` (
  `file_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '文件id',
  `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '检验模板id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='检验模板文件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_invalid_time_period`
--

DROP TABLE IF EXISTS `mes_invalid_time_period`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_invalid_time_period` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `time_id` int NOT NULL COMMENT '班次时间id',
  `start_time` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '休息开始时间',
  `end_time` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '休息结束时间',
  `description` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '休息区间描述',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='班次时间无效时间';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_machine_related_device`
--

DROP TABLE IF EXISTS `mes_machine_related_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_machine_related_device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `machine_id` int DEFAULT NULL COMMENT '机台id',
  `device_id` int DEFAULT NULL COMMENT '设备id',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `device_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备编码',
  `machine_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机台编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='机台档案_机台设备关联';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_maintain_record`
--

DROP TABLE IF EXISTS `mes_maintain_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_maintain_record` (
  `waiting_for_outsourcing` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '等待外协时长',
  `waiting_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '等件时长',
  `estimated_repair_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '预估维修时长',
  `maintain_id` int DEFAULT NULL COMMENT '维修单id',
  `maintain_start_time` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '维修开始时间',
  `maintain_end_time` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '维修结束时间',
  `maintain_duration` decimal(20,10) DEFAULT NULL COMMENT '维修时长',
  `troubleshooting` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '排查过程',
  `failure_reason` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '故障原因',
  `maintain_situation` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '故障情况',
  `preventive_measure` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '再发生预防措施',
  `spare_parts_id` int DEFAULT NULL COMMENT '备品备件',
  `maintain_route` int DEFAULT NULL COMMENT '维修路线',
  `external_cooperation` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '外协情况',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `operation_condition` int DEFAULT NULL COMMENT '运行状况',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备维修';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_maintenance_records`
--

DROP TABLE IF EXISTS `mes_maintenance_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_maintenance_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='维修记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_malfunction`
--

DROP TABLE IF EXISTS `mes_malfunction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_malfunction` (
  `indexs` int DEFAULT NULL COMMENT '序号',
  `halt_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '停机编号',
  `halt_cause` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '停机原因',
  `halt_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '停机分类',
  `record_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '机型档案编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '序号',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='机型停机故障';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_material_file`
--

DROP TABLE IF EXISTS `mes_material_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_material_file` (
  `product_status` int DEFAULT NULL COMMENT '产品审批状态（1生效2审批中3驳回4工艺未完善）',
  `place` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产地',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除0正常1删除',
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `material_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料名称',
  `mnemonic_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '助记码',
  `category` int DEFAULT NULL COMMENT '物料类别:1.纸张;2.辅料;3.包材;4.产品，5半成品;',
  `status` int DEFAULT NULL COMMENT '状态：1草稿，2生效',
  `group_category` int DEFAULT NULL COMMENT '集团分类(纸张,包材,辅料)',
  `type` int DEFAULT NULL COMMENT '纸张类型(纸张)',
  `procure_unit` int DEFAULT NULL COMMENT '采购单位（纸张，包材,辅料）',
  `specification` int DEFAULT NULL COMMENT '规格（纸张，包材,辅料）',
  `standard_procure_price` int DEFAULT NULL COMMENT '标准采购价格（纸张，包材,辅料）',
  `standard_unit` int DEFAULT NULL COMMENT '标准单位（纸张，包材,辅料）主单位',
  `brand` int DEFAULT NULL COMMENT '品牌（纸张，包材,辅料）',
  `conversion_rate` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '折算率（纸张，包材）',
  `description` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述（纸张，包材,辅料）',
  `gram_weight` decimal(20,10) DEFAULT NULL COMMENT '克重(纸张,产品)',
  `max_stock` decimal(20,10) DEFAULT NULL COMMENT '最大库存量（纸张，包材,辅料）',
  `min_stock` decimal(20,10) DEFAULT NULL COMMENT '最小库存量（纸张，包材,辅料）',
  `safe_stock` decimal(20,10) DEFAULT NULL COMMENT '安全库存量(辅料)',
  `min_procure_quantity` decimal(20,10) DEFAULT NULL COMMENT '最小采购数量(纸张,包材)',
  `paper_name` int DEFAULT NULL COMMENT '纸张_名称(纸张)',
  `expiration_days` int DEFAULT NULL COMMENT '保质天数(纸张)',
  `big_code` int DEFAULT NULL COMMENT '大类(包材,辅料,产品)',
  `small_code` int DEFAULT NULL COMMENT '小类(包材,辅料,产品)',
  `is_group_product` int DEFAULT NULL COMMENT '是否为组产品（1是2否）',
  `file_type` int DEFAULT NULL COMMENT '档案类型(1成本BOM，2批量BOM，3打样BOM)',
  `version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `product_unit` int DEFAULT NULL COMMENT '产品单位',
  `product_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸（产品）',
  `spread_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '展开尺寸（产品）',
  `size_unit` int DEFAULT NULL COMMENT '尺寸单位(产品)',
  `is_support_fsc` int DEFAULT NULL COMMENT '是否支持fsc（产品）1是2否',
  `fsc_statement` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'fsc声明（产品）',
  `is_support_rohs` int DEFAULT NULL COMMENT '是否支持ROHS（产品）（1是2否）',
  `material_weight` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '材质克重（产品）',
  `update_content` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新内容（产品）',
  `part_information` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部件信息（包含网点数和网点类型）',
  `makeup_product` int DEFAULT NULL COMMENT '拼版主产品（1是2否）',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `brand_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '品牌枚举',
  `specification_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规格枚举',
  `gram_weight_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '克重枚举',
  `big_code_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '大类枚举',
  `small_code_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '小类枚举',
  `standard_unit_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准单位枚举',
  `size_unit_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸单位枚举',
  `procure_unit_cn` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单位枚举',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `big_code` (`big_code`) USING BTREE,
  KEY `brand` (`brand`) USING BTREE,
  KEY `categroy` (`category`) USING BTREE,
  KEY `material_code` (`material_code`) USING BTREE,
  KEY `material_name` (`material_name`) USING BTREE,
  KEY `mnemonic_code` (`mnemonic_code`) USING BTREE,
  KEY `product_status` (`product_status`) USING BTREE,
  KEY `small_code` (`small_code`) USING BTREE,
  KEY `specification` (`specification`) USING BTREE,
  KEY `standard_unit` (`standard_unit`) USING BTREE,
  KEY `status` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=88378 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='物料档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_mechanics`
--

DROP TABLE IF EXISTS `mes_mechanics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_mechanics` (
  `maintain_by_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '维修人名称',
  `device_maintain_id` int NOT NULL COMMENT '维修单id',
  `maintain_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '维修人',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=115 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备维修人';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_modelfile`
--

DROP TABLE IF EXISTS `mes_modelfile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_modelfile` (
  `tooth_devices` int DEFAULT NULL COMMENT '牙排设备0禁用1启用',
  `describes` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机型编码',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '名称',
  `work_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工作中心',
  `status` int DEFAULT '0' COMMENT '状态',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mes_机型档案表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_new_inspection_template`
--

DROP TABLE IF EXISTS `mes_new_inspection_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_new_inspection_template` (
  `dai_relevance_sampling_sta` int DEFAULT NULL COMMENT '代抽样标准',
  `status` int DEFAULT NULL COMMENT '审核状态（1待审批，2审批通过，3驳回）',
  `type` int DEFAULT '0' COMMENT 'type',
  `parent_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父id',
  `type_of_test` int DEFAULT NULL COMMENT '检验类型(1来料检验，2过程检验，3成品检验，4出库检验)',
  `relevance_sampling_standard` int DEFAULT NULL COMMENT '关联抽样标准',
  `vindicate_remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验模板维护备注',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `template_name` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验模板名称',
  `remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `common_validation_template` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公共检验模板ids',
  `reasons_for_change` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '变更原因',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=267 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='检验模板新';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_new_inspection_template_ver`
--

DROP TABLE IF EXISTS `mes_new_inspection_template_ver`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_new_inspection_template_ver` (
  `template_id` int DEFAULT NULL COMMENT '检验模板主表id',
  `status` int DEFAULT NULL COMMENT '审核状态（1待审批，2审批通过，3驳回）',
  `type` int DEFAULT '0' COMMENT 'type',
  `parent_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父id',
  `type_of_test` int DEFAULT NULL COMMENT '检验类型(1来料检验，2过程检验，3成品检验，4出库检验)',
  `relevance_sampling_standard` int DEFAULT NULL COMMENT '关联抽样标准',
  `vindicate_remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验模板维护备注',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `template_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验模板名称',
  `remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `common_validation_template` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公共检验模板ids',
  `reasons_for_change` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '变更原因',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `file_url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件地址',
  `dai_relevance_sampling_sta` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '代抽样标准',
  `mkid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'mkid',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=479 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='检验模板新版本';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_new_template_project`
--

DROP TABLE IF EXISTS `mes_new_template_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_new_template_project` (
  `inspection_item_id` int DEFAULT NULL COMMENT '检验项目id',
  `arithmetic` int DEFAULT NULL COMMENT '算法(1乘，2加)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `template_id` int NOT NULL COMMENT '关联检验模板id',
  `detail_property` int DEFAULT NULL COMMENT '项目属性(1定性，2定量)',
  `standard_type` int DEFAULT NULL COMMENT '标准类型(1固定值，2变动值)',
  `source_of_indicators` int DEFAULT NULL COMMENT '指标来源(1长，2宽，3高)',
  `operator` int DEFAULT NULL COMMENT '操作符(1人工判定，2大于，3大于等于，4等于，5小于，6小于等于，7介于)',
  `min_value` decimal(20,10) DEFAULT NULL COMMENT '最小值',
  `max_value` decimal(20,10) DEFAULT NULL COMMENT '最大值',
  `standard_values` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准值',
  `unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位',
  `editing_type` int DEFAULT NULL COMMENT '编辑类型(1数值，2文本，3下拉框)',
  `data_accuracy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数据精度',
  `enumeration_values` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '枚举值',
  `defect_levels` int DEFAULT NULL COMMENT '缺陷等级(1一般缺陷，2严重缺陷，3致命缺陷)',
  `remake` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `checking_roles` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验角色(1QC,2实验室)',
  `first_inspection_machin` int DEFAULT NULL COMMENT '机台首检(1是，2否)',
  `machine_self_test` int DEFAULT NULL COMMENT '机台自检(1是，2否)',
  `qc_first_inspection` int DEFAULT NULL COMMENT 'QC首检(1是，2否)',
  `routing_inspection` int DEFAULT NULL COMMENT '巡检(1是，2否)',
  `tailed_test` int DEFAULT NULL COMMENT '尾检(1是，2否)',
  `spc_control` int DEFAULT NULL COMMENT 'SPC控制(1是，2否)',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2126 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='检验模板关联项目新';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_operation_record_modified`
--

DROP TABLE IF EXISTS `mes_operation_record_modified`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_operation_record_modified` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `field_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '字段名称（域）',
  `original_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '原值',
  `new_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '新值',
  `record_id` int DEFAULT NULL COMMENT '操作记录id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10553 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='操作记录相关修改字段';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_package_extraction_scheme`
--

DROP TABLE IF EXISTS `mes_package_extraction_scheme`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_package_extraction_scheme` (
  `sampling_id` int NOT NULL COMMENT '抽样标准id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `min_carton_numbers` int NOT NULL COMMENT '最小箱数',
  `max_carton_numbers` int NOT NULL COMMENT '最大箱数',
  `change_or_immobilization` int NOT NULL COMMENT '变动值/固定值（1固定值，2变动值）',
  `sample` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '抽样',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='包装抽取方案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_packaging_materials`
--

DROP TABLE IF EXISTS `mes_packaging_materials`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_packaging_materials` (
  `project_origin` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '产地',
  `flag_status` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '数据状态1草稿，2生效',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `packaging_materials_code` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '包材编码',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `packaging_material_name` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '包材名称',
  `big_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '大类',
  `small_type` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '小类',
  `specification` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '规格',
  `standard_units` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '标准单位',
  `brand` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '品牌',
  `safety_stock` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL DEFAULT '1' COMMENT '安全库存1,不选，2选中',
  `minimum_inventory` decimal(16,8) DEFAULT NULL COMMENT '最小库存数量',
  `big_inventory` decimal(16,8) DEFAULT NULL COMMENT '最大裤衩数量',
  `description` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述',
  `standard_price` decimal(14,6) DEFAULT NULL COMMENT '标准采购价格',
  `purchasing_unit` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '采购单位',
  `minimum_purch` decimal(14,6) NOT NULL DEFAULT '1.000000' COMMENT '最小采购量',
  `conversion_rate` decimal(14,6) DEFAULT '1.000000' COMMENT '折算率',
  `Item_picture` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料图片',
  `annex` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '附件',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='包材档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_packaging_materials_file`
--

DROP TABLE IF EXISTS `mes_packaging_materials_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_packaging_materials_file` (
  `category` int NOT NULL COMMENT '物料类别:1.纸张;2.辅料;3.包材;4.产品',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `material_file_code` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '物料档案code',
  `file_type` int NOT NULL COMMENT '文件类型1图片，2附件',
  `file_name` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '文件名称',
  `file_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '文件上传后返回的id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='物料档案文件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_paper_document`
--

DROP TABLE IF EXISTS `mes_paper_document`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_paper_document` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `paper_Id` int NOT NULL COMMENT '纸张档案id',
  `file_type` int NOT NULL COMMENT '文件类型',
  `file_name` char(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '文件名称',
  `file_url` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '文件上传后返回的id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='纸张档案文件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_paper_file`
--

DROP TABLE IF EXISTS `mes_paper_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_paper_file` (
  `standard_procure_price` double DEFAULT NULL COMMENT '标准采购价格',
  `procure_unit` int NOT NULL COMMENT '采购单位',
  `conversion_rate` double NOT NULL COMMENT '折算率',
  `min_procure_quantity` double NOT NULL COMMENT '最小采购量',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `paper_code` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '纸张编码',
  `paper_name` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '纸张全称',
  `name` int NOT NULL COMMENT '名称',
  `type` char(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '类型',
  `gram_weight` int NOT NULL COMMENT '克重',
  `specs` int NOT NULL COMMENT '规格',
  `brand` int NOT NULL COMMENT '品牌',
  `standard_unit` int NOT NULL COMMENT '标准单位',
  `status` int NOT NULL COMMENT '状态;0:草稿;1:完成',
  `max_stock` double DEFAULT NULL COMMENT '最大库存数',
  `min_stock` double DEFAULT NULL COMMENT '最小库存数',
  `description` varchar(300) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `product_place` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产地',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='纸张档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_parameter_data`
--

DROP TABLE IF EXISTS `mes_parameter_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_parameter_data` (
  `high` int DEFAULT NULL COMMENT '高',
  `wide` int DEFAULT NULL COMMENT '宽',
  `length` int DEFAULT NULL COMMENT '长',
  `info_id` int DEFAULT NULL COMMENT '参数设置id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `data_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '参数编号',
  `data_value` char(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '参数值',
  `parent_id` int DEFAULT NULL COMMENT '上级参数值',
  `state` int DEFAULT '0' COMMENT '状态(0=启用；1=禁用)',
  `remarks` char(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `decimal_places` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '小数点位数',
  `digit` int DEFAULT '0' COMMENT '小数点位数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=26308 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='参数详情';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_parameter_design`
--

DROP TABLE IF EXISTS `mes_parameter_design`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_parameter_design` (
  `machine_coding` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '机台id',
  `minsize` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最小尺寸',
  `maximum` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最大尺寸',
  `mouth` int DEFAULT NULL COMMENT '叼口尺寸',
  `forbidden` int DEFAULT NULL COMMENT '是否禁用',
  `reversible` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '可翻转(位置)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `maximum_speed` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最大速度(张/小时)',
  `maximum_size` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最大尺寸(mm)',
  `ps_specification` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'PS版规格(mm)',
  `material_categories` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '原材料类别',
  `printer_tone` int DEFAULT NULL COMMENT '印刷机色次',
  `standard_speed` int DEFAULT NULL COMMENT '标准速度(张/小时)',
  `minimum_size` int DEFAULT NULL COMMENT '最小尺寸(mm)',
  `mouth_size` int DEFAULT NULL COMMENT '叼口尺寸(mm)',
  `by_bite` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '借咬口(mm)',
  `ps_printability` varchar(225) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '默认PS版耐印率',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='机台档案_参数设计';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_parameter_info`
--

DROP TABLE IF EXISTS `mes_parameter_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_parameter_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `material_type` int NOT NULL COMMENT '物料类型（0纸张，1辅料，2包材）',
  `info_name` char(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '参数项名称',
  `type` int NOT NULL COMMENT '参数类型（0普通，1规格）',
  `parent_id` int DEFAULT NULL COMMENT '上级参数项ID',
  `remarks` char(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='参数设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_place_bom_manage`
--

DROP TABLE IF EXISTS `mes_place_bom_manage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_place_bom_manage` (
  `device_type_id` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备类型id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `place_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '部位编码',
  `place_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '部位名称',
  `parent_id` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '父级ID',
  `menu_level` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '菜单级别',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备BOM管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_process_consumption_ratio`
--

DROP TABLE IF EXISTS `mes_process_consumption_ratio`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_process_consumption_ratio` (
  `min_consumption` int DEFAULT NULL COMMENT '最低运转消耗',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `process_id` int NOT NULL COMMENT '工序id',
  `start_value` int NOT NULL COMMENT '起始数字',
  `end_value` int NOT NULL COMMENT '结束数字',
  `consumption_rate` decimal(10,5) DEFAULT NULL COMMENT '消耗率',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序运转消耗比例';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_process_parameter`
--

DROP TABLE IF EXISTS `mes_process_parameter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_process_parameter` (
  `index` int NOT NULL COMMENT '序号',
  `process_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工艺名称',
  `datatype` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '数据类型',
  `parameter_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '枚举类型',
  `parameter_string` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '枚举串',
  `default` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '默认值',
  `state` int NOT NULL DEFAULT '0' COMMENT '状态',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mes_工艺参数设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_process_parameter_setting`
--

DROP TABLE IF EXISTS `mes_process_parameter_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_process_parameter_setting` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `index` int NOT NULL COMMENT '序号',
  `process_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工艺名称',
  `code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `datatype` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '数据类型',
  `type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '枚举类型',
  `string` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '枚举串',
  `default` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '默认值',
  `state` int NOT NULL DEFAULT '0' COMMENT '状态',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mes_工艺参数设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_process_setting`
--

DROP TABLE IF EXISTS `mes_process_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_process_setting` (
  `operation_unit` char(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '作业单位（以逗号分割）',
  `default_unit` char(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '默认作业单位',
  `plate_type` char(80) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用版类型',
  `qa_require` char(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '质检要求',
  `need_place` char(3) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '半成品静置是否开启',
  `place_time` int DEFAULT NULL COMMENT '半成品静置时间',
  `minimum_consumption` int DEFAULT NULL COMMENT '最低消耗(张)',
  `prepare_consumption` int DEFAULT NULL COMMENT '准备消耗(张)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `process_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序编码',
  `process_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序名称',
  `process_nick_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序昵称',
  `display_panel` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '显示板',
  `printing_operation` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '晒版作业',
  `physical_version` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实物版',
  `process_type` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序类型',
  `status` varchar(3) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '状态',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=144 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序设置mes_process_setting';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_ban_assunit`
--

DROP TABLE IF EXISTS `mes_product_ban_assunit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_ban_assunit` (
  `product_codes` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码',
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `out_of_book` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '出本Out of book',
  `product_code` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部件/产品编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `ban_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版档案编码',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1778492 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品版档案关联部件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_bom_list`
--

DROP TABLE IF EXISTS `mes_product_bom_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_bom_list` (
  `mnemonic_code` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '助记码',
  `kaifa` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '开法',
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `pushing_open_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '开法开数',
  `packing_unit` int DEFAULT NULL COMMENT '包装单位',
  `product_count` decimal(20,10) DEFAULT NULL COMMENT '产品数量（包装）',
  `weight` decimal(20,10) DEFAULT NULL COMMENT '重(kg)包装',
  `packing_parameter` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '包装参数',
  `box_mark_content` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '箱签内容',
  `pack_other_sizes` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '包装其他尺寸',
  `pack_background_color` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '包装其他背景颜色',
  `pack_other_texture` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '包装其他材质',
  `pack_paste_method` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '包装其他粘贴方法',
  `product_codes` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码',
  `chromatic_degree` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色次',
  `color_sequence` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色序',
  `size_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸单位',
  `color_order_quotation` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '色序报价',
  `part_information` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部件信息(部件，纸张)',
  `material_code` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码(纸张，辅料)',
  `material_name` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '名称(纸张，辅料)',
  `fixed_amount` int DEFAULT NULL COMMENT '固定用量',
  `dosage_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位用量',
  `consumption_rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '消耗率支持负数和4位小数',
  `remake` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `makeup_product` int DEFAULT NULL COMMENT '拼版主产品',
  `is_off_side` int DEFAULT NULL COMMENT '偏开',
  `pushing` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '开法尺寸',
  `notes_on_cutting` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '分切备注',
  `slitting` int DEFAULT NULL COMMENT '分切',
  `cut_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸',
  `cut_remake` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '裁切备注',
  `dosage_unit_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位用量单位（辅料）',
  `consume_unit` int DEFAULT NULL COMMENT '消耗单位',
  `consume_round_up` int DEFAULT NULL COMMENT '消耗取整',
  `ink_formulation` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '油墨配方',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `parent_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父编码',
  `product_code` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编号（部件）',
  `product_name` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品名称（部件）',
  `type` int DEFAULT NULL COMMENT '类别(0部件，1纸张，2辅料，3产品)',
  `count` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数量（部件）',
  `size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸(部件，纸张)',
  `unfold_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '展开尺寸',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `collect_materials_order` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '按单领料',
  `indexs` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '顺位标志',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7665211 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品BOM明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_client`
--

DROP TABLE IF EXISTS `mes_product_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_client` (
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `product_codes` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码(唯一标识)',
  `client_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户编码',
  `object_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户料号',
  `grade_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户品名',
  `unit` int DEFAULT NULL COMMENT '单位',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `client_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '客户名称',
  `mnecode` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '助记码',
  `pro_mnecode` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品助记码',
  `is_disabled` varchar(255) DEFAULT NULL COMMENT '禁用 0  激活1',
  `customer_general_requirements` varchar(255) DEFAULT NULL COMMENT '客户通用需求',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `client_code` (`client_code`),
  KEY `product_codes` (`product_codes`),
  KEY `product_version` (`product_version`)
) ENGINE=InnoDB AUTO_INCREMENT=564795 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品与客户料号';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_craft`
--

DROP TABLE IF EXISTS `mes_product_craft`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_craft` (
  `out_of_book` int DEFAULT NULL COMMENT '出本',
  `indexs` int DEFAULT NULL COMMENT '顺序',
  `product_codes` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码（唯一标识）',
  `material_code` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '物料',
  `ys_fixed_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用时固定用时',
  `ys_fixed_rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用时比率',
  `craft_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工艺编码',
  `work_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工作中心编码',
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `status` int DEFAULT NULL COMMENT '1产品/2部件',
  `fixed_consumption` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '固定消耗',
  `rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '比率',
  `xh_fixed_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '消耗固定用时',
  `xh_fixed_rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '消耗固定用时比率',
  `standing_time` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '停留时间',
  `manual_speed` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '手工速度',
  `ban_id` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品ban档案表关联的code',
  `technological_parameter` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '工艺参数',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `product_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品/部件编码',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3866705 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品部件关联工艺路线';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_craft_copy`
--

DROP TABLE IF EXISTS `mes_product_craft_copy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_craft_copy` (
  `out_of_book` int DEFAULT NULL COMMENT '出本',
  `indexs` int DEFAULT NULL COMMENT '顺序',
  `product_codes` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品编码（唯一标识）',
  `material_code` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '物料',
  `ys_fixed_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用时固定用时',
  `ys_fixed_rate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用时比率',
  `craft_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工艺编码',
  `work_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工作中心编码',
  `product_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品版本',
  `status` int DEFAULT NULL COMMENT '1产品/2部件',
  `fixed_consumption` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '固定消耗',
  `rate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '比率',
  `xh_fixed_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '消耗固定用时',
  `xh_fixed_rate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '消耗固定用时比率',
  `standing_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '停留时间',
  `manual_speed` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手工速度',
  `ban_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品ban档案表关联的code',
  `technological_parameter` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '工艺参数',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品/部件编码',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='产品部件关联工艺路线';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_craft_copy1`
--

DROP TABLE IF EXISTS `mes_product_craft_copy1`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_craft_copy1` (
  `out_of_book` int DEFAULT NULL COMMENT '出本',
  `indexs` int DEFAULT NULL COMMENT '顺序',
  `product_codes` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品编码（唯一标识）',
  `material_code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '物料',
  `ys_fixed_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用时固定用时',
  `ys_fixed_rate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '用时比率',
  `craft_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工艺编码',
  `work_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工作中心编码',
  `product_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品版本',
  `status` int DEFAULT NULL COMMENT '1产品/2部件',
  `fixed_consumption` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '固定消耗',
  `rate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '比率',
  `xh_fixed_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '消耗固定用时',
  `xh_fixed_rate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '消耗固定用时比率',
  `standing_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '停留时间',
  `manual_speed` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '手工速度',
  `ban_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品ban档案表关联的code',
  `technological_parameter` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '工艺参数',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品/部件编码',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='产品部件关联工艺路线';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_file`
--

DROP TABLE IF EXISTS `mes_product_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_file` (
  `product_codes` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码（唯一标识）',
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `product_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码',
  `file_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件id',
  `file_name` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件名称',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `file_url` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=38834 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品关联文件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_of_ban`
--

DROP TABLE IF EXISTS `mes_product_of_ban`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_of_ban` (
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `product_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码',
  `ban_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版档案编码',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `relevance_material_name` varchar(255) DEFAULT NULL COMMENT '关联辅料名称',
  `relevance_material_code` varchar(255) DEFAULT NULL COMMENT '关联辅料编码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ban_code` (`ban_code`),
  KEY `product_code` (`product_code`),
  KEY `product_version` (`product_version`)
) ENGINE=InnoDB AUTO_INCREMENT=1780733 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品版档案关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_product_version`
--

DROP TABLE IF EXISTS `mes_product_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_product_version` (
  `parent_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '父code',
  `parent_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT '0' COMMENT '父版本',
  `product_status` int DEFAULT NULL COMMENT '状态 1、生效，2、工艺未完善(草稿)，3、审批中，4、驳回，5、归档，6、锁定',
  `place` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产地',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除0正常1删除',
  `product_version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `material_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码',
  `material_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料名称',
  `mnemonic_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '助记码',
  `category` int DEFAULT NULL COMMENT '物料类别:1.纸张;2.辅料;3.包材;4.产品;',
  `status` int DEFAULT NULL COMMENT '状态 1、生效，2、工艺未完善(草稿)，3、审批中，4、驳回，5、归档，6、锁定',
  `group_category` int DEFAULT NULL COMMENT '集团分类(纸张,包材,辅料)',
  `type` int DEFAULT NULL COMMENT '纸张类型(纸张)',
  `procure_unit` int DEFAULT NULL COMMENT '采购单位（纸张，包材,辅料）',
  `specification` int DEFAULT NULL COMMENT '规格（纸张，包材,辅料）',
  `standard_procure_price` int DEFAULT NULL COMMENT '标准采购价格（纸张，包材,辅料）',
  `standard_unit` int DEFAULT NULL COMMENT '标准单位（纸张，包材,辅料）',
  `brand` int DEFAULT NULL COMMENT '品牌（纸张，包材,辅料）',
  `conversion_rate` decimal(20,10) DEFAULT NULL COMMENT '折算率（纸张，包材）',
  `description` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述（纸张，包材,辅料）',
  `gram_weight` decimal(20,10) DEFAULT NULL COMMENT '克重(纸张,产品)',
  `max_stock` decimal(20,10) DEFAULT NULL COMMENT '最大库存量（纸张，包材,辅料）',
  `min_stock` decimal(20,10) DEFAULT NULL COMMENT '最小库存量（纸张，包材,辅料）',
  `safe_stock` decimal(20,10) DEFAULT NULL COMMENT '安全库存量(辅料)',
  `min_procure_quantity` decimal(20,10) DEFAULT NULL COMMENT '最小采购数量(纸张,包材)',
  `paper_name` int DEFAULT NULL COMMENT '纸张_名称(纸张)',
  `expiration_days` int DEFAULT NULL COMMENT '保质天数(纸张)',
  `big_code` int DEFAULT NULL COMMENT '大类(包材,辅料,产品)',
  `small_code` int DEFAULT NULL COMMENT '小类(包材,辅料,产品)',
  `is_group_product` int DEFAULT NULL COMMENT '是否为组产品（1是2否）',
  `file_type` int DEFAULT NULL COMMENT '档案类型(1成本BOM，2批量BOM，3打样BOM)',
  `version` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品版本',
  `product_unit` int DEFAULT NULL COMMENT '产品单位',
  `product_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸（产品）',
  `spread_size` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '展开尺寸（产品）',
  `size_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸单位',
  `is_support_fsc` int DEFAULT NULL COMMENT '是否支持fsc（产品）1是2否',
  `fsc_statement` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'fsc声明（产品）',
  `is_support_rohs` int DEFAULT NULL COMMENT '是否支持ROHS（产品）（1是2否）',
  `material_weight` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '材质克重（产品）',
  `update_content` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '更新内容（产品）',
  `part_information` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部件信息（包含网点数和网点类型）',
  `makeup_product` int DEFAULT NULL COMMENT '拼版主产品（1是2否）',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `default_factory_cn` varchar(255) DEFAULT NULL COMMENT '默认工厂枚举',
  `default_factory` int DEFAULT NULL COMMENT '默认工厂',
  `is_synchronization` int DEFAULT '0' COMMENT '是否同步',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=83408 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='产品版本';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_production_process_ins`
--

DROP TABLE IF EXISTS `mes_production_process_ins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_production_process_ins` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `the_machine_first_check` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机台首检',
  `min` int DEFAULT NULL COMMENT 'min',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='生产过程检验';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_proof_content`
--

DROP TABLE IF EXISTS `mes_proof_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_proof_content` (
  `test_product` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '测试产品',
  `categroy` int DEFAULT '0' COMMENT '0方案1设备2维修',
  `verification_result` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '验证结果',
  `type_id` int NOT NULL COMMENT '验证方法类型：1.安装确认、2.运行确认、3.性能确认、4.工艺适性确认、5结论分析6.其他',
  `zid` int NOT NULL COMMENT '主表id',
  `test_require` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '测试要求',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `verify_id` int NOT NULL COMMENT '适用范围：1.新设备验证2.定期验证2.变更验证',
  `project` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检查项目',
  `require1` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '验证要求',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='验证方案内容';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_quality_department_process`
--

DROP TABLE IF EXISTS `mes_quality_department_process`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_quality_department_process` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `auto` int NOT NULL COMMENT '机台首检完成后自动生成',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='质量部过程检验';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_resource_center`
--

DROP TABLE IF EXISTS `mes_resource_center`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_resource_center` (
  `scheduling_process` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '排产工序',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `resource_center_name` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源中心名称',
  `resource_center_code` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源中心编号',
  `owning_workshop` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属车间',
  `forbidden_check` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '禁用or开启(0,启用；1，禁用)',
  `remark` varchar(260) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=169 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='资源中心表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_sampling_standard`
--

DROP TABLE IF EXISTS `mes_sampling_standard`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_sampling_standard` (
  `default_or_defined` int DEFAULT NULL COMMENT '默认/自定义（1默认，2自定义）',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `standard_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '抽样标准名称',
  `quality_level` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '质检等级',
  `remake` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='抽样标准表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_shift_collect`
--

DROP TABLE IF EXISTS `mes_shift_collect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_shift_collect` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `shift_code` char(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '班次集合码',
  `shift_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '班次集合名称',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='班次集合';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_shift_time`
--

DROP TABLE IF EXISTS `mes_shift_time`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_shift_time` (
  `description` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '描述信息',
  `shift_time_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '班次名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `shift_time_code` char(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '班次时间码',
  `start_time` char(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '开始时间',
  `end_time` char(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '结束时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='班次时间';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_shift_time_info`
--

DROP TABLE IF EXISTS `mes_shift_time_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_shift_time_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `shift_id` int NOT NULL COMMENT '班次集合id',
  `shift_time_id` int NOT NULL COMMENT '班次时间id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='班次集合与班次时间对应关系';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_shipping_address`
--

DROP TABLE IF EXISTS `mes_shipping_address`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_shipping_address` (
  `customer_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '客户编码',
  `customer_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '客户名称',
  `customer_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '客户地址',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='客户收货地址';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_site_operation`
--

DROP TABLE IF EXISTS `mes_site_operation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_site_operation` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `maintain_record_id` int NOT NULL COMMENT '维修记录id',
  `category` int NOT NULL COMMENT '类别区分:category:1现场清理;2,运行状况',
  `check_item` int NOT NULL COMMENT '选择项',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=123 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='维修记录附加';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_spot_check_item`
--

DROP TABLE IF EXISTS `mes_spot_check_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_spot_check_item` (
  `choice_enum_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单选枚举值',
  `day_of_selection` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '选中日',
  `week_of_selection` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '选中周',
  `frequency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '频率（1月，2周，3年）',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '状态(1true启用,2false禁用)',
  `reading_reference_value_low` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '读数参考值限下',
  `reading_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '读数单位',
  `reading_reference_value_up` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '读数参考值上限',
  `choice_reference_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '单选参考值',
  `spot_check_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检值类型(1单选型，2读数型)',
  `spot_check_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检方法',
  `spot_check_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检内容',
  `spot_check_project` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检部位/项目',
  `spot_check_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '点检项编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `device_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设备类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='点检项管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_supplier_contact`
--

DROP TABLE IF EXISTS `mes_supplier_contact`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_supplier_contact` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系人编码',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系人电话',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系人姓名',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  `customer_address` varchar(255) DEFAULT NULL COMMENT '客户地址',
  `customer_name` varchar(255) DEFAULT NULL COMMENT '客户名称',
  `address_enable` varchar(255) DEFAULT NULL COMMENT '客户联系人状态，2：启用，3：停用',
  `pk_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '客户地址主键',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1801 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='供应商联系人';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_supplier_file`
--

DROP TABLE IF EXISTS `mes_supplier_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_supplier_file` (
  `shortname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商简称',
  `common_carrier` int DEFAULT NULL COMMENT '是否承运商0是1否',
  `merchant_contractor` int DEFAULT NULL COMMENT '是否委外商0是1否',
  `qualified_supplier` int DEFAULT NULL COMMENT '是否合格供应商0是1否',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商编码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商名称',
  `supplier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商英文名称',
  `mnecode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '助剂码',
  `pk_supplierclass_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商基本分类名称',
  `client` int DEFAULT NULL COMMENT '是否客户0是1否',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2309 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='供应商档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_templatem_project`
--

DROP TABLE IF EXISTS `mes_templatem_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_templatem_project` (
  `template_id` int NOT NULL COMMENT '检验模板id',
  `machine_inspection` int DEFAULT NULL COMMENT '机台首检（1是，2否）',
  `QC_first_inspection` int DEFAULT NULL COMMENT 'QC首检(1是2否)',
  `self_inspection` int DEFAULT NULL COMMENT '自检(1是2否)',
  `routing_inspection` int DEFAULT NULL COMMENT '巡检',
  `add_SPC_analysis` int DEFAULT NULL COMMENT '是否加入SPC分析',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `detail_property` int NOT NULL COMMENT '项目属性',
  `project_name` int NOT NULL COMMENT '检验项目',
  `mothed` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检验方法',
  `operator` int NOT NULL COMMENT '操作符（1人工判断，2大于，3大于等于，4等于，5小于，6小于等于，7介于）',
  `standard_values` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准值',
  `min_value` int DEFAULT NULL COMMENT '最小值',
  `max_value` int DEFAULT NULL COMMENT '最大值',
  `standard_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '标准单位',
  `editing_type` int NOT NULL COMMENT '编辑类型(1文本，2数值，3下拉框)',
  `data_accuracy` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '数据精度',
  `enums` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '枚举值',
  `defect_grade` int NOT NULL COMMENT '缺陷等级(1一般缺陷，2严重缺陷，3致命缺陷)',
  `remake` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `check_type` int NOT NULL COMMENT '检验类型(1物流检验，2常规检验，3实验室检验)',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='检验模板关联检验项目';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_tray_category`
--

DROP TABLE IF EXISTS `mes_tray_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_tray_category` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `category_code` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '类别编码',
  `category_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '类别名称',
  `material_quality` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '材质',
  `status` int DEFAULT NULL COMMENT '状态:0:启用;1:禁用;默认启用',
  `weight` double(12,2) DEFAULT NULL COMMENT '重量',
  `per_group_number` int DEFAULT NULL COMMENT '每组个数',
  `size` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '尺寸',
  `production_process_id` int DEFAULT NULL COMMENT '工序id',
  `use_to` int DEFAULT NULL COMMENT '用途:分切专用;成品专用;半成品专用;卷筒纸专用',
  `description` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `validate_term` int NOT NULL COMMENT '有效期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='托盘类别';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_tray_file`
--

DROP TABLE IF EXISTS `mes_tray_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_tray_file` (
  `tray_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '托盘类型',
  `production_date` date NOT NULL COMMENT '生产日期',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `tray_code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '托盘编码',
  `tray_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '托盘名称',
  `supplier` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商',
  `status` int NOT NULL COMMENT '状态：待使用:0，已使用:1，已损坏:2;默认已使用',
  `description` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7961 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='托盘档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_verify_apparatus`
--

DROP TABLE IF EXISTS `mes_verify_apparatus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_verify_apparatus` (
  `type` int DEFAULT NULL COMMENT '1、设备验证仪器2、维修验证仪器',
  `parend_id` int NOT NULL COMMENT '验证方案关联id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `apparatus_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '仪器名称',
  `apparatus_code` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '型号及编码',
  `calibration_date` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '校准日期',
  `validity_date` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '有效期',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='验证用仪器';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_warehouse_type_data`
--

DROP TABLE IF EXISTS `mes_warehouse_type_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_warehouse_type_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除（1=未选中；0=已选中）',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `warehouse_id` int NOT NULL COMMENT '库房id',
  `warehouse_type_id` int NOT NULL COMMENT '库房类型（数据字典）id',
  `warehouse_type_code` char(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '库房类型（数据字典）code',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mes_warehouse_type_data库房类型数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_work_center_file`
--

DROP TABLE IF EXISTS `mes_work_center_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_work_center_file` (
  `resource_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资源组code',
  `status` int NOT NULL DEFAULT '1' COMMENT '禁用（0禁用，1启用）',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `wkf_code` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT 'mes系统的工作中心档案的编号',
  `wfk_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT 'mes工作档案中心名称',
  `wfk_workshop_code` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '车间档案表的编号',
  `wfk_dictionaries_code` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '机型类型的编码，数据字典中存储',
  `wfk_remake` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工作中心档案备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `last_decide` char(2) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最终判定结果',
  `treatment_measure` char(2) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '处理措施',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mes_工作中心档案表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_work_center_process`
--

DROP TABLE IF EXISTS `mes_work_center_process`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_work_center_process` (
  `indexs` int NOT NULL COMMENT '顺位',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `center_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工作中心编码',
  `process_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工序编码',
  `fixed_reserve_consumption` int DEFAULT NULL COMMENT '固定准备消耗',
  `color_reserve_consumption` int DEFAULT NULL COMMENT '每色准备消耗',
  `revolve_consumption` decimal(20,10) DEFAULT NULL COMMENT '运转消耗',
  `process_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工艺名称',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工作中心和工序关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_workshop`
--

DROP TABLE IF EXISTS `mes_workshop`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_workshop` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_disable` int DEFAULT '0' COMMENT '是否被禁用',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编号',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `department` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '所属部门',
  `exegesis` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '注释',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `work_manager` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '所属主管',
  `work_manager_name` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '所属主管名字',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mes_车间管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mes_workshop_archives`
--

DROP TABLE IF EXISTS `mes_workshop_archives`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mes_workshop_archives` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `workshop_no` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '车间编号',
  `workshop_name` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '车间名称',
  `floor` int NOT NULL COMMENT '楼层',
  `workshop_area` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '车间面积(㎡)',
  `state` int NOT NULL COMMENT '状态（0启用1禁用）',
  `remark` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='mes_车间档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `mse_ssociated_process`
--

DROP TABLE IF EXISTS `mse_ssociated_process`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mse_ssociated_process` (
  `readiness_time` int DEFAULT NULL COMMENT '每色准备时间(小时/色)',
  `preparation_time` int DEFAULT NULL COMMENT '准备时间(小时）',
  `minimum_value` int DEFAULT NULL COMMENT '最低值',
  `theoretical_capacity` int DEFAULT NULL COMMENT '理论产能',
  `rated_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '定额单位',
  `machine_oding` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '机台编码',
  `standard_capacity` int DEFAULT NULL COMMENT '额定速度',
  `running_cost` int DEFAULT NULL COMMENT '运转消耗（%）',
  `consume_color` int DEFAULT NULL COMMENT '每色准备消耗',
  `fixed_consumption` int DEFAULT NULL COMMENT '固定准备消耗',
  `indexs` int NOT NULL COMMENT '序号',
  `flag_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `process_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工序编号',
  `process_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工序名称',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=520 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='机台关联工序工时定额消耗参数';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `objecttablesthq_mdm00000000`
--

DROP TABLE IF EXISTS `objecttablesthq_mdm00000000`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `objecttablesthq_mdm00000000` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `object_id` int DEFAULT NULL COMMENT '实体id',
  `voltage` int DEFAULT NULL COMMENT '电压',
  `current` int DEFAULT NULL COMMENT '电流',
  `speed` int DEFAULT NULL COMMENT '速度',
  `pressure` int DEFAULT NULL COMMENT '压力',
  `viberration` int DEFAULT NULL COMMENT '震动',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='SMT设备机组';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `operating_record`
--

DROP TABLE IF EXISTS `operating_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `operating_record` (
  `dept_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '部门名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `module_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作模块名称',
  `operation_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作类型(新增编辑删除)',
  `operations_table` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作表',
  `table_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作表id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=30183 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='操作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `place_bom_manage`
--

DROP TABLE IF EXISTS `place_bom_manage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `place_bom_manage` (
  `device_type_id` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备类型id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `place_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '部位编码',
  `place_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '部位名称',
  `parent_id` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '父级ID',
  `menu_level` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '菜单级别',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=69 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='设备BOM管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `process_inspection`
--

DROP TABLE IF EXISTS `process_inspection`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `process_inspection` (
  `z_status` int DEFAULT NULL COMMENT '1、QC检验2、实验室检验',
  `qc_or_not` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否QC检验（0未检验，1已检验）',
  `laboratory_or_not` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否实验室检验（0未检验，1已检验）',
  `upload_attachments` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '上传附件',
  `inspection_opinions` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验意见',
  `result` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验结果（0不合格，1合格）',
  `reason` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '不合格原因',
  `status` int DEFAULT '0' COMMENT '状态（0未提交，1已提交）',
  `inspection_time` datetime DEFAULT NULL COMMENT '送检时间',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除(0未删除，1已删除)',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `document_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单据编号',
  `sale_order` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '销售订单',
  `production_batch_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '生产批次号',
  `code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品编码',
  `product_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '产品名称',
  `inspection_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验类型',
  `source` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '来源',
  `production_processes` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '工序',
  `machine` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '机台',
  `team_group` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '班组',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `sys_result` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室结果判定（不合格0，合格1）',
  `sys_reason` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室不合格原因',
  `sys_inspection_opinions` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室意见',
  `qc_inspection_opinions` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc检验意见',
  `qc_result` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc结果判定（不合格0，合格1）',
  `qc_reason` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc不合格原因',
  `number_received` int DEFAULT NULL COMMENT '接收数量',
  `quantity_output` int DEFAULT NULL COMMENT '产出数量',
  `quantity_waste_products` int DEFAULT NULL COMMENT '废品数量',
  `batch_balancing` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '批平衡',
  `batch_yield` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '批收率',
  `forecast_quantity_finished` int DEFAULT NULL COMMENT '预估成品数量',
  `number_instructions` int DEFAULT NULL COMMENT '指令数量',
  `whether_sensitive_section` int DEFAULT NULL COMMENT '是否敏感区段',
  `qc_number_received` int DEFAULT NULL COMMENT 'qc接收数量',
  `qc_quantity_output` int DEFAULT NULL COMMENT 'qc产出数量',
  `qc_quantity_waste_products` int DEFAULT NULL COMMENT 'qc废品数量',
  `qc_batch_balancing` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc批平衡',
  `qc_batch_yield` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc批收率',
  `wj_status` int DEFAULT NULL COMMENT '尾检（0未检验，1已检验）',
  `template_procedure` int DEFAULT NULL COMMENT '检验模板项目步骤（1仅qc 2仅实验室 3qc实验室均有）',
  `qc_create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '质量检验人',
  `qc_create_time` datetime DEFAULT NULL COMMENT '质量检验时间',
  `sys_create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室检验人',
  `sys_create_time` datetime DEFAULT NULL COMMENT '实验室检验时间',
  `treatment_measure` int DEFAULT NULL COMMENT '处理措施（1返工，2报废，3偏差使用）',
  `is_err_manage` int DEFAULT '0' COMMENT '是否异常处理（0否，1是）',
  `job_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '作业条id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=650 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='过程检验';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `process_inspection_qc`
--

DROP TABLE IF EXISTS `process_inspection_qc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `process_inspection_qc` (
  `gcjy_id` int DEFAULT NULL COMMENT '过程检验主表的id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspection_item` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验项目',
  `operator` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作符',
  `standard_value` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准值',
  `minimum` int DEFAULT NULL COMMENT '最小值',
  `maximum` int DEFAULT NULL COMMENT '最大值',
  `standard_unit` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准单位',
  `defect_level` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缺陷等级',
  `note` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `inspection_result` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验结果',
  `qualified` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否合格',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `inspection_attribute` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验属性',
  `inspection_standard` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验标准',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=992 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='过程检验_QC';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `process_parameter_setting`
--

DROP TABLE IF EXISTS `process_parameter_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `process_parameter_setting` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `index` int NOT NULL COMMENT '序号',
  `process_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工艺名称',
  `data_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '数据类型',
  `box_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编辑框类型',
  `enum_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '枚举类型',
  `enumerated` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '枚举串',
  `default` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '默认值',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工艺参数设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `process_setting`
--

DROP TABLE IF EXISTS `process_setting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `process_setting` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
  `name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `indexs` int NOT NULL COMMENT '序号',
  `process_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '工艺名称',
  `data_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '数据类型',
  `box_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编辑框类型',
  `enum_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '枚举类型',
  `enumerated` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL,
  `default_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '默认值',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工艺参数设置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `proof_scheme`
--

DROP TABLE IF EXISTS `proof_scheme`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proof_scheme` (
  `facility_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备编码',
  `facility_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '设备名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `oa_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT 'OA申请编码',
  `oa_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT 'OA申请名称',
  `oa_state` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '状态',
  `current_man` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '当前处理人',
  `configured` int NOT NULL COMMENT '配置设备',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='验证方案管理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_finished_product`
--

DROP TABLE IF EXISTS `qms_finished_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_finished_product` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `mit_id` int DEFAULT NULL COMMENT 'mes_Inspection_templatem检验模板',
  `jybd_id` int DEFAULT NULL COMMENT '检验表单的id',
  `cpda_id` int DEFAULT NULL COMMENT '产品档案的id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `wx_template_id` int DEFAULT NULL COMMENT '外协检验模板id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29744 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='成品检验模板配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_incoming_inspection`
--

DROP TABLE IF EXISTS `qms_incoming_inspection`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_incoming_inspection` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `purchase_order_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货单号',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `arrival_type` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货类型',
  `arrival_date` datetime DEFAULT NULL COMMENT '到货日期',
  `purchaser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购员',
  `arrival_header_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货单表头主键',
  `line_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '行号',
  `procure_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '单位',
  `arrival_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '数量',
  `order_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主数量',
  `standard_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主单位',
  `conversion_rate` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '换算率',
  `gift` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '赠品',
  `planned_delivery_date` date DEFAULT NULL COMMENT '计划出库日期',
  `arrival_body_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '到货单表体主键',
  `isLaboratory` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否是实验室检验0是1否',
  `isQC` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否是QC检验0是1否',
  `specification_and_model` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '规格型号',
  `order_num` int NOT NULL COMMENT '订单数量',
  `received_num` int NOT NULL COMMENT '收货数量',
  `sampling_num` int NOT NULL COMMENT '抽样数',
  `mes_Inspection_templatem_id` int NOT NULL COMMENT '检验模板的id',
  `manufacture_time` datetime NOT NULL COMMENT '生产日期',
  `reamke` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `supplier_batch` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '供应商批次',
  `logistics_check_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物流检验人',
  `logistics_check_time` datetime DEFAULT NULL COMMENT '物流检验时间',
  `QC_check_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'QC送检人',
  `QC_check_time` datetime DEFAULT NULL COMMENT 'QC送检时间',
  `laboratory_check_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室送检人',
  `laboratory_check_time` datetime DEFAULT NULL COMMENT '实验室送检时间',
  `results` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '结果',
  `status` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '状态',
  `schedule` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '进度',
  `submitted_time` datetime NOT NULL COMMENT '送检时间',
  `submitted_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '送检人',
  `supplier_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '供应商',
  `supplier_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '供应商编号',
  `code` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
  `product_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '产品名称',
  `material_type` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '物料类型',
  `document_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '单据编号',
  `inspection_template_id` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验模板id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_incoming_inspection_file`
--

DROP TABLE IF EXISTS `qms_incoming_inspection_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_incoming_inspection_file` (
  `qms_Incoming_Inspection_id` int DEFAULT NULL COMMENT '来料检验主表的id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `warhourse_file_id` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '库房附件',
  `warhourse_supplier_reports_id` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '供应商报告',
  `warhuorse_sampile` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '库房是否留样0是1否',
  `warhourse_opinion` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '库房意见',
  `warhourse_device` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '库房判定0合格1不合格',
  `warhourse_device_opinion` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '库房不合格原因',
  `qc_file_id` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT 'qc附件',
  `qc_sampile` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc确认留样0是1否',
  `qc_opinion` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc意见',
  `qc_device` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc判定0合格1不合格',
  `qc_device_opinion` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc不合格意见',
  `larbratory_file_id` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '实验室附件',
  `larbratory_opinion` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室意见',
  `larbratory_device` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室判定0合格1不合格',
  `larbratory_device_opinion` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室不合格原因',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验文件意见表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_incoming_inspection_nting`
--

DROP TABLE IF EXISTS `qms_incoming_inspection_nting`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_incoming_inspection_nting` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `purchase_order_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '采购订单号',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验弃用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_incoming_inspection_sub`
--

DROP TABLE IF EXISTS `qms_incoming_inspection_sub`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_incoming_inspection_sub` (
  `specification_and_model` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '规格型号',
  `order_num` int NOT NULL COMMENT '订单数量',
  `received_num` int NOT NULL COMMENT '收货数量',
  `sampling_num` int NOT NULL COMMENT '抽样数',
  `mes_Inspection_templatem_id` int NOT NULL COMMENT '检验模板的id',
  `manufacture_time` datetime NOT NULL COMMENT '生产日期',
  `reamke` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `supplier_batch` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '供应商批次',
  `logistics_check_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物流检验人',
  `logistics_check_time` datetime DEFAULT NULL COMMENT '物流检验时间',
  `QC_check_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'QC送检人',
  `QC_check_time` datetime DEFAULT NULL COMMENT 'QC送检时间',
  `laboratory_check_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室送检人',
  `laboratory_check_time` datetime DEFAULT NULL COMMENT '实验室送检时间',
  `results` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '结果',
  `status` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '状态',
  `schedule` varchar(2) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '进度',
  `purchase_order_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '采购订单号',
  `submitted_time` datetime NOT NULL COMMENT '送检时间',
  `submitted_by` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '送检人',
  `supplier_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '供应商',
  `supplier_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '供应商编号',
  `code` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '编码',
  `product_name` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '产品名称',
  `material_type` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '物料类型',
  `document_no` varchar(30) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '单据编号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验子表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_incomingi_laboratory`
--

DROP TABLE IF EXISTS `qms_incomingi_laboratory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_incomingi_laboratory` (
  `detail_property` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '项目属性',
  `project_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验项目',
  `mothed` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验方法',
  `operator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作符（1人工判断，2大于，3大于等于，4等于，5小于，6小于等于，7介于）',
  `standard_values` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准值',
  `min_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最小值',
  `max_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最大值',
  `standard_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准单位',
  `defect_grade` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缺陷等级(1一般缺陷，2严重缺陷，3致命缺陷)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspection_template_id` int DEFAULT NULL COMMENT '检验模板id',
  `templatem_project_id` int DEFAULT NULL COMMENT '关联检验模板检验项的id',
  `qms_Incoming_Inspection_id` int DEFAULT NULL COMMENT '来料检验主表的id',
  `laboratory_result` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '实验室抽样结果',
  `larboratory_isOK` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '这一项检验是否合格0合格1不合格',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验实验室检验表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_incomingi_qc`
--

DROP TABLE IF EXISTS `qms_incomingi_qc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_incomingi_qc` (
  `detail_property` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '项目属性',
  `project_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验项目',
  `mothed` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '检验方法',
  `operator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作符（1人工判断，2大于，3大于等于，4等于，5小于，6小于等于，7介于）',
  `standard_values` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准值',
  `min_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最小值',
  `max_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最大值',
  `standard_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准单位',
  `defect_grade` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缺陷等级(1一般缺陷，2严重缺陷，3致命缺陷)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspection_template_id` int DEFAULT NULL COMMENT '检验模板id',
  `templatem_project_id` int DEFAULT NULL COMMENT '检验模板关联项的id',
  `qms_Incoming_Inspection_id` int DEFAULT NULL COMMENT '来料检验主表的id',
  `qc_result` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'qc抽样结果',
  `qc_isOK` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '这一项检验是否合格0合格1不合格',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验qc检验表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_incomingi_warhourse`
--

DROP TABLE IF EXISTS `qms_incomingi_warhourse`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_incomingi_warhourse` (
  `detail_property` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '项目属性',
  `project_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验项目',
  `mothed` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验方法',
  `operator` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作符（1人工判断，2大于，3大于等于，4等于，5小于，6小于等于，7介于）',
  `standard_values` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准值',
  `min_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最小值',
  `max_value` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最大值',
  `standard_unit` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '标准单位',
  `defect_grade` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '缺陷等级(1一般缺陷，2严重缺陷，3致命缺陷)',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `inspection_template_id` int DEFAULT NULL COMMENT '检验模板id',
  `templatem_project_id` int DEFAULT NULL COMMENT '检验模板关联项的id',
  `qms_Incoming_Inspection_id` int DEFAULT NULL COMMENT '来料检验主表的id',
  `warhourse_result` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '库房检验结果',
  `warhourse_isOK` varchar(1) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '这一项检验是否合格0合格1不合格',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `remake` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='来料检验库房检验表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_process_inspection_templat`
--

DROP TABLE IF EXISTS `qms_process_inspection_templat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_process_inspection_templat` (
  `sc_inspection_templatem_id` int DEFAULT NULL COMMENT '生产检验模板id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `process_setting_id` int DEFAULT NULL COMMENT '工序设置表的id',
  `zl_inspection_templatem_id` int DEFAULT NULL COMMENT '检验模板表的id',
  `inspection_form_id` int DEFAULT NULL COMMENT '配置检验表单表的id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序检验模板检验表单中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `qms_process_template`
--

DROP TABLE IF EXISTS `qms_process_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qms_process_template` (
  `sc_inspection_templatem_id` int DEFAULT NULL COMMENT '生产检验模板id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `process_setting_id` int DEFAULT NULL COMMENT '工序设置表的id',
  `zl_inspection_templatem_id` int DEFAULT NULL COMMENT '检验模板表的id',
  `inspection_form_id` int DEFAULT NULL COMMENT '配置检验表单表的id',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='工序检验模板检验表单中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `synchronize_data`
--

DROP TABLE IF EXISTS `synchronize_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `synchronize_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `type` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '系统类型',
  `genre` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '功能类型',
  `duration` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '时长',
  `renew_time` datetime DEFAULT NULL COMMENT '更新时间',
  `address` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci,
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='定时接口调用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_product_design`
--

DROP TABLE IF EXISTS `t_product_design`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_product_design` (
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `design_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '设计i文件',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品名称',
  `product_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '产品编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='产品设计档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `test_llw`
--

DROP TABLE IF EXISTS `test_llw`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `test_llw` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `t_data` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '数据',
  `remark` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci NOT NULL COMMENT '备注',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=158 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='测试日志数据';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_group`
--

DROP TABLE IF EXISTS `user_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '编码',
  `name` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
  `type` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'notice' COMMENT '默认 notice',
  `flag_delete` tinyint unsigned DEFAULT '0' COMMENT '是否已删除',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `creator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `modifier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `creator_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人ip',
  `modifier_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人ip',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户分组';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_group_detail`
--

DROP TABLE IF EXISTS `user_group_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_group_detail` (
  `id` int NOT NULL AUTO_INCREMENT,
  `group_code` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '编码',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `flag_delete` tinyint unsigned DEFAULT '0' COMMENT '是否已删除',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `creator` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人',
  `modifier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
  `creator_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '创建人ip',
  `modifier_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人ip',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='用户分组详细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `verify_method`
--

DROP TABLE IF EXISTS `verify_method`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `verify_method` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `oid` int NOT NULL COMMENT '主表id',
  `type_id` int NOT NULL COMMENT '确定方式:1.安装确定2.运行确认3.性能确认',
  `max_type` int DEFAULT NULL COMMENT '类型:1.验证方法与内容2.工艺性确认3.其他',
  `apply_scope` int NOT NULL COMMENT '适用范围',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='验证方法与内容';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'hq_mdm_b'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-10-28 13:50:14
