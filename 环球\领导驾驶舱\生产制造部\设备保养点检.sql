-- 保养
SELECT machine_code,
       machine_name,
       SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END)   AS routine_maintenance,
       SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END)   AS total_productive_maintenance,
       SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS pending_execution,
       SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) AS on_schedule_execution,
       SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) AS overdue_execution,
       SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) AS not_executed_overdue,
       SUM(CASE WHEN status = 7 THEN 1 ELSE 0 END) AS not_due
FROM (SELECT DISTINCT t1.*, t3.code AS machine_code, t3.machine_name
      FROM cockpit.ods_em_maintenance_task t1
               JOIN cockpit.ods_mes_machine_related_device t2 ON t1.device_code = t2.device_code
               JOIN cockpit.ods_machine_file t3 ON t2.machine_code = t3.code
      WHERE t1.flag_deleted = 0
        AND t2.flag_deleted = 0
        AND t3.isdelete = 0
        AND t1.data_source_type = 1
        AND ((:start_date is null or :start_date = '') or (plan_date >= :start_date))
        AND ((:end_date is null or :end_date = '') or (plan_date <= :end_date))
        AND ((:machine_code is null or :machine_code = '') or (machine_code = :machine_code))) temp
GROUP BY machine_code, machine_name
order by machine_name
limit :page_size offset :offset;

-- 保养明细
SELECT DISTINCT t1.id,
                t3.code AS machine_code ,
                t3.machine_name AS machine_name,
                (SELECT devicename
                 FROM cockpit.ods_em_fixed_assets
                 WHERE flag_deleted = 0
                   AND id = t1.device_id) AS    device_name,
                (SELECT equipment_coding
                 FROM cockpit.ods_em_fixed_assets
                 WHERE flag_deleted = 0
                   AND id = t1.device_id) AS    device_code,
                (select executor_name
                 from cockpit.ods_em_maintenance_result oemr
                 where oemr.flag_deleted=0
                   and oemr.task_id=t1.id) as executor_name,
                case t1.status when 1 then '未到期'
                               when 2 then '待执行'
                               when 3 then '验证中'
                               when 4 then '按期执行'
                               when 5 then '逾期执行'
                               when 6 then '逾期未执行'
                               else '逾期未执行'
                    end as status,
        (select cost_amount
         from cockpit.ods_em_maintenance_result oemr
         where oemr.flag_deleted=0
           and oemr.task_id=t1.id) as cost_amount,
                t1.remark remark
FROM cockpit.ods_em_maintenance_task t1
         JOIN cockpit.ods_mes_machine_related_device t2 ON t1.device_code = t2.device_code
         JOIN cockpit.ods_machine_file t3 ON t2.machine_code = t3.code
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.isdelete = 0
  AND t1.data_source_type = 1
  AND ((:start_date is null or :start_date = '') or (plan_date >= :start_date))
  AND ((:end_date is null or :end_date = '') or (plan_date <= :end_date))
  AND ((:machine_code is null or :machine_code = '') or (machine_code = :machine_code))
order by t1.id desc
limit :page_size offset :offset
;


-- 巡检
SELECT machine_code,
       machine_name,
       count(machine_code)                         AS inspection,
       SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) AS pending_execution,
       SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) AS on_schedule_execution,
       SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) AS overdue_execution,
       SUM(CASE WHEN status = 6 THEN 1 ELSE 0 END) AS not_executed_overdue,
       SUM(CASE WHEN status = 7 THEN 1 ELSE 0 END) AS not_due
FROM (SELECT DISTINCT t1.*, t3.code AS machine_code, t3.machine_name
      FROM cockpit.ods_em_maintenance_task t1
               JOIN cockpit.ods_mes_machine_related_device t2 ON t1.device_code = t2.device_code
               JOIN cockpit.ods_machine_file t3 ON t2.machine_code = t3.code
      WHERE t1.flag_deleted = 0
        AND t2.flag_deleted = 0
        AND t3.isdelete = 0
        AND t1.data_source_type = 2
        AND ((:start_date is null or :start_date = '') or (plan_date >= :start_date))
        AND ((:end_date is null or :end_date = '') or (plan_date <= :end_date))
        AND ((:machine_code is null or :machine_code = '') or (machine_code = :machine_code))) temp
GROUP BY machine_code, machine_name
order by machine_name
limit :page_size offset :offset;

-- 巡检明细
SELECT DISTINCT t1.id,
                t3.code AS machine_code ,
                t3.machine_name AS machine_name,
                (SELECT devicename
                 FROM cockpit.ods_em_fixed_assets
                 WHERE flag_deleted = 0
                   AND id = t1.device_id) AS    device_name,
                (SELECT equipment_coding
                 FROM cockpit.ods_em_fixed_assets
                 WHERE flag_deleted = 0
                   AND id = t1.device_id) AS    device_code,
               (select executor_name
                from cockpit.ods_em_maintenance_result oemr
                where oemr.flag_deleted=0
                and oemr.task_id=t1.id) as executor_name,
                case t1.status when 1 then '未到期'
                               when 2 then '待执行'
                               when 3 then '验证中'
                               when 4 then '按期执行'
                               when 5 then '逾期执行'
                               when 6 then '逾期未执行'
                                else '逾期未执行'
                                end as status,
                 t1.remark remark
FROM cockpit.ods_em_maintenance_task t1
         JOIN cockpit.ods_mes_machine_related_device t2 ON t1.device_code = t2.device_code
         JOIN cockpit.ods_machine_file t3 ON t2.machine_code = t3.code
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.isdelete = 0
  AND t1.data_source_type = 2
  AND ((:start_date is null or :start_date = '') or (plan_date >= :start_date))
  AND ((:end_date is null or :end_date = '') or (plan_date <= :end_date))
  AND ((:machine_code is null or :machine_code = '') or (machine_code = :machine_code))
order by t1.id desc
limit :page_size offset :offset;

-- 设备维修
select machine_code,
       machine_name,
       count(machine_code)                                  AS total,
       SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END)          AS to_be_submitted,
       SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END)          AS to_be_assigned,
       SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END)          AS to_be_repaired,
       SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END)          AS under_repair,
       SUM(CASE WHEN status in (4, 5, 6) THEN 1 ELSE 0 END) AS under_acceptance,
       SUM(CASE WHEN status = 7 THEN 1 ELSE 0 END)          AS under_verification,
       SUM(CASE WHEN status = 8 THEN 1 ELSE 0 END)          AS completed
from (select distinct t3.code AS machine_code, t3.machine_name, t1.*
      from cockpit.ods_mes_device_maintain t1
               JOIN cockpit.ods_mes_machine_related_device t2 ON t1.failure_deveice_id = t2.device_id
               JOIN cockpit.ods_machine_file t3 ON t2.machine_code = t3.code
      where t1.flag_deleted = 0
        AND t2.flag_deleted = 0
        AND t3.isdelete = 0
        AND ((:start_date is null or :start_date = '') or (t1.create_time >= :start_date))
        AND ((:end_date is null or :end_date = '') or (t1.create_time <= :end_date))
        AND ((:machine_code is null or :machine_code = '') or (machine_code = :machine_code))) temp
group by machine_code, machine_name
order by machine_name
limit :page_size offset :offset;

-- 设备维修明细
select distinct t1.id,
                t3.code                            AS         machine_code,
                t3.machine_name,
                (SELECT devicename
                 FROM cockpit.ods_em_fixed_assets
                 WHERE flag_deleted = 0
                   AND id = t1.failure_deveice_id) AS         device_name,
                (SELECT equipment_coding
                 FROM cockpit.ods_em_fixed_assets
                 WHERE flag_deleted = 0
                   AND id = t1.failure_deveice_id) AS         device_code,
                (SELECT place_name
                 FROM cockpit.ods_place_bom_manage
                 WHERE flag_deleted = 0
                   AND id = t1.failure_location)   as         failure_location_name,
                (select data_name
                 from cockpit.ods_mes_defect
                 where flag_deleted = 0
                   and id = t1.failure_type)       as         failure_type,
                if(t1.key_parts = 1, '是', '否')   as         key_parts,
                (select item_name from cockpit.ods_ems_dict_item di where di.flag_delete=0
                    and catalog_code='priority' and di.id=t1.failure_level) faliure_level,
                (SELECT group_concat(mm.maintain_by_name SEPARATOR ',')
                 FROM cockpit.ods_mes_mechanics mm
                 WHERE mm.flag_deleted = 0
                   AND mm.device_maintain_id = t1.id
                   AND mm.type = '1')              AS         repair_person,
                (select name
                 from cockpit.ods_sys_user
                 where t1.flag_deleted = 0
                   and user_id = t1.create_by)                report_person,
                ifnull(t1.repair_report_time, t1.create_time) report_time,
                if(t1.status!=8,null,t1.update_time) repair_time,
                (SELECT status_desc
                 FROM cockpit.ods_em_maintain_status
                 WHERE status_code = t1.status)    AS         status,
                t1.failure_description  as failure_description
from cockpit.ods_mes_device_maintain t1
         JOIN cockpit.ods_mes_machine_related_device t2 ON t1.failure_deveice_id = t2.device_id
         JOIN cockpit.ods_machine_file t3 ON t2.machine_code = t3.code
where t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.isdelete = 0
  AND ((:start_date is null or :start_date = '') or (t1.create_time >= :start_date))
  AND ((:end_date is null or :end_date = '') or (t1.create_time <= :end_date))
  AND ((:machine_code is null or :machine_code = '') or (machine_code = :machine_code))
order by t1.id desc
limit :page_size offset :offset;

-- 故障部位数量饼图
select failure_location_name label,
       count(failure_location_name) value from
(select distinct t1.id,
                (SELECT place_name
                 FROM cockpit.ods_place_bom_manage
                 WHERE flag_deleted = 0
                   AND id = t1.failure_location)   as         failure_location_name
from cockpit.ods_mes_device_maintain t1
         JOIN cockpit.ods_mes_machine_related_device t2 ON t1.failure_deveice_id = t2.device_id
         JOIN cockpit.ods_machine_file t3 ON t2.machine_code = t3.code
where t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.isdelete = 0
  AND ((:start_date is null or :start_date = '') or (t1.create_time >= :start_date))
  AND ((:end_date is null or :end_date = '') or (t1.create_time <= :end_date))
  AND ((:machine_code is null or :machine_code = '') or (machine_code = :machine_code))
    ) temp
group by failure_location_name
order by temp.failure_location_name desc;

-- 维修成本
select failure_location_name label,
       sum(ifnull(total_price,0)) value
       from
(select distinct t1.id,
                 (SELECT place_name
                  FROM cockpit.ods_place_bom_manage
                  WHERE flag_deleted = 0
                    AND id = t1.failure_location)   as         failure_location_name,
                 t4.total_price total_price
 from cockpit.ods_mes_device_maintain t1
          JOIN cockpit.ods_mes_machine_related_device t2 ON t1.failure_deveice_id = t2.device_id
          JOIN cockpit.ods_machine_file t3 ON t2.machine_code = t3.code
          left JOIN cockpit.ods_em_maintain_spare_parts t4 on t4.maintain_id=t1.id
 where t1.flag_deleted = 0
   AND t2.flag_deleted = 0
   AND t3.isdelete = 0
   AND ((:start_date is null or :start_date = '') or (t1.create_time >= :start_date))
   AND ((:end_date is null or :end_date = '') or (t1.create_time <= :end_date))
   AND ((:machine_code is null or :machine_code = '') or (machine_code = :machine_code))
) temp
group by failure_location_name
order by temp.failure_location_name desc;




