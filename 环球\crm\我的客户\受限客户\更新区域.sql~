-- 查下区域
select t1.id,t1.cust_code, t1.region,t2.department_region from crm_abnormal_feedback t1
join crm_cust_basic t2 on t1.cust_code=t2.cust_code ;
-- 更新区域
update crm_abnormal_feedback t1
    join crm_cust_basic t2 on t1.cust_code=t2.cust_code
set t1.region=t2.department_region
where 1=1
;
-- 查下回款周期
select t1.id,t1.cust_code, t1.payment_terms,t2.payback_period_day
from crm_abnormal_feedback t1
  join crm_cust_basic t2 on t1.cust_code=t2.cust_code ;

update crm_abnormal_feedback t1
    join crm_cust_basic t2 on t1.cust_code=t2.cust_code
set t1.payment_terms=t2.payback_period_day
where 1=1;

-- 查下超期
select t1.id,t1.cust_code, t1.inventory_overdue_days,t1.payment_overdue_days,t2.payback_period_day,inventory_cycle_day
from crm_abnormal_feedback t1
         join crm_cust_basic t2 on t1.cust_code=t2.cust_code ;
update crm_abnormal_feedback t1
    join crm_cust_basic t2 on t1.cust_code=t2.cust_code
set t1.payment_overdue_days=t1.t2.payback_period_day-t2.payback_period_day
where t2.payback_period_day is not null;

select ifnull(sum(qkbbbye), 0) unpaid_money,
       ifnull(max(qkts), 0)    unpaid_days
FROM crm_account_receivable_age
WHERE flag_deleted = 0
  and cust_code = 'C000116';
