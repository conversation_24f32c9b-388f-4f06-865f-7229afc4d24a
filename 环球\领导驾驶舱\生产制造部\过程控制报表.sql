WITH non_overlay_overflow_num AS (
    SELECT
        DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
        round(SUM(COALESCE(received_quantity, 0) - COALESCE(release_quantity, 0) - COALESCE(release_quantity * if(end_floating_rate is null,0,replace(end_floating_rate,'%',''))/100, 0)),2) AS non_overlay_overflow_quantity,
        0 AS non_overlay_shortage_quantity,
        COUNT(production_batch_number) AS non_overlay_overflow_batches,
        0 AS non_overlay_shortage_batches,
        0 AS overlay_overflow_quantity,
        0 AS overlay_shortage_quantity,
        0 AS overlay_overflow_batches,
        0 AS overlay_shortage_batches,
        0 AS count_floating_overflow,
        0 AS count_floatinge_positive_difference,
        0 AS count_zero_floating_rate,
        0 AS overflow_batch_num,
        0 AS shortage_batch_num,
        0 AS count_float,
        0 AS all_batch_non_floating,
        0 AS makeup_batch_num,
        0 AS rework_batch_num,
        0 AS rework_quantity,
        0 AS all_batch
    FROM
        pm_job_order
    WHERE
        flag_deleted = 0
      AND production_order_status = 3
      AND source_type = 1
      AND production_batch_number NOT LIKE 'QS%'
      AND is_collage='1'
      AND YEAR(finish_date) = :yearL
    GROUP BY
        month_sum
    HAVING
        non_overlay_overflow_quantity > 0
),
     non_overlay_shortage_num AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             SUM(COALESCE(release_quantity, 0) - COALESCE(received_quantity, 0) - COALESCE(release_quantity * if(start_floating_rate is null,0,abs(replace(start_floating_rate,'%',''))/100), 0)) AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             COUNT(production_batch_number) AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             0 AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             0 AS overlay_shortage_batches,
             0 AS count_floating_overflow,
             0 AS count_floatinge_positive_difference,
             0 AS count_zero_floating_rate,
             0 AS overflow_batch_num,
             0 AS shortage_batch_num,
             0 AS count_float,
             0 AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             0 AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND is_collage='1'
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
         HAVING
             non_overlay_shortage_quantity > 0
     ),
     overlay_overflow_num AS (
    SELECT
    DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
    0 AS non_overlay_overflow_quantity,
    0 AS non_overlay_shortage_quantity,
    0 AS non_overlay_overflow_batches,
    0 AS non_overlay_shortage_batches,
    SUM(COALESCE(received_quantity, 0) - COALESCE(release_quantity, 0) - COALESCE(release_quantity * if(end_floating_rate is null,0,replace(end_floating_rate,'%',''))/100, 0)) AS overlay_overflow_quantity,
    0 AS overlay_shortage_quantity,
    COUNT(production_batch_number) AS overlay_overflow_batches,
    0 AS overlay_shortage_batches,
    0 AS count_floating_overflow,
    0 AS count_floatinge_positive_difference,
    0 AS count_zero_floating_rate,
    0 AS overflow_batch_num,
    0 AS shortage_batch_num,
    0 AS count_float,
    0 AS all_batch_non_floating,
    0 AS makeup_batch_num,
    0 AS rework_batch_num,
    0 AS rework_quantity,
    0 AS all_batch
    FROM
    pm_job_order
    WHERE
    flag_deleted = 0
    AND production_order_status = 3
    AND source_type = 1
    AND production_batch_number NOT LIKE 'QS%'
    AND is_collage='0'
    AND YEAR(finish_date) = :yearL
    GROUP BY
    month_sum
    HAVING
    overlay_overflow_quantity > 0
    ),
     overlay_shortage_num AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             0 AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             0 AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             round(SUM(COALESCE(release_quantity, 0) - COALESCE(received_quantity, 0) - COALESCE(release_quantity * if(start_floating_rate is null,0,abs(replace(start_floating_rate,'%',''))/100), 0)),2) AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             COUNT(production_batch_number) AS overlay_shortage_batches,
             0 AS count_floating_overflow,
             0 AS count_floatinge_positive_difference,
             0 AS count_zero_floating_rate,
             0 AS overflow_batch_num,
             0 AS shortage_batch_num,
             0 AS count_float,
             0 AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             0 AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND is_collage='0'
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
         HAVING
             overlay_shortage_quantity > 0
     ),
    overflow_batch AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             0 AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             0 AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             0 AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             0 AS overlay_shortage_batches,
             COUNT(production_batch_number) AS count_floating_overflow,
             0 AS count_floatinge_positive_difference,
             0 AS count_zero_floating_rate,
             0 AS overflow_batch_num,
             0 AS shortage_batch_num,
             SUM(COALESCE(received_quantity, 0) - COALESCE(release_quantity, 0) - COALESCE(release_quantity * if(end_floating_rate is null,0,replace(end_floating_rate,'%',''))/100, 0)) AS count_float,
             0 AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             0 AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND (start_floating_rate IS NULL OR start_floating_rate = '' OR start_floating_rate = 0)
           AND (end_floating_rate IS NULL OR end_floating_rate = '' OR end_floating_rate = 0)
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
         HAVING
             count_float > 0
     ),
     shortage_batch AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             0 AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             0 AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             0 AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             0 AS overlay_shortage_batches,
             0 AS count_floating_overflow,
             COUNT(production_batch_number) AS count_floatinge_positive_difference,
             0 AS count_zero_floating_rate,
             0 AS overflow_batch_num,
             0 AS shortage_batch_num,
             SUM(COALESCE(release_quantity, 0) - COALESCE(received_quantity, 0) - COALESCE(release_quantity * if(start_floating_rate is null,0,abs(replace(start_floating_rate,'%',''))/100), 0)) AS count_float,
             0 AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             0 AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND (start_floating_rate IS NULL OR start_floating_rate = '' OR start_floating_rate = 0)
           AND (end_floating_rate IS NULL OR end_floating_rate = '' OR end_floating_rate = 0)
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
         HAVING
             count_float > 0
     ),
     order_data AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             0 AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             0 AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             0 AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             0 AS overlay_shortage_batches,
             0 AS count_floating_overflow,
             0 AS count_floatinge_positive_difference,
             COUNT(production_batch_number) AS count_zero_floating_rate,
             0 AS overflow_batch_num,
             0 AS shortage_batch_num,
             0 AS count_float,
             0 AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             0 AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND (start_floating_rate IS NULL OR start_floating_rate = '' OR start_floating_rate = 0)
           AND (end_floating_rate IS NULL OR end_floating_rate = '' OR end_floating_rate = 0)
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
     ),
     all_batch_data_non_floating AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             0 AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             0 AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             0 AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             0 AS overlay_shortage_batches,
             0 AS count_floating_overflow,
             0 AS count_floatinge_positive_difference,
             0 AS count_zero_floating_rate,
             0 AS overflow_batch_num,
             0 AS shortage_batch_num,
             0 AS count_float,
             COUNT(production_batch_number) AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             0 AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND (start_floating_rate !=0 and end_floating_rate != 0)
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
     ),
     float_overflow AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             0 AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             0 AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             0 AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             0 AS overlay_shortage_batches,
             0 AS count_floating_overflow,
             0 AS count_floatinge_positive_difference,
             0 AS count_zero_floating_rate,
             COUNT(production_batch_number) AS overflow_batch_num,
             0 AS shortage_batch_num,
             SUM(COALESCE(received_quantity, 0) - COALESCE(release_quantity, 0) - COALESCE(release_quantity * if(end_floating_rate is null,0,replace(end_floating_rate,'%',''))/100, 0)) AS count_float,
             0 AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             0 AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND (start_floating_rate IS NOT NULL OR start_floating_rate != '' OR start_floating_rate != 0)
           AND (end_floating_rate IS NOT NULL OR end_floating_rate != '' OR end_floating_rate != 0)
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
         HAVING
             count_float > 0
     ),
     float_shortage AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             0 AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             0 AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             0 AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             0 AS overlay_shortage_batches,
             0 AS count_floating_overflow,
             0 AS count_floatinge_positive_difference,
             0 AS count_zero_floating_rate,
             0 AS overflow_batch_num,
             COUNT(production_batch_number) AS shortage_batch_num,
             SUM(COALESCE(release_quantity, 0) - COALESCE(received_quantity, 0) - COALESCE(release_quantity * if(start_floating_rate is null,0,replace(start_floating_rate,'%',''))/100, 0)) AS count_float,
             0 AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             0 AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND (start_floating_rate IS NOT NULL OR start_floating_rate != '' OR start_floating_rate != 0)
           AND (end_floating_rate IS NOT NULL OR end_floating_rate != '' OR end_floating_rate != 0)
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
         HAVING
             count_float > 0
     ),
   makeup_data as (
        select DATE_FORMAT(t2.finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
                0 AS non_overlay_shortage_quantity,
               0 AS non_overlay_overflow_batches,
               0 AS non_overlay_shortage_batches,
               0 AS overlay_overflow_quantity,
               0 AS overlay_shortage_quantity,
               0 AS overlay_overflow_batches,
               0 AS overlay_shortage_batches,
               0 AS count_floating_overflow,
               0 AS count_floatinge_positive_difference,
               0 AS count_zero_floating_rate,
               0 AS overflow_batch_num,
               0 AS shortage_batch_num,
               0 AS count_float,
               0 AS all_batch_non_floating,
               count(distinct t2.production_batch_number)  AS makeup_batch_num,
               0 AS rework_batch_num,
               0 AS rework_quantity,
               0 AS all_batch
        from pm_job_order_restock t1
        join pm_job_order t2 on t1.production_batch_number = t2.production_batch_number
        where t1.type=1 and t1.flag_deleted=0 and t2.flag_deleted=0
        AND t2.production_order_status = 3
        AND t2.source_type = 1
        AND YEAR(t2.finish_date) = :yearL
        group by month_sum
    ),
     rework_data as (
         select DATE_FORMAT(t2.finish_date, '%Y-%m') AS month_sum,
                0 AS non_overlay_overflow_quantity,
                0 AS non_overlay_shortage_quantity,
                0 AS non_overlay_overflow_batches,
                0 AS non_overlay_shortage_batches,
                0 AS overlay_overflow_quantity,
                0 AS overlay_shortage_quantity,
                0 AS overlay_overflow_batches,
                0 AS overlay_shortage_batches,
                0 AS count_floating_overflow,
                0 AS count_floatinge_positive_difference,
                0 AS count_zero_floating_rate,
                0 AS overflow_batch_num,
                0 AS shortage_batch_num,
                0 AS count_float,
                0 AS all_batch_non_floating,
                0  AS makeup_batch_num,
                count(distinct t2.production_batch_number) AS rework_batch_num,
                sum(received_quantity) AS rework_quantity,
                0 AS all_batch
         from pm_job_order_restock t1
                  join pm_job_order t2 on t1.production_batch_number = t2.production_batch_number
         where t1.type=2 and t1.flag_deleted=0 and t2.flag_deleted=0
         AND t2.production_order_status = 3
         AND t2.source_type = 1
         AND YEAR(t2.finish_date) = :yearL
         group by month_sum
     ),
     all_batch_data AS (
         SELECT
             DATE_FORMAT(finish_date, '%Y-%m') AS month_sum,
             0 AS non_overlay_overflow_quantity,
             0 AS non_overlay_shortage_quantity,
             0 AS non_overlay_overflow_batches,
             0 AS non_overlay_shortage_batches,
             0 AS overlay_overflow_quantity,
             0 AS overlay_shortage_quantity,
             0 AS overlay_overflow_batches,
             0 AS overlay_shortage_batches,
             0 AS count_floating_overflow,
             0 AS count_floatinge_positive_difference,
             0 AS count_zero_floating_rate,
             0 AS overflow_batch_num,
             0 AS shortage_batch_num,
             0 AS count_float,
             0 AS all_batch_non_floating,
             0 AS makeup_batch_num,
             0 AS rework_batch_num,
             0 AS rework_quantity,
             COUNT(production_batch_number) AS all_batch
         FROM
             pm_job_order
         WHERE
             flag_deleted = 0
           AND production_order_status = 3
           AND source_type = 1
           AND production_batch_number NOT LIKE 'QS%'
           AND YEAR(finish_date) = :yearL
         GROUP BY
             month_sum
     ),
     dataL AS (
         SELECT * FROM non_overlay_overflow_num onl
         UNION
         SELECT * FROM non_overlay_shortage_num snl
         UNION
         SELECT * FROM overlay_overflow_num onl2
         UNION
         SELECT * FROM overlay_shortage_num snl2
         UNION
         SELECT * FROM overflow_batch obl
         UNION
         SELECT * FROM shortage_batch sbl
         UNION
         SELECT * FROM order_data odl
         UNION
         SELECT * FROM all_batch_data_non_floating abd
         UNION
         SELECT * FROM float_overflow fol
         UNION
         SELECT * FROM float_shortage fsl
         UNION
         SELECT * FROM makeup_data
         UNION
         SELECT * FROM rework_data
         UNION
         SELECT * FROM all_batch_data
     )
SELECT
    month_sum AS month,
    round(MAX(non_overlay_overflow_quantity),2) AS non_overlay_overflow_quantity,
    round(MAX(non_overlay_shortage_quantity),2) AS non_overlay_shortage_quantity,
    ROUND(MAX(non_overlay_overflow_batches), 2) AS non_overlay_overflow_batches,
    ROUND(MAX(non_overlay_shortage_batches), 2) AS non_overlay_shortage_batches,
    ROUND(MAX(overlay_overflow_quantity), 2) AS overlay_overflow_quantity,
    ROUND(MAX(overlay_shortage_quantity), 2) AS overlay_shortage_quantity,
    ROUND(MAX(overlay_overflow_batches), 2) AS overlay_overflow_batches,
    ROUND(MAX(overlay_shortage_batches), 2) AS overlay_shortage_batches,
    ROUND(MAX(overlay_overflow_quantity), 2) AS overlay_overflow_quantity,
    ROUND(MAX(overlay_shortage_quantity), 2) AS overlay_shortage_quantity,
    ROUND(MAX(overlay_overflow_batches), 2) AS overlay_overflow_batches,
    ROUND(MAX(overlay_shortage_batches), 2) AS overlay_shortage_batches,
    ROUND(MAX(count_floating_overflow), 2) AS count_floating_overflow,
    ROUND(MAX(count_floatinge_positive_difference), 2) AS count_floatinge_positive_difference,
    ROUND(MAX(count_zero_floating_rate), 2) AS count_zero_floating_rate,
    ROUND(MAX(overflow_batch_num), 2) AS overflow_batch_num,
    ROUND(MAX(shortage_batch_num), 2) AS shortage_batch_num,
    ROUND(MAX(all_batch_non_floating), 2) AS all_batch_non_floating,
    IF(MAX(all_batch_non_floating) = 0, 0, ROUND(MAX(overflow_batch_num) / MAX(all_batch_non_floating), 2)) AS overflow_batch_rate,
    IF(MAX(all_batch_non_floating) = 0, 0, ROUND(MAX(shortage_batch_num) / MAX(all_batch_non_floating), 2)) AS missing_batch_rate,
    ROUND(MAX(makeup_batch_num), 2) AS makeup_batch_num,
    ROUND(MAX(rework_batch_num), 2) AS rework_batch_num,
    ROUND(MAX(rework_quantity), 2) AS rework_quantity,
    ROUND(MAX(all_batch), 2) AS all_batch,
    IF(MAX(all_batch) = 0,0,ROUND(((MAX(all_batch) - MAX(non_overlay_overflow_batches) - MAX(non_overlay_shortage_batches) - MAX(overlay_overflow_batches) - MAX(overlay_shortage_batches)) /MAX(all_batch)),2)) AS normal_rate
FROM
    dataL
GROUP BY
    month_sum;




select abs(if(start_floating_rate is null,0,replace(start_floating_rate,'%',''))/100) from pm_job_order pjo limit 100;
select if(end_floating_rate is null,0,replace(end_floating_rate,'%',''))/100 from pm_job_order pjo limit 100;
;
