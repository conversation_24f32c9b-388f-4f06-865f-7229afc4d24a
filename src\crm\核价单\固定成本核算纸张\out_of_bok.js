var obj={"craftArr":[{"craft_name":"原料分切","work_name":"GPFQZX","indexs":"ZDQX00014810000002","technological_parameter":"[]","operation_unit":"张","product_code":"ZDQX0001481","minimum_consumption":20,"create_by":"2","update_time":"2025-02-14 18:04:08","rate":0,"parent_code":"ZDQX0001481","ban_json_code":"版编号：ZDQX0001481_1_0_01类型：印版类别：CTP版版名称：测试色次CTP版出本：10,版编号：ZDQX0001481_1_2_01类型：模切版类别：模切版版名称：测试色次模切版出本：7","r_craft_code":"D0035","process_type":"1","ys_fixed_time":0,"id":0,"update_by":"2","material_name":"编码：1010101010000010 名称：787 400g 宁波金丽白卡 index：ZDQX00014810000001","source_name":"GP分切中心","r_work_code":"GPFQZX","flag_deleted":0,"ys_standing_time":0,"product_version":"1.7","work_code":"GPFQZX","create_time":"2025-02-14 18:04:08","craft_code":"D0035","count":"1","index":"ZDQX00014810000002","material_json_code":"编码：1010101010000010 名称：787 400g 宁波金丽白卡","version":"1.0","ys_fixed_rate":0,"out_of_book":10,"fixed_consumption":0,"ys_manual_speed":0,"source_code":"GPFQZX"},{"craft_name":"印刷","work_name":"GPYSKBSJYSZX","indexs":"ZDQX00014810000003","technological_parameter":"[]","operation_unit":"张","product_code":"ZDQX0001481","minimum_consumption":150,"create_by":"2","update_time":"2025-02-14 18:04:08","rate":0,"parent_code":"ZDQX0001481","ban_json_code":"版编号：ZDQX0001481_1_2_01类型：模切版类别：模切版版名称：测试色次模切版出本：7","r_craft_code":"D0102","process_type":"1","ys_fixed_time":0,"id":1,"update_by":"2","material_name":"编码：1010201010000008 名称：新金冠 蓝（高光） index：ZDQX00014810000004,编码：1010101010000010 名称：787 400g 宁波金丽白卡 index：ZDQX00014810000001","source_name":"GP可变数据印刷中心","r_work_code":"GPYSKBSJYSZX","flag_deleted":0,"ys_standing_time":0,"product_version":"1.7","work_code":"GPYSKBSJYSZX","create_time":"2025-02-14 18:04:08","craft_code":"D0102","count":"1","index":"ZDQX00014810000003","material_json_code":"编码：1010201010000008 名称：新金冠 蓝（高光）,编码：1010101010000010 名称：787 400g 宁波金丽白卡","version":"1.0","ys_fixed_rate":0,"out_of_book":"7","fixed_consumption":0,"ys_manual_speed":0,"source_code":"GPYSKBSJYSZX"},{"craft_name":"覆膜","work_name":"GPZHRGXTZX","technological_parameter":"[]","operation_unit":"张","product_code":"ZDQX0001481","ban_id":"","minimum_consumption":35,"rate":0,"parent_code":"ZDQX0001481_C1","ban_json_code":"版编号：ZDQX0001481_1_0_03类型：印版类别：CTP版版名称：测试色次CTP版出本：18","process_type":"1","r_craft_code":"D0103","ys_fixed_time":0,"id":2,"material_name":"编码：1010101010000007 名称：770 380g 瑞典伊格森德Invercote G白卡 index：ZDQX0001481_C10000011","r_work_code":"GPZHRGXTZX","ys_standing_time":0,"product_version":"1.7","work_code":"GPZHRGXTZX","craft_code":"D0103","count":1,"index":"ZDQX0001481_C10000024","material_json_code":"编码：1010101010000007 名称：770 380g 瑞典伊格森德Invercote G白卡","ys_fixed_rate":0,"out_of_book":"18","fixed_consumption":0,"ys_manual_speed":0},{"craft_name":"粘盒","work_name":"GPZHRGXTZX","technological_parameter":"[]","operation_unit":"张,只","product_code":"ZDQX0001481","ban_id":"","minimum_consumption":500,"rate":0,"parent_code":"ZDQX0001481_C1","ban_json_code":"版编号：ZDQX0001481_1_0_03类型：印版类别：CTP版版名称：测试色次CTP版出本：18","process_type":"2","r_craft_code":"D0036","ys_fixed_time":0,"id":3,"material_name":"编码：1010101010000007 名称：770 380g 瑞典伊格森德Invercote G白卡 index：ZDQX0001481_C10000011","r_work_code":"GPZHRGXTZX","ys_standing_time":0,"product_version":"1.7","work_code":"GPZHRGXTZX","craft_code":"D0036","count":1,"index":"ZDQX0001481_C10000025","material_json_code":"编码：1010101010000007 名称：770 380g 瑞典伊格森德Invercote G白卡","ys_fixed_rate":0,"out_of_book":1,"fixed_consumption":0,"ys_manual_speed":0}],"banArr":[{"flag":false,"edition_name":"测试色次模切版","product_code":"ZDQX0001481","edition_type_id":"554","edition_categroy":"832","create_by":"2","edition_cleanliness":"","update_time":"2025-02-14 18:04:06","edition_code":"ZDQX0001481_1_2_01","relevance_material_code":"1010201010000010","parent_code":"ZDQX0001481","share":0,"id":0,"update_by":"2","edition_categroy_cn":"模切版","flag_deleted":0,"product_version":"1.6","create_time":"2025-02-14 18:04:06","color_sequence":"1","version":"1.0","product_name":"ZDQX0001481","edition_categroy_id":"832","relevance_material_name":"新金冠 蓝（高光）","perimeter":0,"out_of_book":"7","print_rate":"700000","edition_type":"554","edition_type_cn":"模切版","chromatic_degree":"1/1","material_code":"ZDQX0001481"},{"flag":false,"edition_name":"测试色次CTP版","product_code":"ZDQX0001481","edition_type_id":"553","edition_categroy":"829","create_by":"2","update_time":"2025-02-14 18:04:06","edition_code":"ZDQX0001481_1_0_03","relevance_material_code":"1010201010000006","parent_code":"ZDQX0001481","share":0,"id":1,"update_by":"2","edition_categroy_cn":"CTP版","flag_deleted":0,"product_version":"1.6","create_time":"2025-02-14 18:04:06","color_sequence":"1","version":"1.0","product_name":"ZDQX0001481_C1","edition_categroy_id":"829","relevance_material_name":"新金冠 红（高光）","perimeter":0,"out_of_book":"18","print_rate":"40000","edition_type":"553","edition_type_cn":"印版","chromatic_degree":"1/1","material_code":"ZDQX0001481_C1"},{"flag":false,"edition_name":"测试色次CTP版","product_code":"ZDQX0001481","edition_type_id":"553","edition_categroy":"829","create_by":"2","update_time":"2025-02-14 18:04:06","edition_code":"ZDQX0001481_1_0_01","relevance_material_code":"1010201010000009","parent_code":"ZDQX0001481","share":0,"id":2,"update_by":"2","edition_categroy_cn":"CTP版","flag_deleted":0,"product_version":"1.6","create_time":"2025-02-14 18:04:06","color_sequence":"1","version":"1.0","product_name":"ZDQX0001481","edition_categroy_id":"829","relevance_material_name":"新金冠 黄（高光）","perimeter":0,"out_of_book":"10","print_rate":"40000","edition_type":"553","edition_type_cn":"印版","chromatic_degree":"1/1","material_code":"ZDQX0001481"}],"materialResult":[{"standard_unit_cn":"mm","procure_unit":"9077","remark":"","product_code":"ZDQX0001481","create_by":"2","update_time":"2025-02-14 18:04:06","id":950949,"component_count":"1","update_by":"2","material_name":"测试色次","flag_deleted":0,"bom_type":"产品","product_version":"1.7","procure_unit_cn":"mm","create_time":"2025-02-14 18:04:06","categroy":3,"color_sequence":"1","product_size":"1.0*1.0*1.0","version":"1.0","component_size":"1.0*1.0*1.0","spread_size":"1*1","standard_unit":"9077","chromatic_degree":"1/1","material_code":"ZDQX0001481"},{"standard_unit_cn":"测试api-2","procure_unit":"6566","remark":"","product_code":"ZDQX0001481","color_order_quotation":"1(1)","create_by":"2","update_time":"2025-02-14 18:04:06","makeup_product":0,"parent_code":"ZDQX0001481","id":950950,"component_count":"1/1","update_by":"2","material_name":"测试色次1","flag_deleted":0,"bom_type":"部件","product_version":"1.7","procure_unit_cn":"测试api-2","create_time":"2025-02-14 18:04:06","categroy":0,"color_sequence":"1","product_size":"1*1*1","version":"1.0","component_size":"1*1*1","spread_size":"1*1","part_information":"","standard_unit":"6566","chromatic_degree":"1/1","material_code":"ZDQX0001481_C1"},{"standard_unit_cn":"米","indexs":"ZDQX0001481_C10000011","product_code":"ZDQX0001481","main_class":"卷筒","create_by":"2","is_off_side":0,"update_time":"2025-02-14 18:04:06","parent_code":"ZDQX0001481_C1","mnemonic_code":"J07670470050006","id":950951,"component_count":"1","update_by":"2","material_name":"770 380g 瑞典伊格森德Invercote G白卡","flag_deleted":0,"fixed_amount":"0","product_version":"1.7","create_time":"2025-02-14 18:04:06","dosage_unit":"1","categroy":1,"gram_weight":"380","index":"ZDQX0001481_C10000011","specification":"770","version":"1.0","component_size":"1*1","cut_size":"1*1","pushing":"1*2(1)","direct_material":0,"sub_class":"白卡纸","consumption_rate":"0","part_information":"","standard_unit":"米","slitting":0,"material_code":"1010101010000007"},{"standard_unit_cn":"米","indexs":"ZDQX00014810000001","product_code":"ZDQX0001481","main_class":"卷筒","create_by":"2","is_off_side":0,"update_time":"2025-02-14 18:04:06","parent_code":"ZDQX0001481","mnemonic_code":"J07700220610006","id":950952,"component_count":"1","update_by":"2","material_name":"787 400g 宁波金丽白卡","flag_deleted":0,"fixed_amount":"0","product_version":"1.7","create_time":"2025-02-14 18:04:06","dosage_unit":"1","categroy":1,"gram_weight":"400","index":"ZDQX00014810000001","specification":"787","version":"1.0","component_size":"1*1","cut_size":"1*1","pushing":"1*1(2)","direct_material":0,"sub_class":"白卡纸","consumption_rate":"0","part_information":"","standard_unit":"米","slitting":0,"material_code":"1010101010000010"},{"consume_unit":"6588","standard_unit_cn":"kg","indexs":"ZDQX00014810000004","product_code":"ZDQX0001481","main_class":"油墨类","dosage_unit_unit_cn":"kg/万","create_by":"2","update_time":"2025-02-14 18:04:06","parent_code":"ZDQX0001481","mnemonic_code":"010100070","id":950953,"component_count":"1","update_by":"2","material_name":"新金冠 蓝（高光）","flag_deleted":0,"fixed_amount":"0","product_version":"1.7","create_time":"2025-02-14 18:04:06","dosage_unit":"0.000000","categroy":2,"consume_unit_cn":"kg","index":"ZDQX00014810000004","specification":"200kg/桶","version":"1.0","component_size":"200kg/桶","consume_round_up":0,"direct_material":0,"sub_class":"普通油墨","consumption_rate":"0","part_information":"","standard_unit":"kg","material_code":"1010201010000008"},{"consume_unit":"6588","standard_unit_cn":"kg","indexs":"ZDQX0001481_C10000014","product_code":"ZDQX0001481","main_class":"油墨类","dosage_unit_unit_cn":"kg/万","create_by":"2","update_time":"2025-02-14 18:04:06","parent_code":"ZDQX0001481_C1","mnemonic_code":"010100070","id":950954,"component_count":"1","update_by":"2","material_name":"新金冠 蓝（高光）","flag_deleted":0,"fixed_amount":"0","product_version":"1.7","create_time":"2025-02-14 18:04:06","dosage_unit":"0.000000","categroy":2,"consume_unit_cn":"kg","index":"ZDQX0001481_C10000014","specification":"200kg/桶","version":"1.0","component_size":"200kg/桶","consume_round_up":0,"direct_material":0,"sub_class":"普通油墨","consumption_rate":"0","part_information":"","standard_unit":"kg","material_code":"1010201010000008"},{"packing_unit":"6577","product_count":"1","product_code":"ZDQX0001481","create_by":"2","update_time":"2025-02-14 18:04:06","pack_other_texture":"","parent_code":"","id":950955,"pack_other_sizes":"","component_count":"1","pack_paste_method":"","update_by":"2","material_name":"纸箱","flag_deleted":0,"rest":"尺寸：,背景颜色：,材质：,粘贴方法：","product_version":"1.7","packing_unit_cn":"套","create_time":"2025-02-14 18:04:06","categroy":4,"weight":"1.0000","version":"1.0","pack_background_color":"","unit_weight":1,"material_code":"ZDQX0001481Pk_01"}]}
var entity={"material_code":"ZDQX0001481","material_name":"测试色次","mnemonic_code":"","product_version":"1.7","bom_type":"成本BOM","main_class":"折叠纸盒类","sub_class":"医疗器械类","product_size":"1.0*1.0*1.0","spread_size":"1*1","procure_unit":"mm","product_weight":1,"material_weight":"","is_support_rohs":false,"is_group_product":false,"is_support_fsc":false,"fsc_statement":"","remark":"","update_content":"","default_factory":"27435","standard_unit":"6591"}
var crm_price_manager=[{"selling_price":"3.4741","material_code":"1010101010000007"},{"selling_price":"3.9912","material_code":"1010101010000010"},{"selling_price":"40.885","material_code":"1010201010000009"},{"selling_price":"47.3892","material_code":"1010201010000010"},{"selling_price":"47.3894","material_code":"1010201010000008"},{"selling_price":"44.8849","material_code":"1010201010000006"}]
var quotation_code="HJ252000134"
var new_version =13
var materialResult = JSON.parse(JSON.stringify(obj.materialResult))
var paperlArr = []
var craftArr = JSON.parse(JSON.stringify(obj.craftArr)) // 传进来的参数工艺路线
var inCarftArr = [] //需要存核价单的工艺路线
var bjArr = []
// 获取所有parent_code及其对应最大出本值的映射关系
function getAllParentCodeOutOfBookMap(craftArr) {
    var parentCodeMap = {};
    
    // 首先收集所有不同的parent_code
    craftArr.forEach(function(item) {
        if (item.parent_code && !parentCodeMap[item.parent_code]) {
            parentCodeMap[item.parent_code] = 0;
        }
    });
    
    // 遍历每个parent_code，获取其最大出本值
    craftArr.forEach(function(item) {
        if (item.parent_code) {
            // 检查ban_json_code中的出本值
            if (item.ban_json_code) {
                var banInfos = item.ban_json_code.split(',');
                
                banInfos.forEach(function(banInfo) {
                    var outOfBookStart = banInfo.indexOf('出本：') + 3;
                    if (outOfBookStart > 2) {
                        var outOfBookEnd = banInfo.indexOf(',', outOfBookStart);
                        var outOfBookStr = outOfBookEnd > outOfBookStart ? 
                            banInfo.substring(outOfBookStart, outOfBookEnd) : 
                            banInfo.substring(outOfBookStart);
                        var outOfBook = parseInt(outOfBookStr);
                        if (!isNaN(outOfBook)) {
                            parentCodeMap[item.parent_code] = Math.max(
                                parentCodeMap[item.parent_code], 
                                outOfBook
                            );
                        }
                    }
                });
            }
            
            // 同时检查item自身的out_of_book值
            if (item.out_of_book) {
                var outOfBook = parseInt(item.out_of_book);
                if (!isNaN(outOfBook)) {
                    parentCodeMap[item.parent_code] = Math.max(
                        parentCodeMap[item.parent_code], 
                        outOfBook
                    );
                }
            }
        }
    });
    
    return parentCodeMap;
}
// 调用函数并打印结果
var parentCodeMap = getAllParentCodeOutOfBookMap(obj.craftArr);

//处理纸张成本
var parent_code = ""
for (var x = 0; x < materialResult.length; x++) {
  if (materialResult[x].categroy == 1) {
    //处理纸张物价
    for (var y = 0; y < crm_price_manager.length; y++) {
      if (materialResult[x].material_code == crm_price_manager[y].material_code) {
        materialResult[x]['selling_price'] = (crm_price_manager[y].selling_price*1).toFixed(4)
        break
      }
    }
    if (!materialResult[x].selling_price) {
      materialResult[x]['selling_price'] = 0
    }
    //处理分切尺寸
    if (materialResult[x].cut_size) {
    var cutArr = materialResult[x].cut_size.split("*")
    materialResult[x]['fqcc'] = cutArr[0]*1*cutArr[1]*1
  }else{
    materialResult[x]['fqcc'] = 0
  }
    //处理开数
    if (materialResult[x].pushing) {
      var pushing = materialResult[x].pushing
      var kaishu = null
      if (pushing.indexOf(",") != -1) {
        var arr = pushing.split(",")
        for (var y = 0; y < arr.length; y++) {
          var index = arr[y].indexOf("(")
          var index2 = arr[y].indexOf(")")
           kaishu += arr[y].substring(index+1,index2)*1
        }
      }else{
          var index = pushing.indexOf("(")
          var index2 = pushing.indexOf(")")
          kaishu = pushing.substring(index+1,index2)*1
      }
      materialResult[x]['kaishu'] = kaishu
    }else{
      materialResult[x]['kaishu'] = 1
    }
    if (materialResult[x].parent_code != parent_code) {
      paperlArr.push(materialResult[x])
    }
    parent_code = materialResult[x].parent_code
  }
  if (materialResult[x].categroy == 3 || materialResult[x].categroy == 0 || materialResult[x].categroy == 7) {
    bjArr.push(materialResult[x])
  }
} 
var first_out_of_book = 0
var max_out_of_book = 0
var multi_product = {};
for (var i = 0; i < craftArr.length; i++) {
  if(craftArr[i].sub_code){
    if (craftArr[i].process_type == "1") {
      if(!multi_product[craftArr[i].sub_code]){
        multi_product[craftArr[i].sub_code]=0
      }
    if (craftArr[i].out_of_book) {
      if (Number(craftArr[i].out_of_book) > multi_product[craftArr[i].sub_code]) {
        // multi_product[craftArr[i].sub_code] = Number(craftArr[i].out_of_book)
        multi_product[craftArr[i].sub_code] = Number(parentCodeMap[craftArr[i].parent_code])
      }
    }
  }
  }else{
    if (craftArr[i].process_type == "1") {
    if (craftArr[i].out_of_book) {
      if (Number(craftArr[i].out_of_book) > max_out_of_book) {
        max_out_of_book = Number(craftArr[i].out_of_book)
      }
    }
  }
  }
  
}
for (var x = 0; x < craftArr.length ; x++) {
  var obj = {}
  obj['parent_code'] = craftArr[x].parent_code  //parent_code
  obj['categroy'] = "10"  //categroy
  obj['craft_name'] = craftArr[x].craft_name  //工艺 
  obj['resource'] = craftArr[x].work_code  //资源
  obj['work_unit'] = craftArr[x].operation_unit  //作业单位
  obj['minimum_operation_cost'] = craftArr[x].minimum_consumption  //工序最低消耗
  obj['fixed_difficulty_cost'] = craftArr[x].fixed_consumption  //工序作业难度消耗
  obj['total_fixed_cost'] = obj['minimum_operation_cost']*1 + obj['fixed_difficulty_cost']*1  //总固定消耗
  if (x == 0) {
    first_out_of_book = craftArr[x].out_of_book //出本
  }
  if (craftArr[x].process_type == "1") {   //大张工序
    if(craftArr[x].sub_code){
       //obj['operation_joint_number'] = multi_product[craftArr[x].sub_code];
       obj['operation_joint_number'] = Number(parentCodeMap[craftArr[x].parent_code]) //出本
    }else{
    //   obj['operation_joint_number'] = max_out_of_book //出本
       obj['operation_joint_number'] = Number(parentCodeMap[craftArr[x].parent_code]) //出本
    }
    for (var y = 0; y < paperlArr.length; y++) {
      if (craftArr[x].parent_code == paperlArr[y].parent_code) {
        if(paperlArr[y].main_class == "卷筒"){
          obj['folded_paper_quantity'] = ((Number(paperlArr[y].fqcc)/1000000)*Number(paperlArr[y].gram_weight)/1000)/Number(paperlArr[y].kaishu) * Number(obj.total_fixed_cost)
        }else if (paperlArr[y].main_class == "平张") {
          obj['folded_paper_quantity'] = Number(obj.total_fixed_cost)/Number(paperlArr[y].kaishu)
        }
        obj['folded_paper_quantity'] = obj['folded_paper_quantity'].toFixed(11)
        obj['material_price'] = (paperlArr[y].selling_price*1).toFixed(4)
        obj['paper_cutting_number'] = paperlArr[y].kaishu
        break
      } 
    }
  }else{ 
    // obj['operation_joint_number'] = craftArr[x].out_of_book //小张工序
    obj['operation_joint_number'] = Number(parentCodeMap[craftArr[x].parent_code]) //小张工序
    if(craftArr[x].sub_code){
       max_out_of_book = multi_product[craftArr[x].sub_code];
    }
    for (var y = 0; y < paperlArr.length; y++) {
      if (craftArr[x].parent_code == paperlArr[y].parent_code) {
        if(paperlArr[y].main_class == "卷筒"){
          obj['folded_paper_quantity'] = ((Number(paperlArr[y].fqcc)/1000000) *Number(paperlArr[y].gram_weight)/1000)/Number(paperlArr[y].kaishu)/Number(parentCodeMap[craftArr[x].parent_code]) * Number(obj.total_fixed_cost)
          //obj['folded_paper_quantity'] = ((paperlArr[y].fqcc/1000000) *paperlArr[y].gram_weight/1000)/paperlArr[y].kaishu/first_out_of_book * obj.total_fixed_cost
        }else if (paperlArr[y].main_class == "平张") {
          obj['folded_paper_quantity'] = Number(obj.total_fixed_cost)/Number(paperlArr[y].kaishu)/Number(parentCodeMap[craftArr[x].parent_code])
          //obj['folded_paper_quantity'] = obj.total_fixed_cost/paperlArr[y].kaishu/first_out_of_book
        }
        obj['folded_paper_quantity'] = obj['folded_paper_quantity'].toFixed(11)
        obj['material_price'] = (paperlArr[y].selling_price*1).toFixed(4)
         obj['paper_cutting_number'] = paperlArr[y].kaishu
        break
      }
    }
  }
  obj['amount'] = (obj.folded_paper_quantity * 1) * (obj.material_price*1)
  obj['amount'] = (obj.amount*1).toFixed(12)
  obj['process_type'] = craftArr[x].process_type
  inCarftArr.push(obj)
}
var inArr = []
inArr = inArr.concat(paperlArr)
inArr = inArr.concat(inCarftArr)
  inCarftArr.push(obj)
inArr = inArr.concat(bjArr) 
for(var k=0;k<inArr.length;k++){
  inArr[k].quotation_code = quotation_code;
  inArr[k].new_version = new_version;
  inArr[k].version = "1.0";
}
console.log(JSON.stringify(inArr))
const fs = require('fs');
fs.writeFileSync('./inArr.json', JSON.stringify(inArr, null, 2));
return inArr