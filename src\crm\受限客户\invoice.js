var invoice_arr = [{"invoice_date":"2024-01-01","invoice_money":1000},
    {"invoice_date":"2024-01-02","invoice_money":2000},
    {"invoice_date":"2025-01-20","invoice_money":3000}];
var receive_money = 6000;

// 定义一个数组来存储冲减完成的开票数据的开票日期
var reduced_invoice_dates = "";
var unpaid_days = 0;
var invoice_total = 0;
// 遍历发票数组，对开票金额进行冲减，并记录冲减完成的开票数据的开票日期
for (var i = 0; i < invoice_arr.length; i++) {
    invoice_total = invoice_total + invoice_arr[i].invoice_money;
    if (invoice_total > receive_money) {
        reduced_invoice_dates = invoice_arr[i].invoice_date;
        break;
    } else if (invoice_total === receive_money) {
        if (i < invoice_arr.length - 1) {
            reduced_invoice_dates = invoice_arr[i + 1].invoice_date;
            break;
        } else {
            reduced_invoice_dates = invoice_arr[i].invoice_date;
            break;
        }
    }
}

// 输出冲减完成的开票数据的开票日期
console.log("冲减完成的开票数据的开票日期:", reduced_invoice_dates);
if (reduced_invoice_dates != "") {
    var current_date = new Date();
    var reduced_date = new Date(reduced_invoice_dates);
    var time_difference = current_date - reduced_date;
    var day_difference = time_difference / (1000 * 3600 * 24);
    unpaid_days = Math.floor(day_difference);
}
console.log("未收到款天数:", unpaid_days);
return unpaid_days
