JSON.stringify(list)
var list=[{"craft_name":"纸张分切","create_time":"2024-08-31 14:52:46","standard_cost_price":"230","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:52:46","material_price":"0","rated_speed":"10000","process_name":"常规分切","id":38,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"喷码","create_time":"2024-08-31 14:53:08","standard_cost_price":"195","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:53:08","material_price":"0","rated_speed":"4500","process_name":"一维码喷码","id":40,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"喷码","create_time":"2024-08-31 14:53:24","standard_cost_price":"195","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:53:24","material_price":"0","rated_speed":"4500","process_name":"二维码喷码","id":41,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"正版印刷","create_time":"2024-08-31 14:54:37","standard_cost_price":"900","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:54:37","material_price":"0","rated_speed":"12000","process_name":"普通4色印刷","id":44,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"背版印刷","create_time":"2024-08-31 14:57:12","standard_cost_price":"1000","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:57:12","material_price":"0","rated_speed":"12000","process_name":"普通5色印刷","id":46,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"一遍印刷","create_time":"2024-08-31 14:57:33","standard_cost_price":"1300","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:57:33","material_price":"0","rated_speed":"12000","process_name":"普通6色印刷","id":47,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"二遍印刷","create_time":"2024-08-31 14:57:53","standard_cost_price":"1400","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:57:53","material_price":"0","rated_speed":"12000","process_name":"普通7色印刷","id":49,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"模切","create_time":"2024-08-31 14:58:28","standard_cost_price":"280","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:58:28","material_price":"0","rated_speed":"6000","process_name":"普通模切","id":52,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"覆膜","create_time":"2024-08-31 14:59:24","standard_cost_price":"210","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:59:24","material_price":"0.35","rated_speed":"4500","process_name":"预涂哑膜","id":53,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"烫金","create_time":"2024-08-31 14:59:47","standard_cost_price":"290","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 14:59:47","material_price":"0","rated_speed":"4000","process_name":"普通烫金","id":55,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"击凸","create_time":"2024-08-31 15:00:08","standard_cost_price":"280","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-11-30 17:27:16","material_price":"3","rated_speed":"5000","process_name":"普通击凸","id":56,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"粘盒","create_time":"2024-08-31 15:00:43","standard_cost_price":"350","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 15:00:43","material_price":"0","rated_speed":"70000","process_name":"粘盒-直线盒15开以上","id":57,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"裁切价格","create_time":"2024-08-31 15:01:18","standard_cost_price":"100","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 15:01:18","material_price":"0","rated_speed":"60000","process_name":"原料裁切","id":58,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"人工选剔","create_time":"2024-08-31 15:01:48","standard_cost_price":"120","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-08-31 15:01:48","material_price":"0","rated_speed":"150000","process_name":"说明书人工选剔","id":59,"update_by":"1731954709814677505","flag_deleted":0},{"material_price":"7","rated_speed":"7777","craft_name":"纸张分切7","process_name":"常规分切7","standard_cost_price":"77","id":69,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"分切","process_name":"常规分切","standard_cost_price":"230","id":99,"type":"2","flag_deleted":0},{"rated_speed":"4500","craft_name":"喷码","process_name":"一维码喷码","standard_cost_price":"195","id":100,"type":"2","flag_deleted":0},{"rated_speed":"4500","craft_name":"喷码","process_name":"二维码喷码","standard_cost_price":"195","id":101,"type":"2","flag_deleted":0},{"rated_speed":"4000","craft_name":"喷码","process_name":"一维码+二维码喷码","standard_cost_price":"195","id":102,"type":"2","flag_deleted":0},{"rated_speed":"12000","craft_name":"印刷","process_name":"普通4色印刷","standard_cost_price":"900","id":103,"type":"2","flag_deleted":0},{"rated_speed":"12000","craft_name":"印刷","process_name":"普通5色印刷","standard_cost_price":"1000","id":104,"type":"2","flag_deleted":0},{"rated_speed":"12000","craft_name":"印刷","process_name":"普通6色印刷","standard_cost_price":"1300","id":105,"type":"2","flag_deleted":0},{"rated_speed":"12000","craft_name":"印刷","process_name":"普通7色印刷","standard_cost_price":"1400","id":106,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"印刷","process_name":"UV4色印刷","standard_cost_price":"1500","id":107,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"印刷","process_name":"UV5色印刷","standard_cost_price":"1500","id":108,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"印刷","process_name":"UV6色印刷","standard_cost_price":"1500","id":109,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"印刷","process_name":"UV7色印刷","standard_cost_price":"1500","id":110,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"印刷","process_name":"UV8色印刷","standard_cost_price":"1700","id":111,"type":"2","flag_deleted":0},{"rated_speed":"6000","craft_name":"模切","process_name":"普通模切","standard_cost_price":"280","id":112,"type":"2","flag_deleted":0},{"rated_speed":"5000","craft_name":"模切","process_name":"模击一次","standard_cost_price":"280","id":113,"type":"2","flag_deleted":0},{"rated_speed":"5000","craft_name":"击凸","process_name":"普通击凸","standard_cost_price":"280","id":114,"type":"2","flag_deleted":0},{"rated_speed":"4000","craft_name":"盲文击凸","process_name":"盲文击凸","standard_cost_price":"280","id":115,"type":"2","flag_deleted":0},{"rated_speed":"4000","craft_name":"烫金","process_name":"普通烫金","standard_cost_price":"290","id":116,"type":"2","flag_deleted":0},{"rated_speed":"4000","craft_name":"烫金","process_name":"定位烫","standard_cost_price":"290","id":117,"type":"2","flag_deleted":0},{"rated_speed":"70000","craft_name":"粘盒","process_name":"粘盒-直线盒15开以上","standard_cost_price":"350","id":118,"type":"2","flag_deleted":0},{"rated_speed":"50000","craft_name":"粘盒","process_name":"粘盒-直线盒8-15","standard_cost_price":"350","id":119,"type":"2","flag_deleted":0},{"rated_speed":"30000","craft_name":"粘盒","process_name":"粘盒-直线盒1-8","standard_cost_price":"350","id":120,"type":"2","flag_deleted":0},{"rated_speed":"30000","craft_name":"粘盒","process_name":"粘盒-自封底15开以上","standard_cost_price":"350","id":121,"type":"2","flag_deleted":0},{"rated_speed":"20000","craft_name":"粘盒","process_name":"粘盒-自封底8-15开以上","standard_cost_price":"350","id":122,"type":"2","flag_deleted":0},{"update_time":"2024-11-30 17:28:48","material_price":"5","rated_speed":"10000","craft_name":"粘盒","process_name":"粘盒-自封底1-8","standard_cost_price":"350","id":123,"type":"2","update_by":"1731954709814677505","flag_deleted":0},{"rated_speed":"60000","craft_name":"粘盒","process_name":"粘盒-盒中盒15开以上","standard_cost_price":"350","id":124,"type":"2","flag_deleted":0},{"rated_speed":"30000","craft_name":"粘盒","process_name":"粘盒-盒中盒8-15","standard_cost_price":"350","id":125,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"粘盒","process_name":"粘盒-盒中盒1-8","standard_cost_price":"350","id":126,"type":"2","flag_deleted":0},{"material_price":"0.35","rated_speed":"4500","craft_name":"覆膜","process_name":"预涂哑膜","standard_cost_price":"210","id":127,"type":"2","flag_deleted":0},{"material_price":"0.3","rated_speed":"4500","craft_name":"覆膜","process_name":"预涂光膜","standard_cost_price":"210","id":128,"type":"2","flag_deleted":0},{"material_price":"0.9","rated_speed":"3000","craft_name":"覆膜","process_name":"触感膜","standard_cost_price":"210","id":129,"type":"2","flag_deleted":0},{"material_price":"0.9","rated_speed":"3000","craft_name":"覆膜","process_name":"防刮花膜","standard_cost_price":"210","id":130,"type":"2","flag_deleted":0},{"material_price":"1.4","rated_speed":"3000","craft_name":"覆膜","process_name":"防刮花触感膜","standard_cost_price":"210","id":131,"type":"2","flag_deleted":0},{"rated_speed":"60000","craft_name":"原料裁切","process_name":"原料裁切","standard_cost_price":"100","id":132,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"印刷","process_name":"说明书正背单黑","standard_cost_price":"250","id":133,"type":"2","flag_deleted":0},{"rated_speed":"8571","craft_name":"印刷","process_name":"说明书正背双色","standard_cost_price":"400","id":134,"type":"2","flag_deleted":0},{"rated_speed":"150000","craft_name":"成品裁切","process_name":"成品裁切","standard_cost_price":"100","id":135,"type":"2","flag_deleted":0},{"rated_speed":"150000","craft_name":"说明书人工选剔","process_name":"说明书人工选剔","standard_cost_price":"120","id":136,"type":"2","flag_deleted":0},{"rated_speed":"18495","craft_name":"折页","process_name":"MBO折页1-4","standard_cost_price":"320","id":137,"type":"2","flag_deleted":0},{"rated_speed":"15000","craft_name":"折页","process_name":"MBO折页4-8","standard_cost_price":"320","id":138,"type":"2","flag_deleted":0},{"rated_speed":"10000","craft_name":"折页","process_name":"MBO折页8-10","standard_cost_price":"320","id":139,"type":"2","flag_deleted":0},{"rated_speed":"4000","craft_name":"折页","process_name":"GUK折页","standard_cost_price":"620","id":140,"type":"2","flag_deleted":0},{"craft_name":"喷码","create_time":"2024-11-30 17:22:54","standard_cost_price":"1","type":"2","version":"1.0","create_by":"1731954709814677505","update_time":"2024-11-30 17:22:54","material_price":"1","rated_speed":"2","process_name":"一维喷码","id":141,"update_by":"1731954709814677505","flag_deleted":0},{"craft_name":"纸张分切,喷码,背版印刷","create_time":"2024-12-04 09:35:21","standard_cost_price":"43","type":"2","version":"1.0","create_by":"2","update_time":"2024-12-28 15:45:37","material_price":"34","rated_speed":"43","process_name":"4343","id":142,"update_by":"1821390245527687170","flag_deleted":0}]
var kaipai=3
var zhizhang='常规分切'
var pengma=null
var zbyingshua="普通6色印刷"
var bbyingshua="普通7色印刷"
var fumo="成品裁切"
var tangjin=null
var ji_ao="MBO折页8-10"
var zzcd=50
var zzkf=787
var total = 0
for(var i=0;i<list.length;i++){
  var item = list[i];
  if( zhizhang && item.process_name == zhizhang && item.material_price&& item.material_price !== null && item.material_price !== undefined){
    total = total + Number(item.material_price);
    continue;
  }
  if( pengma && item.process_name == pengma && item.material_price&& item.material_price !== null && item.material_price !== undefined){
    total = total + Number(item.material_price);
    continue;
  }
  if( zbyingshua && item.process_name == zbyingshua && item.material_price&& item.material_price !== null && item.material_price !== undefined){
    total = total + Number(item.material_price);
    continue;
  }
  if( bbyingshua && item.process_name == bbyingshua && item.material_price&& item.material_price !== null && item.material_price !== undefined){
    total = total + Number(item.material_price);
    continue;
  }
  if( fumo && item.process_name == fumo && item.material_price&& item.material_price !== null && item.material_price !== undefined){
    total = total + Number(item.material_price);
    continue;
  }
  if( tangjin && item.process_name == tangjin && item.material_price&& item.material_price !== null && item.material_price !== undefined){
    total = total + Number(item.material_price && item.material_price);
    continue;
  }
  if( ji_ao && item.process_name == ji_ao&& item.material_price !== null && item.material_price !== undefined){
    total = total + Number(item.material_price);
    continue;
  }
}
console.log(total*Number(zzcd)*Number(zzkf)/1000000/kaipai)
return total*Number(zzcd)*Number(zzkf)/1000000/kaipai;