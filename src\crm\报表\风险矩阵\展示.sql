CREATE TABLE dwd_customer_risk_data
(
    id          INT AUTO_INCREMENT PRIMARY KEY,
    cust_code   VARCHAR(50)   COMMENT '客户编码',
    cust_name   VARCHAR(100)  COMMENT '客户名称',
    amount      varchar(100) DEFAULT 0 COMMENT '金额',
    days        varchar(100) DEFAULT 0 COMMENT '天数',
    risk_score  varchar(100) DEFAULT 0 COMMENT '风险评分',
    risk_level  VARCHAR(20) NOT NULL COMMENT '风险等级',
    data_year   varchar(4) NOT NULL comment '数据年份',
    data_month  varchar(2)   NOT NULL COMMENT '数据月份',
    type        varchar(2) comment '类型:1-库存2-欠款3-已发货未开票',
    INDEX idx_cust_code (cust_code),
    INDEX idx_data_year (data_year),
    INDEX idx_data_month (data_month)
) COMMENT ='客户风险数据表';
