var data=[{"standard_unit_cn":"米","indexs":"ZDDJ00037090000003","remark":"11","product_code":"ZDDJ0003709","create_by":"1821396441761148929","is_off_side":false,"update_time":"2025-06-12 10:22:41","parent_code":"ZDDJ0003709","mnemonic_code":"J07620770720006","id":312284,"component_count":"1","update_by":"1821396441761148929","material_name":"875 245g 芬兰Pro FBB Bright白卡","flag_deleted":0,"fixed_amount":"0","product_version":"1.1","create_time":"2025-06-12 10:22:41","dosage_unit":"1","categroy":"纸张","index":"ZDDJ00037090000003","version":"1.0","direct_material":false,"standard_unit":"米","slitting":false,"material_code":"1010101010000002"},{"consume_unit":"卷","standard_unit_cn":"卷","indexs":"ZDDJ00037090000001","remark":"11","product_code":"ZDDJ0003709","dosage_unit_unit_cn":"卷","create_by":"1821396441761148929","update_time":"2025-06-12 10:22:41","parent_code":"ZDDJ0003709","mnemonic_code":"02020021","id":312285,"component_count":"1","update_by":"1821396441761148929","material_name":"泛太克数码打印纸","flag_deleted":0,"product_version":"1.1","create_time":"2025-06-12 10:22:41","dosage_unit_unit":"卷","dosage_unit":"0.000000","categroy":"辅料","consume_unit_cn":"卷","index":"ZDDJ00037090000001","version":"1.0","component_size":"180g-431mm","consume_round_up":false,"direct_material":false,"consumption_rate":"0","standard_unit":"卷","material_code":"1010203990000026"},{"consume_unit":"千枚","standard_unit_cn":"千枚","indexs":"ZDDJ00037090000002","remark":"11","product_code":"ZDDJ0003709","dosage_unit_unit_cn":"千枚","create_by":"1821396441761148929","update_time":"2025-06-12 10:22:41","parent_code":"ZDDJ0003709","mnemonic_code":"030600031","id":312286,"component_count":"1","update_by":"1821396441761148929","material_name":"气血和12粒×3板×3袋纸盒定位标","flag_deleted":0,"product_version":"1.1","create_time":"2025-06-12 10:22:41","dosage_unit_unit":"千枚","dosage_unit":"0.000000","categroy":"辅料","consume_unit_cn":"千枚","index":"ZDDJ00037090000002","version":"1.0","component_size":"28.14千枚/卷/3*40mm","consume_round_up":false,"direct_material":false,"consumption_rate":"0","standard_unit":"千枚","material_code":"1010204060000005"}]
var sourceRecord ={"consume_unit":"千枚","standard_unit_cn":"千枚","indexs":"ZDDJ00037090000002","remark":"11","product_code":"ZDDJ0003709","dosage_unit_unit_cn":"千枚","create_by":"1821396441761148929","update_time":"2025-06-12 10:22:41","parent_code":"ZDDJ0003709","mnemonic_code":"030600031","id":312286,"component_count":"1","update_by":"1821396441761148929","material_name":"气血和12粒×3板×3袋纸盒定位标","flag_deleted":0,"product_version":"1.1","create_time":"2025-06-12 10:22:41","dosage_unit_unit":"千枚","dosage_unit":"0.000000","categroy":"辅料","consume_unit_cn":"千枚","index":"ZDDJ00037090000002","version":"1.0","component_size":"28.14千枚/卷/3*40mm","consume_round_up":false,"direct_material":false,"consumption_rate":"0","standard_unit":"千枚","material_code":"1010204060000005"}
var sourceIndex=2
var targetRecord= {"standard_unit_cn":"米","indexs":"ZDDJ00037090000003","remark":"11","product_code":"ZDDJ0003709","create_by":"1821396441761148929","is_off_side":false,"update_time":"2025-06-12 10:22:41","parent_code":"ZDDJ0003709","mnemonic_code":"J07620770720006","id":312284,"component_count":"1","update_by":"1821396441761148929","material_name":"875 245g 芬兰Pro FBB Bright白卡","flag_deleted":0,"fixed_amount":"0","product_version":"1.1","create_time":"2025-06-12 10:22:41","dosage_unit":"1","categroy":"纸张","index":"ZDDJ00037090000003","version":"1.0","direct_material":false,"standard_unit":"米","slitting":false,"material_code":"1010101010000002"}
var targetIndex=0
// 我在执行拖拽，根据 data 是原始的表格数据
// 被拖拽的行,是sourceRecord,对应索引sourceIndex
// 拖拽到目标行,是targetRecord,对应索引targetIndex
// 帮我移动数据，注意先删除 sourceRecord 会导致索引偏移问题
const moveData = (data, sourceRecord, sourceIndex, targetRecord, targetIndex) => {
    //用新变量防止修改老data
    let newData = [...data];
    // 删除原位置的数据
    newData.splice(sourceIndex, 1);


    // 如果目标索引大于源索引，由于已经删除了一个元素，目标索引需要减一
    if (targetIndex > sourceIndex) {
        targetIndex--;
    }
    // 插入数据到新的位置
    newData.splice(targetIndex, 0, sourceRecord);
    return newData;
};
moveData(data, sourceRecord, sourceIndex, targetRecord, targetIndex);
for(var i=0;i<data.length;i++){
    console.log(data[i].material_code);
}