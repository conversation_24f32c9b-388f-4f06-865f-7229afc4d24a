-- 查询当月的值
select round(sum(amount_exclusive_tax),2) amount_exclusive_tax,
       date_format(now(), '%Y-%m') update_time
from crm_sales_order t1
         join crm_sales_order_product t2 on t1.sales_order_code = t2.sales_order_code and t2.flag_deleted = 0
where status != 8
  and t1.flag_deleted = 0
  and date_format(t1.create_time, '%Y') = date_format(now(), '%Y')
  and date_format(t1.create_time, '%m') <= date_format(now(), '%m');

-- 我想把今年12月的累计值都列出来,没有的显示0
-- 使用递归生成月份序列
WITH RECURSIVE months AS (
    -- 基础查询:从当年1月开始
    SELECT 1 AS month, DATE_FORMAT(NOW(), '%Y') AS year
    UNION ALL 
    -- 递归部分:生成之后的月份,直到12月
    SELECT month + 1, year
    FROM months
    WHERE month < 12
),
-- 计算销售数据
sales_data AS (
    SELECT 
        DATE_FORMAT(t1.create_time, '%Y') AS year,
        DATE_FORMAT(t1.create_time, '%m') AS month,
        ROUND(SUM(amount_exclusive_tax), 2) AS amount_exclusive_tax
    FROM crm_sales_order t1
    JOIN crm_sales_order_product t2 
        ON t1.sales_order_code = t2.sales_order_code 
        AND t2.flag_deleted = 0
    WHERE status != 8
        AND t1.flag_deleted = 0
        AND DATE_FORMAT(t1.create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
    GROUP BY 
        DATE_FORMAT(t1.create_time, '%Y'),
        DATE_FORMAT(t1.create_time, '%m')
)
-- 最终查询:计算每月累计值
SELECT 
    CONCAT(m.year, '-', LPAD(m.month, 2, '0')) year_month,
    COALESCE(
        (SELECT SUM(s2.amount_exclusive_tax) 
         FROM sales_data s2 
         WHERE CAST(s2.month AS SIGNED) <= m.month), 
        0
    ) cumulative_amount,
    DATE_FORMAT(NOW(), '%Y-%m') update_time
FROM months m
ORDER BY m.year, m.month;
