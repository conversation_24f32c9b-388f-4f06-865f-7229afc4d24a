select date_format(create_time, '%Y') year, date_format(create_time, '%m') month, count(*) cust_size
from crm_account_receivable_age csrp
group by date_format(create_time, '%Y'), date_format(create_time, '%m');
-- 获取去年 1月到今年12月的数据,如果缺月份,进行补全,cust_size 为 0

select t1.*,date_format(now(),'%Y-%m') update_time from dwd_crm_sale_inventory_detail t1;
;
drop table dws_crm_sale_inventory_month;
create table dws_crm_sale_inventory_month
(
    id                   int(10) auto_increment
        primary key,
    sale_company         varchar(100)   not null comment '销售公司',
    department_code      varchar(255)   null comment '部门编码',
    department_name      varchar(255)   null comment '部门名称',
    region               varchar(100)   null comment '区域',
    sales_assistant_code varchar(1000)  null comment '销售助理编号',
    sales_assistant_name varchar(1000)  null comment '销售助理名称',
    cust_manager_code    varchar(100)   null comment '负责人编号',
    cust_manager_name    varchar(100)   null comment '负责人名称',
    cust_type            varchar(100)   null comment '客户类别编码',
    cust_type_name       varchar(100)   null comment '客户类别名称',
    cust_code            varchar(100)   null comment '客户编号',
    cust_name            varchar(100)   null comment '客户名称',
    sales_order_code     varchar(100)   null comment '拆分订单号',
    factory_assigned     varchar(100)   null comment '下达工厂',
    sales_order_date     date           null comment '订单日期',
    main_class           varchar(100)   null comment '产品大类',
    sub_class            varchar(100)   null comment '产品小类',
    material_code        varchar(100)   null comment '产品编码',
    material_name        varchar(100)   null comment '产品名称',
    order_quantity       varchar(100)   null comment '订单数量',
    production_order     varchar(100)   null comment '生产单号',
    inventory_lot_num    varchar(100)   null comment '入库批号',
    inventory_date       date           null comment '入库日期',
    inventory_qty        varchar(100)   null comment '在库数量',
    unit_price_exclusive varchar(100)   null comment '未含税单价',
    inventory_cost       decimal(20, 2) null comment '库存成本',
    item_age             varchar(100)   null comment '产品库龄',
    product_version      varchar(50)    null comment '产品版本号',
    remark               varchar(550)   null comment '订单备注',
    update_time          varchar(20)    null comment '更新时间'
)
    comment 'crm库存报表_月度表' engine = InnoDB;

-- 获取今年和去年
select temp1.`year`, temp1.`month`, ifnull(temp2.cust_size, 0) cust_size
from (select `year_month`, `year`, `month` from cockpit.dim_month dm where dm.year in (year(now()), year(now()) - 1)) temp1
         left join (select update_time, count(*) cust_size
                    from cockpit.dws_crm_sale_inventory_month
                    where item_age > 90
                    group by update_time) temp2 on temp1.`year_month` = temp2.update_time
order by year asc,month asc
;


