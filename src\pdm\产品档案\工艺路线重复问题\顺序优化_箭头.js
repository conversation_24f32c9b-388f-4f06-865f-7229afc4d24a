var craftArr=[
    {
      "craft_name": "原料分切",
      "indexs": "1",
      "technological_parameter": "",
      "product_code": "ZDYY0002177",
      "create_by": "1731954709814677505",
      "update_time": "2025-03-14 09:24:26",
      "rate": "0",
      "parent_code": "ZDYY0002177",
      "ban_json_code": "版编号：试制-ZDYY009856-BV1类型：印版类别：CTP版版名称：布洛芬缓释胶囊16粒中盒逆向效果版出本：4",
      "r_craft_code": "D0035",
      "ys_fixed_time": "0",
      "id": 0,
      "update_by": "1731954709814677505",
      "material_name": "编码：1010101010000093 名称：889 350g 山东华夏太阳白卡 index：7857967",
      "source_name": "GP分切中心",
      "r_work_code": "GPFQZX",
      "flag_deleted": 0,
      "ys_standing_time": "0",
      "product_version": "1.2",
      "work_code": "GP分切中心",
      "create_time": "2025-03-14 09:24:26",
      "craft_code": "原料分切",
      "index": "1",
      "material_json_code": "编码：1010101010000093 名称：889 350g 山东华夏太阳白卡 index：7857967",
      "version": "1.0",
      "ys_fixed_rate": "0",
      "out_of_book": "4",
      "fixed_consumption": "0",
      "ys_manual_speed": "0",
      "source_code": "GPFQZX"
    },
    {
      "craft_name": "喷码",
      "indexs": "2",
      "technological_parameter": "",
      "product_code": "ZDYY0002177",
      "create_by": "1731954709814677505",
      "update_time": "2025-03-14 09:24:26",
      "rate": 0,
      "parent_code": "ZDYY0002177",
      "ban_json_code": "版编号：试制-ZDYY009856-BV1类型：印版类别：CTP版版名称：布洛芬缓释胶囊16粒中盒逆向效果版出本：4",
      "r_craft_code": "D0121",
      "ys_fixed_time": 0,
      "id": 1,
      "update_by": "1731954709814677505",
      "material_name": "编码：1010201060000002 名称：S系列黑色LED UV墨水 BLACK(1升/瓶 北京) index：7857446",
      "source_name": "GP可变数据印刷中心",
      "r_work_code": "GPYSKBSJYSZX",
      "flag_deleted": 0,
      "ys_standing_time": 0,
      "product_version": "1.2",
      "work_code": "GP可变数据印刷中心",
      "create_time": "2025-03-14 09:24:26",
      "craft_code": "喷码",
      "index": "2",
      "material_json_code": "编码：1010201060000002 名称：S系列黑色LED UV墨水 BLACK(1升/瓶 北京)",
      "version": "1.0",
      "ys_fixed_rate": 0,
      "out_of_book": "4",
      "fixed_consumption": 0,
      "ys_manual_speed": 0,
      "source_code": "GPYSKBSJYSZX",
      "count": 1
    },
    {
      "craft_name": "印刷",
      "indexs": "3",
      "technological_parameter": "印刷类型:UV印刷,印刷面:正版,咬口尺寸:12,印刷规矩:靠身,跟色标准:印刷颜色专色跟纸质潘通色谱，K按公司标准密度印刷,正版联机上光组油:满版UV油,油棍类型:100线网纹辊,油版材质:橡皮布,印刷条码等级:B级及以上,耐摩擦次数要求:UV印刷800次,备注:1.打样喷码印刷示意，不印刷\"版式示意\"；2.打样过满版UV光油；3.墨斗型油2（tjuv代）用于过防爆底油，放第一组，当组烘干。,",
      "product_code": "ZDYY0002177",
      "create_by": "1731954709814677505",
      "update_time": "2025-03-14 09:24:26",
      "rate": "0",
      "parent_code": "ZDYY0002177",
      "ban_json_code": "版编号：试制-ZDYY009856-BV1类型：印版类别：CTP版版名称：布洛芬缓释胶囊16粒中盒逆向效果版出本：4",
      "r_craft_code": "D0102",
      "ys_fixed_time": "0",
      "id": 2,
      "update_by": "1731954709814677505",
      "material_name": "编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：7857490,编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：7857490,编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：7857490,编码：1010201020000005 名称：UV油墨abilio-21 Process Black 黑(1kg*12罐/箱 深圳深日油墨有限公司) index：7857447,编码：1010201040000015 名称：770-9741-2UV胶印超柔韧底涂(1kg/罐 国产) index：7857973,编码：1010201040000004 名称：UV HJK底油G(1kg/罐 杭华油墨股份有限公司) index：7857974,编码：1010202030000002 名称：杭华UV EMBOSS光油-SURFACE(GZ)面油(25kg/桶 杭华油墨股份有限公司) index：7857975",
      "source_name": "GP七色普通印刷中心",
      "r_work_code": "GPYS7SZX",
      "flag_deleted": 0,
      "ys_standing_time": "0",
      "product_version": "1.2",
      "work_code": "GP七色普通印刷中心",
      "create_time": "2025-03-14 09:24:26",
      "craft_code": "印刷",
      "index": "3",
      "material_json_code": "编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：7857490,编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：7857490,编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：7857490,编码：1010201020000005 名称：UV油墨abilio-21 Process Black 黑(1kg*12罐/箱 深圳深日油墨有限公司) index：7857447,编码：1010201040000015 名称：770-9741-2UV胶印超柔韧底涂(1kg/罐 国产) index：7857973,编码：1010201040000004 名称：UV HJK底油G(1kg/罐 杭华油墨股份有限公司) index：7857974,编码：1010202030000002 名称：杭华UV EMBOSS光油-SURFACE(GZ)面油(25kg/桶 杭华油墨股份有限公司) index：7857975",
      "version": "1.0",
      "ys_fixed_rate": "0.5",
      "out_of_book": "4",
      "fixed_consumption": "0",
      "ys_manual_speed": "0",
      "source_code": "GPYS7SZX"
    },
    {
      "craft_name": "模切",
      "indexs": "4",
      "technological_parameter": "底模类型:压痕条,备注:打样新订激光刀版，刀版号：试制-ZDYY009856-MV1，1只/1K；2.模切4号位；3.模切时请关注压痕线是否有爆色。,",
      "product_code": "ZDYY0002177",
      "create_by": "1731954709814677505",
      "update_time": "2025-03-14 09:24:26",
      "rate": "0",
      "parent_code": "ZDYY0002177",
      "ban_json_code": "版编号：核价-ZDYY009856-MV1类型：模切版类别：模切版版名称：布洛芬缓释胶囊16粒中盒逆向效果版出本：4",
      "r_craft_code": "X0023",
      "ys_fixed_time": "0",
      "id": 3,
      "update_by": "1731954709814677505",
      "material_name": "",
      "source_name": "GP模切中心",
      "r_work_code": "GPZHMQZX",
      "flag_deleted": 0,
      "ys_standing_time": "0",
      "product_version": "1.2",
      "work_code": "GP模切中心",
      "create_time": "2025-03-14 09:24:26",
      "craft_code": "模切",
      "index": "4",
      "material_json_code": "",
      "version": "1.0",
      "ys_fixed_rate": "0",
      "out_of_book": "4",
      "fixed_consumption": "0",
      "ys_manual_speed": "0",
      "source_code": "GPZHMQZX"
    },
    {
      "craft_code": "打纸",
      "process_type": 2,
      "r_craft_code": "X0108",
      "ys_fixed_time": 0,
      "fixed_consumption": 0,
      "rate": 0,
      "ys_fixed_rate": 0,
      "ys_manual_speed": 0,
      "ys_standing_time": 0,
      "ban_id": "",
      "technological_parameter": "",
      "product_code": "",
      "parent_code": "ZDYY0002177",
      "index": "6",
      "id": 6
    },
    {
      "craft_name": "纸盒品检机选剔",
      "indexs": "6",
      "technological_parameter": "",
      "product_code": "ZDYY0002177",
      "create_by": "1731954709814677505",
      "update_time": "2025-03-14 09:24:26",
      "rate": "0",
      "parent_code": "ZDYY0002177",
      "ban_json_code": "",
      "r_craft_code": "X0104",
      "ys_fixed_time": "0",
      "id": 4,
      "update_by": "1731954709814677505",
      "material_name": "",
      "source_name": "GP品检中心",
      "r_work_code": "GPZHPJZX",
      "flag_deleted": 0,
      "ys_standing_time": "0",
      "product_version": "1.2",
      "work_code": "GP品检中心",
      "create_time": "2025-03-14 09:24:26",
      "craft_code": "纸盒品检机选剔",
      "index": "7",
      "material_json_code": "",
      "version": "1.0",
      "ys_fixed_rate": "0.7",
      "out_of_book": "1",
      "fixed_consumption": "0",
      "ys_manual_speed": "0",
      "source_code": "GPZHPJZX"
    },
    {
      "craft_name": "粘盒",
      "indexs": "7",
      "technological_parameter": "盒型:插底粘侧,预折要求:一线160°~170°、三线120°~130°，2、4线成型,上胶方式:轮胶,客户包装方式:手工包装,单箱数量偏差:±3‰,走向长度（mm）:248.50,备注:打样手工粘盒,",
      "product_code": "ZDYY0002177",
      "create_by": "1731954709814677505",
      "update_time": "2025-03-14 09:24:26",
      "rate": "0",
      "parent_code": "ZDYY0002177",
      "ban_json_code": "",
      "r_craft_code": "D0036",
      "ys_fixed_time": "0",
      "id": 5,
      "update_by": "1731954709814677505",
      "material_name": "编码：1010206010000001 名称：冠力糊盒胶MA-768(50kg/桶) index：7857453,编码：10103040000001 名称：塑料袋 index：7857474,编码：1010301010000025 名称：纸箱524223 index：7857979",
      "source_name": "GP品检粘盒中心",
      "r_work_code": "GPZHPJNHZX",
      "flag_deleted": 0,
      "ys_standing_time": "0",
      "product_version": "1.2",
      "work_code": "GP品检粘盒中心",
      "create_time": "2025-03-14 09:24:26",
      "craft_code": "粘盒",
      "index": "ZDYY00021770000001",
      "material_json_code": "编码：1010206010000001 名称：冠力糊盒胶MA-768(50kg/桶) index：7857453,编码：10103040000001 名称：塑料袋 index：7857474,编码：1010301010000025 名称：纸箱524223 index：7857979",
      "version": "1.0",
      "ys_fixed_rate": "2.5",
      "out_of_book": "1",
      "fixed_consumption": "0",
      "ys_manual_speed": "0",
      "source_code": "GPZHPJNHZX"
    }
  ]
  
// 按照parent_code分组并重新排序index
const reorder_craft_index = (craftArr) => {
  // 创建一个对象来存储按parent_code分组的数据
  var grouped_by_parent = {};
  
  // 遍历craftArr，按parent_code分组
  for (var i = 0; i < craftArr.length; i++) {
    var item = craftArr[i];
    var parent_code = item.parent_code || "";
    
    if (!grouped_by_parent[parent_code]) {
      grouped_by_parent[parent_code] = [];
    }
    
    grouped_by_parent[parent_code].push(item);
  }
  
  // 对每个分组进行排序并重新赋值index
  for (var parent_code in grouped_by_parent) {
    if (grouped_by_parent.hasOwnProperty(parent_code)) {
      var group = grouped_by_parent[parent_code];
      
      // 按照index字段的字符串排序
      group.sort((a, b) => {
        var index_a = String(a.index || "");
        var index_b = String(b.index || "");
        return index_a.localeCompare(index_b);
      });
      
      // 重新赋值index，从1开始
      for (var j = 0; j < group.length; j++) {
        group[j].index = String(j + 1);
        // 同时更新indexs字段，如果存在的话
        if (group[j].hasOwnProperty("indexs")) {
          group[j].indexs = String(j + 1);
        }
      }
    }
  }
  
  // 将分组后的数据合并回一个数组
  var result = [];
  for (var parent_code in grouped_by_parent) {
    if (grouped_by_parent.hasOwnProperty(parent_code)) {
      result = result.concat(grouped_by_parent[parent_code]);
    }
  }
  
  return result;
};

// 执行重排序
craftArr = reorder_craft_index(craftArr);

// 输出重排序后的结果
console.log(JSON.stringify(craftArr));
  