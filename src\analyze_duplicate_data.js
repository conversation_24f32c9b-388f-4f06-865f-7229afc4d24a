// 分析Data字段中的重复数据
const dataString = `[[\"2024/25\",\"NM\",1752768000,0,0,0,0,0,0,0,0,\"中蔗9号             \",\"Z09\",0,0,0],[\"2024/25\",\"NM\",1752768000,0,0,0,0,0,0,0,0,\"桂柳05136           \",\"E36\",0,0,0],[\"2024/25\",\"NM\",1752768000,0,0,0,0,0,0,0,0,\"桂糖42号            \",\"G42\",0,0,0],[\"2024/25\",\"NM\",1752768000,0,0,0,0,0,0,0,0,\"桂糖55号            \",\"G55\",0,0,0],[\"2024/25\",\"NM\",1752768000,0,0,0,0,0,0,0,0,\"粤糖93159           \",\"F59\",0,0,0],[\"2024/25\",\"FNR\",1752768000,27825.88932499994,1456.6214830000004,4916.880000000004,252.50000000000003,4434766.864584,774762.2499999999,685916.5043540001,119274.46,\"中糖3号             \",\"T03\",45.72,12810.84,5169.380000000004],[\"2024/25\",\"FNR\",1752768000,27825.88932499994,1456.6214830000004,4916.880000000004,252.50000000000003,4434766.864584,774762.2499999999,685916.5043540001,119274.46,\"中蔗9号             \",\"Z09\",3060.7,195212.72999999998,5169.380000000004],[\"2024/25\",\"FNR\",1752768000,27825.88932499994,1456.6214830000004,4916.880000000004,252.50000000000003,4434766.864584,774762.2499999999,685916.5043540001,119274.46,\"壮糖6号             \",\"Z06\",14.71,1999.34,5169.380000000004],[\"2024/25\",\"FNR\",1752768000,27825.88932499994,1456.6214830000004,4916.880000000004,252.50000000000003,4434766.864584,774762.2499999999,685916.5043540001,119274.46,\"桂柳05136           \",\"E36\",44.29,51545.84,5169.380000000004],[\"2024/25\",\"FNR\",1752768000,27825.88932499994,1456.6214830000004,4916.880000000004,252.50000000000003,4434766.864584,774762.2499999999,685916.5043540001,119274.46,\"桂柳07150           \",\"E50\",149.42,32616.940000000002,5169.380000000004],[\"2024/25\",\"FNR\",1752768000,27825.88932499994,1456.6214830000004,4916.880000000004,252.50000000000003,4434766.864584,774762.2499999999,685916.5043540001,119274.46,\"桂糖42号            \",\"G42\",1434.7199999999996,463200.73,5169.380000000004],[\"2024/25\",\"FNR\",1752768000,27825.88932499994,1456.6214830000004,4916.880000000004,252.50000000000003,4434766.864584,774762.2499999999,685916.5043540001,119274.46,\"桂糖44号            \",\"G44\",249.90999999999997,29283.93,5169.380000000004],[\"2024/25\",\"FNR\",1752768000,27825.88932499994,1456.6214830000004,4916.880000000004,252.50000000000003,4434766.864584,774762.2499999999,685916.5043540001,119274.46,\"桂糖55号            \",\"G55\",169.91,29609.59,5169.380000000004],[\"2024/25\",\"CZ\",1752768000,7801.495165999997,78.780884,1427.510000000001,14.07,14498791.599012006,2612883.4899999993,167261.266496,30060.350000000002,\"桂柳05136           \",\"E36\",71.83,744558.55,1441.5800000000008],[\"2024/25\",\"CZ\",1752768000,7801.495165999997,78.780884,1427.510000000001,14.07,14498791.599012006,2612883.4899999993,167261.266496,30060.350000000002,\"桂糖42号            \",\"G42\",1139.1000000000006,1818499.2899999993,1441.5800000000008],[\"2024/25\",\"CZ\",1752768000,7801.495165999997,78.780884,1427.510000000001,14.07,14498791.599012006,2612883.4899999993,167261.266496,30060.350000000002,\"桂糖44号            \",\"G44\",30.5,10616.74,1441.5800000000008],[\"2024/25\",\"CZ\",1752768000,7801.495165999997,78.780884,1427.510000000001,14.07,14498791.599012006,2612883.4899999993,167261.266496,30060.350000000002,\"桂糖49号            \",\"G49\",57.98,14881.45,1441.5800000000008],[\"2024/25\",\"CZ\",1752768000,7801.495165999997,78.780884,1427.510000000001,14.07,14498791.599012006,2612883.4899999993,167261.266496,30060.350000000002,\"桂糖55号            \",\"G55\",98.38999999999999,42373.77,1441.5800000000008],[\"2024/25\",\"CZ\",1752768000,7801.495165999997,78.780884,1427.510000000001,14.07,14498791.599012006,2612883.4899999993,167261.266496,30060.350000000002,\"粤糖93159           \",\"F59\",43.78,3167.99,1441.5800000000008],[\"2024/25\",\"NMR\",1752768000,4355.406279000001,0,790.7499999999999,0,6455218.529345999,1175027.2899999996,13124.979578,2366.89,\"中蔗9号             \",\"Z09\",118.05,1016.5799999999999,790.7499999999999],[\"2024/25\",\"NMR\",1752768000,4355.406279000001,0,790.7499999999999,0,6455218.529345999,1175027.2899999996,13124.979578,2366.89,\"桂糖42号            \",\"G42\",657.7599999999999,1032085.4699999997,790.7499999999999],[\"2024/25\",\"NMR\",1752768000,4355.406279000001,0,790.7499999999999,0,6455218.529345999,1175027.2899999996,13124.979578,2366.89,\"桂糖55号            \",\"G55\",14.94,22727.63,790.7499999999999],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"中糖3号             \",\"T03\",84.28,5396.11,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"中蔗9号             \",\"Z09\",1457.7000000000014,334037.17,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"壮糖6号             \",\"Z06\",61.36,3859.96,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"新台糖22号          \",\"C22\",95.11,76549.08,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"桂柳05136           \",\"E36\",0,80164.57,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"桂柳07150           \",\"E50\",438.0200000000001,50140.93,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"桂糖42号            \",\"G42\",401.57,825700.4200000002,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"桂糖44号            \",\"G44\",262.32000000000005,25108.2,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"桂糖46号            \",\"G46\",230.98,4628.88,3625.88],[\"2024/25\",\"FN\",1752768000,17088.225334000006,3430.220918,3020.0900000000033,605.7900000000003,8392850.296814999,1464168.73,78762.96948999999,13821.380000000001,\"桂糖55号            \",\"G55\",594.54,33063.54,3625.88],[\"2024/25\",\"HT\",1752768000,24581.924053000024,0,4446.920000000003,0,7341209.989712,1282751.4499999997,31382.641592,5452.75,\"桂柳05136           \",\"E36\",184.91,52346.47,4446.920000000003],[\"2024/25\",\"HT\",1752768000,24581.924053000024,0,4446.920000000003,0,7341209.989712,1282751.4499999997,31382.641592,5452.75,\"桂柳07150           \",\"E50\",29.46,2891.38,4446.920000000003],[\"2024/25\",\"HT\",1752768000,24581.924053000024,0,4446.920000000003,0,7341209.989712,1282751.4499999997,31382.641592,5452.75,\"桂糖42号            \",\"G42\",4109.1100000000015,1208335.66,4446.920000000003],[\"2024/25\",\"HT\",1752768000,24581.924053000024,0,4446.920000000003,0,7341209.989712,1282751.4499999997,31382.641592,5452.75,\"桂糖55号            \",\"G55\",123.44,20361.84,4446.920000000003],[\"2024/25\",\"TL\",1752768000,30791.77171300003,678.34288,5519.590000000005,121.60000000000001,17081736.959422998,3064896.450000002,265309.58873499994,47692.03,\"桂柳05136           \",\"E36\",45.86,42861.41,5641.190000000003],[\"2024/25\",\"TL\",1752768000,30791.77171300003,678.34288,5519.590000000005,121.60000000000001,17081736.959422998,3064896.450000002,265309.58873499994,47692.03,\"桂糖42号            \",\"G42\",5085.150000000001,2946291.700000001,5641.190000000003],[\"2024/25\",\"TL\",1752768000,30791.77171300003,678.34288,5519.590000000005,121.60000000000001,17081736.959422998,3064896.450000002,265309.58873499994,47692.03,\"桂糖44号            \",\"G44\",15.53,10282.88,5641.190000000003],[\"2024/25\",\"TL\",1752768000,30791.77171300003,678.34288,5519.590000000005,121.60000000000001,17081736.959422998,3064896.450000002,265309.58873499994,47692.03,\"桂糖49号            \",\"G49\",30.33,7611.5199999999995,5641.190000000003],[\"2024/25\",\"TL\",1752768000,30791.77171300003,678.34288,5519.590000000005,121.60000000000001,17081736.959422998,3064896.450000002,265309.58873499994,47692.03,\"桂糖55号            \",\"G55\",417.67,98498.94,5641.190000000003],[\"2024/25\",\"TL\",1752768000,30791.77171300003,678.34288,5519.590000000005,121.60000000000001,17081736.959422998,3064896.450000002,265309.58873499994,47692.03,\"粤糖93159           \",\"F59\",46.65,908.29,5641.190000000003]]`;

// 解析数据
const data = JSON.parse(dataString);

console.log('总数据条数:', data.length);
console.log('');

// 分析重复数据的不同维度
function analyzeData() {
    // 1. 完全重复的行
    const rowMap = new Map();
    const duplicateRows = [];
    
    data.forEach((row, index) => {
        const rowKey = JSON.stringify(row);
        if (rowMap.has(rowKey)) {
            duplicateRows.push({
                index1: rowMap.get(rowKey),
                index2: index,
                data: row
            });
        } else {
            rowMap.set(rowKey, index);
        }
    });
    
    console.log('=== 完全重复的行分析 ===');
    if (duplicateRows.length === 0) {
        console.log('没有发现完全重复的行');
    } else {
        console.log(`发现 ${duplicateRows.length} 组完全重复的行:`);
        duplicateRows.forEach((dup, i) => {
            console.log(`重复组 ${i + 1}: 行 ${dup.index1} 和行 ${dup.index2}`);
            console.log('数据:', dup.data);
        });
    }
    console.log('');
    
    // 2. 按关键字段组合分析重复（榨季+工厂代码+甘蔗品种名称+甘蔗品种代码）
    const keyMap = new Map();
    const keyDuplicates = [];
    
    data.forEach((row, index) => {
        const key = `${row[0]}-${row[1]}-${row[11]}-${row[12]}`;  // 榨季-工厂代码-甘蔗品种名称-甘蔗品种代码
        if (keyMap.has(key)) {
            keyDuplicates.push({
                key: key,
                indexes: [...keyMap.get(key), index],
                data: row
            });
            keyMap.set(key, [...keyMap.get(key), index]);
        } else {
            keyMap.set(key, [index]);
        }
    });
    
    console.log('=== 关键字段组合重复分析 ===');
    console.log('(榨季+工厂代码+甘蔗品种名称+甘蔗品种代码)');
    const actualDuplicates = Array.from(keyMap.entries()).filter(([key, indexes]) => indexes.length > 1);
    
    if (actualDuplicates.length === 0) {
        console.log('没有发现关键字段组合重复的数据');
    } else {
        console.log(`发现 ${actualDuplicates.length} 组关键字段重复的数据:`);
        actualDuplicates.forEach(([key, indexes]) => {
            console.log(`重复键: ${key}`);
            console.log(`出现在行: ${indexes.join(', ')}`);
            console.log(`重复次数: ${indexes.length}`);
            console.log('数据示例:', data[indexes[0]]);
            console.log('---');
        });
    }
    console.log('');
    
    // 3. 按工厂代码统计
    const factoryStats = new Map();
    data.forEach(row => {
        const factory = row[1];
        factoryStats.set(factory, (factoryStats.get(factory) || 0) + 1);
    });
    
    console.log('=== 按工厂代码统计 ===');
    Array.from(factoryStats.entries()).sort((a, b) => b[1] - a[1]).forEach(([factory, count]) => {
        console.log(`${factory}: ${count} 条记录`);
    });
    console.log('');
    
    // 4. 按甘蔗品种统计
    const varietyStats = new Map();
    data.forEach(row => {
        const variety = row[11].trim();  // 甘蔗品种名称
        varietyStats.set(variety, (varietyStats.get(variety) || 0) + 1);
    });
    
    console.log('=== 按甘蔗品种统计 ===');
    Array.from(varietyStats.entries()).sort((a, b) => b[1] - a[1]).forEach(([variety, count]) => {
        console.log(`${variety}: ${count} 条记录`);
    });
    console.log('');
    
    // 5. 数据结构分析
    console.log('=== 数据结构分析 ===');
    console.log('每行数据包含字段数:', data[0].length);
    console.log('数据字段示例 (第一行):');
    data[0].forEach((field, index) => {
        console.log(`字段 ${index}: ${field} (${typeof field})`);
    });
}

analyzeData();
