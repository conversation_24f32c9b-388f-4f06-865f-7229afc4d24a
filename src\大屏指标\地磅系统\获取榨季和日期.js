// 获取当前日期
function get_current_date() {
    var date = new Date();
    var year = date.getFullYear();
    var month = (date.getMonth() + 1).toString();
    month = month.length === 1 ? '0' + month : month;
    var day = date.getDate().toString();
    day = day.length === 1 ? '0' + day : day;
    return year + '-' + month + '-' + day;
}

// 获取榨季
function get_crushing_season(date_str) {
    var date = new Date(date_str);
    var year = date.getFullYear();
    var month = date.getMonth() + 1; // 月份从0开始，需要+1
    
    // 如果月份大于等于11月，榨季年份为当年/次年
    // 如果月份小于11月，榨季年份为前一年/当年
    var season_start_year = month >= 11 ? year : year - 1;
    var season_str = season_start_year.toString().slice(-2) + '/' + (season_start_year + 1).toString().slice(-2);
    
    return season_str;
}

var current_date = get_current_date();
var crushing_season = get_crushing_season('2025-03-11');
console.log('当前日期:', current_date);
console.log('所属榨季:', crushing_season);
console.log(current_date);

