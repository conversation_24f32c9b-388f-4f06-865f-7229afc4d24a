-- auto-generated definition
create table crm_sales_invoice_details
(
    user_code       varchar(255)  null comment '开票人编码',
    user_name       varchar(255)  null comment '开票人名称',
    dbilldate       varchar(255)  null comment '开票日期',
    fstatusflag     char          null comment '发票状态',
    norigtaxmny     varchar(255)  null comment '税价总计',
    ntax            double(20, 2) null comment '税额',
    ntaxrate        double(20, 2) null comment '税率',
    norigmny        double(22, 4) null comment '开票金额',
    norignetprice   double(22, 4) null comment '开票单价',
    nnum            int           null comment '开票数量',
    vdef20          varchar(255)  null comment '发票编号',
    name            varchar(255)  null comment '产品名称',
    materialmnecode varchar(255)  null comment '助记码',
    code            varchar(255)  null comment '产品编码',
    csaleorderbid   varchar(255)  null comment '销售订单行',
    id              int auto_increment comment '自增主键'
        primary key,
    flag_deleted    int default 0 null comment '是否删除',
    version         varchar(20)   null comment '版本',
    create_by       varchar(50)   null comment '创建人',
    create_time     datetime      null comment '创建时间',
    update_by       varchar(50)   null comment '修改人',
    update_time     datetime      null comment '修改时间',
    domain_id       varchar(50)   null comment '域id',
    csaleorderid    varchar(255)  null comment 'CRM拆分订单编号',
    handler_status  char          null comment '处理状态0未处理1已处理',
    csaleinvoicebid varchar(32)   null comment 'bip销售发票行主键',
    csaleinvoiceid  varchar(32)   null comment 'bip销售发票主表主键',
    dsfbtzj         varchar(32)   null comment 'CRM开票申请ID',
    dsfbtzj_b       varchar(32)   null comment 'CRM开票申请行ID'
)
    comment '销售开票明细' charset = utf8mb4
                           row_format = DYNAMIC;

