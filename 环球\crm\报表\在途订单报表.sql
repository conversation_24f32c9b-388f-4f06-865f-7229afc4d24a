-- 在途订单报表
select
       distinct
       '西安环球' as                                                                           sale_company,
       t3.deparment_code,
       t3.deparment_name,
       t3.department_region                                                                    region,
       t3.sales_assistant_code,
       t3.sales_assistant_name,
       t3.cust_manager_code,
       t3.cust_manager_name,
       t3.cust_type,
       CASE
           WHEN t3.cust_type = 0 THEN '新建客户'
           WHEN t3.cust_type = 1 THEN '公海客户'
           WHEN t3.cust_type = 2 THEN '合作客户'
           WHEN t3.cust_type = 3 THEN '开发中客户'
           WHEN t3.cust_type = 4 THEN '受限客户'
           END                                      cust_type_name,
       t3.cust_code,
       t3.cust_name,
       t1.contract_management_code,
       date_format(t1.create_time, '%Y-%m-%d') order_date,
       t1.factory_assigned,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       t2.product_quantity,
       ifnull(t4.inventory_quantity, 0)                                                        inventory_quantity,
       ifnull(t5.shipped_quantity, 0)                                                          shipped_quantity,
       t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0) transit_quantity,
       round(t2.unit_price_exclusive, 6)                                                       unit_price_exclusive,
       round(t2.amount_exclusive_tax, 2)                                                       amount_exclusive_tax,
       round((t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0)) *
             t2.unit_price_exclusive, 2)                                                       transit_amount,
       t1.status,
       case
           when t1.status=10 then '已删除'
           when t1.status=8 then '已取消'
           when t1.status=7 then '已关闭'
           when t1.status=6 then '已开票'
           when (select count(*)
                 from h3chq_crmbusiness1704287359505.invoice_application ia
                 where ia.status IN ('0', '3', '4')
                   and ia.flag_deleted = 0
                   and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
               then '已发货未开票'
           when t1.status=0 then '已拆分'
           when t1.status=1 then '已下达'
           when t1.status=2 then '已排产'
           when t1.status=3 then '已领料'
           when t1.status=4 then '生产中'
           when t1.status=5 then '已入库'
           else '其他'
           end status_desc,
          t1.delivery_date
from h3chq_crmbusiness1704287359505.crm_sales_order  t1
         left join h3chq_crmbusiness1704287359505.crm_sales_order_product  t2
                   on t2.sales_order_code = t1.sales_order_code
         left join h3chq_crmbusiness1704287359505.crm_cust_basic t3 on t3.cust_code = t1.cust_code
         left join (select contract_management_code,
                           contract_product_line_number,
                           sum(nonhandnum) inventory_quantity
                    from (select t1.nonhandnum,
                                 t5.contract_management_code,
                                 t6.contract_product_line_number,
                                 t5.sales_order_code
                          from h3chq_crmbusiness1704287359505.ods_bip_inventory t1
                                   left join hq_pm_b.pm_job_order t2
                                             on t1.vbatchcode = t2.production_batch_number COLLATE utf8mb4_general_ci
                                                 and t1.materialcode = t2.material_code COLLATE utf8mb4_general_ci
                                                 and t2.flag_deleted = 0
                                   left join hq_pm_b.pm_sale_order t3
                                             on t3.order_number = t2.order_code and t3.flag_deleted = 0
                                   left join hq_pm_b.pm_order_product t4 on t4.sales_number = t2.order_code
                              and t4.id = t2.source_detail_id
                              and t4.flag_deleted = 0
                                   left join h3chq_crmbusiness1704287359505.crm_sales_order t5
                                             on t3.erp_production_order = t5.sales_order_code
                                                 and t5.csaleorderid = t3.bip_main_no
                                                 and t5.flag_deleted = 0
                                   left join h3chq_crmbusiness1704287359505.crm_sales_order_product t6
                                             on t6.id = t4.crm_order_num
                                                 and t3.erp_production_order = t6.sales_order_code
                                                 and t6.flag_deleted = 0
                          where t1.flag_deleted = 0
                            and t4.crm_order_num is not null
                            and t5.contract_management_code is not null) temp
                    group by temp.contract_management_code, temp.contract_product_line_number,temp.sales_order_code) t4
                   on t4.contract_management_code = t1.contract_management_code and
                      t4.contract_product_line_number = t2.contract_product_line_number
         left join (select t1.contract_management_code,
                           t1.contract_product_line_number,
                           t1.sales_order_code,
                           sum(ifnull(t2.ship_quantity, 0)) shipped_quantity,
                           sum(ifnull(t3.nnum, 0))          invoiced_quantity
                    from h3chq_crmbusiness1704287359505.crm_sales_order_product t1
                             join h3chq_crmbusiness1704287359505.bip_outbound_order_detail t2 on t1.sales_order_code = t2.split_order_no
                        and t1.material_code = t2.product_code
                        and t1.contract_product_line_number = t2.split_order_line_no
                        and t2.flag_deleted = 0
                             left join (select csrcid, csrcbid, sum(nnum) nnum
                                        from h3chq_crmbusiness1704287359505.crm_sales_invoice_details
                                        where flag_deleted = 0
                                        group by csrcid, csrcbid) t3
                                       on t2.outbound_header = t3.csrcid and t2.outbound_line_id = t3.csrcbid
                    where t1.flag_deleted = 0
                    group by t1.contract_management_code, t1.contract_product_line_number,t1.sales_order_code) t5
                   on t5.contract_management_code = t1.contract_management_code and
                      t5.contract_product_line_number = t2.contract_product_line_number and
                      t5.sales_order_code=t1.sales_order_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and t3.flag_deleted = 0
  and t3.cust_status = 2
  and t3.cust_type not in (0, 1)
  and t1.status !=8
  and t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0)>=0
  and CASE
        WHEN quantity_fluctuation LIKE '+%' THEN
            IF(t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0) <= product_quantity * ( CONVERT(SUBSTRING(quantity_fluctuation, 2), DECIMAL(10,2))/100)
                   AND t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0) >= 0, 'YES', 'NO')
        WHEN quantity_fluctuation LIKE '-%' THEN
            IF(t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0) >= product_quantity * (0- CONVERT(SUBSTRING(quantity_fluctuation, 2), DECIMAL(10,2))/100)
                   AND t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0) <= 0, 'YES', 'NO')
        WHEN quantity_fluctuation = '+0' THEN
            IF(t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0) = 0, 'YES', 'NO')
        WHEN quantity_fluctuation LIKE '±%' OR quantity_fluctuation LIKE '±%' THEN
            IF(t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0) <= product_quantity * ( CONVERT(SUBSTRING(quantity_fluctuation, 2), DECIMAL(10,2))/100)
                   AND t2.product_quantity - ifnull(t4.inventory_quantity, 0) - ifnull(t5.shipped_quantity, 0) >= product_quantity * (0 - CONVERT(SUBSTRING(quantity_fluctuation, 2), DECIMAL(10,2))/100), 'YES', 'NO')
        ELSE 'UNKNOWN FLUCTUATION'
        END  ='NO'
and if(ifnull(t4.inventory_quantity, 0) >0,0,1)
and if(ifnull(t5.shipped_quantity, 0) >0,0,1)
order by order_date desc

;

select * from
(select t1.contract_management_code,
       t1.contract_product_line_number,
       sum(ifnull(t2.ship_quantity, 0)) shipped_quantity,
       sum(ifnull(t3.nnum, 0))          invoiced_quantity,
       date_format(t1.create_time, '%Y-%m-%d') order_time
from h3chq_crmbusiness1704287359505.crm_sales_order_product t1
         join h3chq_crmbusiness1704287359505.bip_outbound_order_detail t2 on t1.sales_order_code = t2.split_order_no
    and t1.material_code = t2.product_code
    and t1.contract_product_line_number = t2.split_order_line_no
    and t2.flag_deleted = 0
         left join (select csrcid, csrcbid, sum(nnum) nnum
                    from h3chq_crmbusiness1704287359505.crm_sales_invoice_details
                    where flag_deleted = 0
                    group by csrcid, csrcbid) t3
                   on t2.outbound_header = t3.csrcid and t2.outbound_line_id = t3.csrcbid
where t1.flag_deleted = 0
group by t1.contract_management_code, t1.contract_product_line_number,date_format(t1.create_time, '%Y-%m-%d'))temp where order_time is null
;



-- ETL建表
drop table if exists dws_in_transit_order;
CREATE TABLE dws_in_transit_order (
                                     `id` int(11) NOT NULL AUTO_INCREMENT primary key COMMENT 'id',
                                      sale_company VARCHAR(255) COMMENT '销售公司',
                                      deparment_code VARCHAR(255) COMMENT '部门编码',
                                      deparment_name VARCHAR(255) COMMENT '部门名称',
                                      region VARCHAR(255) COMMENT '区域',
                                      sales_assistant_code VARCHAR(255) COMMENT '销售助理编码',
                                      sales_assistant_name VARCHAR(255) COMMENT '销售助理姓名',
                                      cust_manager_code VARCHAR(255) COMMENT '客户经理编码',
                                      cust_manager_name VARCHAR(255) COMMENT '客户经理姓名',
                                      cust_type INT COMMENT '客户类型',
                                      cust_type_name VARCHAR(255) COMMENT '客户类型名称',
                                      cust_code VARCHAR(255) COMMENT '客户编码',
                                      cust_name VARCHAR(255) COMMENT '客户名称',
                                      contract_management_code VARCHAR(255) COMMENT '合同管理编号',
                                      order_date varchar(20) comment '下达日期',
                                      factory_assigned VARCHAR(255) COMMENT '分配工厂',
                                      main_class VARCHAR(255) COMMENT '产品主类',
                                      sub_class VARCHAR(255) COMMENT '产品子类',
                                      material_code VARCHAR(255) COMMENT '物料编码',
                                      material_name VARCHAR(255) COMMENT '物料名称',
                                      product_quantity INT COMMENT '产品数量',
                                      inventory_quantity DECIMAL(20, 2) COMMENT '库存数量',
                                      shipped_quantity DECIMAL(20, 2) COMMENT '已发货数量',
                                      transit_quantity DECIMAL(20, 2) COMMENT '在途数量',
                                      unit_price_exclusive DECIMAL(20, 6) COMMENT '不含税单价',
                                      amount_exclusive_tax DECIMAL(20, 2) COMMENT '不含税金额',
                                      transit_amount DECIMAL(20, 2) COMMENT '在途金额',
                                      status varchar(50) COMMENT '订单状态',
                                      status_desc VARCHAR(255) COMMENT '订单状态描述',
                                      delivery_date varchar(20) comment '交付日期',
    index cust_code_index(cust_code)
)comment '在途订单报表';

-- ETL名称:CRM在途订单报表->dws_pending_order
select * from cockpit.dws_in_transit_order dpo
# where if(:admin,
#          1,
#          if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
#       )
#   AND ((:deparment_code IS NULL OR :deparment_code = '') OR (deparment_code = :deparment_code))
#   AND ((:region IS NULL OR :region = '') OR (region = :region))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (cust_code like concat('%', :cust_code, '%')))
#   AND ((:cust_name IS NULL OR :cust_name = '') OR (cust_name like concat('%', :cust_name, '%')))
#   AND ((:material_code IS NULL OR :material_code = '') OR (material_code like concat('%', :material_code, '%')))
#   AND ((:material_name IS NULL OR :material_name = '') OR (material_name like concat('%', :material_name, '%')))
#   AND ((:contract_management_code IS NULL OR :contract_management_code = '') OR
#        (contract_management_code like concat('%', :contract_management_code, '%')))
#   AND if(:cust_manager_size>0, cust_manager_code in (:cust_manager_arr), 1)
#   AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (factory_assigned = :factory_assigned))
#    AND if(:sales_assistant_size>0,sales_assistant_code REGEXP :sales_assistant_str,1)

# order by order_date desc
# limit :page_size offset :offset
;

-- 合计
-- 合计
SELECT '合计' AS                 sale_company,
       sum(product_quantity)     product_quantity,
       sum(inventory_quantity)   inventory_quantity,
       sum(shipped_quantity)     shipped_quantity,
       sum(transit_quantity)     transit_quantity,
       round(sum(amount_exclusive_tax),2) amount_exclusive_tax,
       round(sum(transit_amount),2)       transit_amount
FROM (
         select * from dws_in_transit_order dpo
         where if(:admin,
                  1,
                  if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
               )
           AND ((:deparment_code IS NULL OR :deparment_code = '') OR (deparment_code = :deparment_code))
           AND ((:region IS NULL OR :region = '') OR (region = :region))
           AND ((:cust_code IS NULL OR :cust_code = '') OR (cust_code like concat('%', :cust_code, '%')))
           AND ((:cust_name IS NULL OR :cust_name = '') OR (cust_name like concat('%', :cust_name, '%')))
           AND ((:material_code IS NULL OR :material_code = '') OR (material_code like concat('%', :material_code, '%')))
           AND ((:material_name IS NULL OR :material_name = '') OR (material_name like concat('%', :material_name, '%')))
           AND ((:contract_management_code IS NULL OR :contract_management_code = '') OR
                (contract_management_code like concat('%', :contract_management_code, '%')))
           AND if(:cust_manager_size>0, cust_manager_code in (:cust_manager_arr), 1)
           AND ((:factory_assigned IS NULL OR :factory_assigned = '') OR (factory_assigned = :factory_assigned))
           AND if(:sales_assistant_size>0,sales_assistant_code REGEXP :sales_assistant_str,1)
         order by id asc
     ) temp
;
