const xlsx = require('xlsx');
const path = require('path');

// 读取Excel文件
const workbook = xlsx.readFile('环球剩余问题清单-刘宁.xlsx');
const sheet_name = workbook.SheetNames[0];
const worksheet = workbook.Sheets[sheet_name];

// 读取时保留所有格式
const data = xlsx.utils.sheet_to_json(worksheet, { 
    raw: false,
    defval: '' // 保留空值
});

// 保存原始数据的所有列
const all_data = [...data];

// 提取序号的数字部分
function extract_number(str) {
    const match = String(str).match(/\d+/);
    return match ? parseInt(match[0]) : 0;
}

// 筛选顾俊翔负责的任务并按优先级和行号排序
const filtered_data = data
    .filter(row => row['开发负责人'] === '顾俊翔')
    .sort((a, b) => {
        // 优先级权重
        const priority_weight = {
            '高': 3,
            '中': 2,
            '低': 1
        };
        
        // 首先按优先级排序
        const priority_diff = priority_weight[b['优先级']] - priority_weight[a['优先级']];
        if (priority_diff !== 0) return priority_diff;
        
        // 其次按序号倒序排序（提取数字部分）
        return extract_number(b['序号']) - extract_number(a['序号']);
    });

// 计算工作日
function add_working_days(start_date, days) {
    if (days <= 0) return start_date;
    
    let current_date = new Date(start_date);
    let remaining_days = days;

    while (remaining_days > 0) {
        current_date.setDate(current_date.getDate() + 1);
        // 跳过周末 (0是周日，6是周六)
        if (current_date.getDay() !== 0 && current_date.getDay() !== 6) {
            remaining_days--;
        }
    }

    return current_date;
}

// 格式化日期为YYYY-MM-DD
function format_date(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 计算任务完成时间
let current_date = new Date('2024-03-10'); // 从今天开始
let last_date = null;

// 创建任务完成时间映射
const completion_dates = {};

filtered_data.forEach(row => {
    // 如果已有计划完成时间且包含"待测试"，保留原值
    if (row['计划完成时间'] && row['计划完成时间'].includes('待测试')) {
        completion_dates[row['序号']] = row['计划完成时间'];
        return;
    }

    // 获取工作量评估，如果没有或无法解析则为0
    let work_hours = parseFloat(row['工作量评估(h)']);
    work_hours = isNaN(work_hours) ? 0 : work_hours;
    
    // 计算需要的工作天数（向上取整）
    const work_days = Math.ceil(work_hours / 8);
    
    // 如果工时为0，使用上一个任务的完成时间
    if (work_days === 0 && last_date) {
        current_date = new Date(last_date);
    } else {
        // 计算完成日期（考虑周末）
        current_date = add_working_days(current_date, work_days);
    }
    
    // 保存当前日期作为上一个任务的完成时间
    last_date = new Date(current_date);
    
    // 保存计划完成时间
    completion_dates[row['序号']] = format_date(current_date);
});

// 更新原始数据中的计划完成时间
all_data.forEach(row => {
    const serial_number = row['序号'];
    if (row['开发负责人'] === '顾俊翔' && completion_dates[serial_number]) {
        // 如果原值是"待测试"且新值不包含"待测试"，保留原值
        if (row['计划完成时间'] && 
            row['计划完成时间'].includes('待测试') && 
            !completion_dates[serial_number].includes('待测试')) {
            return;
        }
        row['计划完成时间'] = completion_dates[serial_number];
    }
});

// 创建新的工作表，保持原有格式
const new_worksheet = xlsx.utils.json_to_sheet(all_data, {
    dateNF: 'yyyy-mm-dd'
});

// 复制原工作表的列宽等格式信息
if (worksheet['!cols']) new_worksheet['!cols'] = worksheet['!cols'];
if (worksheet['!rows']) new_worksheet['!rows'] = worksheet['!rows'];
if (worksheet['!merges']) new_worksheet['!merges'] = worksheet['!merges'];

const new_workbook = xlsx.utils.book_new();
xlsx.utils.book_append_sheet(new_workbook, new_worksheet, sheet_name);

// 保存新文件
xlsx.writeFile(new_workbook, '顾俊翔任务排期.xlsx');
