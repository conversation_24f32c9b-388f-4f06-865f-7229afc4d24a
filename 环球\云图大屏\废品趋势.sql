select * from hqyw_process_waste_yield_trend ;


SELECT year AS years,
       month,
       machine_id,
       round(avg(monthly_scrap_rate) * 100 , 2) AS total_monthly_scrap_rate,
       SUM(waste_total) AS total_sum_num
FROM hqyw_process_waste_yield_trend
GROUP BY years, month, machine_id
order by years asc,CAST(month AS UNSIGNED) asc;


select month, max(total_monthly_scrap_rate) total_monthly_scrap_rate
from (SELECT month,
             round(avg(monthly_scrap_rate) * 100, 2) AS total_monthly_scrap_rate
      FROM hqyw_process_waste_yield_trend
      where machine_id=74
      GROUP BY year, month, machine_id
      union
      select 1, 0
      union
      select 2, 0
      union
      select 3, 0
      union
      select 4, 0
      union
      select 5, 0
      union
      select 6, 0
      union
      select 7, 0
      union
      select 8, 0
      union
      select 9, 0
      union
      select 10, 0
      union
      select 11, 0
      union
      select 12, 0) temp
group by month
order by CAST(month AS UNSIGNED) asc
;


