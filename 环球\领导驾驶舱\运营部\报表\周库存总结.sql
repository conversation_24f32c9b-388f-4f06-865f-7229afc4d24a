SELECT
    YEAR(CURDATE()) update_year,
    WEEK(CURDATE()) week_of_year,
    dpsc.code product_big_category,
    dpsc.name product_big_category_name,
    qd.qty2Unit,
    ommf.material_code,
    ommf.gram_weight,
    ommf.specification,
    COALESCE(qd.day_30, 0) day_30,
    COALESCE(qd.day_60, 0) day_60,
    COALESCE(qd.day_90, 0) day_90,
    COALESCE(qd.day_180, 0) day_180,
    COALESCE(qd.day_360, 0) day_360,
    COALESCE(qd.day_720, 0) day_720,
    DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) AS first_day_of_week,
    DATE_ADD(CURDATE(), INTERVAL (6 - WEEKDAY(CURDATE())) DAY) AS last_day_of_week
FROM
    cockpit.dim_paper_small_category dpsc
        LEFT JOIN cockpit.ods_mes_material_file ommf ON
        ommf.small_code = dpsc.code
        LEFT JOIN
    (
        SELECT
            owid.itemCode,
            owid.qty2Unit,
            SUM(CASE WHEN owid.itemAge BETWEEN 0 AND 30 THEN owid.qty2 ELSE 0 END) AS day_30,
            SUM(CASE WHEN owid.itemAge BETWEEN 31 AND 60 THEN owid.qty2 ELSE 0 END) AS day_60,
            SUM(CASE WHEN owid.itemAge BETWEEN 61 AND 90 THEN owid.qty2 ELSE 0 END) AS day_90,
            SUM(CASE WHEN owid.itemAge BETWEEN 91 AND 180 THEN owid.qty2 ELSE 0 END) AS day_180,
            SUM(CASE WHEN owid.itemAge BETWEEN 181 AND 360 THEN owid.qty2 ELSE 0 END) AS day_360,
            SUM(CASE WHEN owid.itemAge BETWEEN 361 AND 720 THEN owid.qty2 ELSE 0 END) AS day_720
        FROM
            cockpit.ods_wms_inventory_detail owid
        GROUP BY
            owid.itemCode,
            owid.qty2Unit
    ) qd ON
        qd.itemCode = ommf.material_code
WHERE
    ommf.flag_deleted = 0
  AND ommf.category = 1;

-- ETL 周库存总结-纸张库存-吨数
SELECT YEAR(CURDATE())         update_year,
       WEEK(CURDATE())         week_of_year,
       dpbc.code               product_big_category,
       dpbc.name               product_big_category_name,
       COALESCE(rd.tonnage, 0) tonnage
FROM dim_paper_big_category dpbc
         LEFT JOIN
     (SELECT ommf.big_code,
             round(SUM(sd.quantity), 2) tonnage
      FROM (select itemCode,
                   sum(
                           case
                               when qty2Unit in ('kg', 'kilogram') then qty2 / 1000
                               when qty2Unit in ('TNE') then qty2
                               when qty2Unit in ('fix') then SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', 1), ' ', -1) *
                                                             SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', -1), 'm', 1) /
                                                             1000000 *
                                                             t3.data_value / 1000000 * qty2
                               end) quantity
            from ods_wms_inventory_collect t1
                     join ods_mes_material_file t2 on t1.itemCode = t2.material_code
                     join ods_mes_parameter_data t3 on t2.gram_weight = t3.id
            where t2.category = 1
              and t2.flag_deleted = 0
            group by itemCode) sd
               LEFT JOIN ods_mes_material_file ommf ON
          ommf.material_code = sd.itemCode
      WHERE ommf.flag_deleted = 0
        AND ommf.category = 1
      GROUP BY ommf.big_code) rd ON
         rd.big_code = dpbc.code;


select
     itemCode,
     sum(
               case
                   when qty2Unit in ('kg', 'kilogram') then qty2 / 1000
                   when qty2Unit in ('TNE') then qty2
                   when qty2Unit in ('fix') then SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', 1), ' ', -1) *
                                                 SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', -1), 'm', 1) / 1000000 *
                                                 t3.data_value / 1000000 * qty2
                   end) quantity
from ods_wms_inventory_collect t1
         join ods_mes_material_file t2 on t1.itemCode = t2.material_code
         join ods_mes_parameter_data t3 on t2.gram_weight = t3.id
where t2.category = 1
  and t2.flag_deleted = 0
group by itemCode;

select
    itemCode,
    sum(
            case
                when qty2Unit in ('kg', 'kilogram') then qty2 / 1000
                when qty2Unit in ('TNE') then qty2
                when qty2Unit in ('fix') then SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', 1), ' ', -1) *
                                              SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', -1), 'm', 1) / 1000000 *
                                              t3.data_value / 1000000 * qty2
                end) quantity
from ods_wms_inventory_detail t1
         join ods_mes_material_file t2 on t1.itemCode = t2.material_code
         join ods_mes_parameter_data t3 on t2.gram_weight = t3.id
where t2.category = 1
  and t2.flag_deleted = 0
  and t1.productType=0
group by itemCode;

-- 纸箱
SELECT
    YEAR(CURDATE()) update_year,
    WEEK(CURDATE()) week_of_year,
    dcbc.code product_big_category,
    dcbc.name product_big_category_name,
    COALESCE(rd.separator_sheets, 0) AS separator_sheets
FROM
    dim_carton_big_category dcbc
        LEFT JOIN
    (
        SELECT
            ommf.big_code,
            SUM(sd.separator_sheets) separator_sheets
        FROM
            (
                SELECT
                    owic.itemCode,
                    SUM(owic.qty) separator_sheets
                FROM
                    ods_wms_inventory_collect owic
                GROUP BY
                    owic.itemCode
            ) sd
                LEFT JOIN ods_mes_material_file ommf ON
                ommf.material_code = sd.itemCode
        WHERE
            ommf.flag_deleted = 0
           AND ommf.category = 3
        GROUP BY
            ommf.big_code
    ) rd ON
        rd.big_code = dcbc.code
WHERE
    dcbc.code = '1010302';

-- 同步库存总结-纸箱大类维度表 name='纸箱类' code=1010301
SELECT
    t1.big_code big_code_id,
    t1.small_code small_code_id,
    t2.data_code  big_code,
    t2.data_value big_name,
    t3.data_code small_code,
    t3.data_value small_name
FROM
    ods_mes_material_file t1
left  join  ods_mes_parameter_data t2 on t1.big_code=t2.id and t2.flag_deleted=0
left  join ods_mes_parameter_data t3 on t1.small_code=t3.id and t3.flag_deleted=0
WHERE
    t1.flag_deleted = 0
   and t1.category=3
   and t2.data_value='纸箱类'
;
select * from
FROM
    ods_mes_material_file t1
left  join ods_mes_parameter_data t3 on t1.small_code=t2.id and t3.flag_deleted=0
WHERE
    t1.flag_deleted = 0
   and t1.category=3
;

SELECT
    t1.big_code,
    t1.small_code,
    ompd.data_code code,
    ompd.data_value name
FROM
    ods_mes_material_file t1
        join  ods_mes_parameter_data ompd on t1.small_code=ompd.id
WHERE
    ompd.flag_deleted = 0
  and t1.category=3
group by ompd.data_code, ompd.data_value;




SELECT
    YEAR(CURDATE()) update_year,
    WEEK(CURDATE()) week_of_year,
    dcbc.code product_big_category,
    dcbc.name product_big_category_name,
    COALESCE(rd.separator_sheets, 0) AS separator_sheets
FROM
    dim_carton_big_category dcbc
        LEFT JOIN
    (
        SELECT
            ompd.data_code big_code,
            SUM(sd.separator_sheets) separator_sheets
        FROM
            (
                SELECT
                    owic.itemCode,
                    SUM(owic.qty) separator_sheets
                FROM
                    ods_wms_inventory_collect owic
                GROUP BY
                    owic.itemCode
            ) sd
                LEFT JOIN ods_mes_material_file ommf ON
                ommf.material_code = sd.itemCode and ommf.flag_deleted = 0
                left join ods_mes_parameter_data ompd on
                ommf.big_code= ompd.id and ompd.flag_deleted=0
        WHERE
           ommf.category = 3
        GROUP BY
            ommf.big_code
    ) rd ON
        rd.big_code = dcbc.code
WHERE
    dcbc.name = '纸箱类';

select * from ods_wms_inventory_collect t1 where t1.itemCode in (select material_code
                                                                 from ods_mes_material_file ommf
                                                                 where flag_deleted=0 and big_code = 6879);



SELECT
    YEAR(CURDATE()) update_year,
    WEEK(CURDATE()) week_of_year,
    dcbc.code product_big_category,
    dcbc.name product_big_category_name,
    COALESCE(rd.pallets, 0) pallets
FROM
    dim_carton_big_category dcbc
        LEFT JOIN
    (
        SELECT
            ompd.data_code big_code,
            SUM(sd.pallets) pallets
        FROM
            (
                SELECT
                    owid.itemCode,
                    COUNT(DISTINCT pallentNo) pallets
                FROM
                    ods_wms_inventory_detail owid
                GROUP BY
                    owid.itemCode
            ) sd
                LEFT JOIN ods_mes_material_file ommf ON
                ommf.material_code = sd.itemCode and ommf.flag_deleted = 0
                left join ods_mes_parameter_data ompd on
                ommf.big_code= ompd.id and ompd.flag_deleted=0
        WHERE
           ommf.category = 3
        GROUP BY
            ommf.big_code
    ) rd ON
        rd.big_code = dcbc.code
WHERE
    dcbc.name = '纸箱类';

SELECT
    YEAR(CURDATE()) update_year,
    WEEK(CURDATE()) week_of_year,
    dcbc.code product_big_category,
    dcbc.name product_big_category_name,
    COALESCE(rd.box_sets, 0) AS box_sets
FROM
    dim_carton_big_category dcbc
        LEFT JOIN
    (
        SELECT
            ompd.data_code big_code,
            SUM(sd.box_sets) box_sets
        FROM
            (
                SELECT
                    owic.itemCode,
                    SUM(owic.qty) box_sets
                FROM
                    ods_wms_inventory_collect owic
                GROUP BY
                    owic.itemCode
            ) sd
                LEFT JOIN ods_mes_material_file ommf ON
                ommf.material_code = sd.itemCode and ommf.flag_deleted = 0
                left join ods_mes_parameter_data ompd on
                ommf.big_code= ompd.id and ompd.flag_deleted=0
        WHERE
           ommf.category = 3
        GROUP BY
            ommf.big_code
    ) rd ON
        rd.big_code = dcbc.code
WHERE
    dcbc.name = '纸箱类';

SELECT
    YEAR(CURDATE()) update_year,
    WEEK(CURDATE()) week_of_year,
    dcbc.code product_big_category,
    dcbc.name product_big_category_name,
    COALESCE(rd.separator_sheets, 0) AS separator_sheets
FROM
    dim_carton_big_category dcbc
        LEFT JOIN
    (
        SELECT
            ompd.data_code big_code,
            SUM(sd.separator_sheets) separator_sheets
        FROM
            (
                SELECT
                    owic.itemCode,
                    SUM(owic.qty) separator_sheets
                FROM
                    ods_wms_inventory_collect owic
                GROUP BY
                    owic.itemCode
            ) sd
                LEFT JOIN ods_mes_material_file ommf ON
                ommf.material_code = sd.itemCode and ommf.flag_deleted = 0
                left join ods_mes_parameter_data ompd on
                ommf.big_code= ompd.id and ompd.flag_deleted=0
        WHERE
            ommf.category = 3
        GROUP BY
            ommf.big_code
    ) rd ON
        rd.big_code = dcbc.code
WHERE
    dcbc.name = '纸箱类';
select * from dwd_box_inventory_week dbiw where product_big_category='1010301';
select product_big_category ,update_year,week_of_year from dwd_box_inventory_week dbiw
group by product_big_category ,update_year,week_of_year having count(*) >1;

SELECT
    YEAR(CURDATE()) update_year,
    WEEK(CURDATE()) week_of_year,
    dcbc.code product_big_category,
    dcbc.name product_big_category_name,
    COALESCE(rd.separator_sheets, 0) AS separator_sheets
FROM
    dim_carton_big_category dcbc
        LEFT JOIN
    (
        SELECT
            ompd.data_code big_code,
            SUM(sd.separator_sheets) separator_sheets
        FROM
            (
                SELECT
                    owic.itemCode,
                    SUM(owic.qty) separator_sheets
                FROM
                    ods_wms_inventory_collect owic
                GROUP BY
                    owic.itemCode
            ) sd
                LEFT JOIN ods_mes_material_file ommf ON
                ommf.material_code = sd.itemCode and ommf.flag_deleted = 0
                left join ods_mes_parameter_data ompd on
                ommf.big_code= ompd.id and ompd.flag_deleted=0
        WHERE
            ommf.category = 3
        GROUP BY
            ommf.big_code
    ) rd ON
        rd.big_code = dcbc.code
WHERE
    dcbc.name = '垫板隔档类';

SELECT
    *
FROM
    cockpit.dim_product_big_category;
