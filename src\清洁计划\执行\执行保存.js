var record_arr=[
    {
      "responsible_person": "33",
      "task_desc": "13",
      "create_by": "顾俊翔",
      "day_11": "--",
      "day_10": "--",
      "update_time": "2025-05-27 17:19:02",
      "day_31": "--",
      "day_30": "--",
      "day_15": "--",
      "day_14": "--",
      "day_13": "--",
      "day_12": "--",
      "day_19": "--",
      "day_18": "--",
      "day_17": "--",
      "id": 3,
      "day_16": "--",
      "update_by": "顾俊翔",
      "flag_deleted": 0,
      "day_2": "计划",
      "day_1": "--",
      "create_time": "2025-05-27 17:19:02",
      "responsible_position": "33",
      "task_code": "Clean0008",
      "distribution_code": "Clean2025052711",
      "exection_json": "{\"day_1\":\"--\",\"day_2\":\"计划\",\"day_3\":\"--\",\"day_4\":\"--\",\"day_5\":\"--\",\"day_6\":\"--\",\"day_7\":\"--\",\"day_8\":\"--\",\"day_9\":\"--\",\"day_10\":\"--\",\"day_11\":\"--\",\"day_12\":\"--\",\"day_13\":\"--\",\"day_14\":\"--\",\"day_15\":\"--\",\"day_16\":\"--\",\"day_17\":\"--\",\"day_18\":\"--\",\"day_19\":\"--\",\"day_20\":\"--\",\"day_21\":\"--\",\"day_22\":\"--\",\"day_23\":\"--\",\"day_24\":\"--\",\"day_25\":\"--\",\"day_26\":\"--\",\"day_27\":\"--\",\"day_28\":\"--\",\"day_29\":\"--\",\"day_30\":\"--\",\"day_31\":\"--\"}",
      "frequency_value": "2号",
      "version": "1.0",
      "day_22": "--",
      "day_21": "--",
      "day_20": "--",
      "plan_json": "{\"day_1\":\"--\",\"day_2\":\"计划\",\"day_3\":\"--\",\"day_4\":\"--\",\"day_5\":\"--\",\"day_6\":\"--\",\"day_7\":\"--\",\"day_8\":\"--\",\"day_9\":\"--\",\"day_10\":\"--\",\"day_11\":\"--\",\"day_12\":\"--\",\"day_13\":\"--\",\"day_14\":\"--\",\"day_15\":\"--\",\"day_16\":\"--\",\"day_17\":\"--\",\"day_18\":\"--\",\"day_19\":\"--\",\"day_20\":\"--\",\"day_21\":\"--\",\"day_22\":\"--\",\"day_23\":\"--\",\"day_24\":\"--\",\"day_25\":\"--\",\"day_26\":\"--\",\"day_27\":\"--\",\"day_28\":\"--\",\"day_29\":\"--\",\"day_30\":\"--\",\"day_31\":\"--\"}",
      "day_26": "--",
      "day_25": "--",
      "day_24": "--",
      "day_23": "--",
      "day_9": "--",
      "day_29": "--",
      "day_8": "--",
      "day_28": "--",
      "day_7": "--",
      "day_27": "--",
      "day_6": "--",
      "day_5": "--",
      "day_4": "--",
      "day_3": "--"
    },
    {
      "responsible_person": "12",
      "task_desc": "12",
      "create_by": "顾俊翔",
      "day_11": "--",
      "day_10": "--",
      "update_time": "2025-05-27 17:19:02",
      "day_31": "--",
      "day_30": "--",
      "day_15": "--",
      "day_14": "--",
      "day_13": "--",
      "day_12": "--",
      "day_19": "--",
      "day_18": "--",
      "day_17": "--",
      "id": 4,
      "day_16": "--",
      "update_by": "顾俊翔",
      "flag_deleted": 0,
      "day_2": "--",
      "day_1": "计划",
      "create_time": "2025-05-27 17:19:02",
      "responsible_position": "12",
      "task_code": "Clean0009",
      "distribution_code": "Clean2025052711",
      "exection_json": "{\"day_1\":\"计划\",\"day_2\":\"--\",\"day_3\":\"--\",\"day_4\":\"--\",\"day_5\":\"--\",\"day_6\":\"--\",\"day_7\":\"--\",\"day_8\":\"--\",\"day_9\":\"--\",\"day_10\":\"--\",\"day_11\":\"--\",\"day_12\":\"--\",\"day_13\":\"--\",\"day_14\":\"--\",\"day_15\":\"--\",\"day_16\":\"--\",\"day_17\":\"--\",\"day_18\":\"--\",\"day_19\":\"--\",\"day_20\":\"--\",\"day_21\":\"--\",\"day_22\":\"--\",\"day_23\":\"--\",\"day_24\":\"--\",\"day_25\":\"--\",\"day_26\":\"--\",\"day_27\":\"--\",\"day_28\":\"--\",\"day_29\":\"--\",\"day_30\":\"--\",\"day_31\":\"--\"}",
      "frequency_value": "1号",
      "version": "1.0",
      "day_22": "--",
      "day_21": "--",
      "day_20": "--",
      "plan_json": "{\"day_1\":\"计划\",\"day_2\":\"--\",\"day_3\":\"--\",\"day_4\":\"--\",\"day_5\":\"--\",\"day_6\":\"--\",\"day_7\":\"--\",\"day_8\":\"--\",\"day_9\":\"--\",\"day_10\":\"--\",\"day_11\":\"--\",\"day_12\":\"--\",\"day_13\":\"--\",\"day_14\":\"--\",\"day_15\":\"--\",\"day_16\":\"--\",\"day_17\":\"--\",\"day_18\":\"--\",\"day_19\":\"--\",\"day_20\":\"--\",\"day_21\":\"--\",\"day_22\":\"--\",\"day_23\":\"--\",\"day_24\":\"--\",\"day_25\":\"--\",\"day_26\":\"--\",\"day_27\":\"--\",\"day_28\":\"--\",\"day_29\":\"--\",\"day_30\":\"--\",\"day_31\":\"--\"}",
      "day_26": "--",
      "day_25": "--",
      "day_24": "--",
      "day_23": "--",
      "day_9": "--",
      "day_29": "--",
      "day_8": "--",
      "day_28": "--",
      "day_7": "--",
      "day_27": "--",
      "day_6": "--",
      "day_5": "--",
      "day_4": "--",
      "day_3": "--"
    }
  ]
for(var i=0;i<record_arr.length;i++){
    var obj=record_arr[i]
    var exection_json = {};
    for (var j = 1; j <= 31; j++) {
        exection_json["day_"+j] = obj["day_"+j] || "--";
    }
    record_arr[i].exection_json=JSON.stringify(exection_json)
}
console.log(JSON.stringify(record_arr))