var param_obj = {
    "tax": tax,
    "abs_tax_rate": Math.abs(record.tax_rate),
    "exchange_rate": record.exchange_rate,
    "product_quantity": record.product_quantity,
    "record_obj": record_obj
}
var cust_code = record_obj.cust_code
for (var i = 0; i < cust_arr.length; i++) {
    if (cust_arr[i].cust_code === cust_code) {
        param_obj.exchange_rate = cust_arr[i].exchange_rate
        param_obj.abs_tax_rate = Math.abs(cust_arr[i].tax_rate)
        break;
    }
}
console.log("param_obj", JSON.stringify(param_obj))
return param_obj