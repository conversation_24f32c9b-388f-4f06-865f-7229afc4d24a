-- 老
SELECT md.update_month,
       md.product_big_category,
       md.product_big_category_name,
       COALESCE(dd.amount, 0) amount,
       0                      amount_yoy,
       0                      amount_mom
FROM (SELECT `year_month` update_month,
             dpbc.code AS product_big_category,
             dpbc.name AS product_big_category_name
      FROM dim_month dm,
           dim_product_big_category dpbc
      WHERE dm.`year` = YEAR(CURDATE())) md
         LEFT JOIN
     (SELECT DATE_FORMAT(opjo.create_time, '%Y-%m') update_month,
             dpbc.code AS                           product_big_category,
             COALESCE(ROUND(SUM(COALESCE(opjo.release_quantity, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0),
                      0)                            amount
      FROM dim_product_big_category dpbc
               LEFT JOIN ods_pm_job_order opjo ON
          opjo.large_category = dpbc.code
               LEFT JOIN ods_pm_order_product b ON
          opjo.order_code = b.sales_number
              AND opjo.material_code = b.product_number
      WHERE opjo.source_type = 1
        AND YEAR(opjo.create_time) = YEAR(CURDATE())
      GROUP BY DATE_FORMAT(opjo.create_time, '%Y-%m'),
               dpbc.code) dd
     ON
         dd.update_month = md.update_month
             AND dd.product_big_category = md.product_big_category
ORDER BY md.update_month;

-- 新
SELECT md.update_month,
       md.product_big_category,
       md.product_big_category_name,
       COALESCE(dd.amount, 0) amount,
       0                      amount_yoy,
       0                      amount_mom
FROM (SELECT `year_month` update_month,
             dpbc.code AS product_big_category,
             dpbc.name AS product_big_category_name
      FROM dim_month dm,
           dim_product_big_category dpbc
      WHERE dm.`year` = YEAR(CURDATE())) md
         LEFT JOIN
     (SELECT DATE_FORMAT(a.create_time, '%Y-%m')                                                     update_month,
             dpbc.code AS                                                                            product_big_category,
             COALESCE(ROUND(SUM(COALESCE(a.total, 0) * COALESCE(a.unit_price_no, 0)) / 10000, 0), 0) amount
      FROM dim_product_big_category dpbc
               LEFT JOIN ods_pm_order_product a ON
          a.major_categories = dpbc.code
               LEFT JOIN ods_pm_sale_order b ON
          b.order_number = a.sales_number
      WHERE b.status in (1, 2, 3, 4)
        AND YEAR(a.create_time) = YEAR(CURDATE())
      GROUP BY DATE_FORMAT(a.create_time, '%Y-%m'),
               dpbc.code) dd ON
         dd.update_month = md.update_month
             AND dd.product_big_category = md.product_big_category
ORDER BY md.update_month;
