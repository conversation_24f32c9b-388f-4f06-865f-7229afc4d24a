function formatData(item) {  
    // 检查 cust_code 是否以 '[' 开头并以 ']' 结尾  
    if (item.cust_code.charAt(0) !== '[' || item.cust_code.charAt(item.cust_code.length - 1) !== ']') {  
        throw new Error('cust_code must be enclosed in square brackets');  
    }  
    // 移除 cust_code 两端的方括号  
    var custCodeStr = item.cust_code.slice(1, -1);  
    // 类似地检查 cust_name  
    if (item.cust_name.charAt(0) !== '[' || item.cust_name.charAt(item.cust_name.length - 1) !== ']') {  
        throw new Error('cust_name must be enclosed in square brackets');  
    }  
    var custNameStr = item.cust_name.slice(1, -1);  
    // 将 cust_code 和 cust_name 字符串分割成数组  
    var custCodeArray = custCodeStr.split(',');  
    var custNameArray = custNameStr.split(',');   
    // 为每个cust_code创建一个新对象  
    var expandedItems = custCodeArray.map(function(custCode, index) {  
        var newObj = {};  
        for (var key in item) {  
            if (item.hasOwnProperty(key) && key !== 'cust_code' && key !== 'cust_name') {  
                newObj[key] = item[key]; // 复制其他属性  
            }  
        }  
        newObj.cust_code = custCode.trim(); // 设置单独的cust_code，并去除可能的空格  
        newObj.cust_name = custNameArray[index].trim(); // 设置对应的cust_name，并去除可能的空格  
        return newObj;  
    });  
  
    return expandedItems;  
}  
// 分页函数，使用ES5语法  
function paginate(data, pageSize, currentPage) {  
    var startIndex = (currentPage - 1) * pageSize;  
    var endIndex = Math.min(startIndex + pageSize, data.length);  
    return data.slice(startIndex, endIndex);  
}  
  
var expandedData = [];  
data.forEach(function(item) {  
    var expandedItems = formatData(item);  
    expandedData = expandedData.concat(expandedItems); // 合并扩展后的对象到总数组中  
});  
var arr = []
for (var x = 0; x < custCodeArr.length; x++) {
  for (var y = 0; y < expandedData.length; y++) {
    if (custCodeArr[x] == expandedData[y].cust_code) {
      arr.push(expandedData[y])
    }
  }
}
var paginatedData = paginate(arr, pageSize, currentPage);  
  
var obj = {};  
obj['records'] = paginatedData;  
obj['total'] = arr.length;  
return obj