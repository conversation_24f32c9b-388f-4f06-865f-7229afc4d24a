var data = [["生产工厂","工号","业务员","客户编码","客户名称","产品编码","产品名称","单价","纸张编码","纸张名称","克重","上年一月生产数量","上年二月生产数量","上年三月生产数量","上年四月生产数量","上年五月生产数量","上年六月生产数量","上年七月生产数量","上年八月生产数量","上年九月生产数量","上年十月生产数量","上年十一月生产数量","上年十二月生产数量","一月预测","一月回顾","二月预测","二月回顾","三月预测","三月回顾","四月预测","四月回顾","五月预测","五月回顾","六月预测","六月回顾","七月预测","七月回顾","八月预测","八月回顾","九月预测","九月回顾","十月预测","十月回顾","十一月预测","十一月回顾","十二月预测","十二月回顾","一月销售额预测","二月销售额预测","三月销售额预测","四月销售额预测","五月销售额预测","六月销售额预测","七月销售额预测","八月销售额预测","九月销售额预测","十月销售额预测","十一月销售额预测","十二月销售额预测"],["","002868","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","",""]]
//
//columnMap是中英文映射的json对象
var columnMap = {
	"十二月回顾": "year_december_review",
	"十二月预测": "year_december_forecast",
	"十一月回顾": "year_november_review",
	"十一月预测": "year_november_forecast",
	"十月回顾": "year_october_review",
	"十月预测": "year_october_forecast",
	"九月回顾": "year_september_review",
	"九月预测": "year_september_forecast",
	"八月回顾": "year_august_review",
	"八月预测": "year_august_forecast",
	"七月回顾": "year_july_review",
	"七月预测": "year_july_forecast",
	"六月回顾": "year_june_review",
	"六月预测": "year_june_forecast",
	"五月回顾": "year_may_review",
	"五月预测": "year_may_forecast",
	"四月回顾": "year_april_review",
	"四月预测": "year_april_forecast",
	"三月回顾": "year_march_review",
	"三月预测": "year_march_forecast",
	"二月回顾": "year_february_review",
	"二月预测": "year_february_forecast",
	"一月回顾": "year_january_review",
	"一月预测": "year_january_forecast",
	"上年十二月生产数量": "last_year_december_production",
	"上年十一月生产数量": "last_year_november_production",
	"上年十月生产数量": "last_year_october_production",
	"上年九月生产数量": "last_year_september_production",
	"上年八月生产数量": "last_year_august_production",
	"上年七月生产数量": "last_year_july_production",
	"上年六月生产数量": "last_year_june_production",
	"上年五月生产数量": "last_year_may_production",
	"上年四月生产数量": "last_year_april_production",
	"上年三月生产数量": "last_year_march_production",
	"上年二月生产数量": "last_year_february_production",
	"上年一月生产数量": "last_year_january_production",
	"纸张编码": "paper_code",
	"纸张名称": "paper_name",
	"工号": "work_code",
	"单价": "unit_sales_price",
	"生产工厂": "production_factory",
	"产品编码": "product_code",
	"产品名称": "product_name",
	"客户编码": "cust_code",
	"客户名称": "cust_name",
	"业务员": "responsible_person",
	"自增主键": "id",
	"是否删除": "flag_deleted",
	"版本": "version",
	"创建人": "create_by",
	"创建时间": "create_time",
	"修改人": "update_by",
	"修改时间": "update_time",
	"域id": "domain_id",
	"纸张预测编码": "paper_forecast_code",
	"预测版本": "forecast_version",
	"十二月销售额预测": "december_sale_money_forecast",
	"十一月销售额预测": "november_sale_money_forecast",
	"十月销售额预测": "october_sale_money_forecast",
	"九月销售额预测": "september_sale_money_forecast",
	"八月销售额预测": "august_sale_money_forecast",
	"七月销售额预测": "july_sale_money_forecast",
	"六月销售额预测": "june_sale_money_forecast",
	"五月销售额预测": "may_sale_money_forecast",
	"四月销售额预测": "april_sale_money_forecast",
	"三月销售额预测": "march_sale_money_forecast",
	"二月销售额预测": "february_sale_money_forecast",
	"一月销售额预测": "january_sale_money_forecast",
	"克重": "gram_weight"
}

var json = []
for (var i = 1; i < data.length; i++) {
	var obj = {}
	for (var j = 0; j < data[i].length; j++) {
		//key用columnMap替换
		var key = columnMap[data[0][j]]
		if (key) {
           obj[key] = data[i][j]
           if (!isNaN(Number(data[i][j]))&& data[i][j] != ""&& !data[i][j].startsWith('00')) {
              //科学计数法转换
              console.log(Number(data[i][j]))
              obj[key] = Number(data[i][j])
           } else {
              obj[key] = data[i][j]
           }
		}
	}
	obj['indexL'] = i;
	json.push(obj)
}
console.log(JSON.stringify(json))

