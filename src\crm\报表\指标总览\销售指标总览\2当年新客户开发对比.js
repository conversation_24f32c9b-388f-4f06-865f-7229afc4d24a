var data_arr = [
    {
        "metric_year": "2024",
        "cust_size": 0,
        "invoiced_amount_new": 0.00,
        "invoiced_amount_old": 0.00
    },
    {
        "metric_year": "2025",
        "cust_size": 465,
        "invoiced_amount_new": 26139047.41,
        "invoiced_amount_old": 0.00
    }

];


var result = [
    {
        "metric": "新客户数量",
        "last_year": data_arr[0].cust_size,
        "this_year": data_arr[1].cust_size
    },
    {
        "metric": "新客户销售",
        "last_year": Math.floor(data_arr[0].invoiced_amount_new / 10000),
        "this_year": Math.floor(data_arr[1].invoiced_amount_new / 10000) + "万"
    },
    {
        "metric": "老客户增减",
        "last_year": (data_arr[0].invoiced_amount_old > 0 ? "+" : "") + Math.floor(data_arr[0].invoiced_amount_old / 10000) + "万",
        "this_year": (data_arr[1].invoiced_amount_old > 0 ? "+" : "") + Math.floor(data_arr[1].invoiced_amount_old / 10000) + "万"
    }
]

return result;