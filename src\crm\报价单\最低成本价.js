var template_info_arr =[{"create_time":"2025-05-24 10:59:22","section_name":"精糖干燥工段","label":"2","template_desc":"12","version":"1.0","template_code":"Tmpl0007","create_by":"顾俊翔","template_name":"2","update_time":"2025-05-27 09:16:18","section_code":"A07","id":8,"update_by":"顾俊翔","value":"Tmpl0007","flag_deleted":0},{"create_time":"2025-05-24 11:18:21","section_name":"精糖干燥工段","label":"1","template_desc":"12","version":"1.0","template_code":"Tmpl0008","create_by":"顾俊翔","template_name":"1","update_time":"2025-05-27 09:16:11","section_code":"A07","id":9,"update_by":"顾俊翔","value":"Tmpl0008","flag_deleted":0},{"create_time":"2025-05-28 13:41:17","section_name":"精糖干燥工段","label":"1213","template_desc":"1234","version":"1.0","template_code":"Tmpl0009","create_by":"顾俊翔","template_name":"1213","update_time":"2025-05-28 13:41:17","section_code":"A07","id":10,"update_by":"顾俊翔","value":"Tmpl0009","flag_deleted":0},{"create_time":"2025-06-16 11:40:50","section_name":"原糖分蜜工段","label":"新模板","template_desc":"","version":"1.0","template_code":"Tmpl0011","create_by":"顾俊翔","template_name":"新模板","update_time":"2025-06-16 11:40:50","section_code":"A04","id":12,"update_by":"顾俊翔","value":"Tmpl0011","flag_deleted":0},{"create_time":"2025-06-17 18:57:16","section_name":"原糖分蜜工段","label":"新模板7","template_desc":"","version":"1.0","template_code":"Tmpl0013","create_by":"顾俊翔","template_name":"新模板7","update_time":"2025-06-17 18:57:16","section_code":"A04","id":14,"update_by":"顾俊翔","value":"Tmpl0013","flag_deleted":0},{"create_time":"2025-06-17 18:57:26","section_name":"原糖分蜜工段","label":"新模板8","template_desc":"","version":"1.0","template_code":"Tmpl0014","create_by":"顾俊翔","template_name":"新模板8","update_time":"2025-06-17 18:57:26","section_code":"A04","id":15,"update_by":"顾俊翔","value":"Tmpl0014","flag_deleted":0}]
var template_code= "Tmpl0014"
var temp_obj={
    "section_code":"",
    "section_name":"",
    "template_code":"",
    "template_name":""
  }
  for (let index = 0; index < template_info_arr.length; index++) {
    if(template_info_arr[index].template_code==template_code){
      temp_obj.section_code=template_info_arr[index].section_code
      temp_obj.section_name=template_info_arr[index].section_name
      temp_obj.template_code=template_info_arr[index].template_code
      temp_obj.template_name=template_info_arr[index].template_name
      break
    }
  }
  console.log(JSON.stringify(temp_obj))
  return temp_obj

