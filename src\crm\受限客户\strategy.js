var crm_restricted_strategy_list_arr = [
    { "customer_parameters": "c_01", "and_or": "or", "days": "3", "conditions": "less", "flag_deleted": 0 },
    { "customer_parameters": "c_02", "and_or": "and", "days": "2", "conditions": "greater", "flag_deleted": 0 },
    { "customer_parameters": "c_03", "and_or": "and", "days": "3", "conditions": "greater", "flag_deleted": 0 },
    { "customer_parameters": "c_04", "and_or": "and", "days": "-1", "conditions": "greater", "flag_deleted": 0 },
  ]
  
  var c_01 = 12
  var c_02 = 4
  var c_03 = 12
  var c_04 = 0
  var data_map = {
    "c_01": c_01, //欠款金额
    "c_02": c_02, //库存天数
    "c_03": c_03, //库存金额
    "c_04": c_04  //欠款天数
  }
  //customer_parameters 参数 ,conditions 条件,days 具体数值, and_or 条件字段
  //根据实际c_01~c_04参数值判断是否满足条件
  
  function check_conditions(strategy_list) {
    // 计算每个策略的结果
    var results = strategy_list.map(function (strategy) {
      var value = data_map[strategy.customer_parameters];
      var days = parseInt(strategy.days);
      var condition = strategy.conditions;
      var result;
      if (condition === "greater") {
        result = value > days;
      } else if (condition === "greater_and_equals") {
        result = value >= days;
      } else if (condition === "equal") {
        result = value === days;
      } else if (condition === "less") {
        result = value < days;
      } else if (condition === "less_and_equals") {
        result = value <= days;
      } else {
        result = false;
      }
      return result;
    });
    var expression="";
    for (var i = 0; i < results.length-1; i++) {
      var combine=strategy_list[i].and_or==='and'?'&&':'||'
      expression=expression+results[i]+combine;
    }
    expression=expression+results[results.length-1]; 
    // 拼接成字符串并使用 eval 计算
    var is_satisfied = eval(expression);
  
    return is_satisfied;
  }
  
  var is_satisfied = check_conditions(crm_restricted_strategy_list_arr);
  
  console.log("是否满足条件:", is_satisfied);
  return is_satisfied