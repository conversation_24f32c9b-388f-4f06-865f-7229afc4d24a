var request = require('request');

// 记录开始时间
var startTime = Date.now();
console.log('请求开始时间:', new Date(startTime).toLocaleString());

var options = {
   'method': 'POST',
   'url': 'http://************:5000/nccloud/api/uapbd/productCostApi/queryprtCost',
   'headers': {
      'access_token': '37201d3a140422743eabf811cfef034e',
      'ucg_flag': 'y',
      'signature': 'db9f1d9f52d5912af0abfda515e122224f026037efc5c8ee50a8ed49796de6e2',
      'repeat_check': 'Y',
      'client_id': 'jiekou',
      'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
      'Content-Type': 'application/json;charset=utf-8',
      'Accept': '*/*',
      'Host': '************:5200',
      'Connection': 'keep-alive'
   },
   body: '{\n  "allDatas": "Y"\n}'
};

console.log('发送请求...');

request(options, function (error, response) {
   // 记录结束时间
   var endTime = Date.now();
   var executionTime = endTime - startTime;

   console.log('请求结束时间:', new Date(endTime).toLocaleString());
   console.log('执行时间:', executionTime + 'ms');
   console.log('执行时间:', (executionTime / 1000).toFixed(2) + 's');

   if (error) {
      console.log('请求出错，错误信息:', error);
      throw new Error(error);
   }

   console.log('请求成功，响应内容:');
   console.log(response.body);
});
