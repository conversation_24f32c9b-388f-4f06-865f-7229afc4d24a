drop table ods_material_weighing;
create table ods_material_weighing
(
    id                                 int auto_increment
        primary key,
    sugarcane_leaf_purchase_quantity decimal(18, 2) comment '蔗叶收购量',
    sugarcane_impurities             decimal(18, 2) comment '甘蔗夹杂物',
    sugar_quantity            decimal(18, 2) comment '当日暂存在FN原糖仓的原糖量',
    update_time                        varchar(20) comment '更新时间',
    unique index update_time_index (update_time)
) comment '地磅系统_物资过磅';

select * from ods_material_weighing order by update_time desc;
delete from ods_material_weighing  where update_time is null;
select datediff('2025-07-14','2024-11-17');



SELECT SUM(fhsl) FROM hw_gb WHERE xmmc = '内部原糖调运' and fcxhdw ='东亚扶南精糖有限公司' and in_date > '2024-11-17 00:00:00' and in_date < '2025-03-17 23:59:59';
SELECT SUM(fhsl) FROM hw_gb WHERE xmmc = '粗甘蔗叶'  and in_date > '2024-11-17 00:00:00' and in_date < '2025-03-17 15:00:00' and fcxhdw ='东亚扶南精糖有限公司';
SELECT SUM(fhsl) FROM hw_gb WHERE xmmc = '甘蔗夹杂物'  and zjmc ='24/25' and fcxhdw ='东亚扶南精糖有限公司';

                              and in_date > '2024-11-17 00:00:00' and in_date < '2025-03-17 15:00:00'把对应时间替换成zjmc ='24/25'就是榨季累计的值了
