-- 当年累计回款金额	当期累计回款金额	当月累计汇款金额	当期期初欠款	当期期末欠款	当期销售金额（含税）	回款周期（一季度）	回款周期（二季度）	回款周期（三季度）	回款周期（四季度）	回款周期（全年）	考核指标

WITH payment_data AS (
    -- 当年,当期,当月回款金额
    SELECT cust_code,
           SUM(CASE
                   WHEN billdate >= DATE_FORMAT(NOW(), '%Y-01-01') THEN money
                   ELSE 0
               END) AS year_payment_amount,
           SUM(CASE
                   WHEN billdate >= DATE_SUB(DATE_FORMAT(NOW(), '%Y-%m-01'),
                                             INTERVAL (MONTH(NOW()) - 1) % 3 MONTH)
                       THEN money
                   ELSE 0
               END) AS quarter_payment_amount,
           SUM(CASE
                   WHEN billdate >= DATE_FORMAT(NOW(), '%Y-%m-01') THEN money
                   ELSE 0
               END) AS month_payment_amount
    FROM crm_sales_received_payments
    WHERE billdate >= DATE_FORMAT(NOW(), '%Y-01-01')
      AND flag_deleted = 0
    GROUP BY cust_code),
     outstanding_data AS (
         -- 期初,期末欠款
         SELECT cust_code,
                SUM(CASE
                        WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(
                                DATE_SUB(
                                        DATE_FORMAT(NOW(), '%Y-%m-01'),
                                        INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
                                ),
                                '%Y-%m'
                                                                 ) THEN qkbbbye
                        ELSE 0
                    END) AS begin_outstanding_amount,
                SUM(CASE
                        WHEN date_format(create_time, '%Y-%m') = date_format(now(), '%Y-%m')
                            THEN qkbbbye
                        ELSE 0
                    END) AS end_outstanding_amount
         FROM crm_account_receivable_age
         WHERE date_format(create_time, '%Y-%m') IN (
                                                     DATE_FORMAT(
                                                             DATE_SUB(
                                                                     DATE_FORMAT(NOW(), '%Y-%m-01'),
                                                                     INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
                                                             ),
                                                             '%Y-%m'
                                                     ),
                                                     date_format(now(), '%Y-%m')
             )
           AND flag_deleted = 0
         GROUP BY cust_code),
     current_sales AS (
         -- 当期销售开票金额
         SELECT customer_no                            AS cust_code,
                ROUND(SUM(invoiced_amount_include), 2) AS invoiced_amount_include_current
         FROM bip_outbound_order_detail bood
                  LEFT JOIN (SELECT csrcid,
                                    csrcbid,
                                    SUM(norigtaxmny)                        invoiced_amount_include,
                                    DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
                             FROM crm_sales_invoice_details
                             WHERE flag_deleted = 0
                             GROUP BY csrcid, csrcbid, dbilldate) csid ON bood.outbound_header = csid.csrcid
             AND bood.outbound_line_id = csid.csrcbid
         WHERE bood.flag_deleted = 0
           AND invoiced_date >= DATE_FORMAT(
                 DATE_SUB(
                         DATE_FORMAT(NOW(), '%Y-%m-01'),
                         INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
                 ),
                 '%Y-%m-01'
                                )
           AND invoiced_date < DATE_FORMAT(now(), '%Y-%m-%d')
         GROUP BY customer_no),
     quarterly_payment_cycle AS (
         -- 季度回款周期
         WITH quarterly_outstanding AS (
             -- 获取每个季度的欠款数据
             SELECT cust_code,
                    ROUND(SUM(CASE
                                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-01')
                                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-03')
                                      THEN qkbbbye
                                  ELSE 0
                        END), 2) AS outstanding_amount_1,
                    ROUND(SUM(CASE
                                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-04')
                                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-06')
                                      THEN qkbbbye
                                  ELSE 0
                        END), 2) AS outstanding_amount_2,
                    ROUND(SUM(CASE
                                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-07')
                                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-09')
                                      THEN qkbbbye
                                  ELSE 0
                        END), 2) AS outstanding_amount_3,
                    ROUND(SUM(CASE
                                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-10')
                                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-12')
                                      THEN qkbbbye
                                  ELSE 0
                        END), 2) AS outstanding_amount_4
             FROM crm_account_receivable_age
             WHERE flag_deleted = 0
               AND date_format(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
             GROUP BY cust_code),
              quarterly_invoice AS (
                  -- 获取每个季度的开票金额
                  SELECT customer_no  AS cust_code,
                         ROUND(SUM(CASE
                                       WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                                           AND invoiced_date < DATE_FORMAT(NOW(), '%Y-04-01')
                                           THEN invoiced_amount_include
                                       ELSE 0
                             END), 2) AS invoiced_amount_include_1,
                         ROUND(SUM(CASE
                                       WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-04-01')
                                           AND invoiced_date < DATE_FORMAT(NOW(), '%Y-07-01')
                                           THEN invoiced_amount_include
                                       ELSE 0
                             END), 2) AS invoiced_amount_include_2,
                         ROUND(SUM(CASE
                                       WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-07-01')
                                           AND invoiced_date < DATE_FORMAT(NOW(), '%Y-10-01')
                                           THEN invoiced_amount_include
                                       ELSE 0
                             END), 2) AS invoiced_amount_include_3,
                         ROUND(SUM(CASE
                                       WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-10-01')
                                           AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                                           THEN invoiced_amount_include
                                       ELSE 0
                             END), 2) AS invoiced_amount_include_4
                  FROM bip_outbound_order_detail bood
                           LEFT JOIN (SELECT csrcid,
                                             csrcbid,
                                             SUM(norigtaxmny)                        invoiced_amount_include,
                                             DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
                                      FROM crm_sales_invoice_details
                                      WHERE flag_deleted = 0
                                      GROUP BY csrcid, csrcbid, dbilldate) csid ON bood.outbound_header = csid.csrcid
                      AND bood.outbound_line_id = csid.csrcbid
                  WHERE bood.flag_deleted = 0
                    AND invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                    AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                  GROUP BY customer_no)

         -- 计算每个季度的回款周期指标
         SELECT o.cust_code,
                -- 第一季度回款周期
                CASE
                    WHEN i.invoiced_amount_include_1 = 0 THEN NULL
                    ELSE ROUND((o.outstanding_amount_1 / 180) / NULLIF(i.invoiced_amount_include_1, 0), 4)
                    END AS payment_cycle_q1,
                -- 第二季度回款周期
                CASE
                    WHEN i.invoiced_amount_include_2 = 0 THEN NULL
                    ELSE ROUND((o.outstanding_amount_2 / 180) / NULLIF(i.invoiced_amount_include_2, 0), 4)
                    END AS payment_cycle_q2,
                -- 第三季度回款周期
                CASE
                    WHEN i.invoiced_amount_include_3 = 0 THEN NULL
                    ELSE ROUND((o.outstanding_amount_3 / 180) / NULLIF(i.invoiced_amount_include_3, 0), 4)
                    END AS payment_cycle_q3,
                -- 第四季度回款周期
                CASE
                    WHEN i.invoiced_amount_include_4 = 0 THEN NULL
                    ELSE ROUND((o.outstanding_amount_4 / 180) / NULLIF(i.invoiced_amount_include_4, 0), 4)
                    END AS payment_cycle_q4
         FROM quarterly_outstanding o
                  LEFT JOIN quarterly_invoice i ON o.cust_code = i.cust_code
         WHERE (i.invoiced_amount_include_1 > 0
             OR i.invoiced_amount_include_2 > 0
             OR i.invoiced_amount_include_3 > 0
             OR i.invoiced_amount_include_4 > 0)),
     yearly_payment_cycle AS (
         -- 年度回款周期
         WITH yearly_outstanding AS (
             -- 年度欠款金额
             SELECT cust_code,
                    ROUND(SUM(CASE
                                  WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-01')
                                      OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-12')
                                      THEN qkbbbye
                                  ELSE 0
                        END), 2) AS outstanding_amount_year
             FROM crm_account_receivable_age
             WHERE flag_deleted = 0
               AND date_format(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
             GROUP BY cust_code),
              yearly_invoice AS (
                  -- 年度开票金额
                  SELECT customer_no                               cust_code,
                         ROUND(SUM(invoiced_amount_include), 2) AS invoiced_amount_include_year
                  FROM bip_outbound_order_detail bood
                           LEFT JOIN (SELECT csrcid,
                                             csrcbid,
                                             SUM(norigtaxmny)                        invoiced_amount_include,
                                             DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
                                      FROM crm_sales_invoice_details
                                      WHERE flag_deleted = 0
                                      GROUP BY csrcid, csrcbid, dbilldate) csid ON bood.outbound_header = csid.csrcid
                      AND bood.outbound_line_id = csid.csrcbid
                  WHERE bood.flag_deleted = 0
                    AND invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                    AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                  GROUP BY customer_no)

         -- 计算年度回款周期指标
         SELECT o.cust_code,
                CASE
                    WHEN i.invoiced_amount_include_year = 0 THEN NULL
                    ELSE ROUND((o.outstanding_amount_year / 720) / NULLIF(i.invoiced_amount_include_year, 0), 4)
                    END AS payment_cycle_year
         FROM yearly_outstanding o
                  LEFT JOIN yearly_invoice i ON o.cust_code = i.cust_code
         WHERE i.invoiced_amount_include_year > 0),
     customer_target AS (
         -- 客户信息及考核指标
         SELECT '西安环球'           sale_company,
                t1.deparment_code,
                t1.deparment_name,
                t1.department_region region,
                t1.cust_code,
                t1.cust_name,
                t1.cust_manager_code,
                t1.cust_manager_name,
                t1.cust_type,
                CASE
                    WHEN t1.cust_type = 0 THEN '新建客户'
                    WHEN t1.cust_type = 1 THEN '公海客户'
                    WHEN t1.cust_type = 2 THEN '合作客户'
                    WHEN t1.cust_type = 3 THEN '开发中客户'
                    WHEN t1.cust_type = 4 THEN '受限客户'
                    END              cust_type_name,
                t2.payment_cycle_target -- 考核指标
         FROM (WITH RankedCustomers AS (SELECT *,
                                               ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
                                        FROM crm_cust_basic
                                        where flag_deleted = 0
                                          and cust_type not in (0))
               SELECT *
               FROM RankedCustomers
               WHERE rn = 1) t1
                  LEFT JOIN metric_person t2 ON t1.cust_manager_code = t2.cust_manager_code AND t2.flag_deleted = 0
             and t2.metric_year = year(now()))

-- 最终报表：按cust_code关联所有数据
SELECT ct.sale_company,
       ct.deparment_code,
       ct.deparment_name,
       ct.region,
       ct.cust_code,
       ct.cust_name,
       ct.cust_manager_code,
       ct.cust_manager_name,
       ct.cust_type,
       CASE
           WHEN ct.cust_type = 0 THEN '新建客户'
           WHEN ct.cust_type = 1 THEN '公海客户'
           WHEN ct.cust_type = 2 THEN '合作客户'
           WHEN ct.cust_type = 3 THEN '开发中客户'
           WHEN ct.cust_type = 4 THEN '受限客户'
           END                                                                                         cust_type_name,
       round(p.year_payment_amount, 2)             AS                                                  '当年累计回款金额',
       round(p.quarter_payment_amount, 2)          AS                                                  '当期累计回款金额',
       round(p.month_payment_amount, 2)            AS                                                  '当月累计汇款金额',
       round(o.begin_outstanding_amount, 2)        AS                                                  '当期期初欠款',
       round(o.end_outstanding_amount, 2)          AS                                                  '当期期末欠款',
       round(s.invoiced_amount_include_current, 2) AS                                                  '当期销售金额（含税）',
       qpc.payment_cycle_q1                        AS                                                  '回款周期（一季度）',
       qpc.payment_cycle_q2                        AS                                                  '回款周期（二季度）',
       qpc.payment_cycle_q3                        AS                                                  '回款周期（三季度）',
       qpc.payment_cycle_q4                        AS                                                  '回款周期（四季度）',
       ypc.payment_cycle_year                      AS                                                  '回款周期（全年）',
       ct.payment_cycle_target                     AS                                                  '考核指标',
       ct.payment_cycle_target -
       (ifnull(qpc.payment_cycle_q1,0) + ifnull(qpc.payment_cycle_q2,0) +ifnull( qpc.payment_cycle_q3,0)  + ifnull(qpc.payment_cycle_q4,0) ) / 4 '差异',
       year(now()) metric_year
FROM customer_target ct
         LEFT JOIN payment_data p ON p.cust_code = ct.cust_code
         LEFT JOIN outstanding_data o ON p.cust_code = o.cust_code
         LEFT JOIN current_sales s ON p.cust_code = s.cust_code
         LEFT JOIN quarterly_payment_cycle qpc ON p.cust_code = qpc.cust_code
         LEFT JOIN yearly_payment_cycle ypc ON p.cust_code = ypc.cust_code
where ct.cust_code is not null
ORDER BY round(p.year_payment_amount, 2) desc;

-- ETL建表语句
drop table dws_payment_cycle_metric;
CREATE TABLE dws_payment_cycle_metric
(
    `id` int(11) NOT NULL AUTO_INCREMENT primary key COMMENT 'id',
    -- 基础信息
    sale_company                    VARCHAR(50)   COMMENT '销售公司',
    deparment_code                  VARCHAR(50)   COMMENT '部门编码',
    deparment_name                  VARCHAR(100)  COMMENT '部门名称',
    region                          VARCHAR(50) COMMENT '区域',
    cust_code                       VARCHAR(50)  NOT NULL COMMENT '客户编码',
    cust_name                       VARCHAR(100)  COMMENT '客户名称',
    cust_manager_code               VARCHAR(50)   COMMENT '客户经理编码',
    cust_manager_name               VARCHAR(50)   COMMENT '客户经理姓名',
    cust_type                       TINYINT       COMMENT '客户类型(0:新建客户,1:公海客户,2:合作客户,3:开发中客户,4:受限客户)',
    cust_type_name                  VARCHAR(20)   COMMENT '客户类型名称',

    -- 金额相关
    year_payment_amount             DECIMAL(18, 2) DEFAULT 0 COMMENT '当年累计回款金额',
    quarter_payment_amount          DECIMAL(18, 2) DEFAULT 0 COMMENT '当期累计回款金额',
    month_payment_amount            DECIMAL(18, 2) DEFAULT 0 COMMENT '当月累计汇款金额',
    begin_outstanding_amount        DECIMAL(18, 2) DEFAULT 0 COMMENT '当期期初欠款',
    end_outstanding_amount          DECIMAL(18, 2) DEFAULT 0 COMMENT '当期期末欠款',
    invoiced_amount_include_current DECIMAL(18, 2) DEFAULT 0 COMMENT '当期销售金额（含税）',

    -- 回款周期指标
    payment_cycle_q1                DECIMAL(10, 4) COMMENT '回款周期（一季度）',
    payment_cycle_q2                DECIMAL(10, 4) COMMENT '回款周期（二季度）',
    payment_cycle_q3                DECIMAL(10, 4) COMMENT '回款周期（三季度）',
    payment_cycle_q4                DECIMAL(10, 4) COMMENT '回款周期（四季度）',
    payment_cycle_year              DECIMAL(10, 4) COMMENT '回款周期（全年）',
    payment_cycle_target            DECIMAL(10, 4) COMMENT '考核指标',
    cycle_difference                DECIMAL(10, 4) COMMENT '差异',
    metric_year                     varchar(4) comment '指标年份',

    -- 创建索引
    INDEX idx_cust_code (cust_code),
    INDEX idx_manager (cust_manager_code),
    INDEX idx_department (deparment_code)
)  COMMENT ='回款周期指标报表';

-- ETL:CRM回款周期指标报表->dws_payment_cycle_metric

select * from cockpit.dws_payment_cycle_metric
# where if(:admin,
#          1,
#          if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
#       )
#   AND ((:deparment_code IS NULL OR :deparment_code = '') OR (deparment_code = :deparment_code))
#   AND ((:metric_year IS NULL OR :metric_year = '') OR (metric_year = :metric_year))
#   AND ((:region IS NULL OR :region = '') OR (region = :region))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (cust_code like concat('%', :cust_code, '%')))
#   AND ((:cust_name IS NULL OR :cust_name = '') OR (cust_name like concat('%', :cust_name, '%')))
#   AND if(:cust_manager_size>0, cust_manager_code in (:cust_manager_arr), 1)
# order by id asc
# limit :page_size offset :offset
;
