-- 隐私客户
WITH t2 AS (
    SELECT '拜耳医药保健有限公司' AS cust_name UNION ALL
    SELECT '拜耳启东' UNION ALL
    SELECT '西安杨森制药有限公司' UNION ALL
    SELECT '云南贝泰妮生物科技集团股份有限公司' UNION ALL
    SELECT '山西锦波生物医药股份有限公司' UNION ALL
    SELECT '吉林瑞尔康光学科技有限公司' UNION ALL
    SELECT '西安科诗美光学科技有限公司' UNION ALL
    SELECT '乐福思健康产业股份公司' UNION ALL
    SELECT '武汉维奥制药有限公司' UNION ALL
    SELECT '杭州康恩贝' UNION ALL
    SELECT '通化东宝药业股份有限公司' UNION ALL
    SELECT '瑞盛生物' UNION ALL
    SELECT '西安巨子生物基因技术股份有限公司' UNION ALL
    SELECT '甘肃康视达科技集团有限公司' UNION ALL
    SELECT '成都蓉生药业有限责任公司' UNION ALL
    SELECT '绽妍生物科技有限公司' UNION ALL
    SELECT '合肥恩瑞特' UNION ALL
    SELECT '长春圣博玛生物材料有限公司' UNION ALL
    SELECT '迈科康' UNION ALL
    SELECT '长春生物制品研究所有限责任公司' UNION ALL
    SELECT '肌理研研' UNION ALL
    SELECT '雅泰乳业' UNION ALL
    SELECT '润玉医疗' UNION ALL
    SELECT '齐鲁制药有限公司' UNION ALL
    SELECT '北京韩美药品有限公司' UNION ALL
    SELECT '安斯泰来制药（中国）有限公司' UNION ALL
    SELECT '北京费森尤斯卡比医药有限公司' UNION ALL
    SELECT '华兰生物疫苗股份有限公司' UNION ALL
    SELECT '辽宁成大生物股份有限公司' UNION ALL
    SELECT '长春金赛药业有限责任公司' UNION ALL
    SELECT '北京民海生物科技有限公司' UNION ALL
    SELECT '石家庄以岭药业股份有限公司' UNION ALL
    SELECT '北京华素制药股份有限公司' UNION ALL
    SELECT '正大天晴集团' UNION ALL
    SELECT '贵州神奇药业有限公司' UNION ALL
    SELECT '河南羚锐制药股份有限公司' UNION ALL
    SELECT '通化万通药业股份有限公司' UNION ALL
    SELECT '山东新华制药股份有限公司' UNION ALL
    SELECT '陕西华西制药股份有限公司' UNION ALL
    SELECT '长春白求恩' UNION ALL
    SELECT '山东博士伦' UNION ALL
    SELECT '上海汇伦' UNION ALL
    SELECT '清华德人西安幸福制药有限公司' UNION ALL
    SELECT '四川科伦药业股份有限公司' UNION ALL
    SELECT 'GSK中美史克' UNION ALL
    SELECT '梓橦宫药业' UNION ALL
    SELECT '天津武田药品有限公司' UNION ALL
    SELECT '陕西摩美得' UNION ALL
    SELECT '赛诺菲（杭州）制药有限公司' UNION ALL
    SELECT '赛诺菲（北京）制药有限公司' UNION ALL
    SELECT '西安凯鑫科半导体材料有限公司' UNION ALL
    SELECT '常州四药制药有限公司' UNION ALL
    SELECT '上海生物制品研究所有限责任公司' UNION ALL
    SELECT '江西青峰药业有限公司' UNION ALL
    SELECT '博瑞制药（苏州）有限公司' UNION ALL
    SELECT '陕西佰傲再生医学有限公司' UNION ALL
    SELECT '西安海欣制药有限公司' UNION ALL
    SELECT '广东一品红' UNION ALL
    SELECT '武汉生物所' UNION ALL
    SELECT '万特制药' UNION ALL
    SELECT '江苏德全' UNION ALL
    SELECT '海南赞邦' UNION ALL
    SELECT '恒昌医药集团' UNION ALL
    SELECT '北京北陆药业股份有限公司' UNION ALL
    SELECT '厦门万泰沧海' UNION ALL
    SELECT '拜耳医药保健有限公司' UNION ALL
    SELECT '拜耳医药保健有限公司启东分公司' UNION ALL
    SELECT '拜耳医药保健有限公司广州分公司' UNION ALL
    SELECT '滇虹药业集团股份有限公司' UNION ALL
    SELECT '南京白敬宇制药有限责任公司' UNION ALL
    SELECT '诺和诺德(中国)制药有限公司'
)
select distinct  t1.name from mes_customer_archives t1
join  t2 on t1.name like concat('%',t2.cust_name,'%');
