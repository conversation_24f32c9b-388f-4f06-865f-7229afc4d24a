-- 当年累计回款金额	当期累计回款金额	当月累计汇款金额	当期期初欠款	当期期末欠款	当期销售金额（含税）	回款周期（一季度）	回款周期（二季度）	回款周期（三季度）	回款周期（四季度）	回款周期（全年）	考核指标

WITH payment_data AS (
    -- 当年,当期,当月回款金额
    SELECT
        cust_code,
        SUM(CASE
                WHEN billdate >= DATE_FORMAT(NOW(), '%Y-01-01') THEN money
                ELSE 0
            END) AS year_payment_amount,
        SUM(CASE
                WHEN billdate >= DATE_SUB(DATE_FORMAT(NOW(), '%Y-%m-01'),
                                          INTERVAL (MONTH(NOW()) - 1) % 3 MONTH)
                    THEN money
                ELSE 0
            END) AS quarter_payment_amount,
        SUM(CASE
                WHEN billdate >= DATE_FORMAT(NOW(), '%Y-%m-01') THEN money
                ELSE 0
            END) AS month_payment_amount
    FROM crm_sales_received_payments
    WHERE billdate >= DATE_FORMAT(NOW(), '%Y-01-01')
      AND flag_deleted = 0
    GROUP BY cust_code
),
     outstanding_data AS (
         -- 期初,期末欠款
         SELECT
             cust_code,
             SUM(CASE
                     WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(
                             DATE_SUB(
                                     DATE_FORMAT(NOW(), '%Y-%m-01'),
                                     INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
                             ),
                             '%Y-%m'
                                                              ) THEN qkbbbye
                     ELSE 0
                 END) AS begin_outstanding_amount,
             SUM(CASE
                     WHEN date_format(create_time, '%Y-%m') = date_format(now(), '%Y-%m')
                         THEN qkbbbye
                     ELSE 0
                 END) AS end_outstanding_amount
         FROM crm_account_receivable_age
         WHERE date_format(create_time, '%Y-%m') IN (
                                                     DATE_FORMAT(
                                                             DATE_SUB(
                                                                     DATE_FORMAT(NOW(), '%Y-%m-01'),
                                                                     INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
                                                             ),
                                                             '%Y-%m'
                                                     ),
                                                     date_format(now(), '%Y-%m')
             )
           AND flag_deleted = 0
         GROUP BY cust_code
     ),
     current_sales AS (
         -- 当期销售开票金额
         SELECT
             customer_no AS cust_code,
             ROUND(SUM(invoiced_amount_include), 2) AS invoiced_amount_include_current
         FROM bip_outbound_order_detail bood
                  LEFT JOIN (
             SELECT
                 csrcid,
                 csrcbid,
                 SUM(norigtaxmny) invoiced_amount_include,
                 DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
             FROM crm_sales_invoice_details
             WHERE flag_deleted = 0
             GROUP BY csrcid, csrcbid, dbilldate
         ) csid ON bood.outbound_header = csid.csrcid
             AND bood.outbound_line_id = csid.csrcbid
         WHERE bood.flag_deleted = 0
           AND invoiced_date >= DATE_FORMAT(
                 DATE_SUB(
                         DATE_FORMAT(NOW(), '%Y-%m-01'),
                         INTERVAL (MONTH(NOW()) - 1) % 3 MONTH
                 ),
                 '%Y-%m-01'
                                )
           AND invoiced_date < DATE_FORMAT(now(),'%Y-%m-%d')
         GROUP BY customer_no
     ),
     quarterly_payment_cycle AS (
         -- 季度回款周期
         WITH quarterly_outstanding AS (
             -- 获取每个季度的欠款数据
             SELECT
                 cust_code,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-01')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-03')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_1,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-04')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-06')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_2,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-07')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-09')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_3,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-10')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-12')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_4
             FROM crm_account_receivable_age
             WHERE flag_deleted = 0
               AND date_format(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
             GROUP BY cust_code
         ),
              quarterly_invoice AS (
                  -- 获取每个季度的开票金额
                  SELECT
                      customer_no AS cust_code,
                      ROUND(SUM(CASE
                                    WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                                        AND invoiced_date < DATE_FORMAT(NOW(), '%Y-04-01')
                                        THEN invoiced_amount_include
                                    ELSE 0
                          END), 2) AS invoiced_amount_include_1,
                      ROUND(SUM(CASE
                                    WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-04-01')
                                        AND invoiced_date < DATE_FORMAT(NOW(), '%Y-07-01')
                                        THEN invoiced_amount_include
                                    ELSE 0
                          END), 2) AS invoiced_amount_include_2,
                      ROUND(SUM(CASE
                                    WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-07-01')
                                        AND invoiced_date < DATE_FORMAT(NOW(), '%Y-10-01')
                                        THEN invoiced_amount_include
                                    ELSE 0
                          END), 2) AS invoiced_amount_include_3,
                      ROUND(SUM(CASE
                                    WHEN invoiced_date >= DATE_FORMAT(NOW(), '%Y-10-01')
                                        AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                                        THEN invoiced_amount_include
                                    ELSE 0
                          END), 2) AS invoiced_amount_include_4
                  FROM bip_outbound_order_detail bood
                           LEFT JOIN (
                      SELECT
                          csrcid,
                          csrcbid,
                          SUM(norigtaxmny) invoiced_amount_include,
                          DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
                      FROM crm_sales_invoice_details
                      WHERE flag_deleted = 0
                      GROUP BY csrcid, csrcbid, dbilldate
                  ) csid ON bood.outbound_header = csid.csrcid
                      AND bood.outbound_line_id = csid.csrcbid
                  WHERE bood.flag_deleted = 0
                    AND invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                    AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                  GROUP BY customer_no
              )

         -- 计算每个季度的回款周期指标
         SELECT
             o.cust_code,
             -- 第一季度回款周期
             CASE
                 WHEN i.invoiced_amount_include_1 = 0 THEN NULL
                 ELSE ROUND((o.outstanding_amount_1 / 180) / NULLIF(i.invoiced_amount_include_1, 0), 4)
                 END AS payment_cycle_q1,
             -- 第二季度回款周期
             CASE
                 WHEN i.invoiced_amount_include_2 = 0 THEN NULL
                 ELSE ROUND((o.outstanding_amount_2 / 180) / NULLIF(i.invoiced_amount_include_2, 0), 4)
                 END AS payment_cycle_q2,
             -- 第三季度回款周期
             CASE
                 WHEN i.invoiced_amount_include_3 = 0 THEN NULL
                 ELSE ROUND((o.outstanding_amount_3 / 180) / NULLIF(i.invoiced_amount_include_3, 0), 4)
                 END AS payment_cycle_q3,
             -- 第四季度回款周期
             CASE
                 WHEN i.invoiced_amount_include_4 = 0 THEN NULL
                 ELSE ROUND((o.outstanding_amount_4 / 180) / NULLIF(i.invoiced_amount_include_4, 0), 4)
                 END AS payment_cycle_q4
         FROM quarterly_outstanding o
                  LEFT JOIN quarterly_invoice i ON o.cust_code = i.cust_code
         WHERE (i.invoiced_amount_include_1 > 0
             OR i.invoiced_amount_include_2 > 0
             OR i.invoiced_amount_include_3 > 0
             OR i.invoiced_amount_include_4 > 0)
     ),
     yearly_payment_cycle AS (
         -- 年度回款周期
         WITH yearly_outstanding AS (
             -- 年度欠款金额
             SELECT
                 cust_code,
                 ROUND(SUM(CASE
                               WHEN date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-01')
                                   OR date_format(create_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-12')
                                   THEN qkbbbye
                               ELSE 0
                     END), 2) AS outstanding_amount_year
             FROM crm_account_receivable_age
             WHERE flag_deleted = 0
               AND date_format(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
             GROUP BY cust_code
         ),
              yearly_invoice AS (
                  -- 年度开票金额
                  SELECT
                      customer_no cust_code,
                      ROUND(SUM(invoiced_amount_include), 2) AS invoiced_amount_include_year
                  FROM bip_outbound_order_detail bood
                           LEFT JOIN (
                      SELECT
                          csrcid,
                          csrcbid,
                          SUM(norigtaxmny) invoiced_amount_include,
                          DATE_FORMAT(MIN(dbilldate), '%Y-%m-%d') invoiced_date
                      FROM crm_sales_invoice_details
                      WHERE flag_deleted = 0
                      GROUP BY csrcid, csrcbid, dbilldate
                  ) csid ON bood.outbound_header = csid.csrcid
                      AND bood.outbound_line_id = csid.csrcbid
                  WHERE bood.flag_deleted = 0
                    AND invoiced_date >= DATE_FORMAT(NOW(), '%Y-01-01')
                    AND invoiced_date < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL 1 YEAR), '%Y-01-01')
                  GROUP BY customer_no
              )

         -- 计算年度回款周期指标
         SELECT
             o.cust_code,
             CASE
                 WHEN i.invoiced_amount_include_year = 0 THEN NULL
                 ELSE ROUND((o.outstanding_amount_year / 720) / NULLIF(i.invoiced_amount_include_year, 0), 4)
                 END AS payment_cycle_year
         FROM yearly_outstanding o
                  LEFT JOIN yearly_invoice i ON o.cust_code = i.cust_code
         WHERE i.invoiced_amount_include_year > 0
     ),
     customer_target AS (
         -- 客户信息及考核指标
         SELECT
             '西安环球'                                                                                           sale_company,
             t1.deparment_code,
             t1.deparment_name,
             t1.department_region region,
             t1.cust_code,
             t1.cust_name,
             t1.cust_manager_code,
             t1.cust_manager_name,
             t1.cust_type,
             CASE
                 WHEN t1.cust_type = 0 THEN '新建客户'
                 WHEN t1.cust_type = 1 THEN '公海客户'
                 WHEN t1.cust_type = 2 THEN '合作客户'
                 WHEN t1.cust_type = 3 THEN '开发中客户'
                 WHEN t1.cust_type = 4 THEN '受限客户'
                 END                                                                                              cust_type_name,
             t2.payment_cycle_target  -- 考核指标
         FROM
             (WITH RankedCustomers AS (
                 SELECT
                     *,
                     ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
                 FROM crm_cust_basic where flag_deleted=0 and cust_type not in (0)
             )
              SELECT
                  *
              FROM RankedCustomers
              WHERE rn = 1)t1
                 LEFT JOIN metric_person t2 ON t1.cust_manager_code=t2.cust_manager_code AND t2.flag_deleted=0
       and t2.metric_year=year(now())
     )

-- 最终报表：按cust_code关联所有数据
SELECT
    ct.sale_company,
    ct.deparment_code,
    ct.deparment_name,
    ct. region,
    ct.cust_code,
    ct.cust_name,
    ct.cust_manager_code,
    ct.cust_manager_name,
    ct.cust_type,
    CASE
        WHEN ct.cust_type = 0 THEN '新建客户'
        WHEN ct.cust_type = 1 THEN '公海客户'
        WHEN ct.cust_type = 2 THEN '合作客户'
        WHEN ct.cust_type = 3 THEN '开发中客户'
        WHEN ct.cust_type = 4 THEN '受限客户'
        END                                                                                              cust_type_name,
    p.year_payment_amount AS '当年累计回款金额',
    p.quarter_payment_amount AS '当期累计回款金额',
    p.month_payment_amount AS '当月累计汇款金额',
    o.begin_outstanding_amount AS '当期期初欠款',
    o.end_outstanding_amount AS '当期期末欠款',
    s.invoiced_amount_include_current AS '当期销售金额（含税）',
    qpc.payment_cycle_q1 AS '回款周期（一季度）',
    qpc.payment_cycle_q2 AS '回款周期（二季度）',
    qpc.payment_cycle_q3 AS '回款周期（三季度）',
    qpc.payment_cycle_q4 AS '回款周期（四季度）',
    ypc.payment_cycle_year AS '回款周期（全年）',
    ct.payment_cycle_target AS '考核指标',
    ct.payment_cycle_target-(qpc.payment_cycle_q1+qpc.payment_cycle_q2+qpc.payment_cycle_q3+qpc.payment_cycle_q4)/4 '差异'
FROM customer_target ct
         LEFT JOIN payment_data p ON p.cust_code = ct.cust_code
         LEFT JOIN outstanding_data o ON p.cust_code = o.cust_code
         LEFT JOIN current_sales s ON p.cust_code = s.cust_code
         LEFT JOIN quarterly_payment_cycle qpc ON p.cust_code = qpc.cust_code
         LEFT JOIN yearly_payment_cycle ypc ON p.cust_code = ypc.cust_code
where o.cust_code is not null
ORDER BY p.cust_code;
