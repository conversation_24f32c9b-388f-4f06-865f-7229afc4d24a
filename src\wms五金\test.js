select  '西安环球'                                                                            sale_company,
        deparment_code,
        deparment_name,
        department_region region,
        sales_assistant_code,
        sales_assistant_name,
        cust_manager_code,
        cust_manager_name,
        cust_type,
        CASE
            WHEN cust_type = 0 THEN '新建客户'
            WHEN cust_type = 1 THEN '公海客户'
            WHEN cust_type = 2 THEN '合作客户'
            WHEN cust_type = 3 THEN '开发中客户'
            WHEN cust_type = 4 THEN '受限客户'
            END                                                                  cust_type_name,
        cust_code,
        cust_name,
        sales_order_code,
        factory_assigned,
        date_format(create_time, '%Y-%m-%d') sale_order_date,
        temp1.main_class,
        temp1.sub_class,
        temp1.materialcode material_code,
        temp1.materialname material_name,
        order_quantity_after_split                                                      order_quantity,       -- 订单数量
        order_code                                                                            production_order,     -- 生产单号
        vbatchcode                                                                            pk_batchcode,         -- 入库批号
        date_format(dinbounddate, '%Y-%m-%d')                                                 inventory_date,       -- 入库日期
        nonhandnum                                                                       qty,                  -- 在库数量
        round(unit_price_no, 4)                                        unit_price_exclusive, -- 未含税单价
        round(nonhandnum * unit_price_no, 2)                           inventory_cost,       -- 库存成本
        datediff(current_date, dinbounddate) item_age,                    -- 库龄
        temp2.product_version,
        temp2.remark
from (
      select distinct t1.vbatchcode,t1.materialcode,t1.materialname,t1.nonhandnum,t1.dinbounddate,t2.erp_production_order,t2.order_code, t2.main_class,t2.sub_class,t2.unit_price_no,t2.bip_detail_no
      from (select vbatchcode,
                   materialcode,
                   materialname,
                   sum(nonhandnum)   nonhandnum,
                   min(dinbounddate) dinbounddate
            from ods_bip_inventory t1
            where  materialcode in (select product_code from ods_pdm_product_client where flag_deleted=0 group by product_code)
            group by vbatchcode, materialcode,materialname) t1
               left join (select distinct t3.major_categories,
                                          t4.data_value main_class,
                                          t3.subclass,
                                          t5.data_value sub_class,
                                          t3.unit_price_no,
                                          t2.production_batch_number,
                                          t2.order_code,
                                          t1.erp_production_order,
                                          t2.material_code,
                                          t3.bip_detail_no
                          from ods_pm_sale_order t1
                                   left join ods_pm_job_order t2 on t2.order_code = t1.order_number and t2.flag_deleted = 0
                                   left join ods_pm_order_product t3
                                             on t2.order_code = t3.sales_number and
                                                t3.id=t2.source_detail_id and
                                                t2.material_code = t3.product_number and
                                                t3.flag_deleted = 0
                                   left join ods_mes_parameter_data t4 on t4.id=t3.major_categories
                                   left join ods_mes_parameter_data t5 on t5.id=t3.subclass) t2
                         on t1.vbatchcode = t2.production_batch_number and t1.materialcode = t2.material_code
               )temp1 left join
    (select distinct t3.deparment_code,
            t3.deparment_name,
            t3.department_region,
            t3.sales_assistant_code,
            t3.sales_assistant_name,
            t3.cust_manager_code,
            t3.cust_manager_name,
            t3.cust_type,
            t3.cust_code,
            t3.cust_name,
            t1.sales_order_code,
            t1.factory_assigned,
            t1.create_time,
            t2.main_class,
            t2.sub_class,
            t2.material_code,
            t2.material_name,
            t2.product_version,
            t2.unit_price_exclusive,
            t2.exchange_rate,
            sum(order_quantity_after_split) order_quantity_after_split,
            t2.remark,
            t2.csaleorderbid
     from ods_crm_sales_order t1
              left join cockpit.ods_crm_sales_order_product t2
                        on t1.sales_order_code = t2.sales_order_code and t2.flag_deleted = 0
              left join cockpit.ods_crm_cust_basic t3
                        on t3.cust_code = t1.cust_code and t3.flag_deleted = 0 and t3.cust_type not in (0, 1) and
                           cust_status = 2
     where t1.flag_deleted = 0
     group by t2.sales_order_code, t2.material_code,
              t3.deparment_code,
              t3.deparment_name,
              t3.department_region,
              t3.sales_assistant_code,
              t3.sales_assistant_name,
              t3.cust_manager_code,
              t3.cust_manager_name,
              t3.cust_type,
              t3.cust_code,
              t3.cust_name,
              t1.factory_assigned,
              t1.create_time,
              t2.main_class,
              t2.sub_class,
              t2.material_name,
              t2.unit_price_exclusive,
              t2.exchange_rate)temp2 on temp1.materialcode = temp2.material_code
and temp1.erp_production_order=temp2.sales_order_code
and temp1.bip_detail_no=temp2.csaleorderbid
order by dinbounddate desc