var data_arr = [
    {"payment_cycle": 10, "quater": "1", "year": 2024},
    {"payment_cycle": 20, "quater": "2", "year": 2024},
    {"payment_cycle": 30, "quater": "3", "year": 2024},
    {"payment_cycle": 40, "quater": "4", "year": 2024},
    {"payment_cycle": 50, "quater": "1", "year": 2025},
    {"payment_cycle": 60, "quater": "2", "year": 2025},
    {"payment_cycle": 70, "quater": "3", "year": 2025},
    {"payment_cycle": 80, "quater": "4", "year": 2025}
];

var years = data_arr.map(function(item) {
    return item.year;
}).filter(function(value, index, self) {
    return self.indexOf(value) === index;
}).sort();

var option = {
    title: {
        text: "销售公司回款周期指标",
        left: "center"
    },
    tooltip: {
        trigger: "axis",
        axisPointer: {
            type: "line"
        }
    },
    legend: {
        data: years.reduce(function(acc, year) {
            acc.push(year + "年柱状图", year + "年趋势线");
            return acc;
        }, []),
        bottom: "5%",
        itemGap: 20,
        textStyle: {
            fontSize: 12,
            color: "#666"
        }
    },
    grid: {
        left: "3%",
        right: "4%",
        bottom: "12%",
        top: "10%",
        containLabel: true
    },
    xAxis: {
        type: "category",
        boundaryGap: true,
        data: ["1", "2", "3", "4"],
        name: "季度",
    },
    yAxis: [{
        type: "value",
        name: "回款周期（天）",
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
            formatter: "{value}%",
            margin: 20,
            align: "right"
        },
        nameGap: 40
    }],
    series: years.reduce(function(acc, year, index) {
        var yearData = data_arr.filter(function(item) {
            return item.year === year;
        }).map(function(item) {
            return item.payment_cycle;
        });

        // 添加柱状图
        acc.push({
            name: year + "年柱状图",
            type: "bar",
            barWidth: "25%",
            barGap: "30%",
            data: yearData,
            itemStyle: {
                color: index === 0 ? "#5470c6" : "#91cc75",
                opacity: 0.8
            },
            label: {
                show: true,
                position: "top",
                formatter: "{c}%"
            }
        });

        // 添加趋势线
        acc.push({
            name: year + "年趋势线",
            type: "line",
            smooth: true,
            data: yearData,
            itemStyle: {
                color: index === 0 ? "#5470c6" : "#91cc75",
                opacity: 0.8
            },
            lineStyle: {
                width: 2,
                type: "dashed",
                opacity: 0.8
            },
            symbol: "circle",
            symbolSize: 6,
            showSymbol: false
        });

        return acc;
    }, [])
};

// 添加目标线
option.markLine = {
    data: [{
        name: "目标线",
        yAxis: 90,
        lineStyle: {
            color: "#fac858",
            type: "solid",
            width: 2,
            opacity: 0.8
        },
        label: {
            formatter: "目标线"
        }
    }]
};
return option;
