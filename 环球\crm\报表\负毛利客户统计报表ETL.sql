-- 负毛利客户统计
select '西安环球'                                                sale_company,
       deparment_code,
       deparment_name,
       department_region                                         region,
       cust_code,
       cust_name,
       cust_manager_code,
       cust_manager_name,
       sales_assistant_code,
       cust_type,
       cust_type_name,
       round(sum(profit_amount), 2)                              profit_amount,
       round(100 * sum(profit_amount) / sum(invoiced_amount), 4) profit_ratio,
       round(sum(marginal_contribution_amount), 2)               marginal_contribution_amount,
       LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) AS        last_month_end
from (SELECT t3.deparment_code,
             t3.deparment_name,
             t3.department_region,
             t3.cust_code,
             t3.cust_name,
             t3.cust_manager_code,
             t3.cust_manager_name,
             t3.sales_assistant_code,
             t3.cust_type,
             CASE
                 WHEN t3.cust_type = 0 THEN '新建客户'
                 WHEN t3.cust_type = 1 THEN '公海客户'
                 WHEN t3.cust_type = 2 THEN '合作客户'
                 WHEN t3.cust_type = 3 THEN '开发中客户'
                 WHEN t3.cust_type = 4 THEN '受限客户'
                 END                       cust_type_name,
             ifnull(t4.invoiced_amount, 0) invoiced_amount,
             round(ifnull(t4.invoiced_amount, 0) - t5.standard_cost_unit_price * ifnull(t4.invoiced_quantity, 0),
                   2)                      profit_amount,
             t4.invoiced_date,
             ifnull(t5.marginal_contribution_amount,0)                          marginal_contribution_amount
      FROM crm_sales_order t1
               LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
               LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
               LEFT JOIN (select split_order_no            sales_order_code,
                                 product_code              material_code,
                                 split_order_line_no,
                                 group_concat(outbound_no) shipment_code,
                                 min(dbilldate)            invoiced_date,
                                 max(outbound_date)        shipment_date,
                                 sum(ship_quantity)        shipped_quantity,
                                 sum(ifnull(nnum, 0))      invoiced_quantity,
                                 sum(ifnull(norigmny, 0))  invoiced_amount
                          from bip_outbound_order_detail bood
                                   join (select csrcid, csrcbid, dbilldate, sum(nnum) nnum, sum(norigmny) norigmny
                                         from crm_sales_invoice_details
                                         where flag_deleted = 0
                                         group by csrcid, csrcbid, dbilldate) csid
                                        on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
                          where bood.flag_deleted = 0
                          group by sales_order_code, material_code, split_order_line_no) t4
                         on t4.sales_order_code = t1.sales_order_code and t2.material_code = t4.material_code
                             and t2.contract_product_line_number = t4.split_order_line_no
               left join (select t2.standard_cost_unit_price,
                                 t1.contract_management_code,
                                 t1.contract_product_line_number,
                                 t2.marginal_contribution_amount
                          from crm_contract_management_product t1
                                   join crm_preliminary_quotation_product t2
                                        on t1.quotation_product_id = t2.id and t2.flag_deleted = 0
                          where t1.flag_deleted = 0) t5 on t1.contract_management_code = t5.contract_management_code
          and t2.contract_product_line_number = t5.contract_product_line_number
      WHERE t1.flag_deleted = 0
        AND t2.flag_deleted = 0
        AND t3.flag_deleted = 0
        and t3.cust_status = 2
        and t3.cust_type not in (0, 1)
        AND t1.status not in (8)) temp
where date_format(invoiced_date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')
group by deparment_code, deparment_name, department_region, cust_code, cust_name, cust_manager_code, cust_manager_name,
         sales_assistant_code, cust_type, cust_type_name
having sum(profit_amount) < 0
;

-- ETL建表语句
CREATE TABLE dws_negative_profit_customer
(
    `id`                         bigint NOT NULL AUTO_INCREMENT primary key COMMENT '主键ID',
    sale_company                 VARCHAR(50) COMMENT '销售公司',
    deparment_code               VARCHAR(50) COMMENT '部门编码',
    deparment_name               VARCHAR(100) COMMENT '部门名称',
    region                       VARCHAR(50) COMMENT '区域',
    cust_code                    VARCHAR(50) COMMENT '客户编码',
    cust_name                    VARCHAR(200) COMMENT '客户名称',
    cust_manager_code            VARCHAR(50) COMMENT '客户经理编码',
    cust_manager_name            VARCHAR(100) COMMENT '客户经理姓名',
    sales_assistant_code         VARCHAR(50) COMMENT '销售助理编码',
    cust_type                    VARCHAR(50) COMMENT '客户类型编码',
    cust_type_name               VARCHAR(100) COMMENT '客户类型名称',
    profit_amount                DECIMAL(18, 2) COMMENT '利润金额',
    profit_ratio                 DECIMAL(10, 4) COMMENT '利润率',
    marginal_contribution_amount DECIMAL(18, 2) COMMENT '边际贡献额',
    last_month_end               DATE COMMENT '上月月末日期,统计截止日期'
) comment '负毛利客户统计报表';


select last_month_end,date_format(last_month_end,'%Y-%m') from cockpit.dws_negative_profit_customer ;
-- 我要获取今年和去年,每个月的利润金额-- Step 1: 获取 dim_month 中今年和去年的月份数据
SELECT
    DATE_FORMAT(last_month_end, '%Y-%m') AS month,
    YEAR(last_month_end) AS year,
    SUM(profit_amount) AS total_profit_amount
FROM
    dws_negative_profit_customer
WHERE
    YEAR(last_month_end) IN (YEAR(CURDATE()), YEAR(CURDATE()) - 1)
GROUP BY
    YEAR(last_month_end), DATE_FORMAT(last_month_end, '%Y-%m')
ORDER BY
    year, month;

select year,month,`year_month` from dim_month dm;

-- 获取 dim_month 中今年和去年的月份数据
SELECT
    md.`year`,
    md.`month`,
    COALESCE(pd.total_profit_amount, 0) AS total_profit_amount
FROM (
    SELECT
        `year`,
        `month`,
        `year_month`
    FROM
        cockpit.dim_month
    WHERE
        `year` IN (YEAR(CURDATE()), YEAR(CURDATE()) - 1)
) md
LEFT JOIN (
    SELECT
        DATE_FORMAT(last_month_end, '%Y-%m') AS month,
        YEAR(last_month_end) AS year,
        SUM(profit_amount) AS total_profit_amount
    FROM
        cockpit.dws_negative_profit_customer
    WHERE
        YEAR(last_month_end) IN (YEAR(CURDATE()), YEAR(CURDATE()) - 1)
    GROUP BY
        YEAR(last_month_end), DATE_FORMAT(last_month_end, '%Y-%m')
) pd
ON md.`year` = pd.year
AND md.year_month = pd.month
ORDER BY
    md.`year`, md.`month`;




