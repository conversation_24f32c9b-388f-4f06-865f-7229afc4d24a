--
SELECT
    pop.bip_detail_no factory_details_id,
    COALESCE(pso.plan_delivery_date, '') delivery_date,
    pjo.production_batch_number,
    pop.product_number,
    0 stock_quantity,
    pjo.version,
    pop.product_version
FROM
    pm_sale_order pso
        LEFT JOIN pm_order_product pop ON
        pop.sales_order_id = pso.id
        LEFT JOIN pm_job_order pjo ON
        pjo.order_code = pso.order_number
        AND pjo.material_code = pop.product_number
           -- AND pjo.version = pop.product_version
WHERE
    pso.flag_deleted = 0
  AND pop.flag_deleted = 0
  AND pop.bip_detail_no IN ('1001A510000000FYSYM4');
