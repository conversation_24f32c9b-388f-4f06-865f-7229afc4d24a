-- 来料统计
select DATE(t1.update_time) date,
       t2.inspection_tool,
       t3.material_code,
       t3.material_name,
       t3.supplier_batch
from cockpit.ods_mes_incoming_inspection t1
         left join cockpit.ods_mes_incoming_inspection_detail t2 on t1.id = t2.incoming_inspection_id
         left join cockpit.ods_mes_arrival_notice_particulars t3 on t1.mes_arrival_notice_particulars = t3.id
where
    t1.flag_deleted=0
  and t2.flag_deleted=0
  and t3.flag_deleted=0
  and  t1.inspection_type = '3'
  AND t1.status = '1'
  AND ((:start_date IS NULL OR :start_date = '') OR (DATE(t1.update_time) >= :start_date))
  AND ((:end_date IS NULL OR :end_date = '') OR (DATE(t1.update_time) <= :end_date))
  AND ((:inspection_tool IS NULL OR :inspection_tool = '') OR (inspection_tool like concat('%',:inspection_tool,'%')))
  AND ((:material_code IS NULL OR :material_code = '') OR (material_code like concat('%',:material_code,'%')))
  AND ((:material_name IS NULL OR :material_name = '') OR (material_name like concat('%',:material_name,'%')))
  AND ((:supplier_batch IS NULL OR :supplier_batch = '') OR (supplier_batch like concat('%',:supplier_batch,'%')))
order by date asc
LIMIT :page_size OFFSET :offset
;

-- 检验统计
select distinct
       date(t1.sys_create_time) date,
       t2.inspection_tool,
       t1.production_batch_number,
       t1.product_name
from cockpit.ods_process_inspection t1
         left join cockpit.ods_mes_gc_new_template_project t2 on t2.gc_id = t1.id
where
    t1.flag_deleted=0
  and t2.flag_deleted=0
  and t1.z_status = 4
  and t2.type = 2
  AND ((:start_date IS NULL OR :start_date = '') OR (DATE(t1.sys_create_time) >= :start_date))
  AND ((:end_date IS NULL OR :end_date = '') OR (DATE(t1.sys_create_time) <= :end_date))
  AND ((:inspection_tool IS NULL OR :inspection_tool = '') OR (inspection_tool like concat('%', :inspection_tool, '%')))
  AND ((:production_batch_number IS NULL OR :production_batch_number = '') OR
       (production_batch_number like concat('%', :production_batch_number, '%')))
  AND ((:product_name IS NULL OR :product_name = '') OR (product_name like concat('%', :product_name, '%')))
order by date asc
LIMIT :page_size OFFSET :offset
;

select count(*) from ods_mes_arrival_notice_particulars omii;
