-- ETL运营部-待产订单->dwd_operate_pending_orders
-- 历史
SELECT COALESCE(ROUND(SUM(COALESCE(a.release_quantity, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')                                                            as time
FROM pm_job_order a
         LEFT JOIN pm_order_product b ON a.order_code = b.sales_number AND a.material_code = b.product_number
WHERE a.production_order_status = '0'
  and a.source_type = 1;

-- 待产订单:生产订单中未生成作业单且来源为销售订单的总金额 (生产订单)
select sum(value) value,DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i:%s') time
from (SELECT COALESCE(ROUND(SUM(COALESCE(a.release_quantity, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS value
      FROM pm_job_order a
               LEFT JOIN pm_order_product b ON a.order_code = b.sales_number AND a.material_code = b.product_number
      WHERE a.production_order_status in ('0','1')
        and a.source_type = 1
        and a.is_collage = 1
        and a.flag_deleted = 0
        and b.flag_deleted = 0
      UNION
      SELECT COALESCE
             (
                     ROUND(SUM(COALESCE(d.release_quantity, 0) * COALESCE(e.unit_price_no, 0)) / 10000, 0),
                     0
             ) AS
                 VALUE
      FROM pm_job_order_management a
               LEFT JOIN pm_job_order b ON b.production_batch_number = a.production_batch_number
               RIGHT JOIN pm_jigsaw_of_product c ON b.order_code = c.order_number
               LEFT JOIN pm_job_order d ON c.order_number = d.order_code
               LEFT JOIN pm_order_product e ON d.order_code = e.sales_number
      WHERE a.is_collage = 0
        AND a.STATUS = 2
        AND d.material_code = e.product_number
        AND d.source_type = 1
        and b.production_order_status in ('0','1')
        and a.flag_deleted = 0
        and b.flag_deleted = 0
        and c.flag_deleted = 0
        and d.flag_deleted = 0
        and e.flag_deleted=0
        ) temp;


-- 待产订单:生产订单中未生成作业单且来源为销售订单的总金额 (生产订单)
select sum(value)    value,
       sum(quantity) quantity,
       major_categories
from (SELECT COALESCE(ROUND(SUM(COALESCE(a.release_quantity, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0),
                      0) AS                       value,
             SUM(COALESCE(a.release_quantity, 0)) quantity,
             a.large_category                     major_categories
      FROM pm_job_order a
               LEFT JOIN pm_order_product b ON a.order_code = b.sales_number AND a.material_code = b.product_number
      WHERE a.production_order_status in ('0', '1')
        and a.source_type = 1
        and a.is_collage = 1
        and a.flag_deleted = 0
        and b.flag_deleted = 0
        AND a.large_category IS NOT NULL
      GROUP BY a.large_category
      UNION
      SELECT COALESCE
             (
                     ROUND(SUM(COALESCE(d.release_quantity, 0) * COALESCE(e.unit_price_no, 0)) / 10000, 0),
                     0
             ) AS
                                                  VALUE,
             SUM(COALESCE(a.release_quantity, 0)) quantity,
             b.large_category                     major_categories
      FROM pm_job_order_management a
               LEFT JOIN pm_job_order b ON b.production_batch_number = a.production_batch_number
               RIGHT JOIN pm_jigsaw_of_product c ON b.order_code = c.order_number
               LEFT JOIN pm_job_order d ON c.order_number = d.order_code
               LEFT JOIN pm_order_product e ON d.order_code = e.sales_number
      WHERE a.is_collage = 0
        AND a.STATUS = 2
        AND d.material_code = e.product_number
        AND d.source_type = 1
        and b.production_order_status in ('0', '1')
        and a.flag_deleted = 0
        and b.flag_deleted = 0
        and c.flag_deleted = 0
        and d.flag_deleted = 0
        and e.flag_deleted = 0
        AND b.large_category IS NOT NULL
      GROUP BY b.large_category) temp
group by major_categories;


SELECT
       JSON_ARRAYAGG(
               JSON_OBJECT(
                       'value', value,
                       'quantity', quantity,
                       'major_categories', major_categories
               )
       ) AS                                    value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') time

FROM (select sum(value)    value,
             sum(quantity) quantity,
             major_categories
      from (SELECT COALESCE(ROUND(SUM(COALESCE(a.release_quantity, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0),
                            0) AS                       value,
                   round(SUM(COALESCE(a.release_quantity, 0))/10000,0) quantity,
                   a.large_category                     major_categories
            FROM pm_job_order a
                     LEFT JOIN pm_order_product b
                               ON a.order_code = b.sales_number AND a.material_code = b.product_number
            WHERE a.production_order_status in ('0', '1')
              and a.source_type = 1
              and a.is_collage = 1
              and a.flag_deleted = 0
              and b.flag_deleted = 0
              AND a.large_category IS NOT NULL
            GROUP BY a.large_category
            UNION
            SELECT COALESCE
                   (
                           ROUND(SUM(COALESCE(d.release_quantity, 0) * COALESCE(e.unit_price_no, 0)) / 10000, 0),
                           0
                   ) AS
                                                        VALUE,
                   round(SUM(COALESCE(a.release_quantity, 0))/10000,0) quantity,
                   b.large_category                     major_categories
            FROM pm_job_order_management a
                     LEFT JOIN pm_job_order b ON b.production_batch_number = a.production_batch_number
                     RIGHT JOIN pm_jigsaw_of_product c ON b.order_code = c.order_number
                     LEFT JOIN pm_job_order d ON c.order_number = d.order_code
                     LEFT JOIN pm_order_product e ON d.order_code = e.sales_number
            WHERE a.is_collage = 0
              AND a.STATUS = 2
              AND d.material_code = e.product_number
              AND d.source_type = 1
              and b.production_order_status in ('0', '1')
              and a.flag_deleted = 0
              and b.flag_deleted = 0
              and c.flag_deleted = 0
              and d.flag_deleted = 0
              and e.flag_deleted = 0
              AND b.large_category IS NOT NULL
            GROUP BY b.large_category) temp
      group by major_categories) AS subquery;
