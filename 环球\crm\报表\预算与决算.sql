-- ETL:bip成本接口
drop table if exists ods_cost_report;
CREATE TABLE `ods_cost_report` (
                                   `water_fee` DECIMAL(10, 2) COMMENT '水费',
                                   `cmatercode` VARCHAR(255) COMMENT '产品编码',
                                   `production_equipment_repair_fee` DECIMAL(10, 2) COMMENT '生产设备修理费',
                                   `basic_endowment_insurance` DECIMAL(10, 2) COMMENT '基本养老保险',
                                   `labor_protection_fee` DECIMAL(10, 2) COMMENT '劳动保护费',
                                   `storage_fee` DECIMAL(10, 2) COMMENT '仓储费',
                                   `postal_telecommunication_fee` DECIMAL(10, 2) COMMENT '邮电通讯费',
                                   `xscode` VARCHAR(255) COMMENT '销售编码',
                                   `organization_construction_fee` DECIMAL(10, 2) COMMENT '组织建设费',
                                   `other_material_fee` DECIMAL(10, 2) COMMENT '其他材料费',
                                   `ccostobjectid` VARCHAR(255) primary key COMMENT '成本对象ID',
                                   `production_management_salary` DECIMAL(10, 2) COMMENT '生产管理人员工资',
                                   `machinery_material_consumption` DECIMAL(10, 2) COMMENT '机物料消耗',
                                   `electricity_fee` DECIMAL(10, 2) COMMENT '电费',
                                   `office_expenses` DECIMAL(10, 2) COMMENT '办公费',
                                   `natural_gas_fee` DECIMAL(10, 2) COMMENT '天然气费',
                                   `insurance_fee` DECIMAL(10, 2) COMMENT '保险费',
                                   `finished_product_collection` DECIMAL(10, 2) COMMENT '成品领用',
                                   `knife_mold` DECIMAL(10, 2) COMMENT '刀版模具',
                                   `direct_auxiliary_materials` DECIMAL(10, 2) COMMENT '直接辅料',
                                   `medical_insurance_fee` DECIMAL(10, 2) COMMENT '医疗保险费',
                                   `quality_loss` DECIMAL(10, 2) COMMENT '质量损失',
                                   `semi_finished_outsourcing_fee` DECIMAL(10, 2) COMMENT '半成品外协费',
                                   `depreciation_buildings` DECIMAL(10, 2) COMMENT '折旧费房屋建筑物类',
                                   `finished_outsourcing_fee` DECIMAL(10, 2) COMMENT '成品外协费',
                                   `public_materials` DECIMAL(10, 2) COMMENT '公共材料',
                                   `printing_plate` DECIMAL(10, 2) COMMENT '印版',
                                   `work_injury` DECIMAL(10, 2) COMMENT '工伤',
                                   `housing_provident_fund` DECIMAL(10, 2) COMMENT '住房公积金',
                                   `safety_production_fee` DECIMAL(10, 2) COMMENT '安全生产费',
                                   `environmental_protection_fee` DECIMAL(10, 2) COMMENT '环境保护费',
                                   `production_service_fee` DECIMAL(10, 2) COMMENT '生产服务费',
                                   `low_value_consumables_amortization` DECIMAL(10, 2) COMMENT '低耗品摊销',
                                   `meeting_expenses` DECIMAL(10, 2) COMMENT '会议费',
                                   `packaging_materials` DECIMAL(10, 2) COMMENT '包材',
                                   `paper` DECIMAL(10, 2) COMMENT '纸张',
                                   `labor_cost_variance_adjustment` DECIMAL(10, 2) COMMENT '人工成本差异调整',
                                   `other_depreciation` DECIMAL(10, 2) COMMENT '折旧费其他类',
                                   `steam_fee` DECIMAL(10, 2) COMMENT '蒸汽费',
                                   `rental_fee` DECIMAL(10, 2) COMMENT '租赁费',
                                   `business_entertainment_expenses` DECIMAL(10, 2) COMMENT '业务招待费',
                                   `other_expenses` DECIMAL(10, 2) COMMENT '其他费用',
                                   `vmocode` VARCHAR(255) COMMENT '生产订单号',
                                   `travel_expenses` DECIMAL(10, 2) COMMENT '差旅费',
                                   `salary` DECIMAL(10, 2) COMMENT '工资',
                                   `maternity_insurance` DECIMAL(10, 2) COMMENT '生育保险',
                                   `supplementary_medical_insurance_fee` DECIMAL(10, 2) COMMENT '补充医疗保险费',
                                   `unemployment_insurance_fee` DECIMAL(10, 2) COMMENT '失业保险费',
                                   `other_asset_amortization` DECIMAL(10, 2) COMMENT '其他资产摊销',
                                   `transportation_fee` DECIMAL(10, 2) COMMENT '运输费',
                                   `ennum` INT COMMENT '数量',
                                index `index_cmatercode` (`cmatercode`)
)comment 'bip成本';

-- 按批次统计所有成本（完整版）
select
    vmocode,
    -- 总成本（包含所有成本项目）
    round(sum(
        -- 材料成本
        ifnull(paper,0) + ifnull(packaging_materials,0) + ifnull(direct_auxiliary_materials,0) +
        ifnull(other_material_fee,0) + ifnull(public_materials,0) + ifnull(finished_product_collection,0) +
        ifnull(semi_finished_outsourcing_fee,0) + ifnull(finished_outsourcing_fee,0) +
        ifnull(knife_mold,0) + ifnull(printing_plate,0) + ifnull(machinery_material_consumption,0) +
        -- 人工成本
        ifnull(salary,0) + ifnull(production_management_salary,0) + ifnull(labor_cost_variance_adjustment,0) +
        -- 社保成本
        ifnull(basic_endowment_insurance,0) + ifnull(medical_insurance_fee,0) + ifnull(work_injury,0) +
        ifnull(housing_provident_fund,0) + ifnull(maternity_insurance,0) + ifnull(supplementary_medical_insurance_fee,0) +
        ifnull(unemployment_insurance_fee,0) +
        -- 水电气费
        ifnull(water_fee,0) + ifnull(electricity_fee,0) + ifnull(natural_gas_fee,0) + ifnull(steam_fee,0) +
        -- 折旧摊销费
        ifnull(depreciation_buildings,0) + ifnull(other_depreciation,0) + ifnull(low_value_consumables_amortization,0) +
        ifnull(other_asset_amortization,0) +
        -- 运输仓储费
        ifnull(transportation_fee,0) + ifnull(storage_fee,0) +
        -- 管理费用
        ifnull(office_expenses,0) + ifnull(postal_telecommunication_fee,0) + ifnull(meeting_expenses,0) +
        ifnull(travel_expenses,0) + ifnull(business_entertainment_expenses,0) + ifnull(rental_fee,0) +
        -- 生产服务费
        ifnull(production_service_fee,0) + ifnull(production_equipment_repair_fee,0) + ifnull(labor_protection_fee,0) +
        ifnull(safety_production_fee,0) + ifnull(environmental_protection_fee,0) + ifnull(organization_construction_fee,0) +
        -- 其他费用
        ifnull(insurance_fee,0) + ifnull(quality_loss,0) + ifnull(other_expenses,0)
    ), 2) as total_all_cost -- 完整总成本
from ods_cost_report ocr
group by vmocode
;

-- 预算与决算表
CREATE TABLE dws_budget_final_settlement_report (
                                                    id INT AUTO_INCREMENT PRIMARY KEY,
                                                    split_order_number VARCHAR(255) COMMENT '拆分订单号',
                                                    quotation_number VARCHAR(255) COMMENT '报价单号',
                                                    split_order_status VARCHAR(255) COMMENT '拆分订单状态',
                                                    split_order_line_number VARCHAR(255) COMMENT '拆分订单行号',
                                                    invoice_number VARCHAR(255) COMMENT '发票编号',
                                                    is_ganged BOOLEAN COMMENT '是否拼版',
                                                    customer_code VARCHAR(255) COMMENT '客户编码',
                                                    customer_name VARCHAR(255) COMMENT '客户名称',
                                                    product_name VARCHAR(255) COMMENT '产品名称',
                                                    product_code VARCHAR(255) COMMENT '产品编号',
                                                    mnemonic_code VARCHAR(255) COMMENT '助记码',
                                                    product_version VARCHAR(255) COMMENT '产品版本',
                                                    main_product VARCHAR(255) COMMENT '主产品',
                                                    ganged_area varchar(200) COMMENT '拼板面积',
                                                    quotation_factory VARCHAR(255) COMMENT '报价工厂',
                                                    quantity INT COMMENT '数量',
                                                    invoice_quantity INT COMMENT '开票数量',
                                                    float_rate varchar(200) COMMENT '浮动率',
                                                    sales_unit_price_excluding_tax_rmb varchar(200) COMMENT '不含税销售单价（RMB）',
                                                    sales_amount_excluding_tax_rmb varchar(200) COMMENT '不含税销售金额（RMB）',
                                                    invoice_unit_price varchar(200) COMMENT '开票单价',
                                                    invoice_amount varchar(200) COMMENT '开票金额',
                                                    budget_gross_profit varchar(200) COMMENT '预算毛利',
                                                    budget_gross_profit_margin_percent varchar(200) COMMENT '预算毛利率%',
                                                    total_cost varchar(200) COMMENT '总成本',
                                                    raw_material_cost varchar(200) COMMENT '原辅料成本',
                                                    water_electricity_labor varchar(200) COMMENT '水电人工',
                                                    depreciation_fee varchar(200) COMMENT '折旧费',
                                                    freight varchar(200) COMMENT '运费',
                                                    standard_cost_price varchar(200) COMMENT '标准成本价',
                                                    final_settlement_total_quantity INT COMMENT '决算总数量',
                                                    final_settlement_total_cost varchar(200) COMMENT '决算总成本',
                                                    final_settlement_raw_material_cost varchar(200) COMMENT '决算原辅料成本',
                                                    final_settlement_water_electricity_labor varchar(200) COMMENT '决算水电人工',
                                                    final_settlement_depreciation_fee varchar(200) COMMENT '决算折旧费',
                                                    final_settlement_freight varchar(200) COMMENT '决算运费',
                                                    final_settlement_gross_profit varchar(200) COMMENT '决算毛利',
                                                    final_settlement_gross_profit_margin_percent varchar(200) COMMENT '决算毛利率%'
)comment '预算与决算报表';

-- 预算与决算sql - 基于实际表结构重写
select
    -- 基础信息
    cso.sales_order_code as split_order_number,                                  -- 拆分订单号
    cso.quotation_code as quotation_number,                                      -- 报价单号
    case
        when cso.status='10' then '已删除'
        when cso.status='8' then '已取消'
        when cso.status='7' then '已关闭'
        when cso.status='6' then '已开票'
        when cso.status='0' then '已拆分'
        when cso.status='1' then '已下达'
        when cso.status='2' then '已排产'
        when cso.status='3' then '已领料'
        when cso.status='4' then '生产中'
        when cso.status='5' then '已入库'
        else '其他'
        end as split_order_status,                                               -- 拆分订单状态
    csop.contract_product_line_number as split_order_line_number,                -- 拆分订单行号
    csid.vdef20 as invoice_number,                                               -- 发票编号

    -- 客户信息
    case when cpq.imposition_quotation = '是' then 1 else 0 end as is_ganged,   -- 是否拼版
    cso.cust_code as customer_code,                                              -- 客户编码
    cso.cust_name as customer_name,                                              -- 客户名称

    -- 产品信息
    csop.material_name as product_name,                                          -- 产品名称
    csop.material_code as product_code,                                          -- 产品编号
    csop.mnemonic_code as mnemonic_code,                                         -- 助记码
    csop.product_version as product_version,                                     -- 产品版本
    coalesce(cpqp.makeup_product, '--') as main_product,                        -- 主产品标识
    coalesce(cpqp.plate_area, '--') as ganged_area,                             -- 拼板面积
    cpqp.quotation_factory as quotation_factory,                                -- 报价工厂

    -- 数量信息
    csop.order_quantity_after_split as quantity,                                -- 拆分后订单数量
    sum(csid.nnum) as invoice_quantity,                                          -- 开票数量
    csop.quantity_fluctuation as float_rate,                                     -- 浮动率

    -- 价格信息
    round(cast(cpqp.unit_price_excluding as decimal(20,6)), 6) as sales_unit_price_excluding_tax_rmb,  -- 不含税销售单价（RMB）
    round(cast(cpqp.sales_amount_excluding as decimal(20,2)), 2) as sales_amount_excluding_tax_rmb,    -- 不含税销售金额（RMB）
    round(avg(csid.norignetprice), 6) as invoice_unit_price,                     -- 开票单价
    round(sum(csid.norigmny), 2) as invoice_amount,                              -- 开票金额

    -- 预算成本信息（从报价单产品表获取）
    round(cast(cpqp.unit_price_excluding as decimal(20,6)) - cast(cpqp.standard_cost_unit_price as decimal(20,6)), 2) as budget_gross_profit, -- 预算毛利
    round(case
        when cast(cpqp.unit_price_excluding as decimal(20,6)) > 0 then
            100 * (cast(cpqp.unit_price_excluding as decimal(20,6)) - cast(cpqp.standard_cost_unit_price as decimal(20,6))) / cast(cpqp.unit_price_excluding as decimal(20,6))
        else 0
    end, 4) as budget_gross_profit_margin_percent,                               -- 预算毛利率%
    round(cast(cpqp.standard_cost_unit_price as decimal(20,6)) * sum(csid.nnum), 2) as total_cost,     -- 总成本
    round(cast(cpqp.raw_material_cost as decimal(20,2)), 2) as raw_material_cost,                      -- 原辅料成本
    round(cast(cpqp.utilities_labor as decimal(20,2)), 2) as water_electricity_labor,                  -- 水电人工
    round(cast(cpqp.depreciation_expense as decimal(20,2)), 2) as depreciation_fee,                    -- 折旧费
    round(cast(cpqp.freight_cost as decimal(20,2)), 2) as freight,                                     -- 运费
    round(cast(cpqp.standard_cost_unit_price as decimal(20,6)), 6) as standard_cost_price,             -- 标准成本价

    -- 决算成本信息（从BIP报表获取）
    coalesce(ocr.ennum, 0) as final_settlement_total_quantity,                   -- 决算总数量
    round(coalesce(ocr.total_cost, 0), 2) as final_settlement_total_cost,        -- 决算总成本
    round(coalesce(ocr.raw_material_cost, 0), 2) as final_settlement_raw_material_cost,       -- 决算原辅料成本
    round(coalesce(ocr.water_electricity_labor, 0), 2) as final_settlement_water_electricity_labor, -- 决算水电人工
    round(coalesce(ocr.depreciation_cost, 0), 2) as final_settlement_depreciation_fee,        -- 决算折旧费
    round(coalesce(ocr.transportation_fee, 0), 2) as final_settlement_freight,   -- 决算运费

    -- 决算毛利计算
    round(sum(csid.norigmny) - coalesce(ocr.total_cost, 0), 2) as final_settlement_gross_profit, -- 决算毛利
    case
        when sum(csid.norigmny) > 0 then
            round(100 * (sum(csid.norigmny) - coalesce(ocr.total_cost, 0)) / sum(csid.norigmny), 4)
        else 0
    end as final_settlement_gross_profit_margin_percent                          -- 决算毛利率%

from crm_sales_order cso
inner join crm_sales_order_product csop on cso.sales_order_code = csop.sales_order_code
    and csop.flag_deleted = 0
left join crm_preliminary_quotation cpq on cso.quotation_code = cpq.preliminary_quotation_code
    and cpq.flag_deleted = 0
left join crm_preliminary_quotation_product cpqp on cpq.preliminary_quotation_code = cpqp.preliminary_quotation_code
    and csop.material_code = cpqp.material_code
    and csop.product_version = cpqp.product_version
    and cpqp.flag_deleted = 0
inner join crm_sales_invoice_details csid on csop.csaleorderbid = csid.csaleorderbid
    and csid.flag_deleted = 0
    and csid.nnum > 0
left join (
    select
        cmatercode as material_code,
        vmocode as production_order_code,
        ennum,
        -- 决算总成本 = 所有成本项之和
        (ifnull(paper,0) + ifnull(packaging_materials,0) + ifnull(direct_auxiliary_materials,0) +
         ifnull(other_material_fee,0) + ifnull(public_materials,0) + ifnull(finished_product_collection,0) +
         ifnull(semi_finished_outsourcing_fee,0) + ifnull(finished_outsourcing_fee,0) +
         ifnull(salary,0) + ifnull(production_management_salary,0) +
         ifnull(water_fee,0) + ifnull(electricity_fee,0) + ifnull(natural_gas_fee,0) + ifnull(steam_fee,0) +
         ifnull(depreciation_buildings,0) + ifnull(other_depreciation,0) +
         ifnull(transportation_fee,0) + ifnull(other_expenses,0)) as total_cost,
        -- 决算原辅料成本
        (ifnull(paper,0) + ifnull(packaging_materials,0) + ifnull(direct_auxiliary_materials,0) +
         ifnull(other_material_fee,0) + ifnull(public_materials,0) + ifnull(finished_product_collection,0) +
         ifnull(semi_finished_outsourcing_fee,0) + ifnull(finished_outsourcing_fee,0)) as raw_material_cost,
        -- 决算水电人工
        (ifnull(salary,0) + ifnull(production_management_salary,0) +
         ifnull(water_fee,0) + ifnull(electricity_fee,0) + ifnull(natural_gas_fee,0) + ifnull(steam_fee,0)) as water_electricity_labor,
        -- 决算折旧费
        (ifnull(depreciation_buildings,0) + ifnull(other_depreciation,0)) as depreciation_cost,
        -- 决算运费
        ifnull(transportation_fee,0) as transportation_fee
    from ods_cost_report
    where ennum > 0
) ocr on csop.material_code = ocr.material_code

where cso.flag_deleted = 0
  and csop.flag_deleted = 0
  and csid.flag_deleted = 0
  and csid.nnum > 0  -- 只取有开票数量的记录

group by
    cso.sales_order_code,
    cso.quotation_code,
    cso.status,
    csop.contract_product_line_number,
    csid.vdef20,
    cpq.imposition_quotation,
    cso.cust_code,
    cso.cust_name,
    csop.material_name,
    csop.material_code,
    csop.mnemonic_code,
    csop.product_version,
    cpqp.makeup_product,
    cpqp.plate_area,
    cpqp.quotation_factory,
    csop.order_quantity_after_split,
    csop.quantity_fluctuation,
    cpqp.unit_price_excluding,
    cpqp.sales_amount_excluding,
    cpqp.standard_cost_unit_price,
    cpqp.raw_material_cost,
    cpqp.utilities_labor,
    cpqp.depreciation_expense,
    cpqp.freight_cost,
    ocr.ennum,
    ocr.total_cost,
    ocr.raw_material_cost,
    ocr.water_electricity_labor,
    ocr.depreciation_cost,
    ocr.transportation_fee

order by cso.sales_order_code, csop.contract_product_line_number;

-- ========== 从material_name中提取克重的SQL示例 ==========

-- 方法1：使用REGEXP_SUBSTR提取克重（推荐，MySQL 8.0+）
SELECT
    material_name,
    REGEXP_SUBSTR(material_name, '[0-9]+g') AS gram_weight
FROM your_table_name
WHERE material_name REGEXP '[0-9]+g'
ORDER BY material_name;

-- 方法2：使用SUBSTRING_INDEX提取克重（兼容性更好）
SELECT
    material_name,
    TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(material_name, ' ', 2), ' ', -1)) AS gram_weight
FROM your_table_name
WHERE material_name LIKE '% %g %'
ORDER BY material_name;

-- 方法3：专门提取157g的查询
SELECT
    material_name,
    '157g' AS extracted_weight
FROM your_table_name
WHERE material_name LIKE '%157g%'
ORDER BY material_name;

-- 方法4：提取所有克重并统计
SELECT
    REGEXP_SUBSTR(material_name, '[0-9]+g') AS gram_weight,
    COUNT(*) AS count
FROM your_table_name
WHERE material_name REGEXP '[0-9]+g'
GROUP BY REGEXP_SUBSTR(material_name, '[0-9]+g')
ORDER BY CAST(REGEXP_SUBSTR(REGEXP_SUBSTR(material_name, '[0-9]+g'), '[0-9]+') AS UNSIGNED);

-- 方法5：只提取数字部分（不含g）
SELECT
    material_name,
    REGEXP_SUBSTR(material_name, '[0-9]+(?=g)') AS gram_weight_number
FROM your_table_name
WHERE material_name REGEXP '[0-9]+g'
ORDER BY material_name;

