-- 纸箱规格统计
SELECT DATE_FORMAT(pmi.create_time, '%Y-%m')          AS month, -- 格式化为月份
       COALESCE(COUNT(DISTINCT ommf.specification), 0) AS total  -- 统计物料档案总数
FROM ods_pm_material_module_id AS pmi
         INNER JOIN
     ods_mes_material_file AS ommf
     ON ommf.material_code = pmi.material_code -- INNER JOIN，确保两个表中都有匹配
WHERE pmi.flag_deleted = 0 -- 只统计未被删除的记录
  and ommf.flag_deleted=0
  and ommf.category=3
GROUP BY month -- 按月份分组
ORDER BY month; -- 按月份排序

SELECT pmi.specification,ommf.specification
FROM ods_pm_material_module_id AS pmi
         INNER JOIN
     ods_mes_material_file AS ommf
     ON ommf.material_code = pmi.material_code -- INNER JOIN，确保两个表中都有匹配
WHERE pmi.flag_deleted = 0 -- 只统计未被删除的记录
  and ommf.flag_deleted=0
  and ommf.category=3
and DATE_FORMAT(pmi.create_time, '%Y-%m') ='2024-12'
; -- 按月份排序
