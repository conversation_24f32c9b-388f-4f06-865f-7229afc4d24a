select t2.month                       month,
       coalesce(t1.process_name, '')     process_name,
       coalesce(defect_percentage, 0) defect_percentage
FROM (SELECT 1 AS month
      UNION ALL
      SELECT 2
      UNION ALL
      SELECT 3
      UNION ALL
      SELECT 4
      UNION ALL
      SELECT 5
      UNION ALL
      SELECT 6
      UNION ALL
      SELECT 7
      UNION ALL
      SELECT 8
      UNION ALL
      SELECT 9
      UNION ALL
      SELECT 10
      UNION ALL
      SELECT 11
      UNION ALL
      SELECT 12) t2
         left join
     (SELECT MONTH(fba.create_time)    AS month,         -- 月份
             jd.process_name           AS process_name,  -- 工序名称
             SUM(fba.quantity_rejects) AS total_rejects, -- 工序废品数量
             100 * SUM(fba.quantity_rejects) / SUM(fba.feed_quantity)
                                          defect_percentage
      FROM ods_final_batch_job_audit fba
               JOIN
           ods_pm_job_detail jd ON fba.job_strip_number = jd.id
      WHERE fba.flag_deleted = 0
        and year(fba.create_time) = year(curdate())
      GROUP BY MONTH(fba.create_time),
               jd.process_name
      ) t1 on t1.month = t2.month
      join (SELECT jd.process_name AS process_name
            FROM ods_final_batch_job_audit fba
                     JOIN
                 ods_pm_job_detail jd ON fba.job_strip_number = jd.id
            WHERE fba.flag_deleted = 0
              and date_format(fba.create_time, '%Y-%m') = date_format(curdate(), '%Y-%m')
            group by jd.process_name
            order by 100 * SUM(fba.quantity_rejects) / SUM(fba.feed_quantity) desc
            limit 10) t3 on t3.process_name=t1.process_name
    order by month asc, defect_percentage desc;

-- 当月前十
SELECT jd.process_name AS process_name
FROM ods_final_batch_job_audit fba
         JOIN
     ods_pm_job_detail jd ON fba.job_strip_number = jd.id
WHERE fba.flag_deleted = 0
  and date_format(fba.create_time, '%Y-%m') = date_format(curdate(), '%Y-%m')
group by jd.process_name
order by 100 * SUM(fba.quantity_rejects) / SUM(fba.feed_quantity) desc
limit 10;
