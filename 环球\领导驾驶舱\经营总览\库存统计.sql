-- 查询纸张物料编码及克重
select material_code as code,
       conversion_rate,
       t2.data_value    gram_weight
from ods_mes_material_file t1
         left join ods_mes_parameter_data t2
                   on t1.gram_weight = t2.id
where category = 1;

-- 纸张(原料)库存
select round(sum(
                     case
                         when qty2Unit in ('kg', 'kilogram') then qty2 / 1000
                         when qty2Unit in ('TNE') then qty2
                         when qty2Unit in ('fix') then SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', 1), ' ', -1) *
                                                       SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', -1), 'm', 1) /
                                                       1000000 *
                                                       t3.data_value / 1000000 * qty2
                         end), 0)              value,
       date_format(now(), '%Y-%m-%d %H:%m:%s') time
from ods_wms_inventory_collect t1
         join ods_mes_material_file t2 on t1.itemCode = t2.material_code
         join ods_mes_parameter_data t3 on t2.gram_weight = t3.id
where t2.category = 1
  and t2.flag_deleted = 0
;

-- 纸张(原料)库存金额
select round(sum(case
                     when qty2Unit in ('kg', 'kilogram') then qty2 / 1000
                     when qty2Unit in ('TNE') then qty2
                     when qty2Unit in ('fix') then SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', 1), ' ', -1) *
                                                   SUBSTRING_INDEX(SUBSTRING_INDEX(spec, '*', -1), 'm', 1) / 1000000 *
                                                   t3.data_value / 1000000 * qty2
                     end * t4.last_month_cost_price) * 1000 / 10000, 0) value,
       date_format(now(), '%Y-%m-%d %H:%m:%s')                          time
from ods_wms_inventory_collect t1
         join ods_mes_material_file t2 on t1.itemCode = t2.material_code
         join ods_mes_parameter_data t3 on t2.gram_weight = t3.id
         join ods_crm_price_manager t4 on t4.material_code = t2.material_code
where t2.category = 1
  and t2.flag_deleted = 0
;

-- 成品库存
select sum(qty)                                value,
       date_format(now(), '%Y-%m-%d %H:%m:%s') time
from ods_wms_inventory_collect t1
         join ods_mes_material_file t2 on t1.itemCode = t2.material_code
where t2.category = 4
  and t2.flag_deleted = 0
;

-- 成品库存
select qty                                     value,
       date_format(now(), '%Y-%m-%d %H:%m:%s') time,
       t1.*
from ods_wms_inventory_collect t1
         join ods_mes_material_file t2 on t1.itemCode = t2.material_code
where t2.category = 4
  and t2.flag_deleted = 0;

-- 成品库存金额
select round(sum(qty * unit_price_no) / 10000, 0) value,
       date_format(now(), '%Y-%m-%d %H:%m:%s')    time
from ods_wms_inventory_detail t1
         join ods_mes_material_file t2 on t1.itemCode = t2.material_code
         join ods_pm_job_order t3 on t3.production_batch_number = t1.lotnum
         join ods_pm_order_product t4 on t4.product_number = t1.itemCode and t3.order_code = t4.sales_number
where t2.category = 4
  and t2.flag_deleted = 0
  and t1.productType = 0
;




