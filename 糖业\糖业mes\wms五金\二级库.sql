-- 二级库配置
select * from inventory_table where type=1;

SELECT
    pi.*,mmi.*,mi.name as supplier_name
FROM pro_inventory pi
         LEFT JOIN mdm_material_information mmi on pi.accessory_code = mmi.code and mmi.flag_deleted=0
         left join mdm_information mi on pi.supplier_code = mi.code_srm and mi.flag_deleted=0
where
    pi.flag_deleted=0
  and  table_code = :table_code
  AND ((:mat_type IS NULL OR :mat_type = '') OR (mat_type = :mat_type))
  and pi.status=3
  and pi.mat_type=0;
SELECT
    pi.*,mmi.*
FROM pro_inventory pi
         LEFT JOIN mdm_material_information mmi on pi.accessory_code = mmi.code
where
    pi.flag_deleted=0
  and mmi.flag_deleted=0
  and  table_code = :table_code
  AND ((:mat_type IS NULL OR :mat_type = '') OR (mat_type = :mat_type))
  and status=3
  and pi.mat_type=0;

-- 二级库_分页查询库存表
select pi.*,mi.name as supplier_name,mmi.material_name as accessory_name,
       mmi.code as accessory_code,mmi.unit,mmi.specification_model as model
from pro_inventory pi
         left join mdm_information mi on pi.supplier_code = mi.code_sap
         left join mdm_material_information mmi on pi.accessory_code = mmi.code
where pi.flag_deleted = 0
and mi.flag_deleted=0
and mmi.flag_deleted=0
#   and (:batch is null or pi.batch like CONCAT('%',:batch,'%'))
#   and (:s_name is null or mi.name like CONCAT('%',:s_name,'%'))
#   and (:a_name is null or mmi.material_name like CONCAT('%',:a_name,'%'))
#   and pi.table_code = :t_code
# limit :pageSize OFFSET :page
;
-- 二级库_分页查询入库记录表
SELECT pdi.shipments_code as apply_code,pdi.supplier_code as supplier_code,pdi.quantity as quantity,pdi.id,
       pdi.batch as batch,pdi.accessory_code as accessory_code,pri.team,
       pdi.shipments_by as apply_by,pdi.shipments_time as apply_time,
       mmi.code as accessory_code,mmi.material_name as accessory_name,
       mmi.specification_model as model,mmi.unit,
       mi.code_sap as supplier_code,mi.name as supplier_name
FROM pro_record_in_detail pdi
         LEFT JOIN pro_record_in pri ON pdi.shipments_code = pri.shipments_code
         LEFT JOIN mdm_material_information mmi ON mmi.code = pdi.accessory_code
         LEFT JOIN mdm_information mi ON mi.code_sap = pdi.supplier_code
WHERE  pdi.flag_deleted = 0
  AND pri.status = 5
 and table_code='clarify_the_auxiliary';
-- 二级库_分页查询出库记录表
SELECT pdo.pick_code as pick_code,pi.supplier_code as supplier_code,pdo.quantity as quantity,pdo.id as id,
       pdo.batch as batch,pdo.accessory_code as accessory_code,
       pro.pick_by as pick_by,pro.pick_time as pick_time,pro.team,
       mmi.code as accessory_code,mmi.material_name as accessory_name,
       mmi.specification_model as model,mmi.unit,
       mi.code_sap as supplier_code,mi.name as supplier_name
FROM pro_record_out_detail pdo
         LEFT JOIN pro_record_out pro ON pdo.pick_code = pro.pick_code
         LEFT JOIN pro_inventory pi ON pi.id = pdo.inventory_id
         LEFT JOIN mdm_material_information mmi ON mmi.code = pdo.accessory_code
         LEFT JOIN mdm_information mi ON mi.code_sap = pi.supplier_code
WHERE  pdo.flag_deleted = 0
  AND pro.status = 3
  AND (:a_name is null or mmi.material_name LIKE CONCAT('%',:a_name,'%'))
  AND (:batch is null or pdo.batch LIKE CONCAT('%',:batch,'%'))
  AND (:s_name is null or mi.name LIKE CONCAT('%',:s_name,'%'))
  AND (:pick_code is null or pdo.pick_code LIKE CONCAT('%',:pick_code,'%'))
  AND pro.table_code = :t_code
ORDER BY pdo.pick_code
LIMIT :pageSize OFFSET :page;

-- 二级库_领用记录新增
select * from pro_record_in pri where table_code='clarify_the_auxiliary';
# delete from pro_record_in where table_code='accessories_small_package';

select * from pro_record_in_detail prid where shipments_code='4902109440';
# delete from pro_record_in_detail where shipments_code='4902109396';
-- 库存查询
select mat_type,pi.* from pro_inventory pi where table_code='clarify_the_auxiliary';

-- 五金仓查询库存
SELECT
    mmi.code,
    mmi.material_name,
    mmi.specification_model,
    mmi.unit,
    pi.id,
    pi.inventory_quantity,
    pi.batch,
    pi.pick_qty,
    pi.supplier_code,
    pi.supplier_name,
    pi.vobillno,
    pi.pick_no,
    pi.pick_row_item
FROM pro_inventory pi
         LEFT JOIN mdm_material_information mmi on pi.accessory_code = mmi.code and mmi.flag_deleted=0
where
    pi.flag_deleted=0
  and  table_code = 'dissolve_accessories'
#   AND ((:mat_type IS NULL OR :mat_type = '') OR (mat_type = :mat_type))
  and pi.status=3
  and pi.mat_type=0
# LIMIT :page_size offset :offset
;
select *  from pro_inventory pi where table_code='dissolve_accessories';

SELECT pdi.shipments_code as apply_code,
       pdi.supplier_code as supplier_code,
       pdi.quantity as quantity,pdi.id,
       pdi.batch as batch,
       pdi.accessory_code as accessory_code,
       pri.team,
       pdi.shipments_by as apply_by,
       pdi.shipments_time as apply_time,
       mmi.code as accessory_code,
       mmi.material_name as accessory_name,
       mmi.specification_model as model,
       mmi.unit,
       mi.code_srm as supplier_code,
       mi.name as supplier_name,
       pdi.apply_code voBillNo
FROM pro_record_in_detail pdi
         LEFT JOIN pro_record_in pri ON pdi.shipments_code = pri.shipments_code
         LEFT JOIN mdm_material_information mmi ON mmi.code = pdi.accessory_code
         LEFT JOIN mdm_information mi ON mi.code_srm = pdi.supplier_code
WHERE  pdi.flag_deleted = 0
  AND pri.status = 5
  AND pri.table_code = 'clarify_the_auxiliary'
;
