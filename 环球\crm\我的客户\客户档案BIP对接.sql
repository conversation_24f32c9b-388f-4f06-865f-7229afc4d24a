select cust_code,cust_mnemonic_code,company_code,cust_name from crm_cust_basic t1
where cust_status =2 and cust_type!=1 and
     ( cust_code like 'CC%' or cust_code='' or cust_code is null );
-- 客户档案查询BIP关联数据
select t1.cust_code,t1.cust_name,t1.company_code,t2.cust_code,t2.cust_name,t2.company_code
from bip_cust_basic t1
    join crm_cust_basic t2 on
        t1.company_code=t2.company_code
where t1.cust_code!=t2.cust_code
and t2.cust_status=2
;
select t1.cust_code,t1.cust_name,t1.company_code,t2.cust_code,t2.cust_name,t2.company_code
from bip_cust_basic t1
         join crm_cust_basic t2 on
    t1.company_code=concat(t2.company_code,'-1')
where t1.cust_code!=t2.cust_code
  and t2.cust_status=2;

select * from crm_cust_basic ccb where company_code='91610132MAB0NALH2K';
-- 查询客户档案中

select * from bip_cust_basic bcb where company_code like '%-%';
select * from crm_cust_basic ccb where cust_code='C004754';

