-- 生产产出  is_collage 0：拼版 1：非拼版
SELECT count(*)  as folded_box_overlay_production_batches,
       sum(CASE
               WHEN received_quantity is not null and received_quantity != '' THEN received_quantity
               ELSE 0 END)                  as folded_box_overlay_production_quantity,
       DATE_FORMAT(finish_date, '%Y-%m')             AS yuefen
FROM pm_job_order
WHERE production_order_status = '3'
  and YEAR(finish_date) = 2024
  and large_category = 6876
  and is_collage = '0'
GROUP BY yuefen


