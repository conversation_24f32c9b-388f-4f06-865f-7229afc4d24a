var materialResult = JSON.parse(JSON.stringify(obj.materialResult))
var craftArr = JSON.parse(JSON.stringify(obj.craftArr)) // 传进来的参数工艺路线
var inCarftArr = [] //需要存核价单的工艺路线
var bjArr = []
//处理部件
var parent_code = ""
for (var x = 0; x < materialResult.length; x++) {
  if (materialResult[x].categroy == 7 || materialResult[x].categroy == 3 || materialResult[x].categroy == 0) {
    bjArr.push(materialResult[x])
  }
}
for (var x = 0; x < craftArr.length ; x++) {
  var obj = {}
  obj['parent_code'] = craftArr[x].parent_code  //parent_code
  obj['categroy'] = "10"  //categroy
  obj['craft_name'] = craftArr[x].craft_name  //工艺 
  obj['production_resource'] = craftArr[x].work_name  //资源
  if (craftArr[x].process_type == "1") {   //大张工序
    obj['operation_joint_number'] = craftArr[x].out_of_book //出本
  }else{                             //小张工序
    obj['operation_joint_number'] = 1  //出本
  }
  obj['process_type'] = craftArr[x].process_type
  for (var y = 0; y < workArr.length; y++) {
    if (craftArr[x].work_code == workArr[y].resource_code) {
      obj['resource_center_standard_hourly_wage'] = workArr[y].labor_hour_costs//资源中心标准时薪
      obj['resource_center_fixed_manufacturing_cost'] = workArr[y].manufacturing_expense_costs //资源中心固定制造费用
      if ( craftArr[x].craft_code == workArr[y].process_code) {
       obj['production_preparation_time'] =  workArr[y].fixed_hours //生产准备用时
       break
     }else{
       obj['production_preparation_time'] =  0//生产准备用时
     }
    }
  }
  if (!obj.production_preparation_time) {
    obj['production_preparation_time'] =  0
  }
  obj['operation_difficulty_fixed_time'] = craftArr[x].ys_fixed_time //作业难度固定用时
  obj['total_preparation_time'] = (obj.production_preparation_time*1)+(obj.operation_difficulty_fixed_time*1)
  obj.total_preparation_time = obj.total_preparation_time.toFixed(2)*1//总准备用时
  obj['fixed_labor'] = (obj.resource_center_standard_hourly_wage*1) * (obj.total_preparation_time *1)
  obj['fixed_manufacturing_cost'] = ((obj.resource_center_fixed_manufacturing_cost*1) * (obj.total_preparation_time *1)).toFixed(2)*1
  for (var y = 0; y < workArr.length; y++) {
    if (craftArr[x].work_code == workArr[y].resource_code) {
      var costs = JSON.parse(workArr[y].manufacturing_expense_json)
    // 初始化一个变量来存储找到的最后一项  
    var lastNonZeroItem = null;  
    // 遍历数组  
    for (var i = 0; i < costs.length; i++) {  
     // 检查当前项的proportion是否不为0  
     if (costs[i].proportion !== 0) {  
         // 如果不为0，则更新最后一项  
         lastNonZeroItem = costs[i].cost_item;  
     }  
    }  
      var price = 0;
    for (var z = 0; z < costs.length; z++) {
      if (costs[z].cost_item == "管理人员工资") {
        obj['executive_salary'] = (obj.fixed_manufacturing_cost * (costs[z].proportion*1/100)).toFixed(2)*1
        if (costs[z].cost_item != lastNonZeroItem) {
          price += obj.executive_salary
        }
      }
      if (costs[z].cost_item == "折旧费（设备及其他类）") {
        obj['depreciation_expense_other'] = (obj.fixed_manufacturing_cost * (costs[z].proportion*1/100)).toFixed(2)*1
        if (costs[z].cost_item != lastNonZeroItem) {
          price += obj.depreciation_expense_other
        }
      }
      if (costs[z].cost_item == "折旧费（房屋建筑物类）") {
        obj['depreciation_expense_buildings'] = (obj.fixed_manufacturing_cost * (costs[z].proportion*1/100)).toFixed(2)*1
        if (costs[z].cost_item != lastNonZeroItem) {
          price += obj.depreciation_expense_buildings
        }
      }
      if (costs[z].cost_item == "修理费") {
        obj['repair_charge'] = (obj.fixed_manufacturing_cost * (costs[z].proportion*1/100)).toFixed(2)*1
        if (costs[z].cost_item != lastNonZeroItem) {
          price += obj.repair_charge
        }
      }
      if (costs[z].cost_item == "水费") {
        obj['water_rate'] = (obj.fixed_manufacturing_cost * (costs[z].proportion*1/100)).toFixed(2)*1
        if (costs[z].cost_item != lastNonZeroItem) {
          price += obj.water_rate
        }
      }
      if (costs[z].cost_item == "电气费") {
        obj['electric_charge'] = (obj.fixed_manufacturing_cost * (costs[z].proportion*1/100)).toFixed(2)*1
         if (costs[z].cost_item != lastNonZeroItem) {
          price += obj.electric_charge
        }
      }
      if (costs[z].cost_item == "仓储费") {
        obj['storage_charge'] = (obj.fixed_manufacturing_cost * (costs[z].proportion*1/100)).toFixed(2)*1
        if (costs[z].cost_item != lastNonZeroItem) {
          price += obj.storage_charge
        }
      }
      if (lastNonZeroItem ==  costs[z].cost_item) {
        if (costs[z].cost_item == "管理人员工资") {
        obj['executive_salary'] = (obj.fixed_manufacturing_cost - price).toFixed(2)*1
      }
      if (costs[z].cost_item == "折旧费（设备及其他类）") {
        obj['depreciation_expense_other'] = (obj.fixed_manufacturing_cost - price).toFixed(2)*1
      }
      if (costs[z].cost_item == "折旧费（房屋建筑物类）") {
        obj['depreciation_expense_buildings'] = (obj.fixed_manufacturing_cost - price).toFixed(2)*1
      }
      if (costs[z].cost_item == "修理费") {
        obj['repair_charge'] = (obj.fixed_manufacturing_cost - price).toFixed(2)*1
      }
      if (costs[z].cost_item == "水费") {
        obj['water_rate'] = (obj.fixed_manufacturing_cost - price).toFixed(2)*1
      }
      if (costs[z].cost_item == "电气费") {
        obj['electric_charge'] = (obj.fixed_manufacturing_cost - price).toFixed(2)*1
      }
      if (costs[z].cost_item == "仓储费") {
        obj['storage_charge'] = (obj.fixed_manufacturing_cost - price).toFixed(2)*1
      }
      }
    }
    }
  }
  inCarftArr.push(obj)
}
var inArr = []
inArr = inArr.concat(inCarftArr)
inArr = inArr.concat(bjArr)
for(var k=0;k<inArr.length;k++){
  inArr[k].quotation_code = quotation_code;
  inArr[k].new_version = new_version;
  inArr[k].version = "1.0";
}
return inArr