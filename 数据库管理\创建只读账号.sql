-- MySQL创建只读账号脚本
-- 使用root账号或具有用户管理权限的账号执行

-- 1. 创建只读用户
-- 方式1: 允许任何IP访问
CREATE USER 'readonly_user'@'%' IDENTIFIED BY 'ReadOnly123!';

-- 方式2: 限制特定IP段访问
-- CREATE USER 'readonly_user'@'172.16.122.%' IDENTIFIED BY 'ReadOnly123!';

-- 方式3: 限制本地访问
-- CREATE USER 'readonly_user'@'localhost' IDENTIFIED BY 'ReadOnly123!';

-- 2. 授予只读权限
-- 根据您的需求选择以下一种或多种方式

-- 2.1 授予特定数据库的只读权限
GRANT SELECT ON cockpit.* TO 'readonly_user'@'%';

-- 2.2 授予多个数据库的只读权限
GRANT SELECT ON h3chq_crmbusiness1704287359505.* TO 'readonly_user'@'%';
GRANT SELECT ON h3chq_pdmbusiness1704287270935.* TO 'readonly_user'@'%';
GRANT SELECT ON hq_mdm_b.* TO 'readonly_user'@'%';

-- 2.3 如果需要查看数据库列表和表结构信息
GRANT SHOW DATABASES ON *.* TO 'readonly_user'@'%';

-- 2.4 如果需要执行SHOW语句查看表结构等信息
GRANT SHOW VIEW ON *.* TO 'readonly_user'@'%';

-- 3. 刷新权限使其生效
FLUSH PRIVILEGES;

-- 4. 验证创建的用户和权限
-- 查看用户权限
SHOW GRANTS FOR 'readonly_user'@'%';

-- 查看所有用户
SELECT User, Host, authentication_string FROM mysql.user WHERE User = 'readonly_user';

-- 5. 测试连接（在应用中使用）
/*
const mysql = require('mysql2');

const readOnlyConnection = mysql.createConnection({
    host: '**************',
    user: 'readonly_user',
    password: 'ReadOnly123!',
    database: 'cockpit'
});

readOnlyConnection.connect((err) => {
    if (err) {
        console.error('只读账号连接失败:', err);
        return;
    }
    console.log('只读账号连接成功');
});
*/

-- 6. 如果需要删除用户（谨慎操作）
-- DROP USER 'readonly_user'@'%';

-- 7. 如果需要修改密码
-- ALTER USER 'readonly_user'@'%' IDENTIFIED BY 'NewPassword123!';

-- 8. 安全建议
-- - 使用强密码
-- - 限制访问IP范围
-- - 定期检查和审计用户权限
-- - 避免授予不必要的权限

-- 9. 常用的只读权限组合
-- 基础只读权限
-- GRANT SELECT ON database_name.* TO 'readonly_user'@'%';

-- 包含查看结构权限的只读
-- GRANT SELECT, SHOW VIEW ON database_name.* TO 'readonly_user'@'%';

-- 包含执行存储过程权限的只读（如果需要）
-- GRANT SELECT, EXECUTE ON database_name.* TO 'readonly_user'@'%';
