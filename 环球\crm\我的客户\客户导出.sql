-- 我的客户_导出
SELECT cust_code '客户编号',
        cust_name '客户名称',
        company_code '社会信用代码',
        department_region '部门区域',
        cust_vip '客户等级',
        industry '所属行业',
        cust_manager_name '客户负责人',
        sales_assistant_name '销售助理' ,
        leader_name '上级客户',
        cust_status '客户状态',
        CASE
            WHEN cust_status = 0 THEN '草稿'
            WHEN cust_status = 1 THEN '审批中'
            WHEN cust_status = 2 THEN '生效'
            WHEN cust_status = 3 THEN '无效'
            WHEN cust_status = 4 THEN '冻结'
            WHEN cust_status = 5 THEN '驳回'
            END                                  '客户类型',
        CASE
            WHEN cust_type = 0 THEN '新建客户'
            WHEN cust_type = 1 THEN '公海客户'
            WHEN cust_type = 2 THEN '合作客户'
            WHEN cust_type = 3 THEN '开发中客户'
            WHEN cust_type = 4 THEN '受限客户'
            END                                  '客户类型',
        create_time '录入时间',
        update_time '更新时间'
FROM crm_cust_basic
WHERE
    flag_deleted = 0
  AND cust_status <> '0'
  AND cust_status <> '3'
  AND cust_version = '1'
  AND cust_type <> '0'
  AND if(cust_type=1,cust_status !='1',1)
ORDER BY create_time DESC
;
