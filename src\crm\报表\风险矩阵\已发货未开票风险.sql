-- 库存金额
SELECT t3.cust_code,
       t3.cust_name,
       round(sum((t4.shipped_quantity - t4.invoiced_quantity) * t2.unit_price_exclusive * t2.exchange_rate),
             2)                                      uninvoiced_amount,
       max(datediff(current_date, t4.shipment_date)) uninvoiced_age
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
         join (select split_order_no            sales_order_code,
                      product_code              material_code,
                      split_order_line_no,
                      group_concat(outbound_no) shipment_code,
                      max(outbound_date)        shipment_date,
                      sum(ship_quantity)        shipped_quantity,
                      sum(ifnull(nnum, 0))      invoiced_quantity
               from bip_outbound_order_detail bood
                        left join (select csrcid, csrcbid, sum(nnum) nnum
                                   from crm_sales_invoice_details
                                   where flag_deleted = 0
                                   group by csrcid, csrcbid) csid
                                  on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
               where bood.flag_deleted = 0
               group by sales_order_code, material_code, split_order_line_no) t4
              on t4.sales_order_code = t2.sales_order_code and t4.material_code = t2.material_code
                  and t4.split_order_line_no = t2.contract_product_line_number
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  and t3.cust_status = 2
  and t3.cust_type not in (0)
  AND (t4.shipped_quantity > t4.invoiced_quantity or t2.order_quantity_after_split > t4.invoiced_quantity)
  and t4.shipped_quantity - t4.invoiced_quantity > 0
group by cust_code, cust_name