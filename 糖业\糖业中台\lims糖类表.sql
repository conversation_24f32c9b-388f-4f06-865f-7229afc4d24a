-- TODO 接口入参对方还未加,需要后续验证
-- 蔗汁、糖浆
drop table bi_ytribao_fac_4;
CREATE TABLE bi_ytribao_fac_4 (
                                  id int auto_increment primary key,
                                  update_time date comment '更新时间',
                                  leibiedm varchar(255) comment '样品名称',
                                  bx decimal(10,2) comment '锤度',
                                  s decimal(10,2) comment '转光度',
                                  ap decimal(10,2) comment '视纯度',
                                  ztf decimal(10,2) comment '蔗糖分',
                                  gp decimal(10,2) comment '重力纯度',
                                  hytf decimal(10,2) comment '还原糖分',
                                  hytf_ztf decimal(10,2) comment '还原糖分%总糖分',
                                  ph decimal(10,2) comment 'ph值',
                                  liuxunqiangdu decimal(10,2) comment '硫熏强度',
                                  sezhi decimal(10,2) comment '色值',
                                  hunzhuodu decimal(10,2) comment '混浊度',
                                  linsuanzhi decimal(10,2) comment '磷酸值',
                                  gaiyan decimal(10,2) comment '钙盐',
                                  quangailiang decimal(10,2) comment '全钙量',
                                  jiandu decimal(10,2) comment '碱度',
                                  brl decimal(10,2) comment '产量',
                              unique key(update_time,leibiedm)
) COMMENT='蔗汁、糖浆';
select * from bi_ytribao_fac_4;
select count(*) from bi_ytribao_fac_4;

-- 成品糖
drop table bi_ytribao_fac_6;
CREATE TABLE bi_ytribao_fac_6 (
                                  id int auto_increment primary key,
                                  update_time date comment '更新时间',
                                  leibiedm varchar(255) comment '样品名称',
                                  cl_d decimal(10,2) comment '产量本日',
                                  cl_m decimal(10,2) comment '产量月累计',
                                  cl_y decimal(10,2) comment '产量榨季累计',
                                  cl_ly decimal(10,2) comment '产量上榨季累计',
                                  ztf decimal(10,2) comment '蔗糖分',
                                  hytf decimal(10,2) comment '还原糖分',
                                  gzsz decimal(10,2) comment '干燥失重',
                                  sz decimal(10,2) comment '色值',
                                  ddhf decimal(10,2) comment '电导灰分',
                                  hzd decimal(10,2) comment '混浊度',
                                  sbr decimal(10,2) comment '水不溶物',
                                  so decimal(10,2) comment '二氧化硫',
                                  jiebie varchar(255) comment '级别',
                              unique key(update_time,leibiedm)
)COMMENT='成品糖';
select * from bi_ytribao_fac_6;
select count(*) from bi_ytribao_fac_6;

-- 糖浆、膏蜜糊
drop table bi_jtribaocn_hx_2;
create table bi_jtribaocn_hx_2 (
                                   id int auto_increment primary key,
                                   update_time date comment '更新时间',
                                   lb varchar(255) comment '类别',
                                   sample_name varchar(255) comment '样品名称',
                                   gs decimal(10,2) comment '糖数',
                                   lfs decimal(10,2) comment '立方数',
                                   zttime decimal(10,2) comment '煮糖时间',
                                   cd decimal(10,2) comment '锤度',
                                   scd decimal(10,2) comment '视红度',
                                   sz decimal(10,2) comment '色值',
                                   hzd decimal(10,2) comment '混浊度',
                                   ph decimal(10,2) comment 'ph值',
                                   sbrw decimal(10,2) comment '水不溶物',
                                   hf decimal(10,2) comment '灰分',
                                   yd decimal(10,2) comment '硬度',
                                   ztf decimal(10,2) comment '总糖分',
                               unique key(update_time,lb,sample_name)
)  comment='糖浆、膏蜜糊';
select * from bi_jtribaocn_hx_2;



-- 精糖蜜、炼糖桔水
drop table bi_jtribaocn_hx_3_1;
CREATE TABLE bi_jtribaocn_hx_3_1
(
                                     id int auto_increment PRIMARY KEY,
                                     update_time date comment '更新时间',
                                     xm VARCHAR(255) not null COMMENT '项目',
                                     brl DECIMAL(10,2) COMMENT '本日量',
                                     yjll DECIMAL(10,2) COMMENT '月累计量',
                                     zjll DECIMAL(10,2) COMMENT '榨季累计量',
                                     cd DECIMAL(10,2) COMMENT '锤度',
                                     scd DECIMAL(10,2) COMMENT '视红度',
                                     ztf DECIMAL(10,2) COMMENT '总糖分',
                                     zlcd DECIMAL(10,2) COMMENT '蔗龄糖度',
                                     hyff DECIMAL(10,2) COMMENT '还原糖分',
                                     sz DECIMAL(10,2) COMMENT '色值',
unique key (update_time,xm)
)COMMENT='精糖蜜、炼糖桔水';
select * from bi_jtribaocn_hx_3_1;
select count(*) from bi_jtribaocn_hx_3_1;
truncate bi_jtribaocn_hx_3_1;

-- 洄溶糖、白砂糖
drop table bi_jtribaocn_hx_3_2;
CREATE TABLE bi_jtribaocn_hx_3_2 (
                                     id int auto_increment primary key,
                                     update_time date comment '更新时间',
                                     sample_name varchar(255) comment '样品名称',
                                     lb varchar(255) comment '类别',
                                     jb varchar(255) comment '级别',
                                     brl decimal(10,2) comment '本日量',
                                     yjll decimal(10,2) comment '月累计量',
                                     zjll decimal(10,2) comment '榨季累计',
                                     sqtq decimal(10,2) comment '上季同期',
                                     ztf decimal(10,2) comment '总糖分',
                                     hytf decimal(10,2) comment '还原糖分',
                                     gzsz decimal(10,2) comment '干燥失重',
                                     hf decimal(10,2) comment '灰分',
                                     sz decimal(10,2) comment '色值',
                                     hzd decimal(10,2) comment '混浊度',
                                     sbrw decimal(10,2) comment '水不溶物',
                                     eyhl decimal(10,2) comment '二氧化硫',
                                     hj decimal(10,2) comment '合计',
                                 unique key(update_time,lb,jb)
) COMMENT='洄溶糖、白砂糖';
select * from bi_jtribaocn_hx_3_2;
select count(*) from bi_jtribaocn_hx_3_2;
truncate bi_jtribaocn_hx_3_2;
select ifnull(sum(zjll),0) from bi_jtribaocn_hx_3_2 where update_time = '2025-03-17'
and lb='洄溶糖' and jb in ('原糖','等外糖','糖粉糖头','其它糖')
;
-- 入库糖
drop table bi_jtribaocn_hx_3_3;
CREATE TABLE `bi_jtribaocn_hx_3_3` (
                                       id int auto_increment primary key,
                                       update_time date comment '更新时间',
                                       `xiangmu` varchar(255) DEFAULT NULL COMMENT '项目',
                                       `jb` varchar(255) DEFAULT NULL COMMENT '级别',
                                       `brl` decimal(18,4) DEFAULT NULL COMMENT '日累计',
                                       `yljl` decimal(18,4) DEFAULT NULL COMMENT '月累计',
                                       `zjljl` decimal(18,4) DEFAULT NULL COMMENT '榨季累计',
                                       `sjtq` decimal(18,4) DEFAULT NULL COMMENT '上季同期',
                                       `hj` decimal(18,4) DEFAULT NULL COMMENT '合计',
                                       unique key(update_time,jb)
) CHARSET=utf8mb4 COMMENT='入库糖';
truncate bi_jtribaocn_hx_3_3;
select * from bi_jtribaocn_hx_3_3;


