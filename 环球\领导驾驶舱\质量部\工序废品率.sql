-- ETL 1
select date_format(t1.create_time, '%Y-%m')                                     update_month,
       process_code,
       process_name,
       sum(quantity_rejects)                                                    scrap_quantity,
       sum(feed_quantity)                                                       feed_quantity,
       ifnull(round(100 * sum(quantity_rejects) / sum(feed_quantity), 2), 0.00) rate
from cockpit.ods_pm_final_batch_job_audit t1
         join cockpit.ods_pm_job_detail t2 on t1.job_strip_number = t2.id
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
group by date_format(t1.create_time, '%Y-%m'), process_code, process_name;

-- ETL 2
select t1.`year_month`             update_month,
       t2.process_code             process_code,
       t2.process_name             process_name,
       COALESCE(scrap_quantity, 0) scrap_quantity,
       COALESCE(feed_quantity, 0)  feed_quantity,
       COALESCE(scrap_rate, 0)     scrap_rate
from dim_month t1
         join
     (select distinct process_code, process_name from dwd_process_scrap_rate_month) t2
         left join dwd_process_scrap_rate_month t3
                   on t1.`year_month` = t3.update_month and t2.process_code = t3.process_code
order by update_month
;


drop table dwd_process_scrap_rate_month;
CREATE TABLE dwd_process_scrap_rate_month
(
    id             int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    update_month   varchar(255)   DEFAULT NULL COMMENT '更新月份yyyy-MM',
    process_code   varchar(255)   DEFAULT NULL COMMENT '工序编码',
    process_name   varchar(255)   DEFAULT NULL COMMENT '工序名称',
    scrap_quantity decimal(20, 2) DEFAULT NULL COMMENT '废品量',
    feed_quantity  decimal(20, 2) DEFAULT NULL COMMENT '投料量',
    scrap_rate     decimal(20, 2) DEFAULT NULL COMMENT '工序废品率',
    PRIMARY KEY (id)
) COMMENT = '工序废品率占比月统计';

-- 工序废品率统计
SELECT process_name,
       MAX(CASE WHEN right(update_month, 2) = '01' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_1',
       MAX(CASE WHEN right(update_month, 2) = '02' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_2',
       MAX(CASE WHEN right(update_month, 2) = '03' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_3',
       MAX(CASE WHEN right(update_month, 2) = '04' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_4',
       MAX(CASE WHEN right(update_month, 2) = '05' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_5',
       MAX(CASE WHEN right(update_month, 2) = '06' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_6',
       MAX(CASE WHEN right(update_month, 2) = '07' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_7',
       MAX(CASE WHEN right(update_month, 2) = '08' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_8',
       MAX(CASE WHEN right(update_month, 2) = '09' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_9',
       MAX(CASE WHEN right(update_month, 2) = '10' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_10',
       MAX(CASE WHEN right(update_month, 2) = '11' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_11',
       MAX(CASE WHEN right(update_month, 2) = '12' THEN concat(round(scrap_rate, 2), '%') ELSE '0.00%' END) AS 'mon_12',
       concat(round(avg(scrap_rate), 2), '%')                                                               as year
FROM cockpit.dwd_process_scrap_rate_month
where ((:year IS NULL OR :year = '') OR (left(update_month, 4) = :year))
GROUP BY process_name
LIMIT :page_size OFFSET :offset
;
-- 平均
select '平均'                                                      process_name,
       concat(max(round(IF(month = '01', scrap_rate, 0), 2)), '%') mon_1,
       concat(max(round(IF(month = '02', scrap_rate, 0), 2)), '%') mon_2,
       concat(max(round(IF(month = '03', scrap_rate, 0), 2)), '%') mon_3,
       concat(max(round(IF(month = '04', scrap_rate, 0), 2)), '%') mon_4,
       concat(max(round(IF(month = '05', scrap_rate, 0), 2)), '%') mon_5,
       concat(max(round(IF(month = '06', scrap_rate, 0), 2)), '%') mon_6,
       concat(max(round(IF(month = '07', scrap_rate, 0), 2)), '%') mon_7,
       concat(max(round(IF(month = '08', scrap_rate, 0), 2)), '%') mon_8,
       concat(max(round(IF(month = '09', scrap_rate, 0), 2)), '%') mon_9,
       concat(max(round(IF(month = '10', scrap_rate, 0), 2)), '%') mon_10,
       concat(max(round(IF(month = '11', scrap_rate, 0), 2)), '%') mon_11,
       concat(max(round(IF(month = '12', scrap_rate, 0), 2)), '%') mon_12,
       concat(round(avg(scrap_rate), 2), '%')                      year
from (select right(update_month, 2) month, avg(scrap_rate) scrap_rate
      from cockpit.dwd_process_scrap_rate_month
      where ((:year IS NULL OR :year = '') OR (left(update_month, 4) = :year))
      group by update_month) temp1;


-- 工序下拉框
select process_code value, process_name label
from cockpit.dwd_process_scrap_rate_month
group by process_code, process_name;

-- 折线图今年
select month, round(max(this_year), 2) this_year, round(max(last_year), 2) last_year
from (select right(update_month, 2) month, avg(scrap_rate) this_year, 0 last_year
      from cockpit.dwd_process_scrap_rate_month
      where ((:year IS NULL OR :year = '') OR (left(update_month, 4) = :year))
        and ((:process_code IS NULL OR :process_code = '') OR (process_code = :process_code))
      group by update_month
      union
      select right(update_month, 2) month, 0 this_year, avg(scrap_rate) last_year
      from cockpit.dwd_process_scrap_rate_month
      where ((:year IS NULL OR :year = '') OR (left(update_month, 4) + 1 = :year))
        and ((:process_code IS NULL OR :process_code = '') OR (process_code = :process_code))
      group by update_month
      union
      select '01', 0, 0
      union
      select '02', 0, 0
      union
      select '03', 0, 0
      union
      select '04', 0, 0
      union
      select '05', 0, 0
      union
      select '06', 0, 0
      union
      select '07', 0, 0
      union
      select '08', 0, 0
      union
      select '09', 0, 0
      union
      select '10', 0, 0
      union
      select '11', 0, 0
      union
      select '12', 0, 0) temp1
group by month
order by month asc
;

