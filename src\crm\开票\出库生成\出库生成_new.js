var productMap = {}
for (var j = 0; j < productList.length; j++) {
    var key = productList[j].material_code + '_' + productList[j].sales_order_code + '_' + productList[j].id;
    productMap[key] = productList[j];
}
// 四舍五入函数，处理精度问题
function roundNumber(number, decimals) {
    return Math.round(number * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

for (var i = 0; i < dataList.length; i++) {

    if (productMap[dataList[i].code + '_' + dataList[i].vdef2 + '_' + dataList[i].vbdef1]) {
        var productObj = productMap[dataList[i].code + '_' + dataList[i].vdef2 + '_' + dataList[i].vbdef1]
        //申请数量
        var applyNum = Number(dataList[i].nnum)
        //税率
        dataList[i].tax_rate = productObj.tax_rate
        var abs_tax_rate = Math.abs(productObj.tax_rate)
        //汇率
        dataList[i].exchange_rate = productObj.exchange_rate
        var exchange_rate = Number(productObj.exchange_rate)
        //币种
        dataList[i].settlement_currency = productObj.settlement_currency
        //订单原结算币种单价（不含税）
        dataList[i].origin_unit_price_exclude_tax = roundNumber(Number(productObj.settlement_currency_price_exclusive), 6)
        //拆分订单的结算币种单价（含税）
        dataList[i].origin_unit_price_include_tax = roundNumber(Number(productObj.settlement_currency_price_inclusive), 6)
        //拆分订单的结算币种金额（不含税）
        dataList[i].origin_ammount_exclude_tax = roundNumber(Number(productObj.settlement_currency_amount_exclusive), 2)
        //拆分订单的结算币种金额（含税）
        dataList[i].origin_ammount_include_tax = roundNumber(Number(productObj.settlement_currency_amount_inclusive), 2)

        dataList[i].split_order_line_no = productObj.contract_product_line_number
        //是否含税
        dataList[i].tax_inclusive = productObj.tax_inclusive
        if (productObj.tax_inclusive == '含税') {
            dataList[i].invoice_include_price = roundNumber(Number(productObj.unit_price_usd), 6)
            //结算币单价不含税
            dataList[i].jsb_unit_price_include_tax = dataList[i].invoice_include_price
            //结算币金额含税
            dataList[i].jsb_amount_include_tax = roundNumber(Number(dataList[i].jsb_unit_price_include_tax * applyNum), 2)
            //金额含税
            dataList[i].amount_tax_inclusive = roundNumber(Number(dataList[i].jsb_amount_include_tax * exchange_rate), 2)
            //单价含税
            dataList[i].price_including_tax = roundNumber(Number(dataList[i].amount_tax_inclusive / applyNum), 6)
            //金额不含税
            dataList[i].amount_exclusive_tax = roundNumber(Number(dataList[i].amount_tax_inclusive / (1 + abs_tax_rate / 100)), 2)

            //结算币金额不含税
            dataList[i].jsb_amount_exclude_tax = roundNumber(Number(dataList[i].amount_exclusive_tax / exchange_rate), 2)
            //结算币单价不含税
            dataList[i].jsb_unit_price_exclude_tax = roundNumber(Number(dataList[i].jsb_amount_exclude_tax / applyNum), 6)
            //单价不含税
            dataList[i].unit_price_exclusive = roundNumber(Number(dataList[i].amount_exclusive_tax / applyNum), 6)
            //税额
            dataList[i].tax_diff = roundNumber((dataList[i].amount_tax_inclusive - dataList[i].amount_exclusive_tax), 2)
            //差价
            dataList[i].price_diff = roundNumber((dataList[i].invoice_include_price - dataList[i].origin_unit_price_include_tax), 6)
        } else if (productObj.tax_inclusive == '不含税') {
            dataList[i].invoice_price = roundNumber(Number(productObj.unit_price_excluding_usd), 6)
            dataList[i]['jsb_unit_price_exclude_tax'] = dataList[i].invoice_price
            // 不含税销售金额(报价币种) =  不含税销售单价(报价币种)*数量
            dataList[i]['jsb_amount_exclude_tax'] = roundNumber((Number(dataList[i]['jsb_unit_price_exclude_tax']) * applyNum), 2)
            // 不含税销售金额(RMB) = 不含税销售金额(报价币种) * 汇率
            dataList[i]['amount_exclusive_tax'] = roundNumber((Number(dataList[i]['jsb_amount_exclude_tax']) * exchange_rate), 2)
            // 不含税销售单价(RMB) = 不含税销售金额(RMB) / 数量      保留6位小数
            dataList[i]['unit_price_exclusive'] = roundNumber((Number(dataList[i]['amount_exclusive_tax']) / applyNum), 6)
            //税额=不含税销售金额(RMB)*税率
            dataList[i]['tax_diff'] = roundNumber((Number(dataList[i]['amount_exclusive_tax']) * abs_tax_rate / 100), 2)
            // 含税销售金额(RMB) = 不含税销售金额(RMB) + 税额
            dataList[i]['amount_tax_inclusive'] = roundNumber((Number(dataList[i]['amount_exclusive_tax']) + Number(dataList[i]['tax_diff'])), 2)
            // 含税销售金额(报价币种) = 含税销售金额(RMB) / 汇率
            dataList[i]['jsb_amount_include_tax'] = roundNumber((Number(dataList[i]['amount_tax_inclusive']) / exchange_rate), 2)
            // 含税销售单价(报价币种) = 含税销售金额(报价币种)/数量  6位小数
            dataList[i]['jsb_unit_price_include_tax'] = roundNumber((Number(dataList[i]['jsb_amount_include_tax']) / applyNum), 6)
            // 含税销售单价(RMB) = 含税销售金额(RMB)/数量  6位小数
            dataList[i]['price_including_tax'] = roundNumber((Number(dataList[i]['amount_tax_inclusive']) / applyNum), 6)
            //差价 = 开票不含税单价-订单原结算币种单价（不含税）
            dataList[i]['price_diff'] = roundNumber((Number(dataList[i]['invoice_price'] - dataList[i]['origin_unit_price_exclude_tax'])), 6)
        }
        dataList[i].object_id = productObj.object_id
        dataList[i].grade_name = productObj.grade_name
        dataList[i].commission_print_number = productObj.commission_print_number
        dataList[i].quotation_factory = productObj.quotation_factory
        dataList[i].stocked_product = productObj.stocked_product
    }
}
return dataList