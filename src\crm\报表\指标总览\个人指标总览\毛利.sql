select year(now()) update_year, '01'  AS update_month,concat(year(now()), '-01') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount,cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m')='01'
group by cust_code
UNION ALL
select year(now()) update_year, '02'  AS update_month,concat(year(now()), '-02') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02')
group by cust_code
UNION ALL
select year(now()) update_year, '03'  AS update_month,concat(year(now()), '-03') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03')
group by cust_code
UNION ALL
select year(now()) update_year, '04'  AS update_month,concat(year(now()), '-04') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04')
group by cust_code
UNION ALL
select year(now()) update_year, '05'  AS update_month,concat(year(now()), '-05') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05')
group by cust_code
UNION ALL
select year(now()) update_year, '06'  AS update_month,concat(year(now()), '-06') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06')
group by cust_code
UNION ALL
select year(now()) update_year, '07'  AS update_month,concat(year(now()), '-07') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07')
group by cust_code
UNION ALL
select year(now()) update_year, '08'  AS update_month,concat(year(now()), '-08') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08')
group by cust_code
UNION ALL
select year(now()) update_year, '09'  AS update_month,concat(year(now()), '-09') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09')
group by cust_code
UNION ALL
select year(now()) update_year, '10'  AS update_month,concat(year(now()), '-10') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10')
group by cust_code
UNION ALL
select year(now()) update_year, '11'  AS update_month,concat(year(now()), '-11') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10','11')
group by cust_code
UNION ALL
select year(now()) update_year, '12'  AS update_month,concat(year(now()), '-12') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount, cust_code from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10','11','12')
group by cust_code
ORDER BY update_month, cust_code; -- 建议在ORDER BY中也加入cust_code，使得结果更清晰