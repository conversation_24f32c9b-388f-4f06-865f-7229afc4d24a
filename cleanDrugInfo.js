function cleanDrugInfo(drugInfo) {

    // 替换常见异常字符
    let cleanedInfo = drugInfo
        .replace(/[\u200b-\u200d\uFEFF]/g, '')          // 移除不可见字符
        .replace(/[（）]/g, '()')                       // 中文括号替换为英文括号
        .replace(/\\/g, '')                              // 移除反斜杠
        .trim();                                        // 去除首尾空白字符

    // 标准化格式
    cleanedInfo = cleanedInfo
        .replace(/\s+/g, ' ')                            // 多个空格替换为单个空格
        .replace(/(\d+)ml/g, '$1 ml')                   // 标准化 "ml" 前的格式
        .replace(/(\d+)mg/g, '$1 mg')                   // 标准化 "mg" 前的格式
        .replace(/(\d+)片/g, '$1 片')                   // 标准化 "片" 前的格式
        .replace(/(\d+)支/g, '$1 支')                   // 标准化 "支" 前的格式
        .replace(/(\d+)单元/g, '$1 单元');             // 标准化 "单元" 前的格式

    return cleanedInfo;
}

// 新增函数：生成 MySQL 替换语句
function generateMySQLReplaceStatement(drugInfo) {
    let mysqlStatement = drugInfo;

    // 替换不可见字符
    mysqlStatement = mysqlStatement
        .replace(/[\u200B-\u200D\uFEFF]/g, '');

    // 替换控制字符
    for (let i = 0; i <= 31; i++) {
        const controlChar = String.fromCharCode(i);
        mysqlStatement = mysqlStatement.replace(new RegExp(controlChar, 'g'), '');
    }

    return mysqlStatement;
}