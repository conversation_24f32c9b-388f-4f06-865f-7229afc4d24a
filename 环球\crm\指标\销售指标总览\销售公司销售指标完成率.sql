-- ETL建表
drop table if exists dws_company_sale_month;
create table dws_company_sale_month
(
    id                int auto_increment primary key comment '自增主键'
        primary key,
    update_year       varchar(10)    null comment '指标年度',
    update_month      varchar(10)    null comment '指标月份',
    update_time       varchar(10)    null comment '指标时间',
    cumulative_amount decimal(20, 2) null comment '累计金额'
) comment '销售公司销售累计';

-- ETL:CRM销售公司销售累计->dws_company_sale_month
WITH RECURSIVE
    months AS (SELECT 1 AS month
               UNION ALL
               SELECT month + 1
               FROM months
               WHERE month < 12),
    sales_data AS (SELECT DATE_FORMAT(t1.create_time, '%m')   AS month,
                          ROUND(SUM(amount_exclusive_tax), 2) AS amount_exclusive_tax
                   FROM crm_sales_order t1
                            JOIN crm_sales_order_product t2
                                 ON t1.sales_order_code = t2.sales_order_code
                                     AND t2.flag_deleted = 0
                   WHERE status != 8
                     AND t1.flag_deleted = 0
                     AND DATE_FORMAT(t1.create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
                   GROUP BY DATE_FORMAT(t1.create_time, '%m'))
SELECT year(now())                                        update_year,
       LPAD(m.month, 2, '0')                           AS update_month,
       concat(year(now()), '-', LPAD(m.month, 2, '0')) AS update_time,
       COALESCE(
               (SELECT SUM(s2.amount_exclusive_tax)
                FROM sales_data s2
                WHERE CAST(s2.month AS SIGNED) <= m.month),
               0
       )                                               AS cumulative_amount
FROM months m
ORDER BY m.month;


select t1.update_time, 100 * cumulative_amount / (t2.sales_target * 10000) as complete_rate
from cockpit.dws_company_sale_month t1
         left join cockpit.ods_metric_company t2 on t1.update_year = t2.metric_year;


select temp1.`year`, temp1.`month`, ifnull(temp2.complete_rate, 0) complete_rate
from (select `year_month`, `year`, `month`
      from cockpit.dim_month dm
      where dm.year in (year(now()), year(now()) - 1)) temp1
         left join (select t1.update_time,round( 100 * cumulative_amount / (t2.sales_target * 10000) ,2) as complete_rate
                    from cockpit.dws_company_sale_month t1
                             left join cockpit.ods_metric_company t2 on t1.update_year = t2.metric_year) temp2
                   on temp1.`year_month` = temp2.update_time
order by temp1.`year`, temp1.`month`
;
