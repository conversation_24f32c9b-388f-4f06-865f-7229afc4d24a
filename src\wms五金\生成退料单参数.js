var pro_accessories_inventory={"inventory_quantity":"1198","create_time":"2024-11-28 16:59:12","mat_type":0,"batch":"00001F","batch_quantity":"200","accessory_code":"N0829001","create_by":"1846028833947742209","update_time":"2025-05-30 14:41:09","table_code":"clarify_the_auxiliary","id":211,"update_by":"1821396441761148929","supplier_code":"GYS0001","flag_deleted":0}
var wms_bill_no="USE250530002_0"
var detail={"accessory_code":"N0829001","create_by":"1821396441761148929","update_time":"2025-05-30 14:39:56","quantity":"1","create_time":"2025-05-30 14:39:56","inventory_id":"211","batch":"00001F","id":299,"update_by":"1821396441761148929","pick_code":"USE250530002","flag_deleted":0}
var pick_code="USE250530002"

var  pro_inventory = pro_accessories_inventory
var  pro_record_out_detail =   detail
var res={
    "table_code": "raw_material", //固定值
    "billNo": wms_bill_no,         //退料单号,不能重复
    "pickNo": pro_inventory.pick_no,  //wms领料单号,与list一致
    "operType": "1",
    "list": [
      {
        "pickNo": pro_inventory.pick_no,  
        "pickRowItem":pro_inventory.pick_row_item,  //wms领料单行号
        "qty": pro_record_out_detail.quantity
      }
    ]
  }
  console.log(JSON.stringify(res))