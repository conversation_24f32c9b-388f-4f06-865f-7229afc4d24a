-- ETL:运营部-累计产值->dwd_accumulated_output_value
-- 累计产值 查询当月生产成品已完成的总金额(成品入库数据)
SELECT COALESCE(ROUND(SUM(COALESCE(c.report_num, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')                                                      AS time
FROM pm_finsih_project_stock c
         LEFT JOIN pm_job_order a on c.pro_batch = a.production_batch_number
         LEFT JOIN pm_order_product b ON a.order_code = b.sales_number AND a.material_code = b.product_number
WHERE c.stock_type in (1)
  and c.storage_status = 4
  AND YEAR(c.update_time) = YEAR(CURDATE())
  AND MONTH(c.update_time) = MONTH(CURDATE())
# and a.flag_deleted=0
# and b.flag_deleted=0
# and c.flag_deleted=0
;
SELECT COALESCE(ROUND(SUM(COALESCE(c.report_num, 0) * COALESCE(b.unit_price_no, 0)) / 10000, 0), 0) AS value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')                                                      AS time
FROM
           pm_job_order a
         LEFT JOIN pm_order_product b ON a.order_code = b.sales_number AND a.material_code = b.product_number
WHERE

a.flag_deleted=0
  and b.flag_deleted=0


;
select * from cockpit.dwd_accumulated_output_value;
-- 新
select product_big_category,
       round(production_value/10000) production_value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') AS time
from dwd_production_value_month
where update_month = date_format(now(), '%Y-%m');

select product_big_category,
       round(sum(production_quantity)/10000) production_quantity,
       round(sum(production_value)/10000) production_value,
       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') AS time
    from
(SELECT
    dpbc.code AS product_big_category,
    dpbc.name AS product_big_category_name,
    COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0)) , 0), 0) AS production_quantity,
    COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0) * COALESCE(opop.unit_price_no, 0)) , 0), 0) AS production_value
FROM
    cockpit.dim_product_big_category dpbc
        LEFT JOIN cockpit.ods_pm_job_order opjo ON
        opjo.large_category = dpbc.code
        LEFT JOIN cockpit.ods_pm_finsih_project_stock opfps ON
        opfps.pro_batch = opjo.production_batch_number
        LEFT JOIN cockpit.ods_pm_order_product opop ON
        opjo.order_code = opop.sales_number
            AND opjo.material_code = opop.product_number
WHERE
    opjo.flag_deleted=0
  and opfps.flag_deleted=0
  and opop.flag_deleted=0
  and opfps.stock_type in  ('1','4')
  AND opfps.storage_status = '4'
  AND DATE_FORMAT(opfps.update_time, '%Y-%m') = date_format(curdate(),'%Y-%m')
GROUP BY
    DATE_FORMAT(opfps.update_time, '%Y-%m-%d'),
    dpbc.code,
    dpbc.name
    )temp group by product_big_category ;
select date_format(curdate(),'%Y-%m');

-- 实时总量 - 0点获取历史总量
select sum(ROUND(received_quantity*unit_price_no,3)) as total_out from pm_job_order
                where   update_time <'2025-04-28 00:00:00';

select order_code,material_code,version,received_quantity,ROUND(received_quantity*unit_price_no,3) as total_out from pm_job_order where update_time < '2025-04-30 23:59:00'  and update_time > '2025-04-01 00:00:00' ;
