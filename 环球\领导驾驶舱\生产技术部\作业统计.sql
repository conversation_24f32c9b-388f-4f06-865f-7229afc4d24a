-- 作业统计ETL
SELECT
    plate_make_operation_code,
    production_lot_number AS production_batch_number,
    product_number AS product_code,
    product_name,
    DATE(t1.create_time) AS issue_date,
    MAX_DATE.actual_completion_time_the AS completion_date,
    UPPER(color_order) color_order,
    COUNT_COLOR.color_count,
    COUNT_SPECIAL_COLOR.special_color_count,
    DATE_FORMAT(DATE(t1.create_time),'%Y-%m') update_month
FROM
    cockpit.ods_pm_printing_operation_management t1
        LEFT JOIN
    (SELECT job_strip_number, DATE(MAX(actual_completion_time_the)) AS actual_completion_time_the
     FROM cockpit.ods_pm_final_batch_job_audit
     WHERE flag_deleted = 0
     GROUP BY job_strip_number) MAX_DATE ON MAX_DATE.job_strip_number = t1.job_bar_id
        LEFT JOIN
    (SELECT plate_management_id, COUNT(DISTINCT color_code) AS color_count
     FROM cockpit.ods_pm_color_number
     WHERE flag_deleted = 0
     GROUP BY plate_management_id) COUNT_COLOR ON COUNT_COLOR.plate_management_id = t1.id
        LEFT JOIN
    (SELECT plate_management_id, COUNT(DISTINCT CASE WHEN UPPER(REPLACE(color_code, '/', '')) NOT IN ('K', 'C', 'M', 'Y') THEN color_code END) AS special_color_count
     FROM cockpit.ods_pm_color_number
     WHERE flag_deleted = 0
     GROUP BY plate_management_id) COUNT_SPECIAL_COLOR ON COUNT_SPECIAL_COLOR.plate_management_id = t1.id
WHERE
    t1.flag_deleted = 0
  AND t1.status = 3
GROUP BY
    t1.plate_make_operation_code, t1.production_lot_number, t1.product_number, t1.product_name, DATE(t1.create_time), color_order
ORDER BY
    issue_date ASC,t1.production_lot_number asc
LIMIT :page_size OFFSET :offset;

-- sql查询
select
           production_batch_number,
           product_code,
           product_name,
           issue_date,
           completion_date,
           color_count,
           special_color_count,
           color_order,
           update_month
from cockpit.dwd_job_color_order_month djcom
where
((:update_month IS NULL OR :update_month = '') OR (update_month = :update_month))
AND ((:start_color_count IS NULL OR :start_color_count = '') OR (color_count >= :start_color_count))
AND ((:end_color_count IS NULL OR :end_color_count = '') OR (color_count <= :end_color_count))
AND ((:production_batch_number IS NULL OR :production_batch_number = '') OR (production_batch_number = :production_batch_number))
AND ((:color_order IS NULL OR :color_order = '') OR (color_order LIKE CONCAT('%', :color_order, '%')))
ORDER BY
    issue_date ASC,production_batch_number asc
LIMIT :page_size OFFSET :offset
