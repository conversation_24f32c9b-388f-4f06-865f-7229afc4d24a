const { parentPort } = require('worker_threads');
const mysql = require('mysql2/promise');

function getFormattedTime() {
    return new Date().toISOString().replace('T', ' ').substr(0, 19);
}

async function processRecords(data) {
    const { records, unit_data, dbConfig, workerId } = data;
    
    // 创建连接池而不是单个连接
    const pool = mysql.createPool({
        ...dbConfig,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0
    });
    
    console.log(`[${getFormattedTime()}] Worker ${workerId}: 数据库连接池创建成功`);

    let processedCount = 0;
    let updatedCount = 0;
    const BATCH_SIZE = 100; // 批量处理大小
    let batch = [];

    try {
        // 发送进度更新
        const sendProgress = () => {
            parentPort.postMessage({
                type: 'progress',
                processedCount,
                updatedCount
            });
        };

        // 批量处理函数
        const processBatch = async (items) => {
            if (items.length === 0) return;

            // 使用事务进行批量更新
            const conn = await pool.getConnection();
            await conn.beginTransaction();

            try {
                for (const item of items) {
                    const { material_code, product_version_big, standard_unit_cn } = item;
                    const standard_unit = unit_data[standard_unit_cn];

                    if (!standard_unit) continue;

                    // 合并查询和更新操作
                    await conn.execute(
                        `UPDATE pdm_product_version pv 
                         SET pv.standard_unit = ?, pv.standard_unit_cn = ?
                         WHERE pv.material_code = ? 
                         AND pv.product_version LIKE CONCAT('%', ?, '.%')
                         AND pv.standard_unit_cn != ?`,
                        [standard_unit, standard_unit_cn, material_code, product_version_big, standard_unit_cn]
                    );

                    // 批量更新相关表
                    await conn.execute(
                        `UPDATE pdm_product_bom 
                         SET standard_unit = ?, standard_unit_cn = ? 
                         WHERE material_code = ? 
                         AND product_version LIKE CONCAT('%', ?, '.%') 
                         AND categroy IN (3,7)`,
                        [standard_unit, standard_unit_cn, material_code, product_version_big]
                    );

                    await conn.execute(
                        `UPDATE pdm_material 
                         SET standard_unit = ?, standard_unit_cn = ? 
                         WHERE material_code = ? 
                         AND category = 4`,
                        [standard_unit, standard_unit_cn, material_code]
                    );

                    updatedCount++;
                }

                await conn.commit();
            } catch (error) {
                await conn.rollback();
                throw error;
            } finally {
                conn.release();
            }
        };

        // 主处理循环
        for (const record of records) {
            batch.push(record);
            processedCount++;

            if (batch.length >= BATCH_SIZE) {
                await processBatch(batch);
                batch = [];
                sendProgress();
            }
        }

        // 处理剩余的记录
        if (batch.length > 0) {
            await processBatch(batch);
            sendProgress();
        }

        await pool.end();
        parentPort.postMessage({
            type: 'complete',
            success: true,
            processedCount,
            updatedCount
        });
        
    } catch (error) {
        console.error(`[${getFormattedTime()}] Worker ${workerId} 错误:`, error);
        parentPort.postMessage({
            type: 'complete',
            success: false,
            error: error.message
        });
        await pool.end();
    }
}

parentPort.on('message', processRecords); 