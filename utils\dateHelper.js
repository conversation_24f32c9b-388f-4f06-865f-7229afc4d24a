const moment = require('moment');

class DateHelper {
    static formatDate(date) {
        return moment(date).format('YYYY-MM-DD');
    }

    static formatDateTime(date) {
        return moment(date).format('YYYY-MM-DD HH:mm:ss');
    }

    static getDateRange(days) {
        const endDate = moment().format('YYYY-MM-DD');
        const startDate = moment().subtract(days, 'days').format('YYYY-MM-DD');
        return { startDate, endDate };
    }

    static isValidDate(date) {
        return moment(date, 'YYYY-MM-DD', true).isValid();
    }
}

module.exports = DateHelper; 