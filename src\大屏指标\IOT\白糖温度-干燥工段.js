var echarts = require('echarts');
var biChartData = {
  data: [
    { date: "2023-08-01 00:00:00", S1: 15, S2: 25 },
    { date: "2023-08-01 04:00:00", S1: 30, S2: 45 },
    { date: "2023-08-01 08:00:00", S1: 45, S2: 60 },
    { date: "2023-08-01 12:00:00", S1: 55, S2: 70 },
    { date: "2023-08-01 16:00:00", S1: 65, S2: 80 },
    { date: "2023-08-01 20:00:00", S1: 75, S2: 90 },
    { date: "2023-08-02 00:00:00", S1: 80, S2: 95 },
    { date: "2023-08-02 04:00:00", S1: 85, S2: 90 },
    { date: "2023-08-02 08:00:00", S1: 90, S2: 85 },
    { date: "2023-08-02 12:00:00", S1: 85, S2: 80 },
    { date: "2023-08-02 16:00:00", S1: 80, S2: 75 },
    { date: "2023-08-02 20:00:00", S1: 75, S2: 70 },
    { date: "2023-08-03 00:00:00", S1: 70, S2: 65 },
    { date: "2023-08-03 04:00:00", S1: 65, S2: 60 },
    { date: "2023-08-03 08:00:00", S1: 60, S2: 55 },
    { date: "2023-08-03 12:00:00", S1: 55, S2: 50 },
    { date: "2023-08-03 16:00:00", S1: 50, S2: 45 },
    { date: "2023-08-03 20:00:00", S1: 45, S2: 40 },
    { date: "2023-08-04 00:00:00", S1: 40, S2: 35 },
    { date: "2023-08-04 04:00:00", S1: 35, S2: 30 },
    { date: "2023-08-04 08:00:00", S1: 30, S2: 25 },
    { date: "2023-08-04 12:00:00", S1: 25, S2: 20 },
    { date: "2023-08-04 16:00:00", S1: 20, S2: 15 },
    { date: "2023-08-04 20:00:00", S1: 15, S2: 10 },
    { date: "2023-08-05 00:00:00", S1: 20, S2: 15 },
    { date: "2023-08-05 04:00:00", S1: 25, S2: 20 },
    { date: "2023-08-05 08:00:00", S1: 30, S2: 25 },
    { date: "2023-08-05 12:00:00", S1: 35, S2: 30 },
    { date: "2023-08-05 16:00:00", S1: 40, S2: 35 },
    { date: "2023-08-05 20:00:00", S1: 45, S2: 40 },
    { date: "2023-08-06 00:00:00", S1: 50, S2: 45 },
    { date: "2023-08-06 04:00:00", S1: 55, S2: 50 },
    { date: "2023-08-06 08:00:00", S1: 60, S2: 55 },
    { date: "2023-08-06 12:00:00", S1: 65, S2: 60 },
    { date: "2023-08-06 16:00:00", S1: 70, S2: 65 },
    { date: "2023-08-06 20:00:00", S1: 75, S2: 70 },
    { date: "2023-08-07 00:00:00", S1: 80, S2: 75 },
    { date: "2023-08-07 04:00:00", S1: 85, S2: 80 },
    { date: "2023-08-07 08:00:00", S1: 90, S2: 85 },
    { date: "2023-08-07 12:00:00", S1: 95, S2: 90 },
    { date: "2023-08-07 16:00:00", S1: 90, S2: 95 },
    { date: "2023-08-07 20:00:00", S1: 85, S2: 100 },

  ]
}
let labelArr = biChartData.data.map((k) => k.date);
let arr1 = biChartData.data.map((k) => k['S1']);
let arr2 = biChartData.data.map((k) => k['S2']);

const baseWidth = 1920; // 基准分辨率宽度(1080p)
const scale = window.innerWidth / baseWidth; // 直接计算比例，不限制最小值
// const scale = 1920 / baseWidth; // 直接计算比例，不限制最小值
const option = {
  legend: {
    show: true,
    textStyle: {
      color: "#DDFAFF", // 红色字体
      fontSize: "0.12rem", // 可选：字体大小
    },
    data: ["S1", "S2"], // 图例项
    itemGap: 40 * scale,
    itemWidth: 18 * scale,
    itemHeight: 2 * scale,
    icon: "path://M0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z",
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
  },
  grid: {
    top: "15%",
    bottom: "10%", // 适当调整底部留白
  },
  xAxis: [
    {
      type: "category",
      data: labelArr,
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(63, 105, 255, 0.31)",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        textStyle: {
          color: "#DDFAFF",
        },
        fontSize: "0.12rem",
        margin: 10 * scale, // 控制标签与轴线的距离，默认是 5，增大它会让标签离轴线更远
      },
      
      axisLabel: {
        textStyle: {
          color: "#DDFAFF",
        },
        fontSize: "0.12rem",
        margin: 10 * scale, // 控制标签与轴线的距离，默认是 5，增大它会让标签离轴线更远
        formatter: function(value) {
          // 提取年-月部分 (YYYY-MM)
          return value.substring(5, 10);
        }
      },

      splitLine: {
        show: false,
      },
      boundaryGap: false,
    },
  ],
  yAxis: [
    {
      type: "value",
      axisTick: {
        show: false,
      },
      min: 0,
      max: 100,
      interval: 25,
      axisLabel: {
        textStyle: {
          color: "#DDFAFF",
        },
        fontSize: "0.12rem",
        width: "0.04rem",
      },
      axisLine: {
        show: false, // 不显示纵轴的轴线
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(63, 105, 255, 0.31)", // 修改网格线颜色
          width: 1, // 让网格线更细
        },
      },
    },
  ],
  series: [
    {
      name: "S1",
      type: "line",
      data: arr1,
      symbolSize: 0,
      smooth: '0.4',
      yAxisIndex: 0, // 右侧Y轴
      showSymbol: false,
      emphasis: {
        focus: "series",
      },
      lineStyle: {
        width: 2 * scale,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#00AAFF" },
          { offset: 1, color: "rgba(0,156,255,0.4)" },
        ]), // 你的原始颜色
      },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#00AAFF" },
          { offset: 1, color: "rgba(0,156,255,0.4)" },
        ]), // 你的原始颜色
        borderColor: "#42D2FF",
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "rgba(0,170,255,0.2)" }, // 颜色更浅
          { offset: 1, color: "rgba(0,170,255,0)" },
        ]),
      }
    },
    {
      name: 'S2',
      type: 'line',
      data: arr2,
      symbolSize: 0,
      smooth: '0.4',
      yAxisIndex: 0, // 左侧Y轴
      showSymbol: false,
      emphasis: {
        focus: 'series',
      },
      lineStyle: {
        width: 2 * scale,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#00FFFB" },
          { offset: 1, color: "rgba(0,255,251,0.31)" },
        ]), // 你的原始颜色
      },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "#00FFFB" },
          { offset: 1, color: "rgba(0,255,251,0.31)" },
        ]), // 你的原始颜色
        borderColor: '#ACFF42',
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: "rgba(0,255,251,0.2)" }, // 颜色更浅
          { offset: 1, color: "rgba(0,255,251,0)" },
        ]),
      }
    },
  ],
};
console.log(JSON.stringify( option));