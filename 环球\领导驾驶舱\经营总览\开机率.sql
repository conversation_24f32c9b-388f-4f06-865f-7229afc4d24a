-- 合成SQL查询：今日开机率、周同比、日环比
select ifnull(start_rate, 0) start_rate, ifnull(week_rate, 0) week_rate, ifnull(day_rate, 0) day_rate
from (SELECT
          -- 今日开机率
          COUNT(DISTINCT CASE WHEN oee.action_properties = 7 THEN oee.machine_id END) /
          (SELECT COUNT(*) FROM ods_machine_file WHERE falg_start = 0) * 100 AS start_rate,

          -- 周同比
          (COUNT(DISTINCT CASE WHEN oee.action_properties = 7 THEN oee.machine_id END) -
           (SELECT COUNT(DISTINCT machine_id)
            FROM ods_pm_oee_records
            WHERE DATE(create_time) = CURDATE() - INTERVAL 7 DAY
              AND action_properties = 7)
              ) /
          (SELECT COUNT(DISTINCT machine_id)
           FROM ods_pm_oee_records
           WHERE DATE(create_time) = CURDATE() - INTERVAL 7 DAY
             AND action_properties = 7) * 100                                AS week_rate,

          -- 日环比
          (COUNT(DISTINCT CASE WHEN oee.action_properties = 7 THEN oee.machine_id END) -
           (SELECT COUNT(DISTINCT machine_id)
            FROM ods_pm_oee_records
            WHERE DATE(create_time) = CURDATE() - INTERVAL 1 DAY
              AND action_properties = 7)
              ) /
          (SELECT COUNT(DISTINCT machine_id)
           FROM ods_pm_oee_records
           WHERE DATE(create_time) = CURDATE() - INTERVAL 1 DAY
             AND action_properties = 7) * 100                                AS day_rate

      FROM ods_pm_oee_records oee
      WHERE DATE(oee.create_time) = CURDATE()) t; -- 只获取当天的记录


select 67,
       count(distinct oee.machine_id),
       3,
       67 - count(distinct oee.machine_id),
       date(create_time) update_time
FROM ods_pm_oee_records oee
where action_properties = 7
group by date(create_time)
order by update_time desc
limit 20
;

create table dws_machine_status_history
(
    id          int(10) auto_increment primary key,
    total       int  null comment '机台总数',
    run         int  null comment '生产中',
    maintain    int  null comment '维修中',
    stop        int  null comment '停机中',
    update_time date null comment '更新日期'
) comment '机台状态历史';

SELECT sum(zg) total, sum(sc) run, sum(wx) maintain, sum(jc) stop, curdate() update_time
from dwd_jingying_gxcl;
select *
from dws_machine_status_history;

SELECT 100 * today_run / today_total                                                         start_rate,
       100 * ifnull((today_run - IFNULL(yestoday_run, 0)) / ifnull(yestoday_run, 0), 0)   AS day_rate,
       100 * ifnull((today_run - IFNULL(last_week_run, 0)) / ifnull(last_week_run, 0), 0) AS week_rate
FROM (SELECT (SELECT COALESCE(SUM(run), 0) FROM dws_machine_status_history WHERE update_time = CURDATE()) AS today_run,
             (SELECT COALESCE(SUM(total), 0)
              FROM dws_machine_status_history
              WHERE update_time = CURDATE())                                                              AS today_total,
             (SELECT COALESCE(SUM(run), 0)
              FROM dws_machine_status_history
              WHERE update_time = DATE_SUB(CURDATE(), INTERVAL 1 DAY))                                    AS yestoday_run,
             (SELECT COALESCE(SUM(run), 0)
              FROM dws_machine_status_history
              WHERE update_time = DATE_SUB(CURDATE(), INTERVAL 1 WEEK))                                   AS last_week_run) AS data;
