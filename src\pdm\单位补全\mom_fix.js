const fs = require('fs');
const csv = require('csv-parser');
const mysql = require('mysql2/promise');
const fastCsv = require('fast-csv');

// 配置参数
const BATCH_SIZE = 100; // 每批处理的记录数
const CONCURRENT_BATCHES = 5; // 并发批次数
const MAX_RETRIES = 3; // 最大重试次数

// 添加日志时间格式化函数
function getFormattedTime() {
    const now = new Date();
    const utc8Time = new Date(now.getTime() + 8 * 60 * 60 * 1000);
    return utc8Time.toISOString().replace('T', ' ').substr(0, 19)
}

// 修复 unit_data 声明
const unit_data = {
    "包": "6567",
    "分米": "6568",
    "秒": "6569",
    "瓶": "6570",
    "斤": "6571",
    "部": "6572",
    "PCS": "6573",
    "厘米": "6574",
    "张": "6575",
    "罐": "6576",
    "套": "6577",
    "千只": "6578",
    "米": "6579",
    "平方米": "6580",
    "盒": "6581",
    "吨": "6582",
    "支": "6583",
    "天": "6584",
    "桶": "6585",
    "m2": "6586",
    "分": "6587",
    "kg": "6588",
    "本": "6589",
    "克": "6590",
    "只": "6591",
    "根": "6592",
    "百只": "6593",
    "L": "6594",
    "袋": "6595",
    "升": "6596",
    "枚": "6597",
    "块": "6598",
    "箱": "6599",
    "片": "6600",
    "平方千米": "6601",
    "卷": "6602",
    "公斤": "6603",
    "千克": "6604",
    "千米": "6605",
    "个": "6606",
    "小时": "6607",
    "千枚": "6608",
    "立方米": "6609",
    "台": "6610",
    "把": "6611",
    "mm": "9077",
    "1": "9078",
    "2": "9079",
    "3": "9080",
    "4": "9081",
    "5": "9082",
    "桶/20": "9177",
    "盘/50": "9178",
    "盘/10": "9179",
    "盘190": "9180",
    "ts": "9181",
    "包/50": "9182",
    "盘": "9183",
    "PCE": "9184",
    "盒/12": "9185",
    "套/2件": "9186",
    "卷/50": "9187",
    "卷/15": "9188",
    "台/短": "9189",
    "付": "9190",
    "吨（T）": "9191",
    "对/2件": "9192",
    "Φ20*": "9193",
    "管": "9194",
    "盘/米": "9195",
    "50": "9196",
    "副": "9197",
    "桶/5升": "9198",
    "1对": "9199",
    "把/2片": "9200",
    "PC": "9201",
    "节": "9202",
    "套/台": "9203",
    "对": "9204",
    "条": "9205",
    "卷/": "9206",
    "颗": "9207",
    "盒/3支": "9208",
    "组": "9209",
    "件": "9210",
    "季度": "23005",
    "月": "23006",
    "半年": "23007",
    "年": "23008",
    "瓦时": "23009",
    "千瓦时": "23010",
    "顶": "23032",
    "提": "23033",
    "双": "23034",
    "pcs": "23727",
    "包100": "25828"
  }

// 添加数据验证函数
function validateRecord(record, lineNumber) {
    const requiredFields = ['material_code', 'product_version_big', 'standard_unit_cn'];
    const missingFields = requiredFields.filter(field => !record[field]);
    
    if (missingFields.length > 0) {
        console.warn(`[${getFormattedTime()}] 第${lineNumber}行数据缺少必要字段: ${missingFields.join(', ')}`);
        return false;
    }
    return true;
}

// 处理单个批次的数据
async function processBatch(records, batchIndex, connection) {
    try {
        // await connection.beginTransaction();
        console.log(`[${getFormattedTime()}] 开始处理批次 ${batchIndex}, 数据量 ${records.length}`);

        for (const record of records) {
            const { material_code, product_version_big } = record;
            
            // 查询产品版本数据
            const [versions] = await connection.execute(
                `SELECT id, material_code, standard_unit 
                 FROM mes_product_version 
                 WHERE material_code = ? 
                 AND product_version LIKE CONCAT(?, '%')`,
                [material_code, product_version_big]
            );
            
            // 如果没有找到版本数据，则跳过当前记录
            if (!versions || versions.length === 0) {
                // console.log(`[${getFormattedTime()}] 批次${batchIndex} - 未找到版本数据 - 物料码: ${material_code}`);
                continue;
            }

            for (const version of versions) {
                //如果version为空,则调过
                if (!version) {
                    console.log(`[${getFormattedTime()}] 批次${batchIndex} - 版本数据为空 - 物料码: ${material_code}`);
                    continue;
                }
                // 如果单位不一致，进行更新
                const standard_unit = unit_data[record.standard_unit_cn];
                if (version.standard_unit != standard_unit) {
                    
                    // console.log(`[${getFormattedTime()}] 批次${batchIndex} - 更新单位 - 物料码: ${material_code}`);

                    // 更新 pdm_product_version
                    await connection.execute(
                        `UPDATE mes_product_version 
                         SET standard_unit = ?
                         WHERE material_code = ? 
                         AND product_version LIKE CONCAT(?, '%')`,
                        [standard_unit, material_code, product_version_big]
                    );

                    // 更新 pdm_product_bom
                    // await connection.execute(
                    //     `UPDATE mes_product_bom_list 
                    //      SET standard_unit = ?
                    //      WHERE material_code = ? 
                    //      AND product_version LIKE CONCAT(?, '.%') 
                    //      AND categroy IN (3,7)`,
                    //     [standard_unit, material_code, product_version_big]
                    // );

                    // 更新 pdm_material
                    await connection.execute(
                        `UPDATE mes_material_file 
                         SET standard_unit = ?
                         WHERE material_code = ? 
                         AND category = 4`,
                        [standard_unit, material_code]
                    );
                }
            }
        }

        // await connection.commit();
        console.log(`[${getFormattedTime()}] 批次 ${batchIndex} 处理完成`);
        return true;
    } catch (error) {
        await connection.rollback();
        console.error(`[${getFormattedTime()}] 批次 ${batchIndex} 处理失败:`, error);
        throw error;
    }
}

// 创建数据库连接池
const pool = mysql.createPool({
    // host: '**************',
    // user: 'root',
    // password: 'of!S84kf',
    host: '**************',
    user: 'root',
    password: '123456',
    database: 'hq_mdm_b',
    connectionLimit: CONCURRENT_BATCHES + 1
});

async function main() {
    console.log(`[${getFormattedTime()}] 开始执行数据更新程序...`);
    
    // 读取CSV文件
    const records = [];
    await new Promise((resolve, reject) => {
        fastCsv.parseFile('src\\pdm\\单位补全\\产品档案-bip.csv', {
            headers: true,
            encoding: 'utf-8',
            delimiter: ',',
            ignoreEmpty: true
        })
        .on('data', (data) => records.push(data))
        .on('error', (error) => {
            console.error(`[${getFormattedTime()}] CSV解析错误:`, error);
            reject(error);
        })
        .on('end', (rowCount) => {
            console.log(`[${getFormattedTime()}] CSV文件读取完成，共 ${rowCount} 行，有效记录 ${records.length} 条`);
            resolve();
        });
    });

    // 将记录分成批次
    const batches = [];
    for (let i = 0; i < records.length; i += BATCH_SIZE) {
        batches.push(records.slice(i, i + BATCH_SIZE));
    }

    console.log(`[${getFormattedTime()}] 共分成 ${batches.length} 个批次，每批 ${BATCH_SIZE} 条记录`);

    // 并发处理批次
    try {
        for (let i = 0; i < batches.length; i += CONCURRENT_BATCHES) {
            const currentBatches = batches.slice(i, Math.min(i + CONCURRENT_BATCHES, batches.length));
            const batchPromises = currentBatches.map(async (batch, idx) => {
                const connection = await pool.getConnection();
                try {
                    let retries = 0;
                    while (retries < MAX_RETRIES) {
                        try {
                            await processBatch(batch, i + idx + 1, connection);
                            break;
                        } catch (error) {
                            retries++;
                            if (retries >= MAX_RETRIES) {
                                throw error;
                            }
                            console.log(`[${getFormattedTime()}] 批次 ${i + idx + 1} 重试第 ${retries} 次`);
                            await new Promise(resolve => setTimeout(resolve, 1000 * retries));
                        }
                    }
                } finally {
                    connection.release();
                }
            });

            await Promise.all(batchPromises);
            console.log(`[${getFormattedTime()}] 完成批次组 ${i + 1} 到 ${i + currentBatches.length}`);
        }

        console.log(`[${getFormattedTime()}] 所有数据处理完成`);
    } catch (error) {
        console.error(`[${getFormattedTime()}] 程序执行出错:`, error);
    } finally {
        await pool.end();
    }
}

main().catch(error => {
    console.error(`[${getFormattedTime()}] 程序执行出错:`, error);
    process.exit(1);
});