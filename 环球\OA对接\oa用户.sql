select distinct t3.dept_id
              , t3.name dept_name
              , t1.name
              , t1.nickname
              ,t3.parent_id
              , t1.*
from sys_user t1
         left join tenant_dept_user t2 on t1.user_id = t2.user_id
         left join sys_dept t3 on t3.dept_id = t2.dept_id
where t1.name in ('mom') and t1.del_flag=0
;

select distinct t3.dept_id
              , t3.name dept_name
              , t1.name
              , t1.*
from sys_user t1
         left join tenant_dept_user t2 on t1.user_id = t2.user_id
         left join sys_dept t3 on t3.dept_id = t2.dept_id
where t1.user_id in ('1744971763818213377');

select distinct t1.h3c_id, concat(t1.user_id,'')  as user_id, t1.username, t1.name, concat(t3.dept_id,'') dept_id, t3.name dept_name,t5.post_id,t5.post_name
from h3c_biip_usercenter.sys_user t1
         left join h3c_biip_usercenter.tenant_dept_user t2 on t1.user_id = t2.user_id and t2.del_flag=0
         left join h3c_biip_usercenter.sys_dept t3 on t3.dept_id = t2.dept_id and t3.del_flag=0
         left join h3c_biip_usercenter.sys_user_post t4 on t1.user_id=t4.user_id
         left join h3c_biip_usercenter.sys_post t5 on t4.post_id=t5.post_id and t5.del_flag=0
where t1.del_flag=0
  AND ((:username IS NULL OR :username = '') OR (username = :username))
#   AND ((:name IS NULL OR :name = '') OR (t1.name = :name))
#   AND ((:dept_id IS NULL OR :dept_id = '') OR (t3.dept_id = :dept_id))
#   AND ((:post_id IS NULL OR :post_id = '') OR (t5.post_id = :post_id))
# limit :page_size offset :offset
;
