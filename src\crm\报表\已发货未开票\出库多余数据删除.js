const mysql = require('mysql');
const util = require('util');

// 数据库配置
const dbConfig = {
    host: '**************',
    user: 'root',
    password: '123456',
    database: 'h3chq_crmbusiness1704287359505'
};

// const dbConfig = {
//     host: '**************',
//     user: 'root',
//     password: 'of!S84kf',
//     database: 'h3chq_crmbusiness1704287359505'
// };


// 创建数据库连接池
const pool = mysql.createPool(dbConfig);
const query = util.promisify(pool.query).bind(pool);

async function updateInvoiceDetails(data) {
    try {
        // 1. 查询所有数据
        const selectQuery = `
            select id,outbound_line_id cgeneralbid,outbound_header cgeneralhid from bip_outbound_order_detail
        `;
        const results = await query(selectQuery);

        console.log('数据库记录样本:', JSON.stringify(results.slice(0, 3)),results.length);
        console.log('API数据样本:', JSON.stringify(data.slice(0, 3)),data.length);

        const matchedIds = [];
        const unmatchedIds = [];

        // 2. 遍历查询数据进行匹配
        for (const row of results) {
            const matchedData = data.find(item => {
                // 确保两边的值都存在
                if (!item.cgeneralbid || !item.cgeneralhid || !row.cgeneralbid || !row.cgeneralhid) {
                    console.log('存在空值:', {
                        row_id: row.id,
                        row_cgeneralbid: row.cgeneralbid,
                        row_cgeneralhid: row.cgeneralhid,
                        item_cgeneralbid: item.cgeneralbid,
                        item_cgeneralhid: item.cgeneralhid
                    });
                    return false;
                }
                
                // 转换为字符串进行比较，避免类型不一致的问题
                return String(item.cgeneralbid) === String(row.cgeneralbid) && 
                       String(item.cgeneralhid) === String(row.cgeneralhid);
            });

            if (!matchedData) {
                unmatchedIds.push(row.id);
                // 3. 更新未匹配的记录
                const updateQuery = `
                    UPDATE bip_outbound_order_detail 
                    SET flag_deleted=1
                    WHERE id = ?
                `;
                await query(updateQuery, [row.id]);
            } else {
                matchedIds.push(row.id);
            }
        }

        console.log('匹配的ID:', matchedIds);
        console.log('未匹配的ID:', unmatchedIds);
        console.log(`总计: ${results.length} 条记录`);
        console.log(`匹配: ${matchedIds.length} 条记录`);
        console.log(`未匹配: ${unmatchedIds.length} 条记录`);
        console.log('数据库记录总数:', results.length);
        console.log('API数据总数:', data.length);
        console.log('匹配率:', (matchedIds.length / results.length * 100).toFixed(2) + '%');

        return { 
            success: true, 
            message: '数据更新成功',
            stats: {
                total: results.length,
                matched: matchedIds.length,
                unmatched: unmatchedIds.length,
                matchedIds,
                unmatchedIds
            }
        };
    } catch (error) {
        console.error('更新失败:', error);
        return { success: false, error: error.message };
    } finally {
        // 关闭连接池
        pool.end();
    }
}

async function fetchDataWithToken() {
    // 第一个请求 - 获取 token
    const tokenHeaders = new Headers();
    tokenHeaders.append("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
    tokenHeaders.append("Accept", "*/*");
    tokenHeaders.append("Host", "**************:30783");
    tokenHeaders.append("Connection", "keep-alive");

    const tokenRequestOptions = {
        method: 'GET',
        headers: tokenHeaders,
        redirect: 'follow'
    };

    try {
        // 获取 token
        const tokenResponse = await fetch("http://**************:30783/mom_test/hqErpSyn/erptoken", tokenRequestOptions);
        const tokenData = await tokenResponse.json();
        const token = tokenData.token;

        // 第二个请求 - 使用获取的 token
        const queryHeaders = new Headers();
        queryHeaders.append("access_token", token); // 使用获取的 token
        queryHeaders.append("ucg_flag", "y");
        queryHeaders.append("signature", "db9f1d9f52d5912af0abfda515e122224f026037efc5c8ee50a8ed49796de6e2");
        queryHeaders.append("repeat_check", "Y");
        queryHeaders.append("client_id", "jiekou");
        queryHeaders.append("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
        queryHeaders.append("Content-Type", "application/json;charset=utf-8");
        queryHeaders.append("Accept", "*/*");
        queryHeaders.append("Host", "************:5000");
        queryHeaders.append("Connection", "keep-alive");

        const raw = {
            "begintime": "2024-01-09",
            "endtime": "2026-10-09"
        };

        const queryRequestOptions = {
            method: 'POST',
            headers: queryHeaders,
            body: JSON.stringify(raw),
            redirect: 'follow'
        };

        // 发送带有 token 的请求
        const queryResponse = await fetch("http://************:5000/nccloud/api/ic/salesOutsHQOpenapi/queryHQsalesOuts", queryRequestOptions);
        const result = await queryResponse.json();
        return result;
    } catch (error) {
        console.error('Error:', error); // 错误处理
        throw error;
    }
}

// 主函数
async function main() {
    try {
        const response = await fetchDataWithToken();
        const data = response.data;
        
        if (!data) {
            throw new Error('No data received from API');
        }

        const result = await updateInvoiceDetails(data);
        console.log(result);
    } catch (error) {
        console.error('Error:', error);
    }
}

// 执行主函数
main();
