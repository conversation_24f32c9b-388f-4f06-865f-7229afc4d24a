import pandas as pd
import os

def compare_csv_files():
    # 读取CSV文件
    old_df = pd.read_csv('order1.csv')
    new_df = pd.read_csv('order2.csv')
    
    # 找出在old中存在但在new中不存在的数据
    diff_df = pd.concat([old_df, new_df]).drop_duplicates(keep=False)
    diff_df = diff_df[diff_df.index.isin(old_df.index)]
    
    # 将结果保存到新的CSV文件
    output_file = 'difference_records.csv'
    diff_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f'总共发现 {len(diff_df)} 条记录在old.csv中存在但在new.csv中不存在')
    print(f'详细结果已保存到 {output_file}')

if __name__ == '__main__':
    compare_csv_files() 