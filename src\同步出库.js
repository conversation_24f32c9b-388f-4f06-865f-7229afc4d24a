var dataList = [{
    "code": "WLSW0000061",
    "materialmnecode": "WLSW000005",
    "user_name": "任延亮",
    "fbillflag": 3,
    "nnum": 380,
    "version": 1,
    "vdef2": "25060249A",
    "vbillcode": "1XC2506000632",
    "crowno": "10",
    "dbizdate": "2025-06-25 16:17:33",
    "vbdef1": "4571",
    "name": "预灌封大箱",
    "cgeneralhid": "1001A5100000012XWBVB",
    "creationtime": "2025-06-28 15:00:39",
    "vbdef41": null,
    "cgeneralbid": "1001A5100000012XWBVC",
    "tax_rate": 13
}]
var productList = [
    {
        "factory_plan_delivery_date": null,
        "sales_plan_delivery_date": null,
        "invoiced_quantity": "0",
        "delivered_quantity": "38000",
        "stock_quantity": null,
        "order_quantity_after_split": "38000",
        "total_order_quantity": "38000",
        "type": null,
        "show_update_by": null,
        "show_create_by": null,
        "delivery_date": "2025-06-21",
        "remark": "客户料号：C2004 委印单号：2025-PD-2-04-0245",
        "settlement_currency_amount_exclusive": "9415.94",
        "settlement_currency_price_exclusive": "0.247788",
        "settlement_currency_amount_inclusive": "10640.00",
        "settlement_currency_price_inclusive": "0.28",
        "tax_rate": "13",
        "exchange_rate": "1",
        "settlement_currency": "人民币",
        "amount_exclusive_tax": "9415.94",
        "unit_price_exclusive": "0.247788",
        "amount_tax_inclusive": "10640.00",
        "unit_price": "0.28",
        "current_issue_quantity": "38000",
        "issued_quantity": "0",
        "stock_order_code": null,
        "quantity_fluctuation": "±0",
        "product_quantity": "38000",
        "sub_class": "生物制品类",
        "main_class": "折叠纸盒类",
        "standard_unit": "只",
        "mnemonic_code": "ZDSW000058",
        "grade_name": "预灌封小盒",
        "product_version": "1.6",
        "object_id": "C2004",
        "material_code": "ZDSW0001331",
        "material_name": "预灌封小盒",
        "contract_management_code": "25060249",
        "sales_order_code": "25060249A",
        "id": 4569,
        "flag_deleted": 0,
        "version": null,
        "create_by": "1892831214991175686",
        "create_time": "2025-06-13 16:38:29",
        "update_by": "null",
        "update_time": "2025-07-08 15:02:13",
        "domain_id": null,
        "contract_product_line_number": "1",
        "csaleorderbid": "1001A5100000012Q94SK",
        "stocked_product": "否",
        "unit_price_excluding_usd": "0.247788",
        "unit_price_usd": "0.280000",
        "tax_inclusive": "含税",
        "commission_print_number": "2025-PD-2-04-0245",
        "quotation_factory": "凌峰"
    },
    {
        "factory_plan_delivery_date": null,
        "sales_plan_delivery_date": null,
        "invoiced_quantity": "0",
        "delivered_quantity": "38000",
        "stock_quantity": null,
        "order_quantity_after_split": "38000",
        "total_order_quantity": "38000",
        "type": null,
        "show_update_by": null,
        "show_create_by": null,
        "delivery_date": "2025-06-21",
        "remark": "客户料号：C2005 委印单号：2025-PD-2-04-0245",
        "settlement_currency_amount_exclusive": "8743.34",
        "settlement_currency_price_exclusive": "0.230088",
        "settlement_currency_amount_inclusive": "9880.00",
        "settlement_currency_price_inclusive": "0.26",
        "tax_rate": "13",
        "exchange_rate": "1",
        "settlement_currency": "人民币",
        "amount_exclusive_tax": "8743.34",
        "unit_price_exclusive": "0.230088",
        "amount_tax_inclusive": "9880.00",
        "unit_price": "0.26",
        "current_issue_quantity": "38000",
        "issued_quantity": "0",
        "stock_order_code": null,
        "quantity_fluctuation": "±0",
        "product_quantity": "38000",
        "sub_class": "生物制品类",
        "main_class": "折叠纸盒类",
        "standard_unit": "只",
        "mnemonic_code": "ZDSW000057",
        "grade_name": "预灌封内托",
        "product_version": "1.4",
        "object_id": "C2005",
        "material_code": "ZDSW0001332",
        "material_name": "预灌封内托",
        "contract_management_code": "25060249",
        "sales_order_code": "25060249A",
        "id": 4570,
        "flag_deleted": 0,
        "version": null,
        "create_by": "1892831214991175686",
        "create_time": "2025-06-13 16:38:29",
        "update_by": "null",
        "update_time": "2025-07-08 15:02:13",
        "domain_id": null,
        "contract_product_line_number": "2",
        "csaleorderbid": "1001A5100000012Q94SL",
        "stocked_product": "否",
        "unit_price_excluding_usd": "0.230088",
        "unit_price_usd": "0.260000",
        "tax_inclusive": "含税",
        "commission_print_number": "2025-PD-2-04-0245",
        "quotation_factory": "凌峰"
    },
    {
        "factory_plan_delivery_date": null,
        "sales_plan_delivery_date": null,
        "invoiced_quantity": "0",
        "delivered_quantity": "380",
        "stock_quantity": null,
        "order_quantity_after_split": "380",
        "total_order_quantity": "380",
        "type": null,
        "show_update_by": null,
        "show_create_by": null,
        "delivery_date": "2025-06-21",
        "remark": "客户料号：C3006 委印单号：2025-PD-2-04-0245",
        "settlement_currency_amount_exclusive": "3699.12",
        "settlement_currency_price_exclusive": "9.734526",
        "settlement_currency_amount_inclusive": "4180.00",
        "settlement_currency_price_inclusive": "11",
        "tax_rate": "13",
        "exchange_rate": "1",
        "settlement_currency": "人民币",
        "amount_exclusive_tax": "3699.12",
        "unit_price_exclusive": "9.734526",
        "amount_tax_inclusive": "4180.00",
        "unit_price": "11",
        "current_issue_quantity": "380",
        "issued_quantity": "0",
        "stock_order_code": null,
        "quantity_fluctuation": "±0",
        "product_quantity": "380",
        "sub_class": "生物制品类",
        "main_class": "瓦楞纸箱类",
        "standard_unit": "套",
        "mnemonic_code": "WLSW000005",
        "grade_name": "预灌封大箱",
        "product_version": "1.3",
        "object_id": "C3006",
        "material_code": "WLSW0000109",
        "material_name": "预灌封大箱",
        "contract_management_code": "25060249",
        "sales_order_code": "25060249A",
        "id": 4571,
        "flag_deleted": 0,
        "version": null,
        "create_by": "1892831214991175686",
        "create_time": "2025-06-13 16:38:29",
        "update_by": "null",
        "update_time": "2025-07-08 15:02:13",
        "domain_id": null,
        "contract_product_line_number": "3",
        "csaleorderbid": "1001A5100000012Q94SM",
        "stocked_product": "否",
        "unit_price_excluding_usd": "9.734526",
        "unit_price_usd": "11.000000",
        "tax_inclusive": "含税",
        "commission_print_number": "2025-PD-2-04-0245",
        "quotation_factory": "凌峰"
    },
    {
        "factory_plan_delivery_date": null,
        "sales_plan_delivery_date": null,
        "invoiced_quantity": "0",
        "delivered_quantity": "3800",
        "stock_quantity": null,
        "order_quantity_after_split": "3800",
        "total_order_quantity": "3800",
        "type": null,
        "show_update_by": null,
        "show_create_by": null,
        "delivery_date": "2025-06-21",
        "remark": "客户料号：C2006 委印单号：2025-PD-2-04-0245",
        "settlement_currency_amount_exclusive": "6557.52",
        "settlement_currency_price_exclusive": "1.725663",
        "settlement_currency_amount_inclusive": "7410.00",
        "settlement_currency_price_inclusive": "1.95",
        "tax_rate": "13",
        "exchange_rate": "1",
        "settlement_currency": "人民币",
        "amount_exclusive_tax": "6557.52",
        "unit_price_exclusive": "1.725663",
        "amount_tax_inclusive": "7410.00",
        "unit_price": "1.95",
        "current_issue_quantity": "3800",
        "issued_quantity": "0",
        "stock_order_code": null,
        "quantity_fluctuation": "±0",
        "product_quantity": "3800",
        "sub_class": "生物制品类",
        "main_class": "折叠纸盒类",
        "standard_unit": "只",
        "mnemonic_code": "ZDSW000060",
        "grade_name": "预灌封中盒",
        "product_version": "1.4",
        "object_id": "C2006",
        "material_code": "ZDSW0001333",
        "material_name": "预灌封中盒",
        "contract_management_code": "25060249",
        "sales_order_code": "25060249A",
        "id": 4572,
        "flag_deleted": 0,
        "version": null,
        "create_by": "1892831214991175686",
        "create_time": "2025-06-13 16:38:29",
        "update_by": "null",
        "update_time": "2025-07-08 15:02:13",
        "domain_id": null,
        "contract_product_line_number": "4",
        "csaleorderbid": "1001A5100000012Q94SN",
        "stocked_product": "否",
        "unit_price_excluding_usd": "1.725663",
        "unit_price_usd": "1.950000",
        "tax_inclusive": "含税",
        "commission_print_number": "2025-PD-2-04-0245",
        "quotation_factory": "凌峰"
    }
]

var productMap = {}
for (var j = 0; j < productList.length; j++) {
    var key = productList[j].material_code + '_' + productList[j].sales_order_code + '_' + productList[j].id;
    productMap[key] = productList[j];
}


for (var i = 0; i < dataList.length; i++) {

    if (productMap[dataList[i].code + '_' + dataList[i].vdef2 + '_' + dataList[i].vbdef1]) {
        var productObj = productMap[dataList[i].code + '_' + dataList[i].vdef2 + '_' + dataList[i].vbdef1]
        //申请数量
        var applyNum = Number(dataList[i].nnum)
        //税率
        dataList[i].tax_rate = productObj.tax_rate
        var abs_tax_rate = Math.abs(productObj.tax_rate)
        //汇率
        dataList[i].exchange_rate = productObj.exchange_rate
        var exchange_rate = Number(productObj.exchange_rate)
        //币种
        dataList[i].settlement_currency = productObj.settlement_currency
        //订单原结算币种单价（不含税）
        dataList[i].origin_unit_price_exclude_tax = Number(productObj.settlement_currency_price_exclusive).toFixed(6)
        //拆分订单的结算币种单价（含税）
        dataList[i].origin_unit_price_include_tax = Number(productObj.settlement_currency_price_inclusive).toFixed(6)
        //拆分订单的结算币种金额（不含税）
        dataList[i].origin_ammount_exclude_tax = Number(productObj.settlement_currency_amount_exclusive).toFixed(2)
        //拆分订单的结算币种金额（含税）
        dataList[i].origin_ammount_include_tax = Number(productObj.settlement_currency_amount_inclusive).toFixed(2)

        dataList[i].split_order_line_no = productObj.contract_product_line_number
        //是否含税
        dataList[i].tax_inclusive = productObj.tax_inclusive
        if (productObj.tax_inclusive == '含税') {
            dataList[i].invoice_include_price = Number(productObj.unit_price_usd).toFixed(6)
            //结算币单价不含税
            dataList[i].jsb_unit_price_include_tax = dataList[i].invoice_include_price
            //结算币金额含税
            dataList[i].jsb_amount_include_tax = Number(dataList[i].jsb_unit_price_include_tax * applyNum).toFixed(2)
            //金额含税
            dataList[i].amount_tax_inclusive = Number(dataList[i].jsb_amount_include_tax * exchange_rate).toFixed(2)
            //单价含税
            dataList[i].price_including_tax = Number(dataList[i].amount_tax_inclusive / applyNum).toFixed(6)
            //金额不含税
            dataList[i].amount_exclusive_tax = Number(dataList[i].amount_tax_inclusive / (1 + abs_tax_rate / 100)).toFixed(2)

            //结算币金额不含税
            dataList[i].jsb_amount_exclude_tax = Number(dataList[i].amount_exclusive_tax / exchange_rate).toFixed(2)
            //结算币单价不含税
            dataList[i].jsb_unit_price_exclude_tax = Number(dataList[i].jsb_amount_exclude_tax / applyNum).toFixed(6)
            //单价不含税
            dataList[i].unit_price_exclusive = Number(dataList[i].amount_exclusive_tax / applyNum).toFixed(6)
            //税额
            dataList[i].tax_diff = (dataList[i].amount_tax_inclusive - dataList[i].amount_exclusive_tax).toFixed(2)
            //差价
            dataList[i].price_diff = (dataList[i].invoice_include_price - dataList[i].origin_unit_price_include_tax).toFixed(2)
        } else if (productObj.tax_inclusive == '不含税') {
            dataList[i].invoice_price = Number(productObj.unit_price_excluding_usd).toFixed(6)
            dataList[i]['jsb_unit_price_exclude_tax'] = dataList[i].invoice_price
            // 不含税销售金额(报价币种) =  不含税销售单价(报价币种)*数量
            dataList[i]['jsb_amount_exclude_tax'] = (Number(dataList[i]['jsb_unit_price_exclude_tax']) * applyNum).toFixed(2)
            // 不含税销售金额(RMB) = 不含税销售金额(报价币种) * 汇率
            dataList[i]['amount_exclusive_tax'] = (Number(dataList[i]['jsb_amount_exclude_tax']) * exchange_rate).toFixed(2)
            // 不含税销售单价(RMB) = 不含税销售金额(RMB) / 数量      保留6位小数
            dataList[i]['unit_price_exclusive'] = (Number(dataList[i]['amount_exclusive_tax']) / applyNum).toFixed(6)
            //税额=不含税销售金额(RMB)*税率
            dataList[i]['tax_diff'] = (Number(dataList[i]['amount_exclusive_tax']) * abs_tax_rate / 100).toFixed(2)
            // 含税销售金额(RMB) = 不含税销售金额(RMB) + 税额
            dataList[i]['amount_tax_inclusive'] = (Number(dataList[i]['amount_exclusive_tax']) + Number(dataList[i]['tax_diff'])).toFixed(2)
            // 含税销售金额(报价币种) = 含税销售金额(RMB) / 汇率
            dataList[i]['jsb_amount_include_tax'] = (Number(dataList[i]['amount_tax_inclusive']) / exchange_rate).toFixed(2)
            // 含税销售单价(报价币种) = 含税销售金额(报价币种)/数量  6位小数
            dataList[i]['jsb_unit_price_include_tax'] = (Number(dataList[i]['jsb_amount_include_tax']) / applyNum).toFixed(6)
            // 含税销售单价(RMB) = 含税销售金额(RMB)/数量  6位小数
            dataList[i]['price_including_tax'] = (Number(dataList[i]['amount_tax_inclusive']) / applyNum).toFixed(6)
            //差价 = 开票不含税单价-订单原结算币种单价（不含税） 
            dataList[i]['price_diff'] = (Number(dataList[i]['invoice_price'] - dataList[i]['origin_unit_price_exclude_tax'])).toFixed(6)

        }

        dataList[i].object_id = productObj.object_id
        dataList[i].grade_name = productObj.grade_name
        dataList[i].commission_print_number = productObj.commission_print_number
        dataList[i].quotation_factory = productObj.quotation_factory
        dataList[i].stocked_product = productObj.stocked_product




    }

}

return dataList