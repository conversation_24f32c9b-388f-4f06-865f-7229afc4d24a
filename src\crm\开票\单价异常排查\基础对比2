//订单原结算币种单价（不含税）
applyObj.origin_unit_price_exclude_tax = roundNumber(Number(productObj.settlement_currency_price_exclusive),6)
//拆分订单的结算币种单价（含税）
applyObj.origin_unit_price_include_tax = roundNumber(Number(productObj.settlement_currency_price_inclusive),6)
//拆分订单的结算币种金额（不含税）
applyObj.origin_ammount_exclude_tax = roundNumber(Number(productObj.settlement_currency_amount_exclusive),6)
//拆分订单的结算币种金额（含税）
applyObj.origin_ammount_include_tax = roundNumber(Number(productObj.settlement_currency_amount_inclusive),6)