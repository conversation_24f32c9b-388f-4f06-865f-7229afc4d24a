SELECT
    -- 接单量金额 (Contract Amount)
    (SELECT round(sum(temp.amount_exclusive_tax), 2) AS contract_amount
     FROM (SELECT case
                      when t1.status in (0, 1, 2) then
                          round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                   t2.amount_exclusive_tax),
                                2)
                      when t1.status = 3 then 0
                      when t1.status = 4 then t4.amount_exclusive_tax
                      end
                      amount_exclusive_tax
           FROM crm_contract_management t1
                    left join crm_contract_management_product t2
                              on t2.contract_management_code = t1.contract_management_code
                    left join crm_cust_basic t3 on t3.cust_code = t1.cust_code
                    left join (SELECT t1.contract_management_code,
                                      round(sum(IF(t2.amount_tax_inclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                                   t2.amount_tax_inclusive)),
                                            2) amount_tax_inclusive,
                                      round(sum(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                                   t2.amount_exclusive_tax)),
                                            2) amount_exclusive_tax,
                                      round(sum(IF(
                                              t2.settlement_currency_amount_exclusive NOT REGEXP
                                              '^-?[0-9]+(\\.[0-9]+)?$', 0,
                                              t2.settlement_currency_amount_exclusive)),
                                            2) settlement_currency_amount_exclusive
                               FROM crm_sales_order t1
                                        LEFT JOIN crm_sales_order_product t2 ON
                                   t1.sales_order_code = t2.sales_order_code
                               WHERE status != 8
                                 AND t1.flag_deleted = 0
                                 AND t2.flag_deleted = 0
                               GROUP BY t1.contract_management_code) t4
                              ON t1.contract_management_code = t4.contract_management_code
           WHERE t1.flag_deleted = 0
             AND t2.flag_deleted = 0
             AND t3.flag_deleted = 0
             AND t3.cust_status = 2
             AND t3.cust_type NOT IN (0, 1)
             AND year(documentation_date) = year(now())
             AND IF(:admin,
                    1,
                    IF(:cust_code_size > 0, t3.cust_code IN (:cust_code_arr), 1))
           ORDER BY t1.id ASC) temp) AS contract_amount,

    -- 拆分订单金额:天津 (Order Amount Tianjin)
    (SELECT round(sum(amount_exclusive_tax), 2) AS order_amount_tianjin
     FROM (SELECT round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                           t2.amount_exclusive_tax),
                        2) AS amount_exclusive_tax
           FROM crm_sales_order t1
                    LEFT JOIN crm_sales_order_product t2 ON
               t1.sales_order_code = t2.sales_order_code
                    LEFT JOIN crm_cust_basic t3 ON
               t3.cust_code = t1.cust_code
           WHERE t1.flag_deleted = 0
             AND t2.flag_deleted = 0
             AND t3.flag_deleted = 0
             AND t3.cust_status = 2
             AND t3.cust_type NOT IN (0, 1)
             AND t1.status NOT IN (8)
             AND t1.factory_assigned = '天津'
             AND year(t1.create_time) = year(now())
             AND IF(:admin,
                    1,
                    IF(:cust_code_size > 0, t3.cust_code IN (:cust_code_arr), 1))
           ORDER BY t1.id ASC) temp) AS order_amount_tianjin,

    -- 拆分订单金额:凌峰 (Order Amount Lingfeng)
    (SELECT round(sum(amount_exclusive_tax), 2) AS order_amount_lingfeng
     FROM (SELECT round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                           t2.amount_exclusive_tax),
                        2) AS amount_exclusive_tax
           FROM crm_sales_order t1
                    LEFT JOIN crm_sales_order_product t2 ON
               t1.sales_order_code = t2.sales_order_code
                    LEFT JOIN crm_cust_basic t3 ON
               t3.cust_code = t1.cust_code
           WHERE t1.flag_deleted = 0
             AND t2.flag_deleted = 0
             AND t3.flag_deleted = 0
             AND t3.cust_status = 2
             AND t3.cust_type NOT IN (0, 1)
             AND t1.status NOT IN (8)
             AND t1.factory_assigned = '凌峰'
             AND year(t1.create_time) = year(now())
             AND IF(:admin,
                    1,
                    IF(:cust_code_size > 0, t3.cust_code IN (:cust_code_arr), 1))
           ORDER BY t1.id ASC) temp) AS order_amount_lingfeng;


-- 本年销售额(开票金额)
select sum(invoiced_amount) invoiced_amount
from cockpit.dws_sales_complete_metric
where year(create_time) = year(now())
  and cust_manager_code = :cust_manager_code
;
-- 本年销售指标
select max(sales_target) * 10000 sales_target
from cockpit.ods_metric_person
where cust_manager_code = :cust_manager_code
  and metric_year = year(now())
  and flag_deleted = 0
;
-- 本年回款额
select sum(year_payment_amount) year_payment_amount
from cockpit.dws_payment_cycle_metric
where metric_year = year(now())
  and if(:admin,
         1,
         if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
      )
;

-- 个人
SELECT (SELECT sum(invoiced_amount) AS invoiced_amount
        FROM cockpit.dws_sales_complete_metric
        WHERE year(create_time) = year(now())
          AND cust_manager_code = :cust_manager_code)                        AS invoiced_amount,

       (SELECT max(sales_target) * 10000 AS sales_target
        FROM cockpit.ods_metric_person
        WHERE cust_manager_code = :cust_manager_code
          AND metric_year = year(now())
          AND flag_deleted = 0)                                              AS sales_target,

       (SELECT sum(year_payment_amount) AS year_payment_amount
        FROM cockpit.dws_payment_cycle_metric
        WHERE metric_year = year(now())
          AND IF(:admin,
                 1,
                 IF(:cust_code_size > 0, cust_code IN (:cust_code_arr), 1))) AS year_payment_amount
;

-- 区域
SELECT (SELECT sum(invoiced_amount) AS invoiced_amount
        FROM cockpit.dws_sales_complete_metric
        WHERE year(create_time) = year(now())
          AND region = :region)                                              AS invoiced_amount,

       (SELECT max(sales_target) * 10000 AS sales_target
        FROM cockpit.ods_metric_region
        WHERE department_region = :region
          AND metric_year = year(now())
          AND flag_deleted = 0)                                              AS sales_target,

       (SELECT sum(year_payment_amount) AS year_payment_amount
        FROM cockpit.dws_payment_cycle_metric
        WHERE metric_year = year(now())
          AND IF(:admin,
                 1,
                 IF(:cust_code_size > 0, cust_code IN (:cust_code_arr), 1))) AS year_payment_amount
;

-- 部门
SELECT (SELECT sum(invoiced_amount) AS invoiced_amount
        FROM cockpit.dws_sales_complete_metric
        WHERE year(create_time) = year(now())
          AND deparment_code = :deparment_code)                              AS invoiced_amount,

       (SELECT max(sales_target) * 10000 AS sales_target
        FROM cockpit.ods_metric_department
        WHERE department_code = :department_code
          AND metric_year = year(now())
          AND flag_deleted = 0)                                              AS sales_target,

       (SELECT sum(year_payment_amount) AS year_payment_amount
        FROM cockpit.dws_payment_cycle_metric
        WHERE metric_year = year(now())
          AND IF(:admin,
                 1,
                 IF(:cust_code_size > 0, cust_code IN (:cust_code_arr), 1))) AS year_payment_amount
;
