-- 欠款金额
select t2.cust_code,
       t2.cust_name,
       sum(qkbbbye) outstanding_amount,
       max(qkts)    outstanding_day
from crm_account_receivable_age t1
         left join (WITH RankedCustomers AS (SELECT *,
                                                    ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY update_time DESC) as rn
                                             FROM crm_cust_basic)
                    SELECT *
                    FROM RankedCustomers
                    WHERE rn = 1) t2 on t1.cust_code = t2.cust_code
where t1.flag_deleted = 0
  and t2.flag_deleted = 0
  and date_format(t1.create_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')
group by cust_code, cust_name;
