var res_obj={
    "code": 200,
    "error": "",
    "obj": [
        {
            "date": "2024-12-01",
            "order_no": "202412012143",
            "zj": "24/25",
            "samplename": "大田甘蔗",
            "sample_no": "FNR-DT-241201015",
            "productioncategory": "榨蔗生产",
            "productcategory": "甘蔗分析",
            "gongsi": "FNR",
            "time": "17:52",
            "class": "乙班",
            "items": [
                {
                    "name": "地区",
                    "value": null
                },
                {
                    "name": "地类",
                    "value": null
                },
                {
                    "name": "车号",
                    "value": null
                },
                {
                    "name": "蔗证号",
                    "value": null
                },
                {
                    "name": "植期",
                    "value": null
                },
                {
                    "name": "品种",
                    "value": null
                },
                {
                    "name": "备注",
                    "value": null
                },
                {
                    "name": "蔗汁锤度",
                    "value": ""
                },
                {
                    "name": "蔗汁改正转光",
                    "value": ""
                },
                {
                    "name": "蔗汁视纯度",
                    "value": ""
                },
                {
                    "name": "蔗汁重力纯度",
                    "value": ""
                },
                {
                    "name": "蔗汁蔗糖分",
                    "value": ""
                },
                {
                    "name": "蔗汁还原糖分",
                    "value": ""
                },
                {
                    "name": "蔗渣水分",
                    "value": ""
                },
                {
                    "name": "蔗渣改正锤度",
                    "value": null
                },
                {
                    "name": "蔗渣改正转光",
                    "value": null
                },
                {
                    "name": "蔗渣锤度",
                    "value": ""
                },
                {
                    "name": "蔗渣与蔗比",
                    "value": ""
                },
                {
                    "name": "蔗渣纤维分",
                    "value": ""
                },
                {
                    "name": "甘蔗纤维分",
                    "value": ""
                },
                {
                    "name": "甘蔗锤度",
                    "value": ""
                },
                {
                    "name": "甘蔗转光度",
                    "value": ""
                },
                {
                    "name": "甘蔗蔗糖分",
                    "value": ""
                },
                {
                    "name": "甘蔗商业糖分",
                    "value": ""
                },
                {
                    "name": "淀粉含量",
                    "value": ""
                },
                {
                    "name": "葡聚糖含量",
                    "value": ""
                }
            ]
        }
    ]    , 
    "status": true
}
//将res.obj[*].items 中的name 作为key  value 作为value 组成一个对象,移动到res.obj[*]中,组成新数据
// 转换数据结构
res_obj.obj = res_obj.obj.map(function(item) {
    // 创建一个新对象，复制原有的非items属性
    var newItem = {};
    for (var key in item) {
        if (key !== 'items') {
            newItem[key] = item[key];
        }
    }

    // 将items数组转换为对象
    item.items.forEach(function(itemData) {
        newItem[itemData.name] = itemData.value;
    });

    return newItem;
});
var start_date='2024-12-02'
var end_date='2024-12-12'
if(start_date&&end_date){
    res_obj.obj = res_obj.obj.filter(function(item) {
        return item.date >= start_date && item.date <= end_date;
    });
}

console.log(JSON.stringify(res_obj))