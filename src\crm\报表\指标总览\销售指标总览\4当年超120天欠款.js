var data=[{"cust_size":0,"month":1,"year":2024},{"cust_size":0,"month":2,"year":2024},{"cust_size":0,"month":3,"year":2024},{"cust_size":0,"month":4,"year":2024},{"cust_size":0,"month":5,"year":2024},{"cust_size":0,"month":6,"year":2024},{"cust_size":0,"month":7,"year":2024},{"cust_size":0,"month":8,"year":2024},{"cust_size":0,"month":9,"year":2024},{"cust_size":0,"month":10,"year":2024},{"cust_size":0,"month":11,"year":2024},{"cust_size":0,"month":12,"year":2024},{"cust_size":0,"month":1,"year":2025},{"cust_size":0,"month":2,"year":2025},{"cust_size":0,"month":3,"year":2025},{"cust_size":0,"month":4,"year":2025},{"cust_size":0,"month":5,"year":2025},{"cust_size":0,"month":6,"year":2025},{"cust_size":0,"month":7,"year":2025},{"cust_size":0,"month":8,"year":2025},{"cust_size":0,"month":9,"year":2025},{"cust_size":0,"month":10,"year":2025},{"cust_size":0,"month":11,"year":2025},{"cust_size":0,"month":12,"year":2025}]

var years = data.map(function(item) {
    return item.year;
}).filter(function(value, index, self) {
    return self.indexOf(value) === index;
}).sort();

var option = {
    title: {
        text: "当年超120天欠款",
        left: "center"
    },
    tooltip: {
        trigger: "axis"
    },
    legend: {
        data: years.map(function(year) {
            return year + '年';
        }),
        top: "25px"
    },
    grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true
    },
    xAxis: {
        type: "category",
        boundaryGap: false,
        data: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]
    },
    yAxis: {
        type: "value",
        axisLabel: {
            formatter: "{value}"
        }
    },
    series: years.map(function(year, index) {
        return {
            name: year + '年',
            type: "line",
            data: data.filter(function(item) {
                return item.year === year;
            }).map(function(item) {
                return item.cust_size;
            }),
            smooth: true,
            lineStyle: {
                color: index === 0 ? '#5470c6' : '#91cc75'
            }
        };
    })
};
console.log(JSON.stringify(option));
return option;

