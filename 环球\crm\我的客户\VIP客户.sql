-- VIP客户查询
select cvl.*,
       case
           cvl.approval_status
           when '0' then '--'
           when '1' then '--'
           when '2' then '无效'
           when '3' then
               if(current_date between validity_start_date and validity_end_date, '有效', '无效')
           else '--'
           end as statuss,
       case
           cvl.approval_status
           when '0' then '草稿'
           when '1' then '审批中'
           when '2' then '审批驳回'
           when '3' then '审批通过'
           else '--'
           end as approval_statuss,
       CONCAT_WS(
               '至',
               CONCAT(
                       year(cvl.validity_start_date), '年',
                       month(cvl.validity_start_date), '月',
                       day(cvl.validity_start_date), '日'
               ),
               CONCAT(
                       year(cvl.validity_end_date), '年',
                       month(cvl.validity_end_date), '月',
                       day(cvl.validity_end_date), '日'
               )
       )       as date_range
from crm_vip_list cvl
where cvl.flag_deleted = 0
  and ((:target_customer_list_code is null
    or :target_customer_list_code = '')
    or (cvl.target_customer_list_code = :target_customer_list_code))
  and ((:target_customer_list_name is null
    or :target_customer_list_name = '')
    or (cvl.target_customer_list_name like CONCAT('%', :target_customer_list_name, '%')))
  and ((:show_creat_by is null
    or :show_creat_by = '')
    or (cvl.show_creat_by like CONCAT('%', :show_creat_by, '%')))
  and ((:create_time is null
    or :create_time = '')
    or (cvl.create_time like CONCAT('%', :create_time, '%')))
  and ((:status is null
    or :status = '')
    or (case
            cvl.approval_status
            when '0' then '--'
            when '1' then '--'
            when '2' then '1'
            when '3' then
                if(current_date between validity_start_date and validity_end_date, '0', '1')
            else '--'
            end = :status))
  and ((:approval_status is null
    or :approval_status = '')
    or (cvl.approval_status = :approval_status))
  and cvl.target_customer_list_code in (:codeList)
order by cvl.create_time desc
limit :pageNo,:pageSize;

select id,
       validity_start_date,
       validity_end_date
       approval_status,
       case
           cvl.approval_status
           when '0' then '--'
           when '1' then '--'
           when '2' then '无效'
           when '3' then
               if(current_date between validity_start_date and validity_end_date, '有效', '无效')
           else '--'
           end  as statuss
from crm_vip_list cvl
limit 1
;
select id,
       validity_start_date,
       validity_end_date
from crm_vip_list cvl
limit 1
;
select '1'=1;

select oa_id from crm_vip_list  cvc;

-- 查询详情列表
select
    cvc.*,
    ccb.sales_assistant_name,
    ccb.cust_manager_name,
    ccb.leader_code,
    ccc.contact_name
from
    crm_vip_cust cvc
        left join
    crm_cust_basic ccb on
        cvc.cust_code = ccb.
            cust_code
        left join crm_cust_contact ccc on
        ccb.cust_mnemonic_code = ccc.cust_mnemonic_code
            and ccc.flag_deleted = 0
            and ccc.is_default = '0'
where
    ccb.flag_deleted = 0
  and ccb.cust_version=1
  and ccc.cust_version=1
  and ccb.cust_type in ('2', '3', '4')
  and cvc.flag_deleted = 0
  and ((:target_customer_list_code is null
    or :target_customer_list_code = '')
    or (cvc.target_customer_list_code = :target_customer_list_code));

select * from crm_vip_list cvl where target_customer_list_code='VIP_202501091';
select * from crm_vip_list cvl where approval_status='1' and request_id is null;


-- VIP客户导入excel查询
select
    ccb.cust_version,
    ccb.leader_code,
    ccb.cust_code,
    ccb.cust_name,
    ccb.region,
    ccb.industry,
    ccb.sales_assistant_name,
    ccb.cust_manager_name,
    ccc.contact_name,
    ccb.cust_vip,
    ccb.deparment_name,
    ccb.deparment_code
from
    crm_cust_basic ccb

        left join crm_cust_contact ccc on
        ccb.cust_code = ccc.cust_code
            and ccc.flag_deleted = 0
            and ccc.is_default = '0'
where
    ccb.flag_deleted = 0
  and ccb.cust_type in ('2', '3', '4')
  and ccb.cust_status=2
  and ccc.cust_version=ccb.cust_version
  and ccb.cust_code in ('C000261')
  and (ccb.leader_code IS NULL OR ccb.leader_code = '');

select * from crm_cust_contact ccc where cust_code='C000261';


with ranked_contacts as (
    select
        ccc.*,
        ROW_NUMBER() OVER (
            PARTITION BY ccc.cust_code, ccc.cust_version
            ORDER BY ccc.is_default asc
            ) as rn
    from crm_cust_contact ccc
    where ccc.flag_deleted = 0
)
select
    ccb.cust_version,
    ccb.leader_code,
    ccb.cust_code,
    ccb.cust_name,
    ccb.region,
    ccb.industry,
    ccb.sales_assistant_name,
    ccb.cust_manager_name,
    rc.contact_name,
    ccb.cust_vip,
    ccb.deparment_name,
    ccb.deparment_code
from
    crm_cust_basic ccb
        left join ranked_contacts rc on
        ccb.cust_code = rc.cust_code
            and ccb.cust_version = rc.cust_version
            and rc.rn = 1
where
    ccb.flag_deleted = 0
  and ccb.cust_type in ('2', '3', '4')
  and ccb.cust_status = 2
  and ccb.cust_code in ('C000413')
  and (ccb.leader_code IS NULL OR ccb.leader_code = '');
