var resourceArr={"float_rate":"±3","crm_one_time_cost_sumAmount":0,"bd_executive_salary":0.003478213884,"delivery_expense":0,"gd_depreciation_expense_other":637,"bd_depreciation_expense_buildings":0.003478213884,"gd_executive_salary":637,"create_by_name":"系统创建","tax_rate":13,"dw_sum_amount":0.095833385356,"variable_auxiliary_materials_sumAmount":0.047870549516000005,"mnemonic_code":"ZDQX000426","net_size":"","id":"ZDQX0000686_1.1_C000331","bd_depreciation_expense_other":0.003478213884,"create_time":"2025-01-26 21:19:46","exchange_rate":"1","sales_gross_profit_margin":20,"gd_sum_amount":4581.***********,"product_size":"50*15*90","index":0,"tax_inclusive":"含税","version":"2.0","object_id":"TiNACON媞娜（半）E044/V01","float_mark":"±","default_factory":"凌峰","reason_approval":"","sub_class":"医疗器械类","zz_unit_variable_labor_cost_sumAmount":0.010434641651999999,"auxiliary_materials_sumAmount":0,"rg_unit_variable_labor_cost_sumAmount":0.***********,"status":"4","handling_expense":0,"product_weight":0.00467,"pure_freight_cost":0,"gd_depreciation_expense_buildings":637,"main_class":"折叠纸盒类","product_quantity":1000000,"create_by":"系统创建","update_time":"2025-02-27 16:26:46","quotation_code":"HJ251034492","fixed_paper_sumAmount":266.***********,"cost_accounting_sumAmount":0.***********,"cust_name":"甘肃康视达科技集团有限公司","quotation_version":"1","update_by":"2","labor_rg_sumAmount":1130,"material_name":"TiNACON媞娜（半）E044/V01软性亲水接触镜1片装半年抛小盒","labor_bd_sumAmount":1911,"flag_deleted":0,"product_version":"1.1","crm_one_time_cost":[{"amount":"0","create_time":"2025-05-22 10:10:03","categroy":"9","serviceability_ratio":"300000","edition_name":"TiNACON媞娜E029/V02软性亲水接触镜1片装半年抛小盒版","version":"1.0","edition_categroy":"光油版","create_by":"2","update_time":"2025-05-22 10:10:03","material_price":"0","quotation_code":"HJ251034492","edition_code":"ZDQX000047-GBV1","edition_type":"印版","parent_code":"ZDQX0000686","quotation_version":"1","id":85497,"update_by":"2","flag_deleted":0},{"amount":"0","create_time":"2025-05-22 10:10:03","categroy":"9","serviceability_ratio":"700000","edition_name":"TiNACON媞娜E029/V02软性亲水接触镜1片装半年抛小盒版","version":"1.0","edition_categroy":"模切版","create_by":"2","update_time":"2025-05-22 10:10:03","material_price":"0","quotation_code":"HJ251034492","edition_code":"ZDQX000047-MV1","edition_type":"模切版","parent_code":"ZDQX0000686","quotation_version":"1","id":85498,"update_by":"2","flag_deleted":0},{"amount":"0","create_time":"2025-05-22 10:10:03","categroy":"9","serviceability_ratio":"700000","edition_name":"TiNACON媞娜（半）E041/V01软性亲水接触镜1片装半年抛小盒版","version":"1.0","edition_categroy":"烫印版","create_by":"2","update_time":"2025-05-22 10:10:03","material_price":"0","quotation_code":"HJ251034492","edition_code":"ZDQX000421-TV1","edition_type":"模切版","parent_code":"ZDQX0000686","quotation_version":"1","id":85499,"update_by":"2","flag_deleted":0},{"amount":"0","create_time":"2025-05-22 10:10:03","categroy":"9","serviceability_ratio":"200000","edition_name":"TiNACON媞娜（半）E044/V01软性亲水接触镜1片装半年抛小盒版","version":"1.0","edition_categroy":"丝印版","create_by":"2","update_time":"2025-05-22 10:10:03","material_price":"0","quotation_code":"HJ251034492","edition_code":"ZDQX000426-SV1","edition_type":"印版","parent_code":"ZDQX0000686","quotation_version":"1","id":85500,"update_by":"2","flag_deleted":0},{"create_time":"2025-05-22 10:10:03","categroy":"3","version":"1.0","create_by":"2","update_time":"2025-05-22 10:10:03","quotation_code":"HJ251034492","parent_code":"0","quotation_version":"1","id":85501,"update_by":"2","material_name":"TiNACON媞娜（半）E044/V01软性亲水接触镜1片装半年抛小盒","material_code":"ZDQX0000686","flag_deleted":0}],"update_by_name":"系统创建","administrative_code":"620402000000","float_info":"3","cust_code":"C000331","carrier_feign_cost":0,"out_of_book":"24","auxiliary_ctp_sumAmount":0,"standard_unit":"只","product_big_version":"1","grade_name":"TiNACON媞娜（半）E044/V01软性亲水接触镜1片装半年抛小盒","material_code":"ZDQX0000686","stocked_product":"否","freight_cost":0,"paper_all_cost":"23559.83","total_cost":"100426.49","raw_material_cost":"71708.49","depreciation_expense":"8230.43","utilities_labor":"20487.58","standard_cost_unit_price":"0.100426","sales_amount_excluding":"125533.11","sales_amount_excluding_usd":"125533.11","unit_price_excluding":"0.125533","unit_price_excluding_usd":"0.125533","unit_price_usd":1.141852,"unit_price":"0.141852","sales_amount_usd":"141852.00","sales_amount":"141852.00","sales_gross_profit":"25106.62","financial_gross_profit_margin":"20.00","break_even_quantity":"154248.07","lowest_cost_unit_price":"0.089069","marginal_contribution_amount":"36464.00","lowest_cost_gross_margin":"-12.75","makeup_product":true}
var newObj = {};

newObj.overall_sales_margin_rate = 0
newObj.total_sales_gross_profit = 0
newObj.total_sales = 0
newObj.total_cost = 0
newObj.raw_materials = 0
newObj.depreciation_expense = 0
newObj.utilities_labor = 0
newObj.freight_cost = 0


for (var i = 0; i < resourceArr.length; i++) {
	var isContain = "stocked_product" in resourceArr[i];
	if (!isContain) {
		// 总毛利额 = =SUM(销售毛利)
		if (!resourceArr[i]['sales_gross_profit']) {
			resourceArr[i]['sales_gross_profit'] = 0;
		}
		newObj.total_sales_gross_profit += parseFloat(resourceArr[i]['sales_gross_profit']);
		// 总销售额 = =SUM(不含税销售金额（RMB）)
		if (!resourceArr[i]['sales_amount_excluding']) {
			resourceArr[i]['sales_amount_excluding'] = 0;
		}
		newObj.total_sales += parseFloat(resourceArr[i]['sales_amount_excluding']);
		// 总成本 = =SUM(总成本)
		if (!resourceArr[i]['total_cost']) {
			resourceArr[i]['total_cost'] = 0;
		}
		newObj.total_cost += parseFloat(resourceArr[i]['total_cost']);
		// 原辅料 = =SUM(原辅料)
		if (!resourceArr[i]['raw_material_cost']) {
			resourceArr[i]['raw_material_cost'] = 0;
		}
		newObj.raw_materials += parseFloat(resourceArr[i]['raw_material_cost']);
		// 折旧 = =SUM(折旧费)
		if (!resourceArr[i]['depreciation_expense']) {
			resourceArr[i]['depreciation_expense'] = 0;
		}
		newObj.depreciation_expense += parseFloat(resourceArr[i]['depreciation_expense']);
		// 水电人工  = =SUM(水电人工)
		if (!resourceArr[i]['utilities_labor']) {
			resourceArr[i]['utilities_labor'] = 0;
		}
		newObj.utilities_labor += parseFloat(resourceArr[i]['utilities_labor']);
		// 运费 = =SUM(运费)
		if (!resourceArr[i]['freight_cost']) {
			resourceArr[i]['freight_cost'] = 0;
		}
		newObj.freight_cost += parseFloat(resourceArr[i]['freight_cost']);
	} else {
		if (!resourceArr[i]['stocked_product']) {
			// 总毛利额 = =SUM(销售毛利)
			if (!resourceArr[i]['sales_gross_profit']) {
				resourceArr[i]['sales_gross_profit'] = 0;
			}
			newObj.total_sales_gross_profit += parseFloat(resourceArr[i]['sales_gross_profit']);
			// 总销售额 = =SUM(不含税销售金额（RMB）)
			if (!resourceArr[i]['sales_amount_excluding']) {
				resourceArr[i]['sales_amount_excluding'] = 0;
			}
			newObj.total_sales += parseFloat(resourceArr[i]['sales_amount_excluding']);
			// 总成本 = =SUM(总成本)
			if (!resourceArr[i]['total_cost']) {
				resourceArr[i]['total_cost'] = 0;
			}
			newObj.total_cost += parseFloat(resourceArr[i]['total_cost']);
			// 原辅料 = =SUM(原辅料)
			if (!resourceArr[i]['raw_material_cost']) {
				resourceArr[i]['raw_material_cost'] = 0;
			}
			newObj.raw_materials += parseFloat(resourceArr[i]['raw_material_cost']);
			// 折旧 = =SUM(折旧费)
			if (!resourceArr[i]['depreciation_expense']) {
				resourceArr[i]['depreciation_expense'] = 0;
			}
			newObj.depreciation_expense += parseFloat(resourceArr[i]['depreciation_expense']);
			// 水电人工  = =SUM(水电人工)
			if (!resourceArr[i]['utilities_labor']) {
				resourceArr[i]['utilities_labor'] = 0;
			}
			newObj.utilities_labor += parseFloat(resourceArr[i]['utilities_labor']);
			// 运费 = =SUM(运费)
			if (!resourceArr[i]['freight_cost']) {
				resourceArr[i]['freight_cost'] = 0;
			}
			newObj.freight_cost += parseFloat(resourceArr[i]['freight_cost']);
		} else {
			continue;
		}
	}
}

// 综合销售毛利率 = =总毛利额/总销售额
if (newObj.total_sales) {
	newObj.overall_sales_margin_rate = (newObj.total_sales_gross_profit / newObj.total_sales) * 100
} else {
	newObj.overall_sales_margin_rate = newObj.total_sales_gross_profit * 100;
}

newObj.overall_sales_margin_rate = newObj.overall_sales_margin_rate.toFixed(2) + "%";
newObj.total_sales_gross_profit = newObj.total_sales_gross_profit.toFixed(2);
newObj.total_sales = newObj.total_sales.toFixed(2);
newObj.total_cost = newObj.total_cost.toFixed(2);
newObj.raw_materials = newObj.raw_materials.toFixed(2);
newObj.depreciation_expense = newObj.depreciation_expense.toFixed(2);
newObj.utilities_labor = newObj.utilities_labor.toFixed(2);
newObj.freight_cost = newObj.freight_cost.toFixed(2);

return newObj;