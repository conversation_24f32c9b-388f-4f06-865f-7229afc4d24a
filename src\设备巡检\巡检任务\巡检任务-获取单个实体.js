var outputContext={"record":[{"device_uu_code":"387","start_end_time":"2025-05-19","device_name":"2#撕解机","inspect_order_name":"撕解机电机前轴温度1","task_code":"XJRW202505190015","device_code":"Dissolver2","plan_code":"XJJH202505190001","frequency":"不重复","status":"待执行"}],"itemArr":[]}
var dcsObj={"code":200,"data":{"Dissolver2":{"LiquidCabStartDone2Drator":{"occurred":1747624492914,"value":0},"LiquidCabAllowStart2Dgrator":{"occurred":1747624492914,"value":0},"FaultState2Disintegrator":{"occurred":1747624492914,"value":0},"DissolverMotorFrontTemp22":{"occurred":1747624492914,"value":28.59},"DissolverMotorFrontTemp12":{"occurred":1747624492914,"value":23.22},"InterlockDisMotor2":{"occurred":1747624492914,"value":1},"DissolverMotorStatorTemp22":{"occurred":1747624492914,"value":29.06},"DissolverMotorStatorTemp12":{"occurred":1747624492914,"value":26.86},"BreakerStatus2Disintegrator":{"occurred":1747624492914,"value":0},"InterlockRunDisMotor2":{"occurred":1747624492914,"value":0},"DissolverMotorCurrent2":{"occurred":1747624492914,"value":0},"DCSCtrlState2Disintegrator":{"occurred":1747624492914,"value":0}}},"message":""}
var dev_inspect_task={"device_uu_code":"387","create_time":"2025-05-19 10:57:49","task_code":"XJRW202505190015","plan_code":"XJJH202505190001","version":"1.0","plan_name":"撕解机电机前轴温度1","frequency":1,"create_by":"1863498952106631170","inspect_plan_time":"2025-05-19","update_time":"2025-05-19 10:57:49","device_name":"2#撕解机","device_code":"Dissolver2","id":15445,"update_by":"1863498952106631170","relation_code":"TR2025051900000058","status":2,"flag_deleted":0}
var items= outputContext.itemArr
var data=dcsObj.data
var device_code =dev_inspect_task.device_code
for (var i = 0; i < items.length; i++) {
    var currentItem = items[i];
    if (currentItem.attribute && data[device_code] && data[device_code][currentItem.attribute]) { // 检查是否存在device_code键以及attribute键
        currentItem.result = data[device_code][currentItem.attribute].value; // 修正对象访问方式
    }
}
return items;