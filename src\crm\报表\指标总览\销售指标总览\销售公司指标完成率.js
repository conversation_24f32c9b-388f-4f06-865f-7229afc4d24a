var data = [
    {"complete_rate": 0, "month": "01", "year": 2024},
    {"complete_rate": 0, "month": "02", "year": 2024},
    {"complete_rate": 0, "month": "03", "year": 2024},
    {"complete_rate": 0, "month": "04", "year": 2024},
    {"complete_rate": 0, "month": "05", "year": 2024},
    {"complete_rate": 0, "month": "06", "year": 2024},
    {"complete_rate": 0, "month": "07", "year": 2024},
    {"complete_rate": 0, "month": "08", "year": 2024},
    {"complete_rate": 0, "month": "09", "year": 2024},
    {"complete_rate": 0, "month": "10", "year": 2024},
    {"complete_rate": 0, "month": "11", "year": 2024},
    {"complete_rate": 0, "month": "12", "year": 2024},
    {"complete_rate": 0, "month": "01", "year": 2025},
    {"complete_rate": 0, "month": "02", "year": 2025},
    {"complete_rate": 4.12, "month": "03", "year": 2025},
    {"complete_rate": 14.41, "month": "04", "year": 2025},
    {"complete_rate": 14.41, "month": "05", "year": 2025},
    {"complete_rate": 14.41, "month": "06", "year": 2025},
    {"complete_rate": 14.41, "month": "07", "year": 2025},
    {"complete_rate": 14.41, "month": "08", "year": 2025},
    {"complete_rate": 14.41, "month": "09", "year": 2025},
    {"complete_rate": 14.41, "month": "10", "year": 2025},
    {"complete_rate": 14.41, "month": "11", "year": 2025},
    {"complete_rate": 14.41, "month": "12", "year": 2025}
];
var years = data.map(function(item) {
    return item.year;
}).filter(function(value, index, self) {
    return self.indexOf(value) === index;
}).sort();

var option = {
    title: {
        text: "销售公司销售指标完成率（当年度累计值）",
        left: "center"
    },
    tooltip: {
        trigger: "axis"
    },
    legend: {
        data: years.map(function(year) {
            return year + '年';
        }),
        top: "25px"
    },
    grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true
    },
    xAxis: {
        type: "category",
        boundaryGap: false,
        data: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]
    },
    yAxis: {
        type: "value",
        axisLabel: {
            formatter: "{value}"
        }
    },
    series: years.map(function(year, index) {
        return {
            name: year + '年',
            type: "line",
            data: data.filter(function(item) {
                return item.year === year;
            }).map(function(item) {
                return item.complete_rate;
            }),
            smooth: true,
            lineStyle: {
                color: index === 0 ? '#5470c6' : '#91cc75'
            }
        };
    })
};
return option;

