SELECT update_month, product_big_category, product_big_category_name, production_quantity, production_value
FROM (
    SELECT
        DATE_FORMAT(opfps.update_time, '%Y-%m-%d') AS update_month,
        dpbc.code AS product_big_category,
        dpbc.name AS product_big_category_name,
        COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0)) , 0), 0) AS production_quantity,
        COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0) * COALESCE(opop.unit_price_no, 0)) , 0), 0) AS production_value
    FROM
        cockpit.dim_product_big_category dpbc
            LEFT JOIN cockpit.ods_pm_job_order opjo ON
            opjo.large_category = dpbc.code
            LEFT JOIN cockpit.ods_pm_finsih_project_stock opfps ON
            opfps.pro_batch = opjo.production_batch_number
            LEFT JOIN cockpit.ods_pm_order_product opop ON
            opjo.order_code = opop.sales_number
                AND opjo.material_code = opop.product_number
    WHERE
        opfps.stock_type = '1'
      AND opfps.storage_status = '4'
      AND DATE_FORMAT(opfps.update_time, '%Y-%m') = :select_month
    GROUP BY
        DATE_FORMAT(opfps.update_time, '%Y-%m-%d'),
        dpbc.code,
        dpbc.name

    UNION ALL

    SELECT
        '合计' AS update_month,
        '合计' AS product_big_category,
        '' AS product_big_category_name,
        COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0)) , 0), 0) AS production_quantity,
        COALESCE(ROUND(SUM(COALESCE(opfps.report_num, 0) * COALESCE(opop.unit_price_no, 0)) , 0), 0) AS production_value
    FROM
        cockpit.dim_product_big_category dpbc
            LEFT JOIN cockpit.ods_pm_job_order opjo ON
            opjo.large_category = dpbc.code
            LEFT JOIN cockpit.ods_pm_finsih_project_stock opfps ON
            opfps.pro_batch = opjo.production_batch_number
            LEFT JOIN cockpit.ods_pm_order_product opop ON
            opjo.order_code = opop.sales_number
                AND opjo.material_code = opop.product_number
    WHERE
        opfps.stock_type = '1'
      AND opfps.storage_status = '4'
      AND DATE_FORMAT(opfps.update_time, '%Y-%m') = :select_month
) t
ORDER BY
    CASE 
        WHEN update_month = '合计' THEN 1 
        ELSE 0 
    END,
    update_month;