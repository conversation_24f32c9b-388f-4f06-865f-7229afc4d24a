const mysql = require('mysql2');

// 创建数据库连接
const connection = mysql.createConnection({
    host: '**************', // 数据库主机地址
    user: 'root', // 数据库用户名
    password: '123456', // 数据库密码
    database: 'cockpit' // 数据库名称
});

connection.connect((err) => {
    if (err) {
        console.error('Error connecting to the database:', err.stack);
        return;
    }
    console.log('Connected to the database.');
    insertDimMonthData();
});

function insertDimMonthData() {
    const startDate = new Date('2023-01-01');
    const endDate = new Date('2033-12-31');
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
        const year = currentDate.getFullYear()+"";
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const yearMonth = `${year}-${month}`;
        const id = `${year}${month}`;
        
        const query = `
            INSERT INTO dim_month (id,\`year\`,\`month\`, \`year_month\`)
            VALUES (?,?, ?, ?)
        `;

        connection.query(query, [id,year, month, yearMonth], (err, results) => {
            if (err) {
                console.error('Error inserting data:', err.stack);
            } else {
                console.log(`Inserted/Updated record for ${yearMonth}`);
            }
        });

        currentDate.setMonth(currentDate.getMonth() + 1);
    }

    connection.end((err) => {
        if (err) {
            console.error('Error closing the connection:', err.stack);
            return;
        }
        console.log('Connection closed.');
    });
}
