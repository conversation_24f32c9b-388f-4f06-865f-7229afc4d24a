-- ETL脚本
-- 来料检验
select date, func, node, unfinish, finish, avg_time
from (select date(create_time)                            date,
             '来料检验'                                   func,
             case
                 when inspection_type = '1' then '库房检验'
                 when inspection_type = '2' then 'QC检验'
                 when inspection_type = '3' then '实验室检验'
                 end                                      node,
             sum(IF(status = 0, 1, 0))                    unfinish,
             sum(IF(status = 1, 1, 0))                    finish,
             round(sum(IF(status = 1, timestampdiff(second, create_time, update_time), 0)) /
                   (sum(IF(status = 1, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_mes_incoming_inspection
      where flag_deleted = 0
      group by date, func, node
      union
-- 过程检验
      select date(create_time)                             date,
             '过程检验'                                    func,
             case
                 when inspection_type = 'jylx_3' then '首检'
                 when inspection_type = 'jylx_4' then '巡检'
                 when inspection_type = 'jylx_5' then '尾检'
                 end                                       node,
             sum(IF(z_status = 1, 1, 0))                   unfinish,
             sum(IF(z_status != 1, 1, 0))                  finish,
             round(sum(IF(status != 1, timestampdiff(second, inspection_time, qc_create_time), 0)) /
                   (sum(IF(status != 1, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_process_inspection
      where flag_deleted = 0
        and inspection_type in ('jylx_3', 'jylx_4', 'jylx_5')
      group by date, func, node
      union
-- 成品检验:qc检验
      select date(create_time)                               date,
             '成品检验'                                      func,
             'QC检验'                                        node,
             sum(IF(qc_status != 2, 1, 0))                   unfinish,
             sum(IF(qc_status = 2, 1, 0))                    finish,
             round(sum(IF(qc_status = 2, timestampdiff(second, submitted_time, qc_proving_time), 0)) /
                   (sum(IF(qc_status = 2, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_qms_finished_inspection
      where flag_deleted = 0
      group by date, func, node
      union
-- 成品检验:实验室检验
      select date(create_time)                                date,
             '成品检验'                                       func,
             '实验室检验'                                     node,
             sum(IF(lab_status = 1, 1, 0))                    unfinish,
             sum(IF(lab_status = 2, 1, 0))                    finish,
             round(sum(IF(lab_status = 2, timestampdiff(second, qc_proving_time, lab_proving_time), 0)) /
                   (sum(IF(lab_status = 2, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_qms_finished_inspection
      where flag_deleted = 0
        and is_lab_inspect = 1
      group by date, func, node
      union
      select date(t1.create_time)                                            date,
             '成品放行'                                                      func,
             '产品生产过程'                                                  node,
             sum(IF(summary_of_the_production = 0, 1, 0))                    unfinish,
             sum(IF(summary_of_the_production = 1, 1, 0))                    finish,
             round(sum(IF(summary_of_the_production = 1, timestampdiff(second, t4.finish_date, t1.create_time), 0)) /
                   (sum(IF(summary_of_the_production = 1, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_qms_job_order_release_audit t1
               join cockpit.ods_qms_approval_intermediate t2 on t2.flow_id = t1.release_id
               join cockpit.ods_qms_job_order_release t3 on t1.release_id = t3.id
               join cockpit.ods_pm_job_order t4 on t3.production_batch_number = t4.production_batch_number
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
        and t3.flag_deleted = 0
        and t4.flag_deleted = 0
        and audit_usage = '01'
      group by date, func, node
      union
      select date(t1.create_time)                                                date,
             '成品放行'                                                          func,
             '过程质量控制'                                                      node,
             sum(IF(process_quality_control_batch = 0, 1, 0))                    unfinish,
             sum(IF(process_quality_control_batch = 1, 1, 0))                    finish,
             round(sum(IF(process_quality_control_batch = 1, timestampdiff(second, t4.finish_date, t1.create_time),
                          0)) /
                   (sum(IF(process_quality_control_batch = 1, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_qms_job_order_release_audit t1
               join cockpit.ods_qms_approval_intermediate t2 on t2.flow_id = t1.release_id
               join cockpit.ods_qms_job_order_release t3 on t1.release_id = t3.id
               join cockpit.ods_pm_job_order t4 on t3.production_batch_number = t4.production_batch_number
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
        and t3.flag_deleted = 0
        and t4.flag_deleted = 0
        and audit_usage = '02'
      group by date, func, node
      union
      select date(t1.create_time)                                                  date,
             '成品放行'                                                            func,
             '成品释放审核'                                                        node,
             sum(IF(release_of_the_finished_product = 0, 1, 0))                    unfinish,
             sum(IF(release_of_the_finished_product = 1, 1, 0))                    finish,
             round(sum(IF(release_of_the_finished_product = 1, timestampdiff(second, t4.finish_date, t1.create_time),
                          0)) /
                   (sum(IF(release_of_the_finished_product = 1, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_qms_job_order_release_audit t1
               join cockpit.ods_qms_approval_intermediate t2 on t2.flow_id = t1.release_id
               join cockpit.ods_qms_job_order_release t3 on t1.release_id = t3.id
               join cockpit.ods_pm_job_order t4 on t3.production_batch_number = t4.production_batch_number
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
        and t3.flag_deleted = 0
        and t4.flag_deleted = 0
        and t1.audit_usage = '03'
      group by date, func, node
      union
-- 异常反馈单:部门主管审核
      select date(t1.create_time)                        date,
             '异常反馈单'                                func,
             '部门主管审核'                              node,
             sum(IF(state = 1, 1, 0))                    unfinish,
             sum(IF(state > 1, 1, 0))                    finish,
             round(sum(IF(state > 1, timestampdiff(second, t1.create_time, t2.department_managers_time), 0)) /
                   (sum(IF(state > 1, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_abnormal_feedback t1
               left join ods_qms_abnormal_feedback_treatment t2 on t1.id = t2.abnormal_feedback_id
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
      group by date
      union
-- 异常反馈单:QC处理
      select date(t1.create_time)                        date,
             '异常反馈单'                                func,
             'QC处理'                                    node,
             sum(IF(state = 2, 1, 0))                    unfinish,
             sum(IF(state > 2, 1, 0))                    finish,
             round(sum(IF(state > 2, timestampdiff(second, t2.department_managers_time, t2.QC_being_time), 0)) /
                   (sum(IF(state > 2, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_abnormal_feedback t1
               left join ods_qms_abnormal_feedback_treatment t2 on t1.id = t2.abnormal_feedback_id
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
      group by date
      union
-- 异常反馈单:QC主管处理
      select date(t1.create_time)                        date,
             '异常反馈单'                                func,
             'QC主管处理'                                node,
             sum(IF(state = 3, 1, 0))                    unfinish,
             sum(IF(state > 3, 1, 0))                    finish,
             round(sum(IF(state > 3, timestampdiff(second, t2.QC_being_time, t2.QC_managers_time), 0)) /
                   (sum(IF(state > 3, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_abnormal_feedback t1
               left join ods_qms_abnormal_feedback_treatment t2 on t1.id = t2.abnormal_feedback_id
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
      group by date
      union
-- 异常反馈单:QA处理
      select date(t1.create_time)                        date,
             '异常反馈单'                                func,
             'QA处理'                                    node,
             sum(IF(state = 4, 1, 0))                    unfinish,
             sum(IF(state > 4, 1, 0))                    finish,
             round(sum(IF(state > 4, timestampdiff(second, t2.QC_managers_time, t2.QA_time), 0)) /
                   (sum(IF(state > 4, 1, 0)) * 3600), 2) avg_time
      from cockpit.ods_abnormal_feedback t1
               left join ods_qms_abnormal_feedback_treatment t2 on t1.id = t2.abnormal_feedback_id
      where t1.flag_deleted = 0
        and t2.flag_deleted = 0
      group by date) temp
order by date, func, node;

-- dwd表
CREATE TABLE dwd_time_node_mom
(
    id          int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    update_date date           DEFAULT NULL COMMENT '日期yyyy-MM-dd',
    func        varchar(255)   DEFAULT NULL COMMENT '功能',
    node        varchar(255)   DEFAULT NULL COMMENT '节点',
    unfinished  int(11)        DEFAULT NULL COMMENT '未完成数量',
    finished    int(11)        DEFAULT NULL COMMENT '已完成数量',
    avg_time    decimal(10, 2) DEFAULT NULL COMMENT '已完成平均耗时',
    PRIMARY KEY (id)
) COMMENT = '时间节点统计MOM';

-- 分页查询
select id, update_date, func, node, unfinished, finished, avg_time
from cockpit.dwd_time_node_mom
where ((:start_date IS NULL OR :start_date = '') OR (update_date >= :start_date))
  AND ((:end_date IS NULL OR :end_date = '') OR (update_date <= :end_date))
  AND ((:func IS NULL OR :func = '') OR (func = :func))
  AND ((:node IS NULL OR :node = '') OR (node = :node))
ORDER BY update_date, func, node
LIMIT :page_size OFFSET :offset;

