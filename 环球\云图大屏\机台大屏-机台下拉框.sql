-- 老工艺
SELECT distinct mw.code,
                mw.name,
                mf.id   as machine_id,
                mf.machine_name,
                mf.code as machine_code,
                mmrd.device_code
FROM mes_workshop mw
         LEFT JOIN
     machine_file mf ON mw.code = mf.workshop
         LEFT JOIN
     mes_machine_related_device mmrd ON mf.code = mmrd.machine_code
WHERE mw.flag_disable = 0
  and mmrd.flag_deleted = 0
;



-- 机台档案表
select mw.code,
       mw.name,
       mf.id   as machine_id,
       mf.machine_name,
       mf.code as machine_code,
       mmrd.device_code
from hq_mdm_b.machine_file as mf
         left join hq_mdm_b.mes_resource_center as wcf on
    mf.workcenter = wcf.resource_center_code
         left join hq_mdm_b.mes_modelfile as mm on
    mf.model = mm.code
         left join hq_mdm_b.mes_workshop as mw on
    wcf.owning_workshop = mw.code
         LEFT JOIN
     hq_mdm_b.mes_machine_related_device mmrd ON mf.code = mmrd.machine_code
where 1 = 1
  and mf.isdelete = 0
  and wcf.flag_deleted = 0
  and mm.flag_deleted = 0
  and mw.flag_disable = 0
order by mf.create_time desc;
