var imgArr22 = {
    '喷印表面工序': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E5%88%86%E5%88%87.webp',
    '平切': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E5%B9%B3%E5%88%87.webp',
    '喷码': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E5%96%B7%E7%A0%81.webp',
    '印刷': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E5%8D%B0%E5%88%B7%E6%9C%BA.webp',
    '烫印': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E7%83%AB%E9%87%91.webp',
    '模烫': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E6%A8%A1%E5%88%87.webp',
    '说明书': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E8%AF%B4%E6%98%8E%E4%B9%A6.webp',
    '折页': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E6%8A%98%E9%A1%B5.webp',
    '品粘': '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E7%B3%8A%E7%9B%92.webp'
};
var menuHuanArr = []
var defaultBindingsArr = [
    {
        menuCode: 'P-LGPPYBMGX',
        menuName: '喷印',
        machine_id: '87'
    },
    {
        menuCode: "P-LGPPNGX",
        menuName: '品粘',
        machine_id: '80'
    },
    {
        menuCode: "P-LGPSMSBZ",
        menuName: '说明书包装工序',
        machine_id: '99'
    },
    {
        menuCode: "P-LGPMTGX",
        menuName: '模烫工序',
        machine_id: '79'
    },
    {
        menuCode: "P-LGPSMSCJ",
        menuName: '说明书车间工序',
        machine_id: '73'
    },
    {
        menuCode: "P-LGPYSGX",
        menuName: '印刷工序',
        machine_id: '71'
    }
]
$(function () {

    initMenuData()
    // setTimeout(() => {
    //     firstLoad()
    // }, 3000)

})

//  let defaultBindings={
//      '喷印':'LGP分切01机台',
//      '品粘':'03号机',
//     '说明书包装工序':'03 号机',
//     '模烫工序':'模切05机台',
// 说明书车间工序
// 默认展示  说明书印刷01机台

//     '印刷工序':'平板UV胶印02机台'
//  }


function getUrl(str) {
    let url = ''
    // 遍历imgArr22对象的所有键
    for (let key in imgArr22) {
        // 判断str是否包含当前键
        if (str.includes(key)) {
            // 如果包含，则返回对应的值
            url = imgArr22[key]
            break; // 如果只需要返回第一个匹配的值，可以使用 break 来结束循环
        }
    }
    return url
}

function initMenuData() {
    axios.get(`/grid/query/Token`).then(function (res) {
        //  console.log('resxxx:',res)
        let newtoken = res.data.data[0].token
        //console.log('newtoken:',newtoken)
        let headers2 = {
            'appCode': 'HQ_MDM',
            'Authorization': `Bearer ${newtoken}`
        }
        //console.log('headers2:',headers2)

        axios({
            method: 'post',
            url: '/icubeos/api/common/api/app/YWLJHQ_MDM0000001435',
            headers: headers2
        }).then(res2 => {
            if (res2.data.code == 200) {
                //console.log('res2:',res2)
                menuHuanArr = res2.data.data
                console.log('menuHuanArr:', menuHuanArr)
                menuHuanArr.forEach(e => {
                    let defaultItem = defaultBindingsArr.find(sw => {
                        return sw.menuCode == e.code
                    })
                    let defaultMachineId = defaultItem.machine_id
                    let defaultItemInfo = e.machines.find(sw2 => {
                        return sw2.machine_id == defaultMachineId
                    })
                    //  console.log('defaultItem:',defaultItem,defaultItemInfo)
                    e.machine_id = defaultItemInfo.machine_id
                    e.machine_name = defaultItemInfo.machine_name
                    e.url = getUrl(e.name)
                    e.machines = e.machines.filter((item,index,arr)=>{
                        return arr.findIndex(_i=>_i.machine_id==item.machine_id)==index
                    })
                    //console.log('e.url:',e)
                })
                menuHuanArr = menuHuanArr.filter(e => { return e.machine_id })
                //console.log('过滤后的mehuHauanArr:',menuHuanArr)
                // menuHuanArr.sort(function(a, b) {
                //         // 如果a.machines和b.machines都不存在，保持不变
                //         if (!a.machines.length && !b.machines.length) {
                //             return 0;
                //         }
                //         // 如果a.machines存在但b.machines不存在，a在前面
                //         else if (a.machines.length && !b.machines.length) {
                //             return -1;
                //         }
                //         // 如果a.machines不存在但b.machines存在，b在前面
                //         else if (!a.machines.length && b.machines.length) {
                //             return 1;
                //         }
                //         // 如果a.machines和b.machines都存在
                //         else {
                //             // 如果a.machines中有machine_id，a在前面
                //             if (a.machines.find(function(machine) { return machine.machine_id })) {
                //                 return -1;
                //             }
                //             // 如果b.machines中有machine_id，b在前面
                //             else if (b.machines.find(function(machine) { return machine.machine_id })) {
                //                 return 1;
                //             }
                //             // 否则保持不变
                //             else {
                //                 return 0;
                //             }
                //         }
                //     });
                //console.log('menuHuanArr:',menuHuanArr) 
                // initMenu()
                setTimeout(() => {
                    initSize()
                }, 1000)
            }

        })
    })


}

var current = 1,
    pageSize = 10; // 分页
var totalPage = 0; // 总页数
var selectedId; // 选中菜单id
function updateMenu(init = false) {
    //console.log('updateMenu')
    const ul = $('#menu2');
    //console.log('ul:',ul,menuHuanArr)
    // 先移除之前的数据
    ul.empty();
    const pageData = menuHuanArr.slice((current - 1) * pageSize, current * pageSize);
    //console.log('pageData:',pageData)
    for (let i = 0; i < pageData.length; i++) {
        const li = createLi(pageData[i]);
        // 默认选中第一个
        // if(i === 0 && init) {
        //     li.classList.add('menu-item-selected');
        //     initBigTitle(pageData[0].name)
        // } 
        ul.append(li);
    }
    setTimeout(() => {
        firstLoad()
    }, 1000)
}

function createLi(item) {
    const li = document.createElement('li');
    li.className = 'menu-item';
    li.id = `li-${item.code}`;
    li.innerHTML = item.name;
    li.onclick = function () {
        selectedId = `li-${item.code}`;
        selectItem2(selectedId);
        localStorage.setItem('current_gongyi', item.name)
        localStorage.setItem('processType', item.name)
        // $("#image84120BF000F64DCC97AF9150846A7481").find("img:first").css("display", 'none!im');

        changeUrl(item);
        // setCardsData(item)
        getDeviceCode(item)

    }
    return li;
}
function getDeviceCode(item){
    axios.get(`/grid/query/machine_equipment`).then(res=>{
        const machine_all_info = res?.data?.data
        console.log('machine_all_info',machine_all_info);
        const findMachine = (id)=>{
           const info =  machine_all_info.find(i=>i.machine_id==id)
            return info||null
        }
        const machines = item.machines.map(_i=>{
            return {
                ..._i,
                machine_name:findMachine(_i.machine_id)?.machine_name,
                machine_code:findMachine(_i.machine_id)?.machine_code,
                device_code:findMachine(_i.machine_id)?.device_code
            }
        })
        console.log('machines',machines);
        //更改接口，deviceCode需要单独获取
        const sendItem = {
            ...item,
            machine_name:findMachine(item.machine_id)?.machine_name,
            machine_code:findMachine(item.machine_id)?.machine_code,
            machines
        }
        setCardsData(sendItem)
    })
}
function selectItem2(id) {
    ////console.log('点击选中')
    $('#menu2 li').removeClass('menu-item-selected');
    $(`#menu2 #${id}`).addClass('menu-item-selected');
}

function prev1() {
    if (current > 1) {
        current -= 1;
        $('#current').text(current);
        updateMenu();
        selectItem2(selectedId);
        if (current <= 1) {
            // 将prev禁止点击
        }
    }
}

function next1() {
    if (current < totalPage) {
        current += 1;
        $('#current').text(current)
        updateMenu();
        selectItem2(selectedId);
        if (current >= totalPage) {
            // 将next禁止点击
        }
    }
}

function initSize() {
    //console.log('进入initSize')
    current = 1, pageSize = 6; // 分页
    if (pageSize < menuHuanArr.length) {
        $('#pagination2').css('display', 'flex');
    }
    totalPage = Math.ceil(menuHuanArr.length / pageSize);
    $('#totalPage').text(totalPage);
    $('#current').text(current);
    updateMenu(true);
}
//  let menuHuanArr=[
//         { name: '分切',machine_id:87, url: '/oss/uni/resource/view?filePath=/%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E5%88%86%E5%88%87.webp' },
//         { name: '平切',machine_id:83,  url: '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E5%B9%B3%E5%88%87.webp' },
//         { name: '喷码',machine_id:74,  url: '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E5%96%B7%E7%A0%81.webp' },
//         { name: '印刷',machine_id:71,  url: '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E5%8D%B0%E5%88%B7%E6%9C%BA.webp' },
//         { name: '烫印',machine_id:84,  url: '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E7%83%AB%E9%87%91.webp' },
//         { name: '模切', machine_id:78, url: '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E6%A8%A1%E5%88%87.webp' },
//         { name: '说明书',machine_id:73,  url: '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E8%AF%B4%E6%98%8E%E4%B9%A6.webp' },
//         { name: '折页',machine_id:82,  url: '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E6%8A%98%E9%A1%B5.webp' },
//         { name: '糊盒',machine_id:81,  url: '/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/%E7%B3%8A%E7%9B%92.webp' }
//         ]
function initMenu() {
    let currentIndex = 4
    let menuHuan = document.getElementById('menuHuan')
    for (let i = 0; i < menuHuanArr.length; i++) {
        let liEle = document.createElement('li')
        liEle.innerHTML = menuHuanArr[i].name
        menuHuan.appendChild(liEle)
        liEle.addEventListener('click', function () {
            //console.log('index:', i);
            currentIndex = i;
            localStorage.setItem('current_gongyi', menuHuanArr[i].name)
            let lis = menuHuan.querySelectorAll('li')
            localStorage.setItem('processType', menuHuanArr[i].name)

            lis.forEach(w => {
                setNoActive(w);
            });
            setActive(lis[currentIndex]);
            initSelect()
            changeUrl(currentIndex);
        });
    }
}

function firstLoad() {
    let menuHuan = document.getElementById('menu2')
    let lis = menuHuan.querySelectorAll('li')
    //console.log('lis:',lis)
    //  selectItem2('li-P-LGPYSGX')
    //  $('#li-P-LGPYSGX').click()
    //  lis[0].click()

    let processType = localStorage.getItem('processType')
    // console.log('获取到的processType:',processType)
    if (!processType) {
        processType = '印刷工序'
    }
    //     //console.log('process:',processType)
    //     //console.log('menuHuanArr:',menuHuanArr)
    let existIndex = menuHuanArr.findIndex(e => { return e.name.includes(processType) })
    //    console.log('existIndex:',existIndex)
    if (existIndex > -1) {
        lis[existIndex].click()
    } else {
        lis[0].click()
    }



}

function setActive(ele) {
    ele.setAttribute("style",
        "font-size: 48px; font-weight:700;background: url(/oss/uni/resource/view?filePath=//%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E8%89%BA/activeBg-4k.png);");

}
function setNoActive(w) {
    w.style.background = 'transparent'
    w.style.fontSize = '32px'
    w.style.fontWeight = '400'
}
function changeUrl(item) {
    setTimeout(() => {
        changeChartsByMachineId(item.machine_id)

    }, 500)
    //顶部标题修改
    $("#text8780CB182AA74FA28E3D814D6562E731").html(`环球${item.name}车间驾驶舱`);
    $("#text8780CB182AA74FA28E3D814D6562E731").css({
        "color": "#fff",
        "font-size": "0.3rem",
        "font-weight": "700",
        "top": '0.16rem',
        'left': '50%',
        'transform': 'translateX(-50%)' 
    });
    //图片下面文字修改
    $("#textC0751EFFFF024163B61D9E018C01C060").html(`${item.name}工艺`);
    $("#textC0751EFFFF024163B61D9E018C01C060").css({
        "color": "#fff",
        "font-size": "0.17rem",
        'font-weight':700,
        "top": '11%',
        'left': '50%',
        'transform': 'translateX(-50%)' 
    });
    //图片修改
    $("#image84120BF000F64DCC97AF9150846A7481").find("img:first").css("display", 'block');

    $("#image84120BF000F64DCC97AF9150846A7481").find("img:first").attr("src", item.url);

}
function changeChartsByMachineId(machine_id) {
    changeRight2(machine_id)
    changeHuancun3(machine_id)
}

