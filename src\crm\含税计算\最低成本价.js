var record={"float_rate":"±3","total_cost":97806.24,"crm_one_time_cost_sumAmount":0,"bd_executive_salary":0.0036159847239999997,"delivery_expense":0,"jsb_unit_price_exclude_tax":null,"gd_depreciation_expense_other":677,"bd_depreciation_expense_buildings":0.0036159847239999997,"gd_executive_salary":677,"create_by_name":"系统创建","lowest_cost_gross_margin":"NaN","jsb_amount_exclude_tax":null,"dw_sum_amount":0.09302006139800001,"variable_auxiliary_materials_sumAmount":0.044174511652,"mnemonic_code":"ZDQX001229","net_size":"","id":"ZDQX0001434_1.1_C000331","bd_depreciation_expense_other":0.0036159847239999997,"jsb_amount_include_tax":null,"sales_amount_usd":"1138152.00","paper_all_cost":"23756.16","utilities_labor":"21018.42","quotation_factory":"凌峰","create_time":"2025-01-26 22:24:34","sales_gross_profit_margin":"20.00","gd_sum_amount":4786.************,"amount_tax_inclusive":null,"product_size":"50*15*90","sales_amount":"1138152.00","sales_gross_profit":24452.17,"unit_price_usd":"1.138152","version":"2.0","object_id":"TiNACON媞娜E053/V01","unit_price_excluding":"1.007214","financial_gross_profit_margin":"20.00","float_mark":"±","default_factory":"凌峰","reason_approval":"","sub_class":"医疗器械类","zz_unit_variable_labor_cost_sumAmount":0.010847954172000001,"auxiliary_materials_sumAmount":0,"marginal_contribution_amount":"NaN","jsb_unit_price_include_tax":null,"rg_unit_variable_labor_cost_sumAmount":0.00700946547,"raw_material_cost":"68201.85","status":"4","unit_price_exclusive":null,"lowest_cost_unit_price":"NaN","handling_expense":0,"product_weight":0.00467,"pure_freight_cost":0,"gd_depreciation_expense_buildings":677,"sales_amount_excluding_usd":"1007214.16","main_class":"折叠纸盒类","sales_amount_excluding":"1007214.16","product_quantity":1000000,"productTotal":7145000,"create_by":"系统创建","update_time":"2025-02-27 16:54:02","quotation_code":"HJ251035563","standard_cost_unit_price":"0.0978","fixed_paper_sumAmount":271.************,"cost_accounting_sumAmount":0.************,"cust_name":"甘肃康视达科技集团有限公司","depreciation_expense":"8585.97","quotation_version":"1","update_by":"2","labor_rg_sumAmount":1130,"material_name":"TiNACON媞娜E053/V01软性亲水接触镜1片装半年抛小盒","labor_bd_sumAmount":2031,"flag_deleted":0,"product_version":"1.1","crm_one_time_cost":[{"amount":"0","create_time":"2025-05-21 17:09:49","categroy":"9","serviceability_ratio":"200000","edition_name":"搭印产品共用版","version":"1.0","edition_categroy":"丝印版","create_by":"2","update_time":"2025-05-21 17:09:49","material_price":"0","quotation_code":"HJ251035563","edition_code":"XGP-搭印产品共用版-SV1","edition_type":"印版","parent_code":"ZDQX0001434","quotation_version":"1","id":85449,"update_by":"2","flag_deleted":0},{"amount":"0","create_time":"2025-05-21 17:09:49","categroy":"9","serviceability_ratio":"300000","edition_name":"TiNACON媞娜E029/V02软性亲水接触镜1片装半年抛小盒版","version":"1.0","edition_categroy":"光油版","create_by":"2","update_time":"2025-05-21 17:09:49","material_price":"0","quotation_code":"HJ251035563","edition_code":"ZDQX000047-GBV1","edition_type":"印版","parent_code":"ZDQX0001434","quotation_version":"1","id":85450,"update_by":"2","flag_deleted":0},{"amount":"0","create_time":"2025-05-21 17:09:49","categroy":"9","serviceability_ratio":"700000","edition_name":"TiNACON媞娜E029/V02软性亲水接触镜1片装半年抛小盒版","version":"1.0","edition_categroy":"模切版","create_by":"2","update_time":"2025-05-21 17:09:49","material_price":"0","quotation_code":"HJ251035563","edition_code":"ZDQX000047-MV1","edition_type":"模切版","parent_code":"ZDQX0001434","quotation_version":"1","id":85451,"update_by":"2","flag_deleted":0},{"amount":"0","create_time":"2025-05-21 17:09:49","categroy":"9","serviceability_ratio":"700000","edition_name":"TiNACON媞娜E053/V01软性亲水接触镜1片装半年抛小盒版","version":"1.0","edition_categroy":"凹凸版","create_by":"2","update_time":"2025-05-21 17:09:49","material_price":"0","quotation_code":"HJ251035563","edition_code":"ZDQX001229-AV1","edition_type":"模切版","parent_code":"ZDQX0001434","quotation_version":"1","id":85452,"update_by":"2","flag_deleted":0},{"create_time":"2025-05-21 17:09:49","categroy":"3","version":"1.0","create_by":"2","update_time":"2025-05-21 17:09:49","quotation_code":"HJ251035563","parent_code":"0","quotation_version":"1","id":85453,"update_by":"2","material_name":"TiNACON媞娜E053/V01软性亲水接触镜1片装半年抛小盒","material_code":"ZDQX0001434","flag_deleted":0}],"update_by_name":"系统创建","amount_exclusive_tax":null,"unit_price_excluding_usd":"1.007214","unit_price":"1.138152","tax_diff":"130937.84","float_info":"3","freight_cost":0,"cust_code":"C000331","carrier_feign_cost":0,"out_of_book":24,"price_including_tax":null,"break_even_quantity":"163697.47","price_diff":null,"auxiliary_ctp_sumAmount":0,"standard_unit":"只","product_big_version":"1","grade_name":"TiNACON媞娜E053/V01软性亲水接触镜1片装半年抛小盒","material_code":"ZDQX0001434"}

// 盈亏平衡数量 = 固定成本/（不含税销售单价（RMB）-单位变动成本）
record['break_even_quantity'] = (record['gd_sum_amount'] / (record['unit_price_excluding'] - record['dw_sum_amount'])).toFixed(2)
if (record['break_even_quantity'] < 0) {
    record['break_even_quantity'] = 0;
}

// 运费= （产品重量*报价数量）*承运商运费
record['freight_cost'] = record['pure_freight_cost'] + record['handling_expense'] + record['delivery_expense']
//record['freight_cost'] = Number((record['product_weight'] * record['product_quantity']) * record['carrier_feign_cost'])  + record['handling_expense'] + record['delivery_expense']
console.log("product_weight",record['product_weight'])
console.log("carrier_feign_cost",record['carrier_feign_cost'])
record['freight_cost'] = Math.round(record['freight_cost'] * 100) / 100;

// 总成本 = 固定成本 +单位变动成本 * 数量 + 运费
record['total_cost'] = Number((record['gd_sum_amount'] + (record['dw_sum_amount'] * record['product_quantity']) + record['freight_cost']).toFixed(2));
console.log("total_cost:",record['total_cost'])
console.log("freight_cost:",record['freight_cost'])
// 原辅料成本 = 固定成本纸张 + 固定成本辅料 + 固定成本CTP + (变动成本纸张 + 变动成本辅料 ) * 数量 + 一次性费用
record['raw_material_cost'] = (parseFloat(record['fixed_paper_sumAmount']) + 
                               parseFloat(record['auxiliary_materials_sumAmount']) + 
                               parseFloat(record['auxiliary_ctp_sumAmount']) + 
                               (
                                 (
                                   parseFloat(record['cost_accounting_sumAmount']) + 
                                   parseFloat(record['variable_auxiliary_materials_sumAmount'])) * 
                                   record['product_quantity']) + 
                               parseFloat(record['crm_one_time_cost_sumAmount'])).toFixed(2)
// 折旧 = 固定成本制造费用（设备及其他设置+房屋建筑）+（单位变动成本制造的设备及其他设置+房屋建筑）*数量
record['depreciation_expense'] = (parseFloat(record['gd_depreciation_expense_other']) + parseFloat(record['gd_depreciation_expense_buildings'])+ ((parseFloat(record['bd_depreciation_expense_other']) + parseFloat(record['bd_depreciation_expense_buildings'])) * record['product_quantity'])).toFixed(2);// 水电人工 =固定成本人工+固定成本制造费用 + (变动人工费用 + 变动制造费用)*数量
record['utilities_labor'] = (parseFloat(record['labor_rg_sumAmount']) + parseFloat(record['labor_bd_sumAmount']) + (parseFloat(record['rg_unit_variable_labor_cost_sumAmount']) + parseFloat(record['zz_unit_variable_labor_cost_sumAmount'])) * record['product_quantity']).toFixed(2);
// 标准成本单价 =固定成本/报价数量+单位变动成本总计，保留4位小数
record['standard_cost_unit_price'] = (parseFloat(record['gd_sum_amount']) / parseFloat(record['product_quantity']) + 
                                      parseFloat(record['dw_sum_amount'])).toFixed(4);

/*最低成本单价（新）=
  (
    (固定成本 - 固定折旧费用 * 92%) / 报价数量 + (单位变动成本 - 变动折旧成本 * 92%) + (一次性费用包含版费等 + 运费) / 报价数量
  )
  固定折旧费用 = 固定成本(制造费用)的首理人员工资+固定成本(制造费用)的折旧费(设备及其他类)+固定成本(制费用)的折旧费(房星建筑物类)
  变动折旧成本 = 单位变动成本(制造费用)的管理人员工资 + 单位变动(制造费用)的折旧费(设备及其他类) + 单位变动(制造费用)的折旧费(房屋建筑物类)
  普通报价：
  一次性费用=核价单中CTP版的费用
  报价中版材费用=根据耐印率计算当前报价单是不是需要计算其他版材费用的那个版费

  拼版报价：
  一次性费用=核价单中CTP版的费用
  报价中的版材费用=报价中填的一次性费用
  保留6位小数，由于固定成本中已经包含了ctp，所以无需再加一次*/
  var gd_depreciation = parseFloat(record['gd_executive_salary']) + parseFloat(record['gd_depreciation_expense_other'])
                    + parseFloat(record['gd_depreciation_expense_buildings'])
  var bd_depreciation = parseFloat(record['bd_executive_salary']) + parseFloat(record['bd_depreciation_expense_other'])
                    + parseFloat(record['bd_depreciation_expense_buildings'])
  record['lowest_cost_unit_price'] = (
    (parseFloat(record['gd_sum_amount']) - gd_depreciation * 0.92) / parseFloat(record['product_quantity'])
    + (parseFloat(record['dw_sum_amount']) - bd_depreciation * 0.92)
    + (parseFloat(record['crm_one_time_cost_sumAmount']) + parseFloat(record['freight_cost'])) / parseFloat(record['product_quantity'])
  ).toFixed(6)

// 销售毛利 = 不含税销售金额（RMB） - 总成本
record['sales_gross_profit'] = Number((record['sales_amount_excluding'] - record['total_cost']).toFixed(2));
// 销售毛利率 = 销售毛利/不含税销售金额（RMB）* 100
record['sales_gross_profit_margin'] = (record['sales_gross_profit'] / record['sales_amount_excluding'] * 100).toFixed(2);
// 财务毛利率% = (销售毛利 + 运费) / 不含税销售金额（RMB）* 100
record['financial_gross_profit_margin'] = ((record['sales_gross_profit'] + record['freight_cost']) / record['sales_amount_excluding'] * 100).toFixed(2);
console.log("sales_gross_profit:",record['sales_gross_profit'])
console.log("sales_amount_excluding:",record['sales_amount_excluding'])

// 边际贡献金额=(不含税销售单价(RMB) * 报价数量 - 最低成本单价 * 订单数量),保留2位小数
record['marginal_contribution_amount'] = (parseFloat(record['unit_price_excluding']) * parseFloat(record['product_quantity']) -
                                            parseFloat(record['lowest_cost_unit_price']) * parseFloat(record['product_quantity'])).toFixed(2)

// 最低成本毛利率 = ((最低成本单价 - 标准成本价) / 最低成本单价 * 100%),保留2位小数
record['lowest_cost_gross_margin'] = (((parseFloat(record['lowest_cost_unit_price']) - parseFloat(record['standard_cost_unit_price'])) / 
                                        parseFloat(record['lowest_cost_unit_price'])) * 100).toFixed(2)

 // 财务毛利率% 如果不含税销售金额(RMB)为0,则判断NAN
record.financial_gross_profit_margin = isNaN(record.financial_gross_profit_margin)?0.00:record.financial_gross_profit_margin
 // 盈亏平衡数量,如果（不含税销售单价（RMB）-单位变动成本）为0
record.break_even_quantity = isNaN(record.break_even_quantity)?0.00:record.break_even_quantity

console.log(record.lowest_cost_unit_price,record.marginal_contribution_amount,record.lowest_cost_gross_margin,);