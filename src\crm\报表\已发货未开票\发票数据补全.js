const mysql = require('mysql');

async function fetchDataWithToken() {
    // 第一个请求 - 获取 token
    const tokenHeaders = new Headers();
    tokenHeaders.append("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
    tokenHeaders.append("Accept", "*/*");
    tokenHeaders.append("Host", "**************:30783");
    tokenHeaders.append("Connection", "keep-alive");

    const tokenRequestOptions = {
        method: 'GET',
        headers: tokenHeaders,
        redirect: 'follow'
    };

    try {
        // 获取 token
        const tokenResponse = await fetch("http://**************:30783/mom_test/hqErpSyn/erptoken", tokenRequestOptions);
        const tokenData = await tokenResponse.json();
        const token = tokenData.token;

        // 第二个请求 - 使用获取的 token
        const queryHeaders = new Headers();
        queryHeaders.append("access_token", token); // 使用获取的 token
        queryHeaders.append("ucg_flag", "y");
        queryHeaders.append("signature", "db9f1d9f52d5912af0abfda515e122224f026037efc5c8ee50a8ed49796de6e2");
        queryHeaders.append("repeat_check", "Y");
        queryHeaders.append("client_id", "jiekou");
        queryHeaders.append("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
        queryHeaders.append("Content-Type", "application/json;charset=utf-8");
        queryHeaders.append("Accept", "*/*");
        queryHeaders.append("Host", "************:5000");
        queryHeaders.append("Connection", "keep-alive");

        const raw = {
            "begintime": "2024-01-09",
            "endtime": "2025-07-01"
        };

        const queryRequestOptions = {
            method: 'POST',
            headers: queryHeaders,
            body: JSON.stringify(raw),
            redirect: 'follow'
        };

        // 发送带有 token 的请求
        const queryResponse = await fetch("http://************:5000/nccloud/api/so/saleinvoiceHQ/queryHQ", queryRequestOptions);
        const result = await queryResponse.json();
        return result;
    } catch (error) {
        console.error('Error:', error); // 错误处理
        throw error;
    }
}

async function main() {
    try {
        // 获取数据
        const result = await fetchDataWithToken();
        const data = result.data;
        
        if (!data || !Array.isArray(data)) {
            throw new Error('No data received or data is not an array');
        }

        // 创建数据库连接
        const connection = mysql.createConnection({
            host: '**************',
            user: 'root',
            password: '123456',
            database: 'h3chq_crmbusiness1704287359505'
        });

        // 将数据库操作转换为Promise
        const queryDatabase = () => {
            return new Promise((resolve, reject) => {
                connection.connect((err) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    console.log('Connected to the database');

                    connection.query('SELECT id, csaleinvoicebid, csaleinvoiceid FROM crm_sales_invoice_details', (error, results) => {
                        if (error) {
                            reject(error);
                            return;
                        }

                        // 遍历查询结果并匹配
                        const unmatchedData = data.filter(item => {
                            return !results.some(result => 
                                result.csaleinvoicebid === item.csaleinvoicebid && 
                                result.csaleinvoiceid === item.csaleinvoiceid
                            );
                        });

                        // 打印未匹配的数据
                        if (unmatchedData.length > 0) {
                            console.log('Unmatched data:', JSON.stringify(unmatchedData.length));
                            
                            // 准备插入未匹配的数据
                            const insertPromises = unmatchedData.map(item => {
                                return new Promise((resolveInsert, rejectInsert) => {
                                    // 准备插入数据，补充缺失字段
                                    const insertData = {
                                        user_code: item.user_code || null,
                                        user_name: item.user_name || null,
                                        dbilldate: item.dbilldate || null,
                                        fstatusflag: item.fstatusflag || null,
                                        norigtaxmny: item.norigtaxmny || null,
                                        ntax: item.ntax || null,
                                        ntaxrate: item.ntaxrate || null,
                                        norigmny: item.norigmny || null,
                                        norignetprice: item.norignetprice || null,
                                        nnum: item.nnum || null,
                                        vdef20: item.vdef20 || null,
                                        name: item.name || null,
                                        materialmnecode: item.materialmnecode || null,
                                        code: item.code || null,
                                        csaleorderbid: item.dsfbtzjorder_b || null,
                                        flag_deleted: 0,
                                        version: item.version || '1.0',
                                        create_by: 'system',
                                        create_time: new Date(),
                                        update_by: 'system',
                                        update_time: new Date(),
                                        domain_id: null,
                                        csaleorderid: item.dsfbtzjorder || null,
                                        handler_status: '0',
                                        csaleinvoicebid: item.csaleinvoicebid || null,
                                        csaleinvoiceid: item.csaleinvoiceid || null,
                                        dsfbtzj: item.dsfbtzj || null,
                                        dsfbtzj_b: item.dsfbtzj_b || null,
                                        fopposeflag: item.fopposeflag || null,
                                        vsrcrowno: item.vsrcrowno || null,
                                        vsrccode: item.vsrccode || null,
                                        dsfbtzjorder_b: item.dsfbtzjorder_b || null,
                                        dsfbtzjorder: item.dsfbtzjorder || null,
                                        csrcbid: item.csrcbid || null,
                                        csrcid: item.csrcid || null,
                                        ntaxmny: item.ntaxmny || null
                                    };

                                    connection.query('INSERT INTO crm_sales_invoice_details SET ?', insertData, (insertError) => {
                                        if (insertError) {
                                            rejectInsert(insertError);
                                            return;
                                        }
                                        resolveInsert();
                                    });
                                });
                            });

                            // 执行所有插入操作
                            Promise.all(insertPromises)
                                .then(() => {
                                    console.log('All unmatched data has been inserted successfully');
                                    connection.end();
                                    resolve(unmatchedData);
                                })
                                .catch(insertError => {
                                    connection.end();
                                    reject(insertError);
                                });
                        } else {
                            console.log('All data matched');
                            connection.end();
                            resolve([]);
                        }
                    });
                });
            });
        };

        // 执行数据库查询
        await queryDatabase();

    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
}

// 执行主函数
main().catch(console.error);

