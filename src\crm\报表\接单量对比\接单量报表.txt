销售合同 合同状态0-待下发、1-部分下发、2-已下发、3-已作废、4-已关闭
拆分订单 订单状态status：0-已拆分,1-已下达,3-已领料,4-生产中,5-已入库,6-已开票,7-已关闭,8-已取消,11-已发货未开票

销售合同状态为
接单量=销售合同金额（销售合同状态为 待下发，部分下发，已下发）
接单量=0 (销售合同状态为已作废)
接单量=拆分订单金额(销售合同状态为已关闭)


SELECT md.update_month,
       md.product_big_category,
       md.product_big_category_name,
       COALESCE(dd.amount, 0) amount,
       0                      amount_yoy,
       0                      amount_mom
FROM (SELECT `year_month` update_month,
             dpbc.code AS product_big_category,
             dpbc.name AS product_big_category_name
      FROM dim_month dm,
           dim_product_big_category dpbc
      WHERE dm.`year` = YEAR(CURDATE())) md
         LEFT JOIN
     (SELECT DATE_FORMAT(a.create_time, '%Y-%m')                                                     update_month,
             dpbc.code AS                                                                            product_big_category,
             COALESCE(ROUND(SUM(COALESCE(a.total, 0) * COALESCE(a.unit_price_no, 0)) / 10000, 0), 0) amount
      FROM dim_product_big_category dpbc
               LEFT JOIN ods_pm_order_product a ON
          a.major_categories = dpbc.code
               LEFT JOIN ods_pm_sale_order b ON
          b.order_number = a.sales_number
      WHERE b.status in (1, 2, 3, 4)
        AND YEAR(a.create_time) = YEAR(CURDATE())
      GROUP BY DATE_FORMAT(a.create_time, '%Y-%m'),
               dpbc.code) dd ON
         dd.update_month = md.update_month
             AND dd.product_big_category = md.product_big_category
ORDER BY md.update_month;
