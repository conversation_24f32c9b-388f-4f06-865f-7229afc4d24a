-- 组产品同步
select  standard_unit,status,product_version,mpv.* from mes_product_version mpv
where material_code='ZDHY0003595' ;
SELECT id, material_code, standard_unit,product_version
FROM mes_product_version
WHERE material_code = 'SMHY0001004'
  AND product_version LIKE CONCAT('1', '%');
select * from mes_product_bom_list
where parent_code='ZDHZ0000601' and product_version='1.4' and type in(1,2) and product_codes='ZDWL0000014';
select product_code,product_name,parent_code,type from mes_product_bom_list
where product_version = '1.4' and product_codes = 'ZDWL0000014';

-- 版档案                                   ;
select * from mes_edition_file mef where edition_code='ZDYY008600-MV1';
-- 工艺路线
select product_codes,product_code,product_version,indexs,update_time,t1.* from mes_product_craft t1
 where product_codes='ZDHY0000688' and product_version=1.3;
;
-- 物料档案
select * from mes_material_file mmf where material_code='ZDSW0001407';
select * from mes_material_file mmf where procure_unit='27539';

select concat('',id,'') as value,data_value as label from mes_parameter_data
where info_id = (select id from mes_parameter_info where info_name = '版类型' and flag_deleted = 0)
and flag_deleted = 0 ;

select * from mes_product_client;

select * from mes_parameter_data where id in (27539) and flag_deleted = 0 ;
select * from mes_parameter_data where data_value like 'mm' and flag_deleted = 0 ;
