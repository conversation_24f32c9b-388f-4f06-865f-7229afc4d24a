-- 产品档案相关表
select id,material_code, mnemonic_code,product_version, main_class, sub_class, is_group_product, t1.*
from pdm_product_version t1
where 1 = 1
#   and material_code in ('ZDHY0003034')
  and mnemonic_code in ('SMQX000021')
#and standard_unit ='27539'
# and procure_unit ='27539'
; -- 产品版本表


select * from pdm_product_ban_assunit ppba where edition_code  like 'ZDSW000057-1%'; -- 产品版档案
select *
from pdm_edition pe where edition_code like '%ZDHY0003431-34-GV1%' ; -- 版档案
select ppb.mnemonic_code,ppb.*
from pdm_product_bom ppb where 1=1
#  and material_code = 'WLSW0000109'
#  and standard_unit = '27539'

; -- 产品BOM明细表
select *
from pdm_product_client ppc where product_code='ZDZY0000902' ; -- 客户料号
select *
from pdm_product_craft ppc  where parent_code='ZDSW000057-1'; -- 工艺路线
select pm.id,pm.category,flag_deleted, pm.mnemonic_code,pm.*
from pdm_material pm where material_code='WLSW0000109'; -- 物料表

select id, standard_unit, procure_unit, t1.*
from pdm_material t1
where mnemonic_code in ('ZDSW000058');
-- 查询物料
select id, t1.product_version, standard_unit, t1.procure_unit,update_time, t1.*
from pdm_product_version t1
where 1=1
#   and material_code = 'ZDSW0001144'
and procure_unit='27539'
;

-- 查询bom
select product_version, t1.*
from pdm_product_bom t1
where product_code = 'ZDYY0003150-4';



SELECT
    pv.id,
    pv.material_code,
    pv.mnemonic_code,
    pv.material_name,
    pv.is_group_product,
    pv.bom_type,
    pv.main_class_cn,
    pv.sub_class_cn,
    pv.product_version,
    pv.status,
    pv.product_size,
    pv.create_by,
    pv.create_time,
    pv.update_by,
    pv.update_time
FROM pdm_product_version pv
WHERE pv.flag_deleted = 0
  AND pv.material_code is not null
  AND pv.status IN (1,2,3,4)
#   AND (:material_code IS NULL OR :material_code = '' OR pv.material_code LIKE CONCAT('%', :material_code, '%'))
#   AND (:material_name IS NULL OR :material_name = '' OR pv.material_name LIKE CONCAT('%', :material_name, '%'))
#   AND (:mnemonic_code IS NULL OR :mnemonic_code = '' OR pv.mnemonic_code LIKE CONCAT('%', :mnemonic_code, '%'))
#   AND (:is_group IS NULL OR :is_group = '' OR pv.is_group_product = :is_group)
#   AND (:bom_type IS NULL OR :bom_type = '' OR pv.bom_type = :bom_type)
#   AND (:product_version IS NULL OR :product_version = '' OR pv.product_version = :product_version)
#   AND (:product_size IS NULL OR :product_size = '' OR pv.product_size LIKE CONCAT('%', :product_size, '%'))
#   AND (:startcreatetime IS NULL OR :startcreatetime = '' OR pv.create_time >= :startcreatetime)
#   AND (:endcreatetime IS NULL OR :endcreatetime = '' OR pv.create_time <= :endcreatetime)
#   AND (:startupdatetime IS NULL OR :startupdatetime = '' OR pv.update_time >= :startupdatetime)
#   AND (:endupdatetime IS NULL OR :endupdatetime = '' OR pv.update_time <= :endupdatetime)
#   AND IF(:main_class_size > 0, pv.main_class IN (:main_class), 1)
#   AND IF(:sub_class_size > 0, pv.sub_class IN (:sub_class), 1)
  AND IF(:client_name IS NULL OR :client_name = '', 1,
         EXISTS (SELECT 1 FROM pdm_product_client pc
                 WHERE pc.product_code = pv.material_code
                   and pc.product_version = pv.product_version
                   AND pc.client_name LIKE CONCAT('%', :client_name, '%')))
#   AND IF(:mater_code IS NULL OR :mater_code = '', 1,
#          EXISTS (SELECT 1 FROM pdm_product_bom pb
#                  WHERE pb.product_code = pv.material_code
#                    AND pb.product_version = pv.product_version
#                    AND pb.material_code LIKE CONCAT('%', :mater_code, '%')))
#
#   AND IF(:mater_name IS NULL OR :mater_name = '', 1,
#          EXISTS (SELECT 1 FROM pdm_product_bom pb
#                  WHERE pb.product_code = pv.material_code
#                    AND pb.product_version = pv.product_version
#                    AND pb.material_name LIKE CONCAT('%', :mater_name, '%')))
#
#   AND IF(:paper_size IS NULL OR :paper_size = '', 1,
#          EXISTS (SELECT 1 FROM pdm_product_bom pb
#                  WHERE pb.product_code = pv.material_code
#                    AND pb.product_version = pv.product_version
#                    AND pb.component_size LIKE CONCAT('%', :paper_size, '%')
#                    AND pb.categroy = '1'))
#   AND IF(:processname IS NULL OR :processname = '', 1,
#          EXISTS (SELECT 1 FROM pdm_product_craft ppc
#                  WHERE ppc.product_code = pv.material_code
#                    AND ppc.product_version = pv.product_version
#                    AND ppc.craft_name LIKE CONCAT('%', :processname, '%')))
#
#   AND IF(:fileFlag = '1',
#          EXISTS (SELECT 1 FROM pdm_upload_file pf
#                  WHERE pf.material_code = pv.material_code
#                    AND pf.product_version = pv.product_version
#                    AND pf.file_id IS NOT NULL), 1)

ORDER BY pv.id DESC
LIMIT :pageNo, :pageSize
;

