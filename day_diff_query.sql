-- 方法1：使用LAG窗口函数计算前后2天数据相减（推荐）
SELECT 
    DATE_FORMAT(DATE_SUB(date, INTERVAL 1 DAY), '%m-%d') AS date,
    MAX(CASE name WHEN 'R3蜜量' THEN value ELSE 0 END) AS R3蜜量,
    MAX(CASE name WHEN 'R3对溶糖比' THEN value ELSE 0 END) AS R3对溶糖比,
    -- 计算与前一天的差值
    MAX(CASE name WHEN 'R3蜜量' THEN value ELSE 0 END) - 
    LAG(MAX(CASE name WHEN 'R3蜜量' THEN value ELSE 0 END), 1, 0) OVER (ORDER BY date) AS R3蜜量_日差,
    MAX(CASE name WHEN 'R3对溶糖比' THEN value ELSE 0 END) - 
    LAG(MAX(CASE name WHEN 'R3对溶糖比' THEN value ELSE 0 END), 1, 0) OVER (ORDER BY date) AS R3对溶糖比_日差
FROM 
    sugar_key_index_r3
WHERE 
    date >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
GROUP BY 
    date
ORDER BY 
    date ASC;

-- 方法2：如果数据库不支持窗口函数，使用自连接
SELECT 
    t1.date,
    t1.R3蜜量,
    t1.R3对溶糖比,
    COALESCE(t1.R3蜜量 - t2.R3蜜量, 0) AS R3蜜量_日差,
    COALESCE(t1.R3对溶糖比 - t2.R3对溶糖比, 0) AS R3对溶糖比_日差
FROM (
    SELECT 
        DATE_FORMAT(DATE_SUB(date, INTERVAL 1 DAY), '%m-%d') AS date,
        date as original_date,
        MAX(CASE name WHEN 'R3蜜量' THEN value ELSE 0 END) AS R3蜜量,
        MAX(CASE name WHEN 'R3对溶糖比' THEN value ELSE 0 END) AS R3对溶糖比
    FROM sugar_key_index_r3
    WHERE date >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
    GROUP BY date
) t1
LEFT JOIN (
    SELECT 
        DATE_FORMAT(DATE_SUB(date, INTERVAL 1 DAY), '%m-%d') AS date,
        date as original_date,
        MAX(CASE name WHEN 'R3蜜量' THEN value ELSE 0 END) AS R3蜜量,
        MAX(CASE name WHEN 'R3对溶糖比' THEN value ELSE 0 END) AS R3对溶糖比
    FROM sugar_key_index_r3
    WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    GROUP BY date
) t2 ON t1.original_date = DATE_ADD(t2.original_date, INTERVAL 1 DAY)
ORDER BY t1.original_date ASC;
