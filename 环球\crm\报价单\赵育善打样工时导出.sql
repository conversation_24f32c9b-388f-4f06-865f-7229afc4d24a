SELECT SUBSTRING_INDEX(c.production_batch_number, '_', 1) as '打样单号',
    pjo1.develop_project_code as '研发项目号',
    c.product_names as '产品名称',
    c.product_codes as '产品编号',
    c.process_name as '工序名称',
    c.planned_time as '计划实际',
    d.actual_time as '实际用时',
    e.report_num as '工序产出',
    c.id
FROM pm_job_detail c
         LEFT JOIN (
    SELECT d1.job_bar as 'job_id', SUM(d1.actual_time) / 60 as 'actual_time'
    FROM pm_oee_records d1
    WHERE d1.job_bar in (
        SELECT ss.id
        FROM (
                 SELECT i.production_batch_number,i.task_list_code,i.id
                 FROM pm_job_order c
                          LEFT JOIN pm_job_order_management d ON c.production_batch_number = d.production_batch_number
                          LEFT JOIN pm_job_detail i ON d.task_code = i.task_code
                          LEFT JOIN pm_trial_order k ON c.trial_order_code = k.trial_order_code
                 WHERE c.is_collage = 1 AND c.flag_deleted = 0 AND d.flag_deleted = 0 AND k.create_time >= '2024-12-01' AND k.create_time < '2025-01-01'
                 UNION
                 SELECT j.production_batch_number,j.task_list_code,j.id
                 FROM pm_job_order e
                          LEFT JOIN pm_job_order_middle f ON e.production_batch_number = f.production_batch_number
                          LEFT JOIN pm_job_order_management h ON f.task_code = h.task_code
                          LEFT JOIN pm_job_detail j ON h.task_code = j.task_code
                          LEFT JOIN pm_trial_order l ON e.trial_order_code = l.trial_order_code
                 WHERE e.is_collage = 0 AND f.flag_deleted = 0 AND h.flag_deleted = 0 AND l.create_time >= '2024-12-01' AND l.create_time < '2025-01-01'
                 ORDER BY production_batch_number,task_list_code
             ) ss
    )
      AND (d1.action_properties = 3 or d1.action_properties = 6)
    GROUP BY d1.job_bar
) d
                   ON c.id = d.job_id
         LEFT JOIN (
    SELECT e1.work_no as 'task_list_code',SUM(e1.report_num) as 'report_num'
    FROM pm_production_reporter e1
             LEFT JOIN pm_job_detail c2
                       ON e1.work_no = c2.task_list_code
    WHERE c2.id in (SELECT ss.id
                    FROM (
                             SELECT i.production_batch_number,i.task_list_code,i.id
                             FROM pm_job_order c
                                      LEFT JOIN pm_job_order_management d ON c.production_batch_number = d.production_batch_number
                                      LEFT JOIN pm_job_detail i ON d.task_code = i.task_code
                                      LEFT JOIN pm_trial_order k ON c.trial_order_code = k.trial_order_code
                             WHERE c.is_collage = 1 AND c.flag_deleted = 0 AND d.flag_deleted = 0 AND k.create_time >= '2024-12-01' AND k.create_time < '2025-01-01'
                             UNION
                             SELECT j.production_batch_number,j.task_list_code,j.id
                             FROM pm_job_order e
                                      LEFT JOIN pm_job_order_middle f ON e.production_batch_number = f.production_batch_number
                                      LEFT JOIN pm_job_order_management h ON f.task_code = h.task_code
                                      LEFT JOIN pm_job_detail j ON h.task_code = j.task_code
                                      LEFT JOIN pm_trial_order l ON e.trial_order_code = l.trial_order_code
                             WHERE e.is_collage = 0 AND f.flag_deleted = 0 AND h.flag_deleted = 0 AND l.create_time >= '2024-12-01' AND l.create_time < '2025-01-01'
                             ORDER BY production_batch_number,task_list_code
                         ) ss) AND e1.report_type in (1,3,4)
    GROUP BY e1.work_no
) e
                   ON e.task_list_code = c.task_list_code
         left join pm_job_order pjo1 on c.production_batch_number = pjo1.production_batch_number
WHERE c.id in (
    SELECT ss.id
    FROM (
             SELECT i.production_batch_number,i.task_list_code,i.id
             FROM pm_job_order c
                      LEFT JOIN pm_job_order_management d ON c.production_batch_number = d.production_batch_number
                      LEFT JOIN pm_job_detail i ON d.task_code = i.task_code
                      LEFT JOIN pm_trial_order k ON c.trial_order_code = k.trial_order_code
             WHERE c.is_collage = 1 AND c.flag_deleted = 0 AND d.flag_deleted = 0 AND k.create_time >= '2024-12-01' AND k.create_time < '2025-01-01'
             UNION
             SELECT j.production_batch_number,j.task_list_code,j.id
             FROM pm_job_order e
                      LEFT JOIN pm_job_order_middle f ON e.production_batch_number = f.production_batch_number
                      LEFT JOIN pm_job_order_management h ON f.task_code = h.task_code
                      LEFT JOIN pm_job_detail j ON h.task_code = j.task_code
                      LEFT JOIN pm_trial_order l ON e.trial_order_code = l.trial_order_code
             WHERE e.is_collage = 0 AND f.flag_deleted = 0 AND h.flag_deleted = 0 AND l.create_time >= '2024-12-01' AND l.create_time < '2025-01-01'
             ORDER BY production_batch_number,task_list_code
         ) ss
)
;
