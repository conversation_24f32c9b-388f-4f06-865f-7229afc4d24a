-- 返工补做统计
SELECT `date`,
       SUM(IF(type != 1, distinct_count, 0)) AS rework_batch,
       SUM(IF(type != 1, distinct_count * avg_cost, 0)) AS rework_cost,
       SUM(IF(type = 1, distinct_count, 0))  AS makeup_batch,
       SUM(IF(type = 1, distinct_count * avg_cost, 0)) AS makeup_cost
FROM (SELECT DATE(t1.preparation_time)                  AS `date`,
             t1.type,
             t1.production_batch_number,
             COUNT(t1.production_batch_number) AS distinct_count
      FROM cockpit.ods_pm_job_order_restock t1
               LEFT JOIN cockpit.ods_pm_job_order_management t2
                         ON t1.production_batch_number = t2.production_batch_number
      WHERE t1.flag_deleted = 0
        AND t2.flag_deleted = 0
        AND ((:start_date IS NULL OR :start_date = '') OR (DATE(t1.preparation_time) >= :start_date))
        AND ((:end_date IS NULL OR :end_date = '') OR (DATE(t1.preparation_time) <= :end_date))
        AND ((:production_batch_number IS NULL OR :production_batch_number = '') OR
             (t1.production_batch_number = :production_batch_number))
        AND ((:product_code IS NULL OR :product_code = '') OR (t2.product_codes = :product_code))
        AND ((:product_name IS NULL OR :product_name = '') OR (t2.product_name = :product_name))
      GROUP BY DATE(t1.preparation_time),t1.type,t1.production_batch_number) AS t1
  join (
    select t1.production_batch_number,round(ifnull(t2.total_all_cost/t1.size,0),2) avg_cost from
        (select production_batch_number, count(*) size
         from cockpit.ods_pm_job_order_restock opjor
         where flag_deleted=0
         group by production_batch_number) t1
            left join (select
                           vmocode production_batch_number,
                           round(sum(
                                         ifnull(paper,0) + ifnull(packaging_materials,0) + ifnull(direct_auxiliary_materials,0) +
                                         ifnull(other_material_fee,0) + ifnull(public_materials,0) + ifnull(finished_product_collection,0) +
                                         ifnull(semi_finished_outsourcing_fee,0) + ifnull(finished_outsourcing_fee,0) +
                                         ifnull(knife_mold,0) + ifnull(printing_plate,0) + ifnull(machinery_material_consumption,0) +
                                         ifnull(salary,0) + ifnull(production_management_salary,0) + ifnull(labor_cost_variance_adjustment,0) +
                                         ifnull(basic_endowment_insurance,0) + ifnull(medical_insurance_fee,0) + ifnull(work_injury,0) +
                                         ifnull(housing_provident_fund,0) + ifnull(maternity_insurance,0) + ifnull(supplementary_medical_insurance_fee,0) +
                                         ifnull(unemployment_insurance_fee,0) +
                                         ifnull(water_fee,0) + ifnull(electricity_fee,0) + ifnull(natural_gas_fee,0) + ifnull(steam_fee,0) +
                                         ifnull(depreciation_buildings,0) + ifnull(other_depreciation,0) + ifnull(low_value_consumables_amortization,0) +
                                         ifnull(other_asset_amortization,0) +
                                         ifnull(transportation_fee,0) + ifnull(storage_fee,0) +
                                         ifnull(office_expenses,0) + ifnull(postal_telecommunication_fee,0) + ifnull(meeting_expenses,0) +
                                         ifnull(travel_expenses,0) + ifnull(business_entertainment_expenses,0) + ifnull(rental_fee,0) +
                                         ifnull(production_service_fee,0) + ifnull(production_equipment_repair_fee,0) + ifnull(labor_protection_fee,0) +
                                         ifnull(safety_production_fee,0) + ifnull(environmental_protection_fee,0) + ifnull(organization_construction_fee,0) +
                                         ifnull(insurance_fee,0) + ifnull(quality_loss,0) + ifnull(other_expenses,0)
                                 ), 2) as total_all_cost
                       from cockpit.ods_cost_report ocr
                       group by vmocode)t2 on t1.production_batch_number=t2.production_batch_number
)t2 on t1.production_batch_number=t2.production_batch_number
GROUP BY `date`
ORDER BY `date` desc
LIMIT :page_size OFFSET :offset
;
-- 成本接口
select t1.production_batch_number,round(ifnull(t2.total_all_cost/t1.size,0),2) avg_cost from
             (select production_batch_number, count(*) size
               from ods_pm_job_order_restock opjor
               where flag_deleted=0
               group by production_batch_number) t1
left join (select
          vmocode production_batch_number,
          -- 总成本（包含所有成本项目）
          round(sum(
                    -- 材料成本
                        ifnull(paper,0) + ifnull(packaging_materials,0) + ifnull(direct_auxiliary_materials,0) +
                        ifnull(other_material_fee,0) + ifnull(public_materials,0) + ifnull(finished_product_collection,0) +
                        ifnull(semi_finished_outsourcing_fee,0) + ifnull(finished_outsourcing_fee,0) +
                        ifnull(knife_mold,0) + ifnull(printing_plate,0) + ifnull(machinery_material_consumption,0) +
                            -- 人工成本
                        ifnull(salary,0) + ifnull(production_management_salary,0) + ifnull(labor_cost_variance_adjustment,0) +
                            -- 社保成本
                        ifnull(basic_endowment_insurance,0) + ifnull(medical_insurance_fee,0) + ifnull(work_injury,0) +
                        ifnull(housing_provident_fund,0) + ifnull(maternity_insurance,0) + ifnull(supplementary_medical_insurance_fee,0) +
                        ifnull(unemployment_insurance_fee,0) +
                            -- 水电气费
                        ifnull(water_fee,0) + ifnull(electricity_fee,0) + ifnull(natural_gas_fee,0) + ifnull(steam_fee,0) +
                            -- 折旧摊销费
                        ifnull(depreciation_buildings,0) + ifnull(other_depreciation,0) + ifnull(low_value_consumables_amortization,0) +
                        ifnull(other_asset_amortization,0) +
                            -- 运输仓储费
                        ifnull(transportation_fee,0) + ifnull(storage_fee,0) +
                            -- 管理费用
                        ifnull(office_expenses,0) + ifnull(postal_telecommunication_fee,0) + ifnull(meeting_expenses,0) +
                        ifnull(travel_expenses,0) + ifnull(business_entertainment_expenses,0) + ifnull(rental_fee,0) +
                            -- 生产服务费
                        ifnull(production_service_fee,0) + ifnull(production_equipment_repair_fee,0) + ifnull(labor_protection_fee,0) +
                        ifnull(safety_production_fee,0) + ifnull(environmental_protection_fee,0) + ifnull(organization_construction_fee,0) +
                            -- 其他费用
                        ifnull(insurance_fee,0) + ifnull(quality_loss,0) + ifnull(other_expenses,0)
                ), 2) as total_all_cost -- 完整总成本
      from ods_cost_report ocr
      group by vmocode)t2 on t1.production_batch_number=t2.production_batch_number
;
-- 返工补做批次
SELECT
    DATE(t1.preparation_time) AS `date`,
    t1.production_batch_number,
    IF(t1.type = 1, '补做', '返工') AS type,
    t1.restock_no,
    opjd.process_name,
    ROUND(SUM(opor.actual_time) / 60, 2) AS total_time,
    temp.avg_cost cost
FROM cockpit.ods_pm_job_order_restock t1
         LEFT JOIN cockpit.ods_pm_job_order_management t2 ON t1.production_batch_number = t2.production_batch_number
         LEFT JOIN (
    SELECT task_list_code, MAX(process_name) AS process_name
    FROM cockpit.ods_pm_job_detail
    WHERE flag_deleted = 0
    GROUP BY task_list_code
) opjd ON opjd.task_list_code = t1.task_list_code
         LEFT JOIN (
    SELECT work_no, SUM(actual_time) AS actual_time
    FROM cockpit.ods_pm_oee_records
    WHERE flag_deleted = 0
    GROUP BY work_no
) opor ON opor.work_no = t1.task_list_code
left join (
    select t1.production_batch_number,round(ifnull(t2.total_all_cost/t1.size,0),2) avg_cost from
        (select production_batch_number, count(*) size
         from cockpit.ods_pm_job_order_restock opjor
         where flag_deleted=0
         group by production_batch_number) t1
            left join (select
                           vmocode production_batch_number,
                           round(sum(
                                         ifnull(paper,0) + ifnull(packaging_materials,0) + ifnull(direct_auxiliary_materials,0) +
                                         ifnull(other_material_fee,0) + ifnull(public_materials,0) + ifnull(finished_product_collection,0) +
                                         ifnull(semi_finished_outsourcing_fee,0) + ifnull(finished_outsourcing_fee,0) +
                                         ifnull(knife_mold,0) + ifnull(printing_plate,0) + ifnull(machinery_material_consumption,0) +
                                         ifnull(salary,0) + ifnull(production_management_salary,0) + ifnull(labor_cost_variance_adjustment,0) +
                                         ifnull(basic_endowment_insurance,0) + ifnull(medical_insurance_fee,0) + ifnull(work_injury,0) +
                                         ifnull(housing_provident_fund,0) + ifnull(maternity_insurance,0) + ifnull(supplementary_medical_insurance_fee,0) +
                                         ifnull(unemployment_insurance_fee,0) +
                                         ifnull(water_fee,0) + ifnull(electricity_fee,0) + ifnull(natural_gas_fee,0) + ifnull(steam_fee,0) +
                                         ifnull(depreciation_buildings,0) + ifnull(other_depreciation,0) + ifnull(low_value_consumables_amortization,0) +
                                         ifnull(other_asset_amortization,0) +
                                         ifnull(transportation_fee,0) + ifnull(storage_fee,0) +
                                         ifnull(office_expenses,0) + ifnull(postal_telecommunication_fee,0) + ifnull(meeting_expenses,0) +
                                         ifnull(travel_expenses,0) + ifnull(business_entertainment_expenses,0) + ifnull(rental_fee,0) +
                                         ifnull(production_service_fee,0) + ifnull(production_equipment_repair_fee,0) + ifnull(labor_protection_fee,0) +
                                         ifnull(safety_production_fee,0) + ifnull(environmental_protection_fee,0) + ifnull(organization_construction_fee,0) +
                                         ifnull(insurance_fee,0) + ifnull(quality_loss,0) + ifnull(other_expenses,0)
                                 ), 2) as total_all_cost
                       from cockpit.ods_cost_report ocr
                       group by vmocode)t2 on t1.production_batch_number=t2.production_batch_number
)temp on temp.production_batch_number=t1.production_batch_number
WHERE
    t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND ((:start_date IS NULL OR :start_date = '') OR (DATE(t1.preparation_time) >= :start_date))
  AND ((:end_date IS NULL OR :end_date = '') OR (DATE(t1.preparation_time) <= :end_date))
  AND ((:production_batch_number IS NULL OR :production_batch_number = '') OR (t1.production_batch_number = :production_batch_number))
  AND ((:product_code IS NULL OR :product_code = '') OR (t2.product_codes = :product_code))
  AND ((:product_name IS NULL OR :product_name = '') OR (t2.product_name = :product_name))
GROUP BY
    DATE(t1.preparation_time),
    t1.production_batch_number,
    t1.type,
    t1.restock_no,
    opjd.process_name
order by `date` desc
LIMIT :page_size OFFSET :offset;



