SELECT td.today_num AS duty_num,
       CASE
           WHEN twd.two_week_num = 0 THEN 0
           ELSE ROUND((yd.yesterday_num - lwd.last_week_num) / twd.two_week_num * 100, 2)
           END         week_rate,
       CASE
           WHEN yd.yesterday_num = 0 THEN 0
           ELSE ROUND((td.today_num - yd.yesterday_num) / yd.yesterday_num * 100, 2)
           END         day_rate
FROM (
         -- 当天上班人数
         SELECT count(DISTINCT ocs.work_code) AS today_num
         FROM ods_sys_user osu
                  LEFT JOIN ods_clock_sign ocs ON
             osu.username = ocs.work_code
         WHERE concat(ocs.sign_date, ' ', ocs.sign_time) between DATE_SUB(NOW(), INTERVAL 1 DAY) and now()) AS td,
     (
         -- 昨天上班人数
         SELECT count(DISTINCT ocs.work_code) AS yesterday_num
         FROM ods_sys_user osu
                  LEFT JOIN ods_clock_sign ocs ON
             osu.username = ocs.work_code
         WHERE concat(ocs.sign_date, ' ', ocs.sign_time) between DATE_SUB(NOW(), INTERVAL 2 DAY) and DATE_SUB(NOW(), INTERVAL 1 DAY)) AS yd,
     (
         -- 上周上班人数
         SELECT count(DISTINCT ocs.work_code) AS last_week_num
         FROM ods_sys_user osu
                  LEFT JOIN ods_clock_sign ocs ON
             osu.username = ocs.work_code
         WHERE ocs.sign_date between DATE_SUB(current_date(), INTERVAL 8 DAY) and DATE_SUB(current_date(), INTERVAL 1 DAY)) AS lwd,
     (
         -- 上上周上班人数
         SELECT count(DISTINCT ocs.work_code) AS two_week_num
         FROM ods_sys_user osu
                  LEFT JOIN ods_clock_sign ocs ON
             osu.username = ocs.work_code
         WHERE ocs.sign_date between DATE_SUB(current_date(), INTERVAL 15 DAY) and DATE_SUB(current_date(), INTERVAL 1 DAY)) AS twd;
