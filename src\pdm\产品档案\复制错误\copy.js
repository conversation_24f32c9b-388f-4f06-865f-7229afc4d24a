var outputContext={"pdm_product_version":{"standard_unit_cn":"只","procure_unit":"6591","main_class":"6876","crm_status":"0","create_by":"null","update_time":"2025-01-25 12:15:36","is_group_product":false,"mnemonic_code":"ZDDJ000341","id":150010,"update_by":"null","material_name":"150ml舒护修敏保湿喷雾花盒","flag_deleted":0,"bom_type":"1","product_version":"1.1","procure_unit_cn":"只","create_time":"2025-01-25 12:15:36","sub_class_cn":"化妆品类","version":"1.0","default_factory":"27435","sub_class":"6816","main_class_cn":"折叠纸盒类","default_factory_cn":"凌峰","standard_unit":"6591","status":1,"material_code":"ZDHZ0000087"},"pdm_product_client":[{"product_version":"4.4","create_time":"2025-06-06 16:30:53","mnecode":"C000028","product_code":"ZDHY0000224","unit_cn":"只","object_id":"023-01","version":"1.0","is_used":1,"create_by":"1770657851665817602","unit":6591,"update_time":"2025-06-06 16:30:53","id":116474,"customer_general_requirements":"COA、箱唛、运输联系单使用西安环球模板、盖西安环球章；标样封面使用统一模板，盖西安环球章；运输联系单上备注：尾箱附模切未粘盒号位样*****只","update_by":"1770657851665817602","grade_name":"单克利培酮口服溶液小盒","client_name":"常州四药制药有限公司","client_code":"C000024","flag_deleted":0}],"pdm_product_ban_assunit":[{"product_version":"4.4","create_time":"2025-06-06 16:30:53","color_sequence":"/","serviceability_ratio":"700000","remark":"","edition_name":"单克利培酮口服溶液小盒版","product_code":"ZDHY0000224","version":"1.0","edition_categroy":830,"create_by":"1770657851665817602","edition_cleanliness":"735*615.75","update_time":"2025-06-06 16:30:53","edition_code":"51201411A-G（天津） ZDYY001505-GV1(西安）","perimeter":"0","out_of_book":"12","edition_type":553,"share":0,"id":255418,"update_by":"1770657851665817602","edition_type_cn":"印版","edition_categroy_cn":"光油版","flag_deleted":0,"chromatic_degree":"","material_code":"ZDHY0000224"},{"product_version":"4.4","create_time":"2025-06-06 16:30:53","color_sequence":"/","serviceability_ratio":"700000","remark":"","edition_name":"单克利培酮口服溶液小盒版","product_code":"ZDHY0000224","version":"1.0","edition_categroy":832,"create_by":"1770657851665817602","edition_cleanliness":"735*615.75","update_time":"2025-06-06 16:30:53","edition_code":"51201411A-M（天津） 1509（西安）","perimeter":"0","out_of_book":"12","edition_type":554,"share":0,"id":255419,"update_by":"1770657851665817602","edition_type_cn":"模切版","edition_categroy_cn":"模切版","flag_deleted":0,"chromatic_degree":"","material_code":"ZDHY0000224"},{"product_version":"4.4","create_time":"2025-06-06 16:30:53","color_sequence":"K,C000028专红185C1,C000028深蓝286C1,C000028浅蓝2905C1/","serviceability_ratio":"700000","remark":"","edition_name":"单克利培酮口溶液小盒版","product_code":"ZDHY0000224","version":"1.0","edition_categroy":829,"create_by":"1770657851665817602","edition_cleanliness":"735*615.75","update_time":"2025-06-06 16:30:53","edition_code":"XGP-ZDYY001505-BV4","perimeter":"0","out_of_book":"12","edition_type":553,"share":0,"id":255420,"update_by":"1770657851665817602","edition_type_cn":"印版","edition_categroy_cn":"CTP版","flag_deleted":0,"chromatic_degree":"4/0","material_code":"ZDHY0000224"}],"pdm_product_bom":[{"consume_unit":6588,"indexs":"4","product_code":"ZDHY0000224","dosage_unit_unit_cn":"kg/万","color_order_quotation":"","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:53","parent_code":"ZDHY0000224","mnemonic_code":"010200046","id":1215203,"component_count":"0","update_by":"1770657851665817602","material_name":"公司自调UV专色油墨(1kg*12罐/箱)","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","dosage_unit":"0.113144","categroy":"2","consume_unit_cn":"kg","color_sequence":"(/)","version":"1.0","cut_remark":"","component_size":"1kg*12罐/箱","consume_round_up":"0","cut_size":"","pushing":"","direct_material":0,"spread_size":"","consumption_rate":"0","part_information":"","slitting":"0","chromatic_degree":"","material_code":"1010201030000035"},{"consume_unit":6588,"indexs":"5","product_code":"ZDHY0000224","dosage_unit_unit_cn":"kg/万","color_order_quotation":"","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:53","parent_code":"ZDHY0000224","mnemonic_code":"010200046","id":1215204,"component_count":"0","update_by":"1770657851665817602","material_name":"公司自调UV专色油墨(1kg*12罐/箱)","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","dosage_unit":"0.113144","categroy":"2","consume_unit_cn":"kg","color_sequence":"(/)","version":"1.0","cut_remark":"","component_size":"1kg*12罐/箱","consume_round_up":"0","cut_size":"","pushing":"","direct_material":0,"spread_size":"","consumption_rate":"0","part_information":"","slitting":"0","chromatic_degree":"","material_code":"1010201030000035"},{"consume_unit":6588,"indexs":"6","product_code":"ZDHY0000224","dosage_unit_unit_cn":"kg/万","color_order_quotation":"","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:53","parent_code":"ZDHY0000224","mnemonic_code":"010200046","id":1215205,"component_count":"0","update_by":"1770657851665817602","material_name":"公司自调UV专色油墨(1kg*12罐/箱)","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","dosage_unit":"0.113144","categroy":"2","consume_unit_cn":"kg","color_sequence":"(/)","version":"1.0","cut_remark":"","component_size":"1kg*12罐/箱","consume_round_up":"0","cut_size":"","pushing":"","direct_material":0,"spread_size":"","consumption_rate":"0","part_information":"","slitting":"0","chromatic_degree":"","material_code":"1010201030000035"},{"consume_unit":6588,"indexs":"7","product_code":"ZDHY0000224","dosage_unit_unit_cn":"kg/万","color_order_quotation":"","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:53","parent_code":"ZDHY0000224","mnemonic_code":"010500101","id":1215206,"component_count":"0","update_by":"1770657851665817602","material_name":"TPU 029 UV辊涂光油(20kg/桶 北京金印联国际供应链管理有限公司)","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","dosage_unit":"1.508587","categroy":"2","consume_unit_cn":"kg","color_sequence":"(/)","version":"1.0","cut_remark":"","component_size":"20kg/桶","consume_round_up":"0","cut_size":"","pushing":"","direct_material":0,"spread_size":"","consumption_rate":"0","part_information":"","slitting":"0","chromatic_degree":"","material_code":"1010202030000005"},{"consume_unit":6588,"indexs":"8","product_code":"ZDHY0000224","dosage_unit_unit_cn":"kg/万","color_order_quotation":"","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:53","parent_code":"ZDHY0000224","mnemonic_code":"05010010","id":1215207,"component_count":"0","update_by":"1770657851665817602","material_name":"冠力糊盒胶MA-768(20kg/桶 东莞冠力胶业有限公司)","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","dosage_unit":"0.472","categroy":"2","consume_unit_cn":"kg","color_sequence":"(/)","version":"1.0","cut_remark":"","component_size":"20kg/桶","consume_round_up":"0","cut_size":"","pushing":"","direct_material":0,"spread_size":"","consumption_rate":"0","part_information":"","slitting":"0","chromatic_degree":"","material_code":"1010206010000008"},{"packing_unit":6599,"standard_unit_cn":"mm","product_count":"1500","box_mark_content":"","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","pack_other_texture":"不干胶","parent_code":"","mnemonic_code":"ZDYY001505_V4_Pk1","id":1215208,"pack_other_sizes":"100*150","component_count":"1","pack_paste_method":"单张不干胶箱唛贴于纸箱一侧","update_by":"1770657851665817602","material_name":"纸箱","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","packing_unit_cn":"箱","create_time":"2025-06-06 16:30:53","dosage_unit":"0","categroy":"4","weight":15.8689,"packing_parameter":"包装要求：;包装方法：1.装箱数量：250只/排×2排/层×3层/箱=1500只/箱。垫板尺寸：670*210mm2.箱内套塑料袋。3.箱外打两道打包带。4.每批产品折翼上的生产日期应和生产作业单以及箱唛上的生产日期一致（箱唛样式见附图）。大版最后一个位号后加'#'，每批生产折翼处日期均要改变，具体信息以'生产作业单'为准。5入库时备注模切未粘盒号位样*****只。6.在尾箱附'成品检验报告'及模切未粘盒的号位样一套装入样品袋，并注明'留样'字样（如要分批发货，每批都要留号位样一套并注明'留样'，如果整批出现断号位的情况，要求在'成品检验报告'中注明）。7.新改版首次生产时箱唛上备注“新版”。8.COA报告纸张按“300g白卡”填写。","version":"1.0","component_size":"480*380*310","pack_background_color":"湖蓝","consumption_rate":"0","standard_unit":9077,"material_code":"ZDYY001505_V4_Pk1"},{"packing_unit":6591,"standard_unit_cn":"mm","product_count":"0","indexs":"1023965","box_mark_content":"","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","pack_other_texture":"","parent_code":"ZDYY001505_V4_Pk1","mnemonic_code":"08010011","id":1215209,"pack_other_sizes":"","component_count":"1","pack_paste_method":"","update_by":"1770657851665817602","material_name":"塑料袋","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","packing_unit_cn":"只","create_time":"2025-06-06 16:30:53","dosage_unit":"0.0006666667","categroy":"6","weight":0,"packing_parameter":"","version":"1.0","component_size":"1000*850mm*2u","pack_background_color":"","consumption_rate":"0","standard_unit":9077,"material_code":"10103040000001"},{"packing_unit":6575,"standard_unit_cn":"mm","product_count":"0","indexs":"1023966","box_mark_content":"","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","pack_other_texture":"","parent_code":"ZDYY001505_V4_Pk1","mnemonic_code":"180300122","id":1215210,"pack_other_sizes":"","component_count":"1","pack_paste_method":"","update_by":"1770657851665817602","material_name":"隔挡3830","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","packing_unit_cn":"张","create_time":"2025-06-06 16:30:53","dosage_unit":"0.0006666667","categroy":"6","weight":0.05586,"packing_parameter":"","version":"1.0","component_size":"","pack_background_color":"","consumption_rate":"0","standard_unit":9077,"material_code":"1010302020000272"},{"packing_unit":6577,"standard_unit_cn":"mm","product_count":"0","indexs":"1023967","box_mark_content":"","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","pack_other_texture":"","parent_code":"ZDYY001505_V4_Pk1","mnemonic_code":"180100026","id":1215211,"pack_other_sizes":"","component_count":"1","pack_paste_method":"","update_by":"1770657851665817602","material_name":"纸箱483831","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","packing_unit_cn":"套","create_time":"2025-06-06 16:30:53","dosage_unit":"0.0006666667","categroy":"6","weight":1.05,"packing_parameter":"","version":"1.0","component_size":"粘箱","pack_background_color":"","consumption_rate":"0","standard_unit":9077,"material_code":"1010301010000114"},{"consume_unit":0,"indexs":"0","product_code":"ZDHY0000224","dosage_unit_unit_cn":"","color_order_quotation":"K(1),C000028专红185C1(1),C000028深蓝286C1(1),C000028浅蓝2905C1(1)","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:52","parent_code":"0","mnemonic_code":"ZDYY001505","id":1215199,"component_count":"1","update_by":"1770657851665817602","material_name":"单克利培酮口服溶液小盒","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:52","dosage_unit":"0","categroy":"3","color_sequence":"(K,C000028专红185C1,C000028深蓝286C1,C000028浅蓝2905C1/)","version":"1.0","cut_remark":"","component_size":"57.0*40.0*118.0","consume_round_up":"0","cut_size":"","pushing":"","spread_size":"","consumption_rate":"0","part_information":"","slitting":"0","chromatic_degree":"4/0","material_code":"ZDHY0000224"},{"consume_unit":6579,"indexs":"1","product_code":"ZDHY0000224","dosage_unit_unit_cn":"","color_order_quotation":"","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:52","parent_code":"ZDHY0000224","mnemonic_code":"J07700200180006","id":1215200,"component_count":"0","update_by":"1770657851665817602","material_name":"787 300g 山东华夏骄阳白卡","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:52","dosage_unit":"1","categroy":"1","consume_unit_cn":"米","color_sequence":"(/)","version":"1.0","cut_remark":"","component_size":"787","consume_round_up":"0","cut_size":"787*645","pushing":"787*645(1)","direct_material":0,"spread_size":"","consumption_rate":"0","part_information":"","slitting":"1","chromatic_degree":"","material_code":"1010101010000085"},{"consume_unit":6570,"indexs":"2","product_code":"ZDHY0000224","dosage_unit_unit_cn":"瓶/万","color_order_quotation":"","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:52","parent_code":"ZDHY0000224","mnemonic_code":"010300050","id":1215201,"component_count":"0","update_by":"1770657851665817602","material_name":"S系列黑色LED UV墨水 BLACK(1升/瓶 北京)","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:52","dosage_unit":"0.020000","categroy":"2","consume_unit_cn":"瓶","color_sequence":"(/)","version":"1.0","cut_remark":"","component_size":"1升/瓶","consume_round_up":"0","cut_size":"","pushing":"","direct_material":0,"spread_size":"","consumption_rate":"0","part_information":"","slitting":"0","chromatic_degree":"","material_code":"1010201060000002"},{"consume_unit":6588,"indexs":"3","product_code":"ZDHY0000224","dosage_unit_unit_cn":"kg/万","color_order_quotation":"","create_by":"1770657851665817602","is_off_side":"0","update_time":"2025-06-06 16:30:52","parent_code":"ZDHY0000224","mnemonic_code":"01020017","id":1215202,"component_count":"0","update_by":"1770657851665817602","material_name":"UV油墨abilio-21 Process Black 黑(1kg*12罐/箱 深圳深日油墨有限公司)","flag_deleted":0,"fixed_amount":"0","product_version":"4.4","create_time":"2025-06-06 16:30:52","dosage_unit":"0.113144","categroy":"2","consume_unit_cn":"kg","color_sequence":"(/)","version":"1.0","cut_remark":"","component_size":"1kg*12罐/箱","consume_round_up":"0","cut_size":"","pushing":"","direct_material":0,"spread_size":"","consumption_rate":"0","part_information":"","slitting":"0","chromatic_degree":"","material_code":"1010201020000005"}],"pdm_product_craft":[{"craft_name":"原料分切","indexs":"1","technological_parameter":"[{\"三期赋码方式\":\"激光灼烧\"},{}]","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","rate":"0.000","parent_code":"ZDHY0000224","ban_json_code":"版编号：XGP-ZDYY001505-BV4类型：印版类别：CTP版版名称：单克利培酮口溶液小盒版出本：12","ys_fixed_time":"0","id":697724,"update_by":"1770657851665817602","material_name":"编码：1010101010000085 名称：787 300g 山东华夏骄阳白卡 index：1","source_name":"GP分切中心","flag_deleted":0,"ys_standing_time":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","craft_code":"D0035","index":"1","material_json_code":"编码：1010101010000085 名称：787 300g 山东华夏骄阳白卡 index：8004321","version":"1.0","ys_fixed_rate":"0.000","out_of_book":"12","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPFQZX"},{"craft_name":"喷码","indexs":"2","technological_parameter":"[{\"喷印次序\":\"先喷后印\"},{\"走纸方向\":\"横向\"},{\"喷码位置\":\"正版\"},{\"喷码类别\":\"一维码\"},{\"喷码分类\":\"药品追溯码\"},{\"喷印分辨率\":\"510*408/1200*1200\"},{\"咬口尺寸\":\"12\"},{\"喷头个数\":\"4个\"},{\"印版喷印文字\":\"印刷咬口、产品编码及生产批号\"},{\"一维码喷码参数\":\"详见附图\"},{\"喷码等级\":\"B级及以上\"},{\"喷码效果\":\"跟标准色样XGP-QEO-QD/EXT/SC-2664，2023-7-12制作\"},{\"走向长度（mm）\":\"645.00\"}]","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","rate":"0","parent_code":"ZDHY0000224","ban_json_code":"版编号：XGP-ZDYY001505-BV4类型：印版类别：CTP版版名称：单克利培酮口溶液小盒版出本：12","ys_fixed_time":"0","id":697725,"update_by":"1770657851665817602","material_name":"编码：1010201060000002 名称：S系列黑色LED UV墨水 BLACK(1升/瓶 北京) index：2","source_name":"GP可变数据印刷中心","flag_deleted":0,"ys_standing_time":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","craft_code":"D0121","count":"1","index":"2","material_json_code":"编码：1010201060000002 名称：S系列黑色LED UV墨水 BLACK(1升/瓶 北京)","version":"1.0","ys_fixed_rate":"0","out_of_book":"12","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPYSKBSJYSZX"},{"craft_name":"印刷","indexs":"3","technological_parameter":"[{\"印刷类型\":\"UV印刷\"},{\"标准化印刷\":\"否\"},{\"印刷面\":\"正版\"},{\"咬口尺寸\":\"12\"},{\"印刷规矩\":\"靠身\"},{\"UV印刷色序及灯管位置\":\"K-专红-深蓝-浅蓝\"},{\"跟色标准\":\"同标准色样（编号：XGP-QEO-QMD/EXT/SC-2664，2023.07.12制作）\"},{\"正版联机上光组油\":\"局部UV油\"},{\"油辊类型\":\"80线网纹辊\"},{\"油版材质\":\"橡皮布\"},{\"过油效果\":\"跟标准色样XGP-QEO-QD/EXT/SC-2664，2023-7-12制作\"},{\"喷印条码等级\":\"B级及以上\"},{\"印刷条码等级\":\"B级及以上\"},{\"耐摩擦次数要求\":\"UV印刷800次\"},{\"三期赋码方式\":\"激光灼烧\"},{\"备注\":\"1.最后一个位号后加#，盒子折翼处生产日期每批变更，具体信息以生产作业单为准\"}]","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","rate":"0","parent_code":"ZDHY0000224","ban_json_code":"版编号：XGP-ZDYY001505-BV4类型：印版类别：CTP版版名称：单克利培酮口溶液小盒版出本：12","ys_fixed_time":"0","id":697726,"update_by":"1770657851665817602","material_name":"编码：1010202030000005 名称：TPU 029 UV辊涂光油(20kg/桶 北京金印联国际供应链管理有限公司) index：7,编码：1010201020000005 名称：UV油墨abilio-21 Process Black 黑(1kg*12罐/箱 深圳深日油墨有限公司) index：3,编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：4,编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：4,编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱) index：4","source_name":"GP七色UV印刷中心","flag_deleted":0,"ys_standing_time":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","craft_code":"D0102","count":"1","index":"3","material_json_code":"编码：1010202030000005 名称：TPU 029 UV辊涂光油(20kg/桶 北京金印联国际供应链管理有限公司),编码：1010201020000005 名称：UV油墨abilio-21 Process Black 黑(1kg*12罐/箱 深圳深日油墨有限公司),编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱),编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱),编码：1010201030000035 名称：公司自调UV专色油墨(1kg*12罐/箱)","version":"1.0","ys_fixed_rate":"300","out_of_book":"12","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPKZY7SZX"},{"craft_name":"模切","indexs":"4","technological_parameter":"[{\"底模类型\":\"激光底膜\"},{\"模切类型\":\"单次模切\"},{\"备注\":\"1.注意模切卡位准确。\"},{}]","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","rate":"0.000","parent_code":"ZDHY0000224","ban_json_code":"版编号：51201411A-M（天津） 1509（西安）类型：模切版类别：模切版版名称：单克利培酮口服溶液小盒版出本：12","ys_fixed_time":"0","id":697727,"update_by":"1770657851665817602","material_name":"","source_name":"GP模切中心","flag_deleted":0,"ys_standing_time":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","craft_code":"X0023","index":"4","material_json_code":"","version":"1.0","ys_fixed_rate":"0.000","out_of_book":"12","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPZHMQZX"},{"craft_name":"打纸","indexs":"5","technological_parameter":"[{\"打纸标准\":\"公司内控标准\"},{}]","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","rate":"0.000","parent_code":"ZDHY0000224","ban_json_code":"","ys_fixed_time":"0","id":697728,"update_by":"1770657851665817602","material_name":"","source_name":"GP清废中心","flag_deleted":0,"ys_standing_time":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","craft_code":"X0108","index":"5","material_json_code":"","version":"1.0","ys_fixed_rate":"0.000","out_of_book":"1","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPZHQFZX"},{"craft_name":"粘盒","indexs":"6","technological_parameter":"[{\"盒型\":\"正反扣\"},{\"预折要求\":\"一线160°~170°、三线120°~130°，2、4线成型\"},{\"上胶方式\":\"轮胶\"},{\"客户包装方式\":\"上自动包装机\"},{\"单箱数量偏差\":\"±3‰\"},{\"走向长度（mm）\":\"219.00\"},{\"开盒力\":\"≤5N\"},{\"备注\":\"1.请粘盒工序在入库单上注明模切未粘盒的位号数量。2.粘盒时注意控制，不能出现内粘（顾客曾反馈）！3.注意卡位准确\"}]","product_code":"ZDHY0000224","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","rate":"0","parent_code":"ZDHY0000224","ban_json_code":"","ys_fixed_time":"0","id":697729,"update_by":"1770657851665817602","material_name":"编码：1010206010000008 名称：冠力糊盒胶MA-768(20kg/桶 东莞冠力胶业有限公司) index：8,编码：10103040000001 名称：塑料袋 index：1023965,编码：1010301010000114 名称：纸箱483831 index：1023967,编码：1010302020000272 名称：隔挡3830 index：1023966","source_name":"GP品检粘盒中心","flag_deleted":0,"ys_standing_time":"0","product_version":"4.4","create_time":"2025-06-06 16:30:53","craft_code":"D0036","count":"1","index":"6","material_json_code":"编码：1010206010000008 名称：冠力糊盒胶MA-768(20kg/桶 东莞冠力胶业有限公司),编码：10103040000001 名称：塑料袋,编码：1010301010000114 名称：纸箱483831,编码：1010302020000272 名称：隔挡3830","version":"1.0","ys_fixed_rate":"481","out_of_book":"1","fixed_consumption":"0","ys_manual_speed":"0","source_code":"GPZHPJNHZX"}],"pdm_upload_file":[{"file_url":"uploadFile/ZDYY001505(4)/单克利培酮口服溶液小盒ZDYY001505-4产品附图.pdf","product_version":"4.4","create_time":"2025-06-06 16:30:53","file_name":"单克利培酮口服溶液小盒ZDYY001505-4产品附图.pdf","type":5,"version":"1.0","create_by":"1770657851665817602","update_time":"2025-06-06 16:30:53","file_id":"8dc5fa02ac6e42c9a1d60d034be5f88b","id":43102,"update_by":"1770657851665817602","material_code":"ZDHY0000224","flag_deleted":0}]}
var productBom=outputContext.pdm_product_bom
var newProductBom={"spread_size":"1*1","product_version":"1.1","categroy":"3","parent_code":"","id":1060367,"component_count":"1","product_code":"ZDHZ0000087","standard_unit":6591,"chromatic_degree":"1/1","flag_deleted":0,"material_code":"ZDHZ0000087"}
var oldProductVersion={"product_approval":"","is_support_rohs":0,"standard_unit_cn":"只","product_weight":0.009842,"procure_unit":27539,"remark":"1、该客户为VIP客户，生产时按照公司内控标准生产。","main_class":6876,"fsc_statement":"","create_by":"1770657851665817602","update_time":"2025-06-06 16:31:00","is_group_product":0,"mnemonic_code":"ZDYY001505","id":169432,"update_by":"1770657851665817602","is_support_fsc":0,"material_name":"单克利培酮口服溶液小盒","flag_deleted":0,"bom_type":2,"product_version":"4.4","procure_unit_cn":"mm","create_time":"2025-06-06 16:30:50","product_size":"57.0*40.0*118.0","sub_class_cn":"化药类","version":"1.0","spread_size":"*","default_factory":27435,"sub_class":6662,"main_class_cn":"折叠纸盒类","default_factory_cn":"凌峰","standard_unit":6591,"status":1,"material_code":"ZDHY0000224"}
var bzCodeMap={"ZDYY001505_V4_Pk1":"ZDHZ0000087Pk_009"}
var banCodeMap={"51201411A-G（天津） ZDYY001505-GV1(西安）":"ZDHZ0000087_1_0_017","XGP-ZDYY001505-BV4":"ZDHZ0000087_1_0_018","51201411A-M（天津） 1509（西安）":"ZDHZ0000087_1_2_013"}
var productBan=outputContext.pdm_product_ban_assunit
var productCraft=outputContext.pdm_product_craft
var productClient=outputContext.pdm_product_client
var productFile=outputContext.pdm_upload_file
var productVersion=outputContext.pdm_product_version



for (var i =0; i < productBom.length;i++) {
    if (productBom[i].categroy == 3 ) {
      productBom[i] = newProductBom
    }
  }
  
  
  
  
  var oldMaterialCode = oldProductVersion.material_code
  var oldMaterialName = oldProductVersion.material_name
  
  //替换productArr -- 产品和部件
  var nameReg = new RegExp(oldMaterialName, 'g'); 
  function replaceCode(newCode, oldCode, data,nameReg,newMaterialName) {
    for (var i = 0; i < data.length; i++) { // 使用i作为循环计数器，避免与newCode混淆  
  if (data[i].categroy == 3 || data[i].categroy == 0) {
      data[i]['material_name'] = data[i].material_name.replace(nameReg, newMaterialName)
  }
       if (data[i].parent_code) {  
         if (data[i].categroy == 5 || data[i].categroy == 6 ||  data[i].categroy == 8) {
       data[i].parent_code = bzCodeMap[data[i].parent_code]
      } else {
        data[i].parent_code = data[i].parent_code.replace(oldCode, newCode); // 使用newCode替换oldCode  
      }
        
      }  
      
      
      if (data[i].material_code) { 
         if (data[i].categroy == 4 || data[i].categroy == 5 ||  data[i].categroy == 8) {
           data[i].material_code = bzCodeMap[data[i].material_code]
         } else {
            data[i].material_code = data[i].material_code.replace(oldCode, newCode); // 使用newCode替换oldCode  
         }
      }  
      // if (data[i].indexs) {  
      //   data[i].indexs = data[i].indexs.replace(oldCode, newCode); // 使用newCode替换oldCode  
      // }  
      if (data[i].product_code) {  
        data[i].product_code = data[i].product_code.replace(oldCode, newCode); // 使用newCode替换oldCode  
      }  
      if (data[i].children) {  
        replaceCode(newCode, oldCode, data[i].children,nameReg,newMaterialName); // 递归调用replaceCode  
      }  
    }
  }
  replaceCode(productVersion.material_code,oldMaterialCode, productBom, nameReg, productVersion.material_name);
  
  var regex = new RegExp(oldMaterialCode, 'g'); 
  function replaceBan(newCode,regex,data) {
    for (var i = 0; i < data.length; i++) { // 使用i作为循环计数器，避免与newCode混淆  
     if (data[i].product_code) {  
         data[i].product_code = data[i].product_code.replace(regex,newCode); // 使用newCode替换oldCode  
       }  
       if (data[i].material_code) {  
         data[i].material_code = data[i].material_code.replace(regex,newCode); // 使用newCode替换oldCode  
       }  
       // if (data[i].edition_code) {  
       //   data[i].edition_code = data[i].edition_code.replace(regex,newCode); // 使用newCode替换oldCode  
       // }
      if (data[i].edition_code && data[i].share != 1) {  
         data[i].edition_code = banCodeMap[data[i].edition_code]; // 使用newCode替换oldCode  
       }
    }
  }
  replaceBan(productVersion.material_code,regex,productBan)
  
  //处理工艺路线
  function replaceCraft(newCode,regex,data) {
    for (var x = 0; x < data.length; x++) {
      // if (data[x].indexs) {
      //  data[x].indexs = data[x].indexs.replace(regex, newCode); 
      // }
      if (data[x].parent_code) {
       data[x].parent_code = data[x].parent_code.replace(regex, newCode); 
      } 
      if (data[x].product_code) {
       data[x].product_code = data[x].product_code.replace(regex,newCode); 
      } 
      if (data[x].material_name) {
       data[x].material_name = data[x].material_name.replace(regex,newCode)
      } 
       if (data[x].ban_json_code) {
         for (key in banCodeMap) {
           var banReg = new RegExp(key, 'g'); 
           data[x].ban_json_code = data[x].ban_json_code.replace(banReg,banCodeMap[key])
         }
      } 
    }
  }
  
  replaceCraft(productVersion.material_code,regex,productCraft)
  //处理客户料号
  function replaceClient(newCode,regex,data) {
    for (var x = 0; x < data.length; x++) {
    if (data[x].product_code) {
      data[x].product_code = data[x].product_code.replace(regex, newCode); 
    } 
    if (data[x].object_id) {
      data[x].object_id = data[x].object_id.replace(regex, newCode); 
    } 
   }
  }
  replaceClient(productVersion.material_code,regex,productClient)
  //处理文件
  function replaceFile(newCode,regex,data) {
    for (var x = 0; x < data.length; x++) {
    if (data[x].material_code) {
      data[x].material_code = data[x].material_code.replace(regex, newCode); 
    } 
   }
  }
  replaceFile(productVersion.material_code,regex,productFile)
  
  