-- 暂停动作统计列表
select machine_name,
       action,
       sum(actual_time)                                                            as loss_time,
       round(100 * sum(actual_time) / (select sum(actual_time)
                                       from cockpit.ods_pm_oee_records
                                       where flag_deleted = 0
                                         and action_properties = 1
                                         AND ((:start_date is null or :start_date = '') or (date(end_time) >= :start_date))
                                         AND ((:end_date is null or :end_date = '') or (date(end_time) <= :end_date))
                                         AND ((:machine_name is null or :machine_name = '') or
                                              (machine_name = :machine_name))), 2) as percentage
from cockpit.ods_pm_oee_records
where flag_deleted = 0
  and action_properties = 1
  AND ((:start_date is null or :start_date = '') or (date(end_time) >= :start_date))
  AND ((:end_date is null or :end_date = '') or (date(end_time) <= :end_date))
  AND ((:machine_name is null or :machine_name = '') or (machine_name = :machine_name))
group by machine_name, action
order by loss_time desc
limit :page_size offset :offset;


select actual_time  from ods_pm_oee_records where flag_deleted = 0
  and action_properties = 1 limit 10;

-- 产能利用率折线图
SELECT
    day_time,
    IFNULL(ROUND((100 * use_time / total_time),2), 0) AS utilization
FROM (
         SELECT
             DATE(end_time) AS day_time,
             SUM(CASE WHEN action_properties != 1 THEN actual_time ELSE 0 END) AS use_time,
             SUM(actual_time) AS total_time
         FROM
             cockpit.ods_pm_oee_records
         WHERE
             flag_deleted = 0
           AND ((:start_date IS NULL OR :start_date = '') OR (date(end_time) >= :start_date))
           AND ((:end_date IS NULL OR :end_date = '') OR (date(end_time) <= :end_date))
           AND ((:machine_name IS NULL OR :machine_name = '') OR (machine_name = :machine_name))
         GROUP BY
             DATE(end_time)
     ) AS temp
WHERE
    day_time IS NOT NULL
GROUP BY
    day_time
ORDER BY
    day_time ASC;

-- 损失因素占比饼图
SELECT
    action label,
    SUM(actual_time) AS value
FROM
    cockpit.ods_pm_oee_records
WHERE
    flag_deleted = 0
  AND action_properties=1
  AND ((:start_date IS NULL OR :start_date = '') OR (date(end_time) >= :start_date))
  AND ((:end_date IS NULL OR :end_date = '') OR (date(end_time) <= :end_date))
  AND ((:machine_name IS NULL OR :machine_name = '') OR (machine_name = :machine_name))
group by action
order by value desc
;
-- 损失因素 原因详情
select t1.production_batch_no     production_batch_number,
       (select product_code
        from cockpit.ods_pm_job_detail t3
        where t3.flag_deleted = 0
          and t3.id = t1.job_bar) product_code,
       t1.product_name            product_name,
       (select process_name
        from cockpit.ods_pm_job_detail t4
        where t4.flag_deleted = 0
          and t4.id = t1.job_bar) process_name,
       t1.machine_name            machine_name,
       (select team_leader
        from cockpit.ods_pm_department t2
        where t2.flag_deleted = 0
          and t2.department_id = t1.team_or_group_id
        order by t2.id desc
        limit 1)                  team_leader,
       t1.action                  action_name,
       t1.pause_reason            remark
from cockpit.ods_pm_oee_records t1
WHERE t1.flag_deleted = 0
  AND action_properties = 1
  AND ((:start_date IS NULL OR :start_date = '') OR (date(end_time) >= :start_date))
  AND ((:end_date IS NULL OR :end_date = '') OR (date(end_time) <= :end_date))
  AND ((:machine_name IS NULL OR :machine_name = '') OR (machine_name = :machine_name))
  AND ((:action_name IS NULL OR :action_name = '') OR (action = :action_name))
order by end_time asc
limit :page_size offset :offset;

