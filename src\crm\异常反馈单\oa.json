{"in0": {"forwardButtonName": "", "workflowBaseInfo": {"workflowName": "异常反馈单", "workflowTypeId": "", "workflowId": "27524", "workflowTypeName": ""}, "workflowDetailTableInfos": {"WorkflowDetailTableInfo": {"tableFieldName": {"string": 0}, "tableDBName": "", "tableTitle": "", "workflowRequestTableRecords": {"WorkflowRequestTableRecord": [{"workflowRequestTableFields": {"WorkflowRequestTableField": [{"fieldName": "ycfkdh", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "YC250415000009", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "sxycyy", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "受限客户策略触发", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "clsj", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "clyj", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "fkdzt", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "1", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}]}, "recordOrder": "1"}, {"workflowRequestTableFields": {"WorkflowRequestTableField": [{"fieldName": "ycfkdh", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "YC250415000009", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "sxycyy", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "受限客户策略触发", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "clsj", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "clyj", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "fkdzt", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "1", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}]}, "recordOrder": "1"}]}}}, "canEdit": "true", "isnextflow": "0", "mustInputRemark": "false", "creatorName": "", "remark": "异常反馈单", "workflowMainTableInfo": {"requestRecords": {"WorkflowRequestTableRecord": {"workflowRequestTableFields": {"WorkflowRequestTableField": [{"fieldName": "sqr", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "sqrbm", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": 41, "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "sqsj", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "2025-04-15 16:37:56", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "sqbh", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "YC250415000009", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "ycfkdh", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "YC250415000009", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "khbm", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "C000001", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "khmc", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "西安杨森制药有限公司", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "khlx", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "2", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "khzt", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "2", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "xsfzr", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "李巧梅", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "sxycyy", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "受限客户策略触发", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "hkzq", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "60", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "hkcqts", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "0", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "kcje", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "0", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "kccqts", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "0", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "sqcs", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "khjdts", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "30", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}, {"fieldName": "clfa", "edit": true, "browserurl": "", "fieldShowName": "", "fieldOrder": 0, "fieldShowValue": "", "fieldValue": "123", "fieldDBType": "varchar2(400)", "filedHtmlShow": 1, "view": true, "mand": true, "fieldHtmlType": "", "fieldType": "varchar2(400)", "fieldFormName": ""}]}, "recordOrder": 0}}, "tableDBName": ""}, "lastOperateTime": "", "requestLevel": "0", "canView": "true", "requestName": "异常反馈单", "messageType": "", "createTime": "", "requestId": "", "currentNodeId": "", "needAffirmance": "false", "currentNodeName": "", "secLevel": "1", "lastOperatorName": ""}}