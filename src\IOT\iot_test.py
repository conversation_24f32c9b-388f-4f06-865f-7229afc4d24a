import requests
import json

url = "http://*************:80/api/device2mom/getCuAllPropertiesByDeviceIds"

payload = json.dumps({
   "deviceNumbers": [
      "QDCJ1"
   ]
})
headers = {
   'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
   'Content-Type': 'application/json',
   'Accept': '*/*',
   'Host': '*************:80',
   'Connection': 'keep-alive'
}

response = requests.request("POST", url, headers=headers, data=payload)

my_data = json.loads(response.text)["data"]["QDCJ1"]

total_value=0
for key in my_data.keys():
    total_value+=my_data[key]["value"]
print(f'总用电量{total_value}')

# 今日零点的数据
today_value=670118
value=total_value-today_value
print(f'今日用电量{value}')


