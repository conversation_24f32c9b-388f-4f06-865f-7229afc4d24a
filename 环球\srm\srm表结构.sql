-- MySQL dump 10.13  Distrib 8.4.4, for Win64 (x86_64)
--
-- Host: **************    Database: h3chq_srmbusiness1704287109232
-- ------------------------------------------------------
-- Server version	8.0.29

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `srm_assessment_template`
--

DROP TABLE IF EXISTS `srm_assessment_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_assessment_template` (
  `remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `is_use` int DEFAULT NULL COMMENT '是否应用(0否，1是)',
  `template_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模板名称',
  `material_category_cn` text CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '物料大小类名称',
  `material_category` int DEFAULT NULL COMMENT '物料大小类',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `template_name` (`template_name`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='考核模板';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_assessment_unite_index`
--

DROP TABLE IF EXISTS `srm_assessment_unite_index`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_assessment_unite_index` (
  `evaluating_index_id` int DEFAULT NULL COMMENT '评价指标id',
  `assessment_template_id` int DEFAULT NULL COMMENT '考核模板id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `assessment_template_id` (`assessment_template_id`),
  KEY `evaluating_index_id` (`evaluating_index_id`)
) ENGINE=InnoDB AUTO_INCREMENT=363 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='考核模板关联评价指标';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_audit_records`
--

DROP TABLE IF EXISTS `srm_audit_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_audit_records` (
  `have_points` int DEFAULT NULL COMMENT '得分',
  `score` int DEFAULT NULL COMMENT '分值',
  `is_score` int DEFAULT NULL COMMENT '是否评分(0否，1是)',
  `result` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '结果',
  `survey_item` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '调查项目名称',
  `template_content_id` int DEFAULT NULL COMMENT '表单模板内容id',
  `form_template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表单模板编码',
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `file_all` varchar(512) DEFAULT NULL COMMENT '附件全称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=951 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='审核记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_delivery`
--

DROP TABLE IF EXISTS `srm_delivery`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_delivery` (
  `push_status` char(2) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '推送状态(0未推送,1已推送)',
  `is_change` int DEFAULT NULL COMMENT '是否变更(0否，1是)',
  `delivery_remarks` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发货单备注',
  `delivery_status` int DEFAULT NULL COMMENT '发货状态0审批中1未入库2已入库3作废',
  `production_date` date DEFAULT NULL COMMENT '制单日期',
  `purchase_unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单位名称',
  `purchase_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单号',
  `delivery_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发货单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `change_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '变更单号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `change_code` (`change_code`),
  KEY `delivery_code` (`delivery_code`),
  KEY `purchase_code` (`purchase_code`)
) ENGINE=InnoDB AUTO_INCREMENT=241 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='发货单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_delivery_change`
--

DROP TABLE IF EXISTS `srm_delivery_change`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_delivery_change` (
  `examine_cause` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审批原因',
  `examine_out` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审批out',
  `applicat` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '申请人',
  `approval_result` int DEFAULT NULL COMMENT '审批结果(0通过、1不通过)',
  `remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `finish_date` date DEFAULT NULL COMMENT '审批结束日期',
  `launch_date` date DEFAULT NULL COMMENT '发起日期',
  `status` int DEFAULT NULL COMMENT '审批状态(0审批中/1审批完成)',
  `purchase_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单号',
  `delivery_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发货单号',
  `change_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '变更单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `change_code` (`change_code`),
  KEY `delivery_code` (`delivery_code`),
  KEY `purchase_code` (`purchase_code`)
) ENGINE=InnoDB AUTO_INCREMENT=129 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='发货变更';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_delivery_change_detail`
--

DROP TABLE IF EXISTS `srm_delivery_change_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_delivery_change_detail` (
  `delivery_num` decimal(16,6) DEFAULT NULL COMMENT '发货数量',
  `change_delivery_date` date DEFAULT NULL COMMENT '变更后反馈到货日期',
  `delivery_date` date DEFAULT NULL COMMENT '原反馈到货日期',
  `change_delivery_num` decimal(16,6) DEFAULT NULL COMMENT '变更后发货数量',
  `material_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码',
  `change_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '变更单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `change_code` (`change_code`),
  KEY `material_code` (`material_code`)
) ENGINE=InnoDB AUTO_INCREMENT=128 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='发货变更明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_delivery_change_policy`
--

DROP TABLE IF EXISTS `srm_delivery_change_policy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_delivery_change_policy` (
  `day` int DEFAULT NULL COMMENT '天数',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='发货变更策略';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_delivery_detail`
--

DROP TABLE IF EXISTS `srm_delivery_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_delivery_detail` (
  `purchase_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单号',
  `inspection_report_cn` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '检验报告名称',
  `inspection_report` longtext CHARACTER SET utf8mb3 COLLATE utf8_general_ci COMMENT '检验报告',
  `feedback_delivery_date` date DEFAULT NULL COMMENT '反馈到货日期',
  `delivery_num` decimal(16,6) DEFAULT NULL COMMENT '发货数量',
  `material_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码',
  `delivery_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发货单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `reality_storage_num` varchar(255) DEFAULT NULL COMMENT '实际入库数量',
  `reality_storage_date` datetime DEFAULT NULL COMMENT '实际入库日期',
  `supplier_batch` varchar(255) DEFAULT NULL COMMENT '供应商批次',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `delivery_code` (`delivery_code`),
  KEY `material_code` (`material_code`),
  KEY `purchase_code` (`purchase_code`)
) ENGINE=InnoDB AUTO_INCREMENT=279 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='发货单明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_delivery_plan_policy`
--

DROP TABLE IF EXISTS `srm_delivery_plan_policy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_delivery_plan_policy` (
  `sub_class_cn` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '小类中文',
  `sub_class` int DEFAULT NULL COMMENT '小类',
  `main_class_cn` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '大类中文',
  `main_class` int DEFAULT NULL COMMENT '大类',
  `category` int DEFAULT NULL COMMENT '物料类型（1.纸张;2.辅料;3.包材;4.产品，5半成品）',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sub_class` (`sub_class`) USING BTREE,
  KEY `sub_class_cn` (`sub_class_cn`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=324 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='发货计划策略';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_evaluate_item`
--

DROP TABLE IF EXISTS `srm_evaluate_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_evaluate_item` (
  `occupy_weight` decimal(16,6) DEFAULT NULL COMMENT '所占权重',
  `item_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '项目名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `item_name` (`item_name`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='评价项目';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_evaluating_detail`
--

DROP TABLE IF EXISTS `srm_evaluating_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_evaluating_detail` (
  `index_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评价指标id',
  `examine_score` int DEFAULT NULL COMMENT '考核得分',
  `evaluating_index_id` int DEFAULT NULL COMMENT '供应商评价id',
  `assessment_template_id` int DEFAULT NULL COMMENT '考核模板id',
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `assessment_template_id` (`assessment_template_id`),
  KEY `evaluating_index_id` (`evaluating_index_id`),
  KEY `index_id` (`index_id`),
  KEY `supplier_code` (`supplier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=434 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='评价明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_evaluating_index`
--

DROP TABLE IF EXISTS `srm_evaluating_index`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_evaluating_index` (
  `evaluate_statement` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评价说明',
  `occupy_weight` decimal(16,6) DEFAULT NULL COMMENT '所占权重',
  `index_type` int DEFAULT NULL COMMENT '指标类型(0全部、1系统预定指标、2客观评价指标)',
  `index_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '指标名称',
  `item_id` int DEFAULT NULL COMMENT '项目id',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `item_id` (`item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='评价指标';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_form_template_content`
--

DROP TABLE IF EXISTS `srm_form_template_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_form_template_content` (
  `score` int DEFAULT NULL COMMENT '  分值',
  `is_score` int DEFAULT NULL COMMENT '  是否评分(0否，1是)',
  `editing_type` int DEFAULT NULL COMMENT '编辑类型(0文本1下拉框2附件)',
  `investigate_item` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '调查项目名称',
  `form_template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表单模板编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `enum_str` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '枚举值',
  `is_input` int DEFAULT NULL COMMENT '是否必输',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `form_template_code` (`form_template_code`)
) ENGINE=InnoDB AUTO_INCREMENT=733 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='表单模板内容';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_presets_index`
--

DROP TABLE IF EXISTS `srm_presets_index`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_presets_index` (
  `unit` varchar(255) DEFAULT NULL COMMENT '指标单位',
  `index_details` longtext,
  `index_name` varchar(255) DEFAULT NULL COMMENT '指标名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_name` (`index_name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='系统预设指标';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_print_records`
--

DROP TABLE IF EXISTS `srm_print_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_print_records` (
  `delivery_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '发货单号',
  `create_by_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `delivery_code` (`delivery_code`)
) ENGINE=InnoDB AUTO_INCREMENT=73 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='打印记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_print_template`
--

DROP TABLE IF EXISTS `srm_print_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_print_template` (
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '打印类型',
  `template_code` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模板编码',
  `template_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模板名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `supplier_code` (`supplier_code`) USING BTREE,
  KEY `template_code` (`template_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='打印模板配制';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_purchase_deliver`
--

DROP TABLE IF EXISTS `srm_purchase_deliver`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_purchase_deliver` (
  `deliver_type_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发货单类型名称',
  `deliver_type_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发货单类型编码',
  `purchase_type_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '采购订单类型名称',
  `purchase_type_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '采购订单类型编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`),
  KEY `deliver_type_code` (`deliver_type_code`),
  KEY `purchase_type_code` (`purchase_type_code`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='采购订单与发货单类型关联关系';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_purchase_material_details`
--

DROP TABLE IF EXISTS `srm_purchase_material_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_purchase_material_details` (
  `reality_storage_num` decimal(16,6) DEFAULT NULL COMMENT '实际入库数量',
  `reality_storage_date` date DEFAULT NULL COMMENT '实际入库日期',
  `is_change` int DEFAULT NULL COMMENT '是否变更(0否，1是)',
  `conversion_rate` varchar(255) DEFAULT NULL COMMENT '折算率',
  `is_delivery_detail` int DEFAULT NULL COMMENT '是否已生成发货明细',
  `is_change_plan` int DEFAULT NULL COMMENT '是否生成发货计划',
  `bip_purchase_detail_code` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'bip采购订单明细主键',
  `status` int DEFAULT NULL COMMENT '状态(0待创建、1已创建、2已发货)',
  `plan_delivery_date` date DEFAULT NULL COMMENT '计划要求到货日期',
  `model_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '型号名称',
  `model` varchar(255) DEFAULT NULL COMMENT '型号',
  `order_delivery_date` date DEFAULT NULL COMMENT '订单约定到货日期',
  `auxiliary_unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '辅单位名称',
  `auxiliary_unit` varchar(255) DEFAULT NULL COMMENT '辅单位',
  `auxiliary_number` decimal(16,6) DEFAULT NULL COMMENT '采购辅数量',
  `main_unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主单位名称',
  `main_unit` varchar(255) DEFAULT NULL COMMENT '主单位',
  `main_number` decimal(16,6) DEFAULT NULL COMMENT '采购主数量',
  `material_specification_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料规格名称',
  `material_specification` varchar(255) DEFAULT NULL COMMENT '物料规格',
  `material_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料名称',
  `material_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码',
  `purchase_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `sub_class` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料小类',
  `material_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料类型（1.纸张;2.辅料;3.包材;4.产品，5半成品）',
  `unit_price` varchar(255) DEFAULT NULL COMMENT '单价（含税）',
  `sum_price` varchar(255) DEFAULT NULL COMMENT '总价（含税）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `requisition_code` varchar(255) DEFAULT NULL COMMENT '请购单号',
  `mainunitcode` varchar(255) DEFAULT NULL COMMENT '主单位编码',
  `mainunitname` varchar(255) DEFAULT NULL COMMENT '主单位名称',
  `nnum` varchar(255) DEFAULT NULL COMMENT '实收主数量',
  `unitcode` varchar(255) DEFAULT NULL COMMENT '单位编码',
  `unitname` varchar(255) DEFAULT NULL COMMENT '单位名称',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `material_code` (`material_code`),
  KEY `purchase_code` (`purchase_code`)
) ENGINE=InnoDB AUTO_INCREMENT=639 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='采购订单明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_purchase_order`
--

DROP TABLE IF EXISTS `srm_purchase_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_purchase_order` (
  `delivery_address` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '送货地址',
  `bip_purchase_code` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'bip采购订单主键',
  `documentation_date` date DEFAULT NULL COMMENT '制单日期',
  `order_type` varchar(255) DEFAULT NULL COMMENT '订单类型',
  `receive_date` date DEFAULT NULL COMMENT '接收时间',
  `receive_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '接收人',
  `remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `status` int DEFAULT NULL COMMENT '订单状态(0待接收1待发货2部分发货3已发货4已入库)',
  `rqstd_delivery_date` date DEFAULT NULL COMMENT '要求到货日期',
  `order_delivery_date` date DEFAULT NULL COMMENT '订单到货日期',
  `purchaser` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购员',
  `purchase_amount` decimal(16,6) DEFAULT NULL COMMENT '采购金额',
  `supplier_name` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商名称',
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码',
  `user_side` varchar(150) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '用户方(甲方)',
  `purchase_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '采购单号',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `settlement_currency` varchar(255) DEFAULT NULL COMMENT '币种名称',
  `user_side_name` varchar(255) DEFAULT NULL COMMENT '用户方名称(甲方)',
  `order_type_name` varchar(255) DEFAULT NULL COMMENT '订单类型名称',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `purchase_code` (`purchase_code`),
  KEY `supplier_code` (`supplier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=259 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='采购订单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_evaluating`
--

DROP TABLE IF EXISTS `srm_supplier_evaluating`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_evaluating` (
  `current_determine_score` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '本次判定得分',
  `oa_audit_status` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA审核状态',
  `audit_name` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核人',
  `result` varchar(10) DEFAULT NULL COMMENT '评审结果(0不通过1通过)',
  `opinion` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评审意见',
  `process_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '流程号',
  `audit_status` int DEFAULT NULL COMMENT '审核状态(1待审核2审核中3已完成)',
  `audit_date` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '审核人',
  `grade` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '履约评级',
  `examine_type` int DEFAULT NULL COMMENT '考核类型',
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `current_determine_level` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '本次判定级别',
  `grade_score` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '本次得分',
  `srm_evaluate_code` varchar(255) DEFAULT NULL COMMENT 'SRM评价编码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `supplier_code` (`supplier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=556 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='供应商评价';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_form_template`
--

DROP TABLE IF EXISTS `srm_supplier_form_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_form_template` (
  `template_type` int DEFAULT NULL COMMENT '模板类型(1供应商调查表，2技术研发试制记录表，3供应商考察评估报告，4供应商准入评价表)',
  `template_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '模板名称',
  `form_template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '表单模板编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `form_template_code` (`form_template_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='供应商表单模板';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_login_account`
--

DROP TABLE IF EXISTS `srm_supplier_login_account`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_login_account` (
  `post_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位名称',
  `post_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '岗位id',
  `dept_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门名称',
  `dept_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '部门id',
  `username` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '工号',
  `name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '姓名',
  `user_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户id',
  `supplier_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供应商编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='供应商登录账号';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_manage`
--

DROP TABLE IF EXISTS `srm_supplier_manage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_manage` (
  `oa_complete_date` datetime DEFAULT NULL COMMENT 'OA审批完成时间',
  `oa_initiate_date` datetime DEFAULT NULL COMMENT 'OA发起时间',
  `oa_initiate_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA发起人',
  `oa_approval_results` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA审批结果',
  `oa_process_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA流程名称',
  `oa_process_number` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'OA流程编号',
  `remark` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
  `audit_type` int DEFAULT NULL COMMENT '审核状态(0内部审查中，1内部审核完成，2OA审核中，3OA审核完成)',
  `business_opinion` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '商务谈判意见',
  `is_business_cross` int DEFAULT NULL COMMENT '商务谈判是否通过(0否，1是)',
  `business_template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '商务谈判模板编码',
  `scene_opinion` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '现场考察意见',
  `is_scene_cross` int DEFAULT NULL COMMENT '现场考察是否通过(0否，1是)',
  `scene_template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '现场考察模板编码',
  `sample_opinion` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '样品验证意见',
  `is_sample_cross` int DEFAULT NULL COMMENT '样品验证是否通过(0否，1是)',
  `sample_template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '样品验证模板编码',
  `aptitude_opinion` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资质审核意见',
  `is_aptitude_cross` int DEFAULT NULL COMMENT '资质审核是否通过(0否，1是)',
  `aptitude_template_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '资质审核模板编码',
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `srm_admittance_code` varchar(100) DEFAULT NULL COMMENT 'SRM准入编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1475 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='供应商管理目录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_material_item`
--

DROP TABLE IF EXISTS `srm_supplier_material_item`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_material_item` (
  `unit_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '单位名称',
  `unit` int DEFAULT NULL COMMENT '单位',
  `number` decimal(16,6) DEFAULT NULL COMMENT '数量',
  `specification_cn` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '规格名称',
  `specification` int DEFAULT NULL COMMENT '规格',
  `material_name` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料名称',
  `material_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '物料编码',
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=82 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='供应商物料明细';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_profile`
--

DROP TABLE IF EXISTS `srm_supplier_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_profile` (
  `is_audit_whitelist` int DEFAULT NULL COMMENT '是否考核白名单（0否，1是）',
  `grade` varchar(5) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '评级（ABCD）',
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码（供应商账号）',
  `supplier_password` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商登录密码',
  `product_services` varchar(500) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '主要供应产品/服务',
  `is_enable_year_audit` int DEFAULT NULL COMMENT '启用年度考核（0否，1是）',
  `is_enable_season_audit` int DEFAULT NULL COMMENT '启用季度考核(0否，1是)',
  `is_enable_audit` int DEFAULT NULL COMMENT '启用考核(0否，1是)',
  `is_supplier_pass` varchar(10) DEFAULT NULL COMMENT '供应商是否合格(0潜在、1合格，2淘汰)',
  `registrant` varchar(50) DEFAULT NULL COMMENT '法人',
  `supplier_address` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '公司地址',
  `supplier_abbreviation` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商简称',
  `is_entrust` varchar(255) DEFAULT NULL COMMENT '是否委外商(否，是)',
  `is_carrier` varchar(255) DEFAULT NULL COMMENT '是否承运商(否，是)',
  `currency` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '币种',
  `tax_type` varchar(255) DEFAULT NULL COMMENT '供应商税类',
  `uscc` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '统一社会信用代码',
  `status` int DEFAULT NULL COMMENT '供应商启用状态(1未启用;2已启用;3已停用)',
  `supplier_nature` int DEFAULT NULL COMMENT '企业性质(1制造型，2代理型，3仓储型，4运输型)',
  `supplier_type` int DEFAULT NULL COMMENT '供应商分类(0外部单位;1内部单位)',
  `supplier_name` varchar(250) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商名称',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  `trade_name` varchar(255) DEFAULT NULL COMMENT '所属经济行业',
  `jindie_code` varchar(100) DEFAULT NULL COMMENT '金蝶系统编码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `supplier_name` (`supplier_name`),
  KEY `uscc` (`uscc`)
) ENGINE=InnoDB AUTO_INCREMENT=1524 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='供应商档案';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_profile_bank`
--

DROP TABLE IF EXISTS `srm_supplier_profile_bank`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_profile_bank` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `supplier_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供应商编码',
  `bank_account` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '银行账号户名',
  `bank_card` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '银行账号',
  `bank_deposit` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '开户银行',
  `bank_category` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '银行类别',
  `contacts` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系人',
  `contacts_phone` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系人电话',
  `currency` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '币种',
  `domain_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`),
  KEY `supplier_code` (`supplier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=806 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='供应商档案银行信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_profile_contacts`
--

DROP TABLE IF EXISTS `srm_supplier_profile_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_profile_contacts` (
  `is_acquiesce` varchar(5) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否默认（0是1否）',
  `mobile` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机',
  `telephone` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话',
  `position` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职位',
  `sex` varchar(5) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '性别',
  `supplier_code` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '供应商编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `contacts` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系人',
  `domain_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`),
  KEY `supplier_code` (`supplier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=193 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='供应商联系人信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `srm_supplier_unite_assessment`
--

DROP TABLE IF EXISTS `srm_supplier_unite_assessment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `srm_supplier_unite_assessment` (
  `status` int DEFAULT NULL COMMENT '状态(0未启用，1启用)',
  `assessment_template_id` int DEFAULT NULL COMMENT '考核模板id',
  `supplier_code` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '供应商编码',
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `flag_deleted` int DEFAULT '0' COMMENT '是否删除',
  `version` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '版本',
  `create_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `domain_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8_general_ci DEFAULT NULL COMMENT '域id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `assessment_template_id` (`assessment_template_id`),
  KEY `supplier_code` (`supplier_code`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC COMMENT='供应商关联考核模板';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-17 11:07:20
