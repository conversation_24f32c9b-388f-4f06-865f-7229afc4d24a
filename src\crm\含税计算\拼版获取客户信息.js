var cust_arr=[
    {
        "delivery_address": "陕西省 西安市  陕西省咸阳市泾阳县王桥镇（关中环线路边）,胡经理,18066591997",
        "addressArr": [
            {
                "administrative_code": "610100000000",
                "label": "陕西省 西安市  陕西省咸阳市泾阳县王桥镇（关中环线路边）,胡经理,18066591997",
                "value": "陕西省 西安市  陕西省咸阳市泾阳县王桥镇（关中环线路边）,胡经理,18066591997"
            }
        ],
        "exchange_rate": 0.066111,
        "create_time": "2025-03-11 15:39:11",
        "show_create_by": "环球管理员",
        "currencyArr": [
            {
                "label": "人民币",
                "value": "CNY"
            },
            {
                "label": "新台币",
                "value": "TWD"
            },
            {
                "label": "英镑",
                "value": "GBP"
            },
            {
                "label": "澳元",
                "value": "AUD"
            },
            {
                "label": "港币",
                "value": "HKD"
            },
            {
                "label": "日元",
                "value": "JPY"
            },
            {
                "label": "美元",
                "value": "USD"
            },
            {
                "label": "欧元",
                "value": "EUR"
            }
        ],
        "preliminary_quotation_code": "BJ2503000032",
        "department_region": "东北中原区",
        "version": "1.0",
        "tax_rate": 11,
        "show_update_by": "环球管理员",
        "settlement_currency": "JPY",
        "create_by": "2",
        "cust_code": "C000954",
        "update_time": "2025-03-11 15:39:11",
        "cust_type": "2",
        "cust_manager_code": "1838574991853551620",
        "cust_name": "陕西瑞盛生物科技有限公司",
        "id": 216,
        "update_by": "2",
        "cust_manager_name": "刘博",
        "flag_deleted": 0
    },
    {
        "delivery_address": "辽宁省 大连市 中山区 河东区0210001号,鱼儿11,145621",
        "addressArr": [
            {
                "administrative_code": "210202000000",
                "label": "辽宁省 大连市 中山区 河东区0210001号,鱼儿11,145621",
                "value": "辽宁省 大连市 中山区 河东区0210001号,鱼儿11,145621"
            }
        ],
        "exchange_rate": 4.794,
        "create_time": "2025-03-11 15:39:11",
        "show_create_by": "环球管理员",
        "currencyArr": [
            {
                "label": "人民币",
                "value": "CNY"
            },
            {
                "label": "新台币",
                "value": "TWD"
            },
            {
                "label": "英镑",
                "value": "GBP"
            },
            {
                "label": "澳元",
                "value": "AUD"
            },
            {
                "label": "港币",
                "value": "HKD"
            }
        ],
        "preliminary_quotation_code": "BJ2503000032",
        "department_region": "东北中原区",
        "cust_vip": "",
        "version": "1.0",
        "tax_rate": 9,
        "show_update_by": "环球管理员",
        "settlement_currency": "AUD",
        "create_by": "2",
        "cust_code": "C004766",
        "update_time": "2025-03-11 15:39:11",
        "cust_type": "3",
        "cust_manager_code": "1731954709814677505",
        "cust_name": "星辰大海",
        "id": 217,
        "update_by": "2",
        "cust_manager_name": "AGV",
        "flag_deleted": 0
    }
]
var cust_code="C004766"


for (var i = 0; i < cust_arr.length; i++) {
    if (cust_arr[i].cust_code === cust_code) {
        result = {
            exchange_rate: cust_arr[i].exchange_rate,
            tax_rate: cust_arr[i].tax_rate
        };
        break;
    }
}

