-- 原糖班报填报 : 原糖班报填报id与其他表id关联
drop table bi_ytbanbao_tb;
CREATE TABLE bi_ytbanbao_tb (
                                ID VARCHAR(255) PRIMARY KEY,
                                NIANDUDM INT COMMENT '年度代码',
                                XIAOQI INT COMMENT '小期',
                                HAO INT COMMENT '号',
                                GongSiDM VARCHAR(255) COMMENT '公司代码',
                                RQ VARCHAR(255) COMMENT '日期',
                                BANBIEDM VARCHAR(255) COMMENT '班别代码',
                                ZHEYE_RSL decimal(18,4) comment '蔗叶燃烧量'
) COMMENT='原糖班报填报';
select * from bi_ytbanbao_tb;

-- 原糖班报实绩
drop table bi_ytbanbao_1;
CREATE TABLE bi_ytbanbao_1 (
                                      id VARCHAR(255) PRIMARY KEY,
                                      update_time date COMMENT '更新时间',
                                      ZHAZHELIANG DECIMAL(18,4) COMMENT '一列榨蔗量',
                                      CHOUCHULV DECIMAL(18,4) COMMENT '一列抽出率',
                                      SHE<PERSON>ISHENGCHANANQUANLV DECIMAL(18,4) COMMENT '设备生产安全率',
                                      GLN_ZHONGLIANG DECIMAL(18,4) COMMENT '干滤泥重量',
                                      ZZ_TANGDU DECIMAL(18,4) COMMENT '蔗渣糖度',
                                      ZZ_SHUIFEN DECIMAL(18,4) COMMENT '蔗渣水分',
                                      ZHEZHA_RSL DECIMAL(18,4) COMMENT '蔗渣燃烧量',
                                      ZHENGQICHANLIANG DECIMAL(18,4) COMMENT '蒸汽产量'
) COMMENT='原糖班报实绩';
select * from bi_ytbanbao_1;

-- 蔗渣_糖浆(原糖班报)
drop table bi_ytbanbao_3;
CREATE TABLE bi_ytbanbao_3 (
                               id VARCHAR(255) ,
                               update_time date COMMENT '更新时间',
                               LEIBIEDM VARCHAR(255) COMMENT '样品名称',
                               BX DECIMAL(18,4) COMMENT '锤度',
                               S DECIMAL(18,4) COMMENT '转光度',
                               AP DECIMAL(18,4) COMMENT '视纯度',
                               ZTF DECIMAL(18,4) COMMENT '蔗糖分',
                               GP DECIMAL(18,4) COMMENT '重力纯度',
                               HYTF DECIMAL(18,4) COMMENT '还原糖分',
                               HYTF_ZTF DECIMAL(18,4) COMMENT '还原糖分%蔗糖分',
                               PH DECIMAL(18,4) COMMENT 'PH值',
                               LiuXunQiangDu DECIMAL(18,4) COMMENT '硫熏强度',
                               SEZHI DECIMAL(18,4) COMMENT '色值',
                               HUNZHUODU DECIMAL(18,4) COMMENT '混浊度',
                               LINSUANZHI DECIMAL(18,4) COMMENT '磷酸值',
                               GAIYAN DECIMAL(18,4) COMMENT '钙盐',
                               QUANGAILIANG DECIMAL(18,4) COMMENT '全钙量',
                               JIANDU DECIMAL(18,4) COMMENT '碱度',
    PRIMARY KEY (id,update_time,LEIBIEDM)
)COMMENT='蔗汁、糖浆';
select * from bi_ytbanbao_3;

-- 膏蜜糊
drop table bi_ytbanbao_4;
CREATE TABLE bi_ytbanbao_4 (
                               id VARCHAR(255) ,
                               update_time date COMMENT '更新时间',
                               LEIBIEDM VARCHAR(255) COMMENT '项目名称',
                               JTG DECIMAL(18,4) COMMENT '甲糖膏',
                               YTG DECIMAL(18,4) COMMENT '乙糖膏',
                               BTG DECIMAL(18,4) COMMENT '丙糖膏',
                               JYM DECIMAL(18,4) COMMENT '甲原蜜',
                               JXM DECIMAL(18,4) COMMENT '甲洗蜜',
                               YYM DECIMAL(18,4) COMMENT '乙原蜜',
                               YT DECIMAL(18,4) COMMENT '乙糖',
                               BT DECIMAL(18,4) COMMENT '丙糖',
                               YTH DECIMAL(18,4) COMMENT '乙糖糊',
                               BYH DECIMAL(18,4) COMMENT '丙糖糊',
                               HRTJ DECIMAL(18,4) COMMENT '回溶糖浆',
                               PRIMARY KEY (id,update_time,LEIBIEDM)
)  COMMENT='膏蜜糊';
select * from bi_ytbanbao_4;

-- 榨蔗桔水
drop table bi_ytbanbao_5;
CREATE TABLE bi_ytbanbao_5 (
                               id VARCHAR(255) ,
                               update_time date COMMENT '更新时间',
                               name VARCHAR(255) COMMENT '样品名称',
                               bianhao VARCHAR(255) COMMENT '编号',
                               chanliang DECIMAL(18,4) COMMENT '产量',
                               bx DECIMAL(18,4) COMMENT '锤度',
                               s DECIMAL(18,4) COMMENT '转光度',
                               ap DECIMAL(18,4) COMMENT '视纯度',
                               ztf DECIMAL(18,4) COMMENT '蔗糖分',
                               gp DECIMAL(18,4) COMMENT '重力纯度',
                               hytf DECIMAL(18,4) COMMENT '还原糖分',
                               zongtangfen DECIMAL(18,4) COMMENT '总糖分',
                                   PRIMARY KEY  (id,update_time,name,bianhao)
)COMMENT='榨蔗桔水';
select * from bi_ytbanbao_5;

-- 成品糖（原糖、白砂糖、金砂糖）
drop table bi_ytbanbao_6;
CREATE TABLE bi_ytbanbao_6 (
                               id VARCHAR(255) ,
                               update_time date COMMENT '更新时间',
                               name VARCHAR(255) COMMENT '样品名称',
                               bianhao VARCHAR(255) COMMENT '编号',
                               chanliang DECIMAL(18,4) COMMENT '产量',
                               titangdunshu DECIMAL(18,4) COMMENT '提糖吨数',
                               ztf DECIMAL(18,4) COMMENT '蔗糖分',
                               hytf DECIMAL(18,4) COMMENT '还原糖分',
                               ganzaoshizhong DECIMAL(18,4) COMMENT '干燥失重',
                               diandaohuifen DECIMAL(18,4) COMMENT '电导灰分',
                               sezhi DECIMAL(18,4) COMMENT '色值',
                               hunzhuodu DECIMAL(18,4) COMMENT '混浊度',
                               burongyushuizazhi DECIMAL(18,4) COMMENT '不溶于水杂质',
                               eryanghualiu DECIMAL(18,4) COMMENT '二氧化硫',
                               shali1 DECIMAL(18,4),
                               shali2 DECIMAL(18,4),
                               shali3 DECIMAL(18,4) COMMENT '粒度',
                               shali4 DECIMAL(18,4),
                               jibie VARCHAR(255) COMMENT '级别',
           PRIMARY KEY  (id,update_time,name,bianhao)
) COMMENT='成品糖（原糖、白砂糖、金砂糖）';
select * from bi_ytbanbao_6;

-- 综合数据
drop table bi_ytbanbao_7;
CREATE TABLE bi_ytbanbao_7 (
                               id VARCHAR(255) ,
                               update_time date COMMENT '更新时间',
                               zhzlsz DECIMAL(18,4) COMMENT '中和汁磷酸值',
                               yhgbmd DECIMAL(18,4) COMMENT '氧化钙波美度',
                               qhzcdc DECIMAL(18,4) COMMENT '清混汁纯度差',
                               qjxl DECIMAL(18,4) COMMENT '清净效率',
                               jgjycdc DECIMAL(18,4) COMMENT '甲膏甲原纯度差',
                               jtgttl DECIMAL(18,4) COMMENT '甲糖膏提糖率',
                               ygyycdc DECIMAL(18,4) COMMENT '乙膏乙原纯度差',
                               ytgttl DECIMAL(18,4) COMMENT '乙糖膏提糖率',
                           primary key (id,update_time)
)COMMENT='综合数据';
select * from bi_ytbanbao_7;

select t1.*,t2.* from bi_ytbanbao_7 t1 left join bi_ytbanbao_tb t2 on t1.id=t2.id;
