
console.log('window1:',window)
console.log('biChartData2:',biChartData)
console.log('biChartData.data:',biChartData.data)
let labelArr = biChartData.data.map(k=>k.id)
let valueArr = biChartData.data.map(k=>k.turnover_day)

console.log('labelArr:', labelArr)
console.log('valueArr:', valueArr)
option = {
  "legend": {
    "data": labelArr,
    "itemGap": 10,
    "itemWidth": 20,
    "itemHeight": 10,
    "textStyle": {
      "fontSize": 12,
      "color": "#fff",
      "fontFamily": "sans-serif",
      "fontStyle": "normal"
    },
    "show": false,
    "orient": "horizontal",
    "align": "left",
    "left": "80%",
    "top": "10%"
  },
  "xAxis": {
    "type": "category",
    "data": labelArr,
    "nameGap": 18,
    "nameTextStyle": {
      "fontSize": 18,
      "color": "#fff",
      "fontFamily": "Microsoft YaHei",
      "fontStyle": "normal"
    },
    "axisLine": {
      "lineStyle": {
        "width": 1,
        "color": "#fff",
        "type": "solid"
      },
      "show": false
    },
    "axisLabel": {
      "interval": 0,
      "rotate": 0,
      "margin": 18,
      "fontSize": 18,
      "show": false,
      "color": "#fff",
      "fontFamily": "Microsoft YaHei",
      "fontStyle": "normal"
    },
    "axisTick": {
      "length": 10,
      "lineStyle": {
        "width": 2,
        "color": "#fff"
      },
      "show": false,
      "inside": false
    },
    "splitLine": {
      "lineStyle": {
        "width": 1,
        "color": "#fff",
        "type": "dashed"
      },
      "show": false
    },
    "nameLocation": "middle",
    "name": "说明书"
  },
  "yAxis": {
    "type": "value",
    "nameGap": 12,
    "nameTextStyle": {
      "fontSize": 12,
      "color": "#fff",
      "fontFamily": "sans-serif",
      "fontStyle": "normal"
    },
    "axisLine": {
      "lineStyle": {
        "width": 1,
        "color": "#fff",
        "type": "solid"
      },
      "show": false
    },
    "axisLabel": {
      "rotate": 0,
      "margin": 10,
      "fontSize": 12,
      "show": false,
      "formatter": "{value}",
      "color": "#fff",
      "fontFamily": "Microsoft YaHei",
      "fontStyle": "normal"
    },
    "axisTick": {
      "length": 10,
      "lineStyle": {
        "width": 2,
        "color": "#fff"
      },
      "show": false,
      "inside": false
    },
    "splitLine": {
      "lineStyle": {
        "width": 1,
        "color": "#fff",
        "type": "dashed"
      },
      "show": false
    },
    "nameLocation": "end",
    "max": "10",
    "min": "0"
  },
  "series": [
    {
      "data": valueArr,
      "type": "bar",
      "label": {
        "rotate": 0,
        "fontSize": 24,
        "show": true,
        "position": "top",
        "distance": 45,
        "formatter": "{c}天",
        "color": "#fff",
        "fontFamily": "Microsoft YaHei",
        "fontStyle": "normal"
      },
      "barMinHeight": 0,
      "barWidth": "90%",
      "barGap": "0%",
      "itemStyle": {
        "color": {
          "type": "linear",
          "x": 0,
          "y": 0,
          "x2": 0,
          "y2": 1,
          "colorStops": [
            {
              "offset": 0,
              "color": "rgba(0, 133, 255, 1)"
            },
            {
              "offset": 0.3,
              "color": "rgba(0, 133, 255, 0.4)"
            },
			{
              "offset": 1,
             "color": "rgba(0, 133, 255, 0)"
            }
          ]
        }
      },
      "name": "说明书周转天数",
      "stack": "",
      "barCategoryGap": ""
    },
    {
      "name": "",
      "position": "top",
      "symbolPosition": "end",
      "type": "pictorialBar",
      "barGap": "100%",
      "formatter": "{a}:{c}天",
      "data": valueArr,
      "symbolSize": [
        30,
        30
      ],
      "symbolOffset": [
        0,
        "-22"
      ],
      "symbol": "image://http://idg.icubeplat.h3c.com/oss/uni/resource/view?filePath=/%E7%8E%AF%E7%90%83%E5%8D%B0%E5%8A%A1%E6%95%B0%E6%8D%AE%E5%A4%A7%E5%B1%8F/%E5%B7%A5%E5%8E%82/icon_4887.png&image_width=28&image_height=27"
    }
  ],
  "title": {
    "textStyle": {
      "fontSize": 15,
      "color": "#fff",
      "fontFamily": "sans-serif",
      "fontStyle": "normal"
    },
    "show": false,
    "left": "2%",
    "top": "2%"
  },
  "tooltip": {
    "appendToBody": true,
    "textStyle": {
      "fontSize": 12,
      "color": "#000000",
      "fontFamily": "sans-serif",
      "fontStyle": "normal"
    },
    "show": true,
    "formatter": "{a}:{c}天",
    "trigger": "item",
    "axisPointer": {
      "type": "none"
    }
  },
  "dataZoom": {
    "moveHandleSize": 0,
    "show": false,
    "start": "0",
    "end": "100",
    "left": "center",
    "top": "bottom"
  },
  "grid": {
    "top": "20%",
    "bottom": "10%",
    "left": "5%",
    "right": "0%"
  },
  "chartId": "B89F1AA02A6349B5AAF30B3F3872AA3B",
  "id": "0E4336EDB88B4EF38A7365601BBB94B5"
};
window["biChart" + biChartData.chart.id + "_" + echartsTimes].setOption( option );
 
 
 