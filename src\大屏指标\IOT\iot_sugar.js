const axios = require('axios');
const jsonData = {
  deviceNumbers: ["RefinedSugarR3"]
};

const options = {
  method: 'POST',
  url: 'http://*************/openapi/mom/getCuAllPropertiesByDeviceIds',
  headers: {
    'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
    'Content-Type': 'application/json',
    'Accept': '*/*',
    'Host': '*************',
    'Connection': 'keep-alive'
  },
  data: jsonData
};

axios.request(options).then((response) => {
  const my_data = response.data.data["RefinedSugarR3"];
  console.log(JSON.stringify(my_data));
  var total_value=my_data.RefinedSugarR3TotalTraffic.value
  
  console.log(`精糖分蜜R3蜜流量总累计${total_value}`);



}).catch((error) => {
  console.error(error);
}).finally(() => {
  console.log("finish");
})
