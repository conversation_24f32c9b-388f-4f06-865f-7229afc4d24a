console.log("备份")
for (var i = 0; i < carftArr.length; i++) {
  var material_code = carftArr[i].material_name
  // 编码：1010101010000097 名称：787 300g 山东华夏太阳白卡 index：12 ,
  //   编码：1010301010000107 名称：纸箱433826,
  //   编码：1010302020000112 名称：隔档3825
  var result = []
  if (material_code != null && material_code != "") {
    if (material_code.indexOf(",") != -1) {
    var material = material_code.split(",")
    for (var x = 0; x < material.length; x++) {
      var material_name  = material[x]
      var index = material_name.split(" index：")[1]; // 提取index  
      var obj = {}
      obj['index'] = index
      result.push(obj)
    }
  }else{
      var index = material_code.split("index：")[1]; // 提取index  
      var obj = {}
      obj['index'] = index
      result.push(obj)
    }
  }

  var result1 = []
  for (var k = 0; k < result.length; k++) {
    for (var j = 0; j < wi_arr.length; j++) {
      if (result[k].index == wi_arr[j].index) {
        var item = {}
        item['index'] = wi_arr[j].index
        item['material_name'] = wi_arr[j].material_name
        item['material_code'] = wi_arr[j].material_code
        result1.push(item);
      }
    }
  }

  var xzwl_arr = result1
  var wl_msg_index = ""
  var wl_msg = ""
  for (var y = 0; y < xzwl_arr.length; y++) {
    wl_msg_index = wl_msg_index +"编码："+xzwl_arr[y].material_code+" 名称："+xzwl_arr[y].material_name+" index："+xzwl_arr[y].index+","
    wl_msg = wl_msg +"编码："+xzwl_arr[y].material_code+" 名称："+xzwl_arr[y].material_name+","
  }
  wl_msg_index = wl_msg_index.substring(0,wl_msg_index.length-1)
  wl_msg = wl_msg.substring(0,wl_msg.length-1)
  carftArr[i]['material_name'] = wl_msg_index
  carftArr[i]['material_json_code'] = wl_msg
}
console.log("debug_carftArr2",JSON.stringify(carftArr))
return carftArr