const mysql = require('mysql2/promise');

// 数据库配置
const db_config = {
    host: '**************',
    user: 'root',
    password: '123456',
    database: 'h3chq_pdmbusiness1704287270935'
};

// 配置参数
const BATCH_SIZE = 50; // 减小批次大小，提高成功率
const CONCURRENT_BATCHES = 3; // 减少并发数，避免数据库压力过大

// 日志函数
const log = (message) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
};

// 查询需要修复的产品数据
async function query_need_fix_products(connection) {
    try {
        const [rows] = await connection.execute(
            `SELECT id, material_code, product_version, is_group_product, standard_unit, standard_unit_cn 
             FROM pdm_product_version 
             WHERE is_group_product IS NULL OR standard_unit = 9077`
        );
        log(`查询到 ${rows.length} 条需要修复的数据`);
        log(`示例数据: ${JSON.stringify(rows[0])}`);
        return rows;
    } catch (error) {
        log(`查询需要修复的产品数据失败: ${error.message}`);
        throw error;
    }
}

// 批量更新组产品标识
async function batch_update_group_product(connection, products) {
    if (products.length === 0) return;
    
    try {
        // 先检查数据
        const material_codes = products.map(p => p.material_code).filter(Boolean);
        if (material_codes.length === 0) {
            log('没有需要更新组产品标识的数据');
            return;
        }

        // 使用 IN 查询时，需要将数组展开为多个参数
        const placeholders = material_codes.map(() => '?').join(',');
        const [result] = await connection.execute(
            `UPDATE pdm_product_version SET is_group_product = 0 
             WHERE material_code IN (${placeholders}) AND is_group_product IS NULL`,
            material_codes
        );
        
        log(`组产品标识更新：总数 ${products.length}, 实际更新 ${result.affectedRows}`);
        return result.affectedRows;
    } catch (error) {
        log(`批量更新组产品标识失败: ${error.message}`);
        throw error;
    }
}

// 批量更新单位信息
async function batch_update_unit_info(connection, products) {
    if (products.length === 0) return;
    
    try {
        // 先检查数据
        const material_codes = products.map(p => p.material_code).filter(Boolean);
        if (material_codes.length === 0) {
            log('没有需要更新单位信息的数据');
            return;
        }

        // 使用 IN 查询时，需要将数组展开为多个参数
        const placeholders = material_codes.map(() => '?').join(',');
        const [result] = await connection.execute(
            `UPDATE pdm_product_version SET standard_unit = 6591, standard_unit_cn = '只' 
             WHERE material_code IN (${placeholders}) AND standard_unit = 9077`,
            material_codes
        );
        
        log(`单位信息更新：总数 ${products.length}, 实际更新 ${result.affectedRows}`);
        return result.affectedRows;
    } catch (error) {
        log(`批量更新单位信息失败: ${error.message}`);
        throw error;
    }
}

// 批量检查产品BOM
async function batch_check_product_bom(connection, products) {
    try {
        if (products.length === 0) return new Set();

        const values = [];
        const conditions = [];
        
        products.forEach(p => {
            if (p.material_code && p.product_version) {
                conditions.push('(material_code = ? AND product_version = ?)');
                values.push(p.material_code, p.product_version);
            }
        });

        if (conditions.length === 0) {
            log('没有需要检查的BOM数据');
            return new Set();
        }

        const [rows] = await connection.execute(
            `SELECT material_code, product_version FROM pdm_product_bom 
             WHERE ${conditions.join(' OR ')}`,
            values
        );
        
        const existing = new Set(rows.map(row => `${row.material_code}_${row.product_version}`));
        log(`BOM检查：总数 ${products.length}, 已存在 ${rows.length}`);
        return existing;
    } catch (error) {
        log(`批量检查产品BOM失败: ${error.message}`);
        throw error;
    }
}

// 批量插入产品BOM数据
async function batch_insert_product_bom(connection, products) {
    if (products.length === 0) return;
    
    try {
        // 数据验证
        const valid_products = products.filter(p => p.material_code && p.product_version);
        if (valid_products.length === 0) {
            log('没有有效的BOM数据需要插入');
            return;
        }

        const values = valid_products.map(p => [
            p.product_version,
            p.material_code,
            '',
            6591,
            '1*1',
            null,
            '1',
            p.material_code,
            '3',
            0,
            '1/1'
        ]);

        const [result] = await connection.query(
            `INSERT INTO pdm_product_bom (
                product_version, product_code, parent_code,
                standard_unit, spread_size, component_size,
                component_count, material_code, categroy,
                flag_deleted, chromatic_degree
            ) VALUES ?`,
            [values]
        );
        
        log(`BOM插入：尝试 ${products.length}, 成功 ${result.affectedRows}`);
        return result.affectedRows;
    } catch (error) {
        log(`批量插入产品BOM失败: ${error.message}`);
        log(`失败数据示例: ${JSON.stringify(products[0])}`);
        throw error;
    }
}

// 处理一批产品
async function process_batch(products, batch_index) {
    let connection;
    try {
        connection = await mysql.createConnection(db_config);
        await connection.beginTransaction();
        
        log(`开始处理批次 ${batch_index}, 数据量 ${products.length}`);
        
        // 1. 批量更新组产品标识
        const group_updates = await batch_update_group_product(connection, products);
        
        // 2. 批量更新单位信息
        const unit_updates = await batch_update_unit_info(connection, products);
        
        // 3. 批量检查和插入BOM
        const existing_boms = await batch_check_product_bom(connection, products);
        const products_need_bom = products.filter(p => 
            !existing_boms.has(`${p.material_code}_${p.product_version}`)
        );
        
        let bom_inserts = 0;
        if (products_need_bom.length > 0) {
            bom_inserts = await batch_insert_product_bom(connection, products_need_bom);
        }
        
        await connection.commit();
        log(`批次 ${batch_index} 处理完成: 组产品更新 ${group_updates}, 单位更新 ${unit_updates}, BOM插入 ${bom_inserts}`);
    } catch (error) {
        if (connection) {
            await connection.rollback();
            log(`批次 ${batch_index} 回滚完成`);
        }
        throw error;
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

// 主函数
async function main() {
    let connection;
    try {
        // 建立数据库连接
        connection = await mysql.createConnection(db_config);
        log('数据库连接成功');
        
        // 1. 查询需要修复的产品数据
        const all_products = await query_need_fix_products(connection);
        await connection.end();
        
        if (all_products.length === 0) {
            log('没有需要修复的数据');
            return;
        }
        
        // 2. 将数据分批
        const batches = [];
        for (let i = 0; i < all_products.length; i += BATCH_SIZE) {
            batches.push(all_products.slice(i, i + BATCH_SIZE));
        }
        
        // 3. 并发处理批次
        log(`开始处理 ${batches.length} 个批次，每批 ${BATCH_SIZE} 条数据`);
        for (let i = 0; i < batches.length; i += CONCURRENT_BATCHES) {
            const current_batches = batches.slice(i, i + CONCURRENT_BATCHES);
            const batch_promises = current_batches.map((batch, idx) => 
                process_batch(batch, i + idx + 1)
            );
            
            try {
                await Promise.all(batch_promises);
                log(`完成批次组 ${i + 1} 到 ${i + current_batches.length}`);
            } catch (error) {
                log(`处理批次组 ${i + 1} 时发生错误: ${error.message}`);
                throw error;
            }
        }
        
        log('所有数据处理完成');
    } catch (error) {
        log(`执行过程中发生错误: ${error.message}`);
        throw error;
    }
}

// 执行主函数
main().catch(error => {
    log(`程序执行失败: ${error.message}`);
    process.exit(1);
});
