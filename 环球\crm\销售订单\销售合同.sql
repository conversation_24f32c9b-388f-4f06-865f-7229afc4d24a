-- 销售合同明细
select contract_product_line_number,product_quantity,issued_quantity,ccmp.*
from crm_contract_management_product ccmp
where contract_management_code='25070334';
select * from crm_contract_management ccm where contract_management_code='25070334';
select * from crm_sales_order_product csop where sales_order_code='25070334A';
-- 销售合同查询
select
    a.id,
    a.contract_management_code,
    a.cust_manager_name,
    a.status,a.cust_name,
    a.quotation_code,
    a.create_by,
    a.create_time,
    a.cancellation_reason,
    max(cpq.imposition_quotation) as imposition_quotation
from crm_contract_management a
         left join crm_contract_management_product b on b.contract_management_code=a.contract_management_code  and b.flag_deleted=0
         left join crm_preliminary_quotation cpq on cpq.preliminary_quotation_code = a.quotation_code
where  a.contract_management_code ='25070303'
group by  a.id;



-- 查询详情表格
SELECT
    DISTINCT
    cpqp.preliminary_quotation_code,
    cmp.contract_management_code,
    cmp.object_id,
    cpqp.object_id,
    cpqp.grade_name,
    cmp.grade_name,
    cmp.*,
    cpqp.commission_print_number,
    cpqp.quotation_factory,
    cpqp.product_weight
FROM
    crm_contract_management_product cmp
        INNER JOIN crm_contract_management cm ON
        cmp.contract_management_code = cm.contract_management_code
        LEFT JOIN crm_preliminary_quotation_product cpqp ON
        cmp.material_code = cpqp.material_code
            AND cpqp.preliminary_quotation_code = cm.quotation_code
            AND cpqp.id = cmp.quotation_product_id
            AND cpqp.flag_deleted = 0
WHERE
    cpqp.quotation_code='HJ252000099';
-- 报价单
select t1.sales_assistant_code,t1.sales_assistant_name,t1.* from crm_contract_management t1 where contract_management_code in ('25070158');
select * from crm_contract_management_product ccmp where contract_management_code in ('25070274');



;
