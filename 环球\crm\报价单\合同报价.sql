-- 报价发送OA日志
select *
from crm_record cr
where logic = 'OA审批'
  and paramL like '%BJ2507000331%';
select cust_code, cust_name,oa_id,cust_manager_code,cust_manager_name
from crm_preliminary_quotation cpq
where cust_manager_name ='';

select t1.status, t1.* from crm_preliminary_quotation t1
where preliminary_quotation_code = 'BJ2507000331';
select * from crm_record where logic = 'OA审批' and paramL like '%BJ2502000545%';
select *
from crm_preliminary_quotation cpq
where preliminary_quotation_code = '25060242A';
SELECT cqp.preliminary_quotation_code
FROM crm_preliminary_quotation cqp
WHERE cqp.flag_deleted = 0
  AND cqp.oa_id = 196249;

select cust_manager_name,cust_manager_code,ccmp.*
from crm_contract_management ccmp
where cust_manager_name ='';

-- 报价单产品
select *
from crm_preliminary_quotation_product cpqp
where preliminary_quotation_code = 'BJ2507000250';
SELECT cqp.preliminary_quotation_code
FROM crm_preliminary_quotation cqp
WHERE cqp.flag_deleted = 0
  AND cqp.oa_id = 196859;
;

select * from crm_sales_order cso where sales_order_code='25060242A';
select * from crm_sales_order_product  cso where sales_order_code='25060242A';
select *
from hq_mdm_b.mdm_mom_afferent mma
where module_name in ('报价合同评审')
and JSON_EXTRACT(data ,'$.id')='242614'
;


select *
from crm_quotation
where id in (36923, 37323, 38314, 38317, 38328);



select * from crm_contract_management ccm where quotation_code='BJ2507000331';
select * from crm_preliminary_quotation cpq where preliminary_quotation_code='BJ2507000331';

select
    cpq.delivery_address,
    cpq.total_cost,
    cpq.remark,
    cpq.type,
    cpq.tax_rate,
    cpq.show_update_by,
    cpq.total_sales_gross_profit,
    cpq.create_by,
    cpq.update_time,
    cpq.quotation_code,
    cpq.deparment,
    cpq.cust_manager_code,
    cpq.depreciation_expense,
    cpq.cust_name,
    cpq.id,
    cpq.total_sales,
    cpq.update_by,
    cpq.flag_deleted,
    cpq.utilities_labor,
    cpq.overall_sales_margin_rate,
    cpq.quotation_factory,
    cpq.exchange_rate,
    cpq.create_time,
    cpq.show_create_by,
    cpq.preliminary_quotation_code,
    cpq.last_year_margin_rate,
    cpq.cust_vip,
    cpq.freight_cost,
    cpq.settlement_currency,
    cpq.cust_code,
    cpq.delivery_date,
    cpq.imposition_quotation,
    cpq.cust_manager_name,
    cpq.status,
    case
        cpq.status
        when 0 then '草稿'
        when 1 then '生效'
        when 2 then '审批中'
        when 3 then '驳回'
        when 4 then '生效'
        when 5 then '作废'
        end as status_desc,
    case
        cpq.cust_vip
        when 1 then 'A'
        when 2 then 'B'
        when 3 then 'C'
        when '' then '无效'
        end as cust_vip_desc,
    cpq.one_expenses,
    cpq.print_count,
    ccb.cust_status,
    cct.street as administrative_code,
    cct.contact_phone
from
    crm_preliminary_quotation cpq
        left join crm_cust_basic ccb on ccb.cust_code = cpq.cust_code and ccb.flag_deleted = 0
        left join crm_cust_transport cct on cct.cust_mnemonic_code = ccb.cust_mnemonic_code and cct.flag_deleted = 0
        AND cct.is_default = '0'
where
    cpq.flag_deleted = 0
  and ((:preliminary_quotation_code is null
    or :preliminary_quotation_code = '')
    or (cpq.preliminary_quotation_code = :preliminary_quotation_code) )
;
select * from crm_contract_management ccm where quotation_code='BJ2507000093';
