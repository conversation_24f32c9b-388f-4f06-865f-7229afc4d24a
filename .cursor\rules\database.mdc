---
description: 数据库相关规范
globs: 
alwaysApply: false
---
---
description: 数据库相关规范
globs: ["**/*.js", "**/*.sql"] 
alwaysApply: true
---

# 数据库规范

## 代码风格
- 所有SQL关键字使用大写（如SELECT, INSERT, UPDATE, DELETE）
- SQL语句中的表名和字段名使用小写，并用下划线分隔（snake_case）

## 数据库连接
- 使用连接池管理数据库连接
- 确保所有数据库连接在操作完成后被正确关闭
- 连接参数（如用户名、密码等）应通过环境变量配置，不要硬编码

## 查询实践
- 避免在循环中执行查询，尽量使用批量操作
- 为复杂查询添加详细注释
- 所有查询必须使用参数化查询以防止SQL注入
- 限制SELECT查询返回的列数量，避免使用SELECT *

## 事务处理
- 确保数据一致性的操作必须使用事务
- 事务应尽可能短小，避免长时间锁定表
- 正确处理事务中的错误，确保在异常情况下回滚

## 索引使用
- 经常用于WHERE子句的列应建立索引
- 定期检查索引使用情况，移除未使用的索引
- 避免对频繁更新的列创建过多索引

## 错误处理
- 所有数据库操作必须包含适当的错误处理
- 记录数据库错误，但不将详细错误信息暴露给最终用户
- 实现重试机制处理临时性数据库连接问题

## 性能优化
- 大型查询应该分页处理
- 定期审查和优化慢查询
- 对于频繁访问的只读数据考虑使用缓存