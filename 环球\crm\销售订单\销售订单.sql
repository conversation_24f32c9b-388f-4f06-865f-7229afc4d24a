-- 查询销售订单 销售订单(status：0-已拆分,1-已下达,2-已排产,3-已领料,4-生产中,5-已完成,6-已开票,7-已关闭,8-已取消,10-已删除)
select cso.status,id,sales_order_code from crm_sales_order cso where sales_order_code like '%25070187A%';
select * from crm_contract_management ccm where contract_management_code='25020332';

-- 日志
select * from crm_record cr where logic='销售订单_新增(crm_sales_order)' and paramL like '%25070489A%';
select * from crm_record cr where logic='销售订单_新增(arr)' and paramL like '%25070489A%';
-- 明细查下
select contract_product_line_number,product_quantity,material_code,product_version,t1.* from crm_sales_order_product t1
                                                          where sales_order_code='25070489A';

select * from crm_sales_order cso where sales_order_code in ('25040019A');
select status,t1.* from crm_preliminary_quotation t1 where preliminary_quotation_code='BJ2502000402';
select * from crm_preliminary_quotation_product;
select * from crm_sales_order where sales_order_code='25020164A';
select * from crm_sales_order_product where material_code='SMHY0000103';
select * from crm_sales_order where sales_order_code='25050132A';
select * from crm_contract_management_product where contract_management_code='25030018';

SELECT
    cqp.preliminary_quotation_code
FROM
    crm_preliminary_quotation cqp
WHERE
    cqp.flag_deleted = 0
  AND cqp.oa_id = '193203';


WITH temp_date AS (
    SELECT
        DISTINCT
        ia.split_order_no,
        cso.sales_order_code
    FROM
        invoice_application ia,
        crm_sales_order cso
    WHERE
        cso.flag_deleted = 0
      AND ia.flag_deleted = 0
      AND ia.status IN ('0', '3', '4')
      AND INSTR(CONCAT(',', ia.split_order_no, ','), CONCAT(',', cso.sales_order_code, ',')) > 0
),
     cust_basic_priority AS (
         SELECT
             cust_code,
             cust_vip,
             sales_assistant_name,
             cust_manager_name,
             ROW_NUMBER() OVER (PARTITION BY cust_code ORDER BY cust_version DESC) AS rn
         FROM
             crm_cust_basic
         WHERE
             flag_deleted = 0
#            AND cust_type NOT IN ('0', '1')
#            AND cust_status IN ('2', '4')
#            AND cust_version IN (1, 2)
     )
SELECT
    DISTINCT
    cso.id,
    cso.sales_order_code,
    CASE
        WHEN cso.status IN ('10', '8', '7', '6') THEN cso.status
        WHEN td.split_order_no IS NOT NULL THEN '11'
        ELSE cso.status
        END status,
    CASE
        WHEN cso.status = '10' THEN '已删除'
        WHEN cso.status = '8' THEN '已取消'
        WHEN cso.status = '7' THEN '已关闭'
        WHEN cso.status = '6' THEN '已开票'
        WHEN td.split_order_no IS NOT NULL THEN '已发货未开票'
        WHEN cso.status = '0' THEN '已拆分'
        WHEN cso.status = '1' THEN '已下达'
        WHEN cso.status = '2' THEN '已排产'
        WHEN cso.status = '3' THEN '已领料'
        WHEN cso.status = '4' THEN '生产中'
        WHEN cso.status = '5' THEN '已入库'
        ELSE cso.status
        END status_name,
    cso.status,td.split_order_no,
    cso.stock_order_code,
    cso.cust_code,
    cso.cust_name,
    ccb.cust_vip,
    cso.printing_price,
    ccb.sales_assistant_name,
    ccb.cust_manager_name,
    cso.create_time,
    cso.show_create_by,
    cso.cancellation_reason,
    cso.remark,
    cso.quotation_code,
    cso.cust_manager_code,
    cso.sales_assistant_code,
    cpq.imposition_quotation,
    cso.oa_out
FROM
    crm_sales_order_product csop
        LEFT JOIN crm_sales_order cso ON
        cso.sales_order_code = csop.sales_order_code
        LEFT JOIN temp_date td ON
        td.sales_order_code = cso.sales_order_code
        LEFT JOIN cust_basic_priority ccb ON
        ccb.cust_code = cso.cust_code
            AND ccb.rn = 1
        LEFT JOIN crm_preliminary_quotation cpq ON
        cpq.preliminary_quotation_code = cso.quotation_code
WHERE
    csop.flag_deleted = 0
  AND cso.flag_deleted = 0
  AND ((:sales_order_code IS NULL OR :sales_order_code = '') OR (cso.sales_order_code LIKE CONCAT("%", :sales_order_code, "%")))
;



