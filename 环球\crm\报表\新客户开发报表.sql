-- 新客户开发
SELECT '西安环球'                    sale_company,
       t1.deparment_code,
       t1.deparment_name,
       t1.department_region          region,
       t1.cust_name,
       t1.cust_manager_code,
       t1.cust_manager_name,
       t1.cust_type,
       CASE
           WHEN t1.cust_type = 0 THEN '新建客户'
           WHEN t1.cust_type = 1 THEN '公海客户'
           WHEN t1.cust_type = 2 THEN '合作客户'
           WHEN t1.cust_type = 3 THEN '开发中客户'
           WHEN t1.cust_type = 4 THEN '受限客户'
           END                       cust_type_name,
       round(ifnull(t2.invoiced_amount, 0),2) invoiced_amount,
       round(ifnull(t3.received_money,0),2) received_money,
       round(ifnull(t4.received_money,0),2)  quater_received_money,
       round(ifnull(t5.received_money,0),2)  month_received_money
FROM crm_cust_basic t1
         LEFT JOIN (select customer_no,
                           sum(ifnull(nnum, 0))     invoiced_quantity,
                           sum(ifnull(norigmny, 0)) invoiced_amount
                    from bip_outbound_order_detail bood
                             join (select csrcid, csrcbid, sum(nnum) nnum, sum(norigmny) norigmny
                                   from crm_sales_invoice_details
                                   where flag_deleted = 0
                                   group by csrcid, csrcbid) csid
                                  on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
                    where bood.flag_deleted = 0
                    group by customer_no) t2 on t2.customer_no = t1.cust_code
         left join (select cust_code,
                           sum(money) received_money
                    from crm_sales_received_payments
                    where date_format(billdate,'%Y-%m-%d')>=DATE_FORMAT(CURRENT_DATE(), '%Y-01-01')
                    group by cust_code) t3 on t3.cust_code = t1.cust_code
         left join (select cust_code,
                           sum(money) received_money
                    from crm_sales_received_payments
                    where date_format(billdate,'%Y-%m-%d')>=(DATE_FORMAT(CURRENT_DATE(), '%Y-%m-01') - INTERVAL (MONTH(CURRENT_DATE())-1) % 3 MONTH)
                    group by cust_code) t4 on t4.cust_code = t1.cust_code
         left join (select cust_code,
                           sum(money) received_money
                    from crm_sales_received_payments
                    where date_format(billdate,'%Y-%m-%d')>=DATE_FORMAT(CURRENT_DATE(), '%Y-%m-01')
                    group by cust_code) t5 on t5.cust_code = t1.cust_code
WHERE t1.flag_deleted = 0
  and t1.cust_status = 2
  and t1.cust_type not in (0, 1)
  and date_format(t1.create_time, '%Y') = date_format(now(), '%Y')
  and round(ifnull(t2.invoiced_amount, 0),2)>0
#   AND if(:admin,
#          if(:condition, t1.cust_manager_code in (:cust_manager_code_arr), 1),
#          if(:condition,
#             t1.cust_manager_code in (:cust_manager_code_arr) and
#             t1.sales_assistant_code like concat('%', :user_id, '%'),
#             t1.cust_manager_code in (:cust_manager_code_arr) or t1.sales_assistant_code like concat('%', :user_id, '%'))
#       )
#   AND ((:region IS NULL OR :region = '') OR (t1.department_region = :region))
#   AND ((:cust_code IS NULL OR :cust_code = '') OR (t1.cust_code like concat('%', :cust_code, '%')))
#   AND ((:cust_name IS NULL OR :cust_name = '') OR (t1.cust_name LIKE concat('%', :cust_name, '%')))
#   AND if(:cust_manager_size>0, t1.cust_manager_code in (:cust_manager_arr), 1)
# ORDER BY t1.id ASC
# LIMIT :page_size offset :offset
;
select * from crm_sales_order cso;
select 5876.11+13808.85+6138.05+6504.43+5810.97+45640.35;
select 83778.76-82728.29;
;
select date_format(billdate,'%Y-%m-%d') from crm_sales_received_payments;
SELECT DATE_FORMAT(CURRENT_DATE(), '%Y-%m-01') AS '当月第一天';
SELECT (DATE_FORMAT(CURRENT_DATE(), '%Y-%m-01') - INTERVAL (MONTH(CURRENT_DATE())-1) % 3 MONTH) AS '当季第一天';
SELECT DATE_FORMAT(CURRENT_DATE(), '%Y-01-01') AS '当年第一天';

-- 合计
SELECT '合计' AS            sale_company,
       round(sum(invoiced_amount), 2)       invoiced_amount,
       round(sum(received_money), 2)        received_money,
       round(sum(quater_received_money), 2) quater_received_money,
       round(sum(month_received_money), 2)  received_money
FROM (SELECT '西安环球'                    sale_company,
             t1.deparment_code,
             t1.deparment_name,
             t1.department_region          region,
             t1.cust_name,
             t1.cust_manager_code,
             t1.cust_manager_name,
             t1.cust_type,
             CASE
                 WHEN t1.cust_type = 0 THEN '新建客户'
                 WHEN t1.cust_type = 1 THEN '公海客户'
                 WHEN t1.cust_type = 2 THEN '合作客户'
                 WHEN t1.cust_type = 3 THEN '开发中客户'
                 WHEN t1.cust_type = 4 THEN '受限客户'
                 END                       cust_type_name,
             ifnull(t2.invoiced_amount, 0) invoiced_amount,
             ifnull(t3.received_money,0) received_money,
             ifnull(t4.received_money,0)  quater_received_money,
             ifnull(t5.received_money,0)  month_received_money
      FROM crm_cust_basic t1
               LEFT JOIN (select customer_no,
                                 sum(ifnull(nnum, 0))     invoiced_quantity,
                                 sum(ifnull(norigmny, 0)) invoiced_amount
                          from bip_outbound_order_detail bood
                                   join (select csrcid, csrcbid, sum(nnum) nnum, sum(norigmny) norigmny
                                         from crm_sales_invoice_details
                                         where flag_deleted = 0
                                         group by csrcid, csrcbid) csid
                                        on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
                          where bood.flag_deleted = 0
                          group by customer_no) t2 on t2.customer_no = t1.cust_code
               left join (select cust_code,
                                 sum(money) received_money
                          from crm_sales_received_payments
                          where date_format(billdate,'%Y-%m-%d')>=DATE_FORMAT(CURRENT_DATE(), '%Y-01-01')
                          group by cust_code) t3 on t3.cust_code = t1.cust_code
               left join (select cust_code,
                                 sum(money) received_money
                          from crm_sales_received_payments
                          where date_format(billdate,'%Y-%m-%d')>=(DATE_FORMAT(CURRENT_DATE(), '%Y-%m-01') - INTERVAL (MONTH(CURRENT_DATE())-1) % 3 MONTH)
                          group by cust_code) t4 on t4.cust_code = t1.cust_code
               left join (select cust_code,
                                 sum(money) received_money
                          from crm_sales_received_payments
                          where date_format(billdate,'%Y-%m-%d')>=DATE_FORMAT(CURRENT_DATE(), '%Y-%m-01')
                          group by cust_code) t5 on t5.cust_code = t1.cust_code
      WHERE t1.flag_deleted = 0
        and t1.cust_status = 2
        and t1.cust_type not in (0, 1)
        and date_format(t1.create_time, '%Y') = date_format(now(), '%Y')
        AND if(:admin,
               if(:condition, t1.cust_manager_code in (:cust_manager_code_arr), 1),
               if(:condition,
                  t1.cust_manager_code in (:cust_manager_code_arr) and
                  t1.sales_assistant_code like concat('%', :user_id, '%'),
                  t1.cust_manager_code in (:cust_manager_code_arr) or t1.sales_assistant_code like concat('%', :user_id, '%'))
            )
        AND ((:region IS NULL OR :region = '') OR (t1.department_region = :region))
        AND ((:cust_code IS NULL OR :cust_code = '') OR (t1.cust_code like concat('%', :cust_code, '%')))
        AND ((:cust_name IS NULL OR :cust_name = '') OR (t1.cust_name LIKE concat('%', :cust_name, '%')))
        AND if(:cust_manager_size>0, t1.cust_manager_code in (:cust_manager_arr), 1)
      ORDER BY t1.id ASC
      ) temp
;
