-- ETL建表
drop table if exists dws_cust_sale_month;
create table dws_cust_sale_month
(
    id                int auto_increment primary key comment '自增主键'
        primary key,
    update_year       varchar(10)    null comment '指标年度',
    update_month      varchar(10)    null comment '指标月份',
    update_time       varchar(10)    null comment '指标时间',
    cumulative_amount decimal(20, 2) null comment '累计金额',
    cust_code         varchar(10)    null comment '客户编码'
) comment '客户销售累计';

-- ETL:CRM客户销售累计->dws_cust_sale_month
WITH RECURSIVE
    months AS (SELECT 1 AS month
               UNION ALL
               SELECT month + 1
               FROM months
               WHERE month < 12),
    sales_data AS (SELECT DATE_FORMAT(t1.create_time, '%m')   AS month,
                          ROUND(SUM(amount_exclusive_tax), 2) AS amount_exclusive_tax,
                          t1.cust_code
                   FROM crm_sales_order t1
                            JOIN crm_sales_order_product t2
                                 ON t1.sales_order_code = t2.sales_order_code
                                     AND t2.flag_deleted = 0
                   WHERE status != 8
                     AND t1.flag_deleted = 0
                     AND DATE_FORMAT(t1.create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
                   GROUP BY DATE_FORMAT(t1.create_time, '%m'), t1.cust_code)
SELECT year(now())                                        AS update_year,
       LPAD(m.month, 2, '0')                           AS update_month,
       concat(year(now()), '-', LPAD(m.month, 2, '0')) AS update_time,
       s.cust_code,
       COALESCE(
               (SELECT SUM(s2.amount_exclusive_tax)
                FROM sales_data s2
                WHERE CAST(s2.month AS SIGNED) <= m.month
                  AND s2.cust_code = s.cust_code),
               0
       )                                               AS cumulative_amount
FROM months m
         CROSS JOIN (SELECT DISTINCT cust_code FROM sales_data) s
GROUP BY m.month, s.cust_code
ORDER BY m.month, s.cust_code;


select t1.update_time,
       round(ifnull(100 * cumulative_amount / (t2.sales_target * 10000) * t3.target_percentage / 100, 0),
             2) as complete_rate
from cockpit.dws_cust_sale_month t1
         left join cockpit.ods_metric_person t2 on t1.update_year = t2.metric_year and t2.flag_deleted = 0
         left join cockpit.dwd_cust_sale_percentage t3
                   on t1.cust_code = t3.cust_code and t2.cust_manager_code = t3.sales_code
where t2.cust_manager_code = :cust_manager_code
  AND if(:admin,
         1,
         if(:cust_code_size > 0, t1.cust_code in (:cust_code_arr), 1)
      )
group by t1.update_time
;


select temp1.`year`, temp1.`month`, ifnull(temp2.complete_rate, 0) complete_rate
from (select `year_month`, `year`, `month`
      from cockpit.dim_month dm
      where dm.year in (year(now()), year(now()) - 1)) temp1
         left join (select t1.update_time,
                           round(ifnull(100 * cumulative_amount / (t2.sales_target * 10000) * t3.target_percentage /
                                        100, 0),
                                 2) as complete_rate
                    from cockpit.dws_cust_sale_month t1
                             left join cockpit.ods_metric_person t2
                                       on t1.update_year = t2.metric_year and t2.flag_deleted = 0
                             left join cockpit.dwd_cust_sale_percentage t3
                                       on t1.cust_code = t3.cust_code and t2.cust_manager_code = t3.sales_code
                    where t2.cust_manager_code = :cust_manager_code
                      AND if(:admin,
                             1,
                             if(:cust_code_size > 0, t1.cust_code in (:cust_code_arr), 1)
                          )
                    group by t1.update_time) temp2
                   on temp1.`year_month` = temp2.update_time
order by temp1.`year`, temp1.`month`
;
