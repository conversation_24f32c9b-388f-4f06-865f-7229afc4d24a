if (productObj.tax_inclusive == '含税') {
    applyObj.invoice_include_price = roundNumber(Number(productObj.unit_price_usd), 6)
    //结算币单价不含税
    applyObj.jsb_unit_price_include_tax = applyObj.invoice_include_price
    //结算币金额含税
    applyObj.jsb_amount_include_tax = roundNumber(Number(applyObj.jsb_unit_price_include_tax) * applyNum, 2)
    //金额含税
    applyObj.amount_tax_inclusive = roundNumber(Number(applyObj.jsb_amount_include_tax) * exchange_rate, 2)
    //单价含税
    applyObj.price_including_tax = roundNumber(Number(applyObj.amount_tax_inclusive) / applyNum, 6)
    //金额不含税
    applyObj.amount_exclusive_tax = roundNumber(Number(applyObj.amount_tax_inclusive) / (1 + abs_tax_rate / 100), 2)

    //结算币金额不含税
    applyObj.jsb_amount_exclude_tax = roundNumber(Number(applyObj.amount_exclusive_tax) / exchange_rate, 2)
    //结算币单价不含税
    applyObj.jsb_unit_price_exclude_tax = roundNumber(Number(applyObj.jsb_amount_exclude_tax) / applyNum, 6)
    //单价不含税
    applyObj.unit_price_exclusive = roundNumber(Number(applyObj.amount_exclusive_tax) / applyNum, 6)
    //税额
    applyObj.tax_diff = roundNumber(Number(applyObj.amount_tax_inclusive) - Number(applyObj.amount_exclusive_tax), 2)
    //差价
    applyObj.price_diff = roundNumber(Number(applyObj.invoice_include_price) - Number(applyObj.origin_unit_price_include_tax), 6)
} else if (productObj.tax_inclusive == '不含税') {
    applyObj.invoice_price = roundNumber(Number(productObj.unit_price_excluding_usd), 6)
    applyObj['jsb_unit_price_exclude_tax'] = applyObj.invoice_price
    // 不含税销售金额(报价币种) =  不含税销售单价(报价币种)*数量
    applyObj['jsb_amount_exclude_tax'] = roundNumber(Number(applyObj['jsb_unit_price_exclude_tax']) * applyNum, 2)
    // 不含税销售金额(RMB) = 不含税销售金额(报价币种) * 汇率
    applyObj['amount_exclusive_tax'] = roundNumber(Number(applyObj['jsb_amount_exclude_tax']) * exchange_rate, 2)
    // 不含税销售单价(RMB) = 不含税销售金额(RMB) / 数量      保留6位小数
    applyObj['unit_price_exclusive'] = roundNumber(Number(applyObj['amount_exclusive_tax']) / applyNum, 6)
    //税额=不含税销售金额(RMB)*税率
    applyObj['tax_diff'] = roundNumber(Number(applyObj['amount_exclusive_tax']) * abs_tax_rate / 100, 2)
    // 含税销售金额(RMB) = 不含税销售金额(RMB) + 税额
    applyObj['amount_tax_inclusive'] = roundNumber(Number(applyObj['amount_exclusive_tax']) + Number(applyObj['tax_diff']), 2)
    // 含税销售金额(报价币种) = 含税销售金额(RMB) / 汇率
    applyObj['jsb_amount_include_tax'] = roundNumber(Number(applyObj['amount_tax_inclusive']) / exchange_rate, 2)
    // 含税销售单价(报价币种) = 含税销售金额(报价币种)/数量  6位小数
    applyObj['jsb_unit_price_include_tax'] = roundNumber(Number(applyObj['jsb_amount_include_tax']) / applyNum, 6)
    // 含税销售单价(RMB) = 含税销售金额(RMB)/数量  6位小数
    applyObj['price_including_tax'] = roundNumber(Number(applyObj['amount_tax_inclusive']) / applyNum, 6)
    //差价 = 开票不含税单价-订单原结算币种单价（不含税） 
    applyObj['price_diff'] = roundNumber(Number(applyObj['invoice_price']) - Number(applyObj['origin_unit_price_exclude_tax']), 6)
}