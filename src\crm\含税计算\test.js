// 四舍五入函数，处理精度问题
const roundNumber = (number, decimals) => {
    return Math.round(number * Math.pow(10, decimals)) / Math.pow(10, decimals);
}
if (tax == '含税') {
    //开票不含税单价
    record['jsb_unit_price_include_tax'] = roundNumber(Number(record['invoice_include_price']), 6)
    // 含税销售单价(报价币种)  6位小数
    record['jsb_unit_price_include_tax'] = roundNumber(Number(record['jsb_unit_price_include_tax']), 6)
    // 含税销售金额(报价币种) = 含税销售单价(报价币种)*数量
    record['jsb_amount_include_tax'] = roundNumber(Number(record['jsb_unit_price_include_tax']) * product_quantity, 2)
    // 含税销售金额(RMB) = 含税销售金额(报价币种) * 汇率 
    record['amount_tax_inclusive'] = roundNumber(Number(record['jsb_amount_include_tax']) * exchange_rate, 2)
    // 含税销售单价(RMB) = 含税销售金额(RMB)/数量   6位小数
    record['price_including_tax'] = roundNumber(Number(record['amount_tax_inclusive']) / product_quantity, 6)
    // 不含税销售金额(RMB) = 含税销售金额(RMB) / (1 + 税率)
    record['amount_exclusive_tax'] = roundNumber(Number(record['amount_tax_inclusive']) / (1 + abs_tax_rate / 100), 2)
    //不含税销售金额(报价币种) = 不含税销售金额(RMB) / 汇率
    record['jsb_amount_exclude_tax'] = roundNumber(Number(record['amount_exclusive_tax']) / exchange_rate, 2)
    //不含税销售单价(报价币种)=不含税销售金额(报价币种)/数量 保留6位小数
    record['jsb_unit_price_exclude_tax'] = roundNumber(Number(record['jsb_amount_exclude_tax']) / product_quantity, 6)
    // 不含税销售单价(RMB) = 不含税销售金额(RMB) / 数量      保留6位小数
    record['unit_price_exclusive'] = roundNumber(Number(record['amount_exclusive_tax']) / product_quantity, 6)
    //税额=含税销售金额(RMB)-不含税销售金额(RMB)
    record['tax_diff'] = roundNumber(Number(record['amount_tax_inclusive']) - Number(record['amount_exclusive_tax']), 2)
    //差价 = 开票含税单价-订单原结算币种单价（含税）
    record['price_diff'] = roundNumber(Number(record['invoice_include_price'] - record['origin_unit_price_include_tax']), 6)
} else {
    //开票不含税单价
    record['jsb_unit_price_exclude_tax'] = roundNumber(Number(record['invoice_price']), 6)
    //不含税销售单价(报价币种) 保留6位小数
    record['jsb_unit_price_exclude_tax'] = roundNumber(Number(record['jsb_unit_price_exclude_tax']), 6)
    // 不含税销售金额(报价币种) =  不含税销售单价(报价币种)*数量
    record['jsb_amount_exclude_tax'] = roundNumber(Number(record['jsb_unit_price_exclude_tax']) * product_quantity, 2)
    // 不含税销售金额(RMB) = 不含税销售金额(报价币种) * 汇率
    record['amount_exclusive_tax'] = roundNumber(Number(record['jsb_amount_exclude_tax']) * exchange_rate, 2)
    // 不含税销售单价(RMB) = 不含税销售金额(RMB) / 数量      保留6位小数
    record['unit_price_exclusive'] = roundNumber(Number(record['amount_exclusive_tax']) / product_quantity, 6)
    //税额=不含税销售金额(RMB)*税率
    record['tax_diff'] = roundNumber(Number(record['amount_exclusive_tax']) * abs_tax_rate / 100, 2)
    // 含税销售金额(RMB) = 不含税销售金额(RMB) + 税额
    record['amount_tax_inclusive'] = roundNumber(Number(record['amount_exclusive_tax']) + Number(record['tax_diff']), 2)
    // 含税销售金额(报价币种) = 含税销售金额(RMB) / 汇率
    record['jsb_amount_include_tax'] = roundNumber(Number(record['amount_tax_inclusive']) / exchange_rate, 2)
    // 含税销售单价(报价币种) = 含税销售金额(报价币种)/数量  6位小数
    record['jsb_unit_price_include_tax'] = roundNumber(Number(record['jsb_amount_include_tax']) / product_quantity, 6)
    // 含税销售单价(RMB) = 含税销售金额(RMB)/数量  6位小数
    record['price_including_tax'] = roundNumber(Number(record['amount_tax_inclusive']) / product_quantity, 6)
    //差价 = 开票不含税单价-订单原结算币种单价（不含税） 
    record['price_diff'] = roundNumber(Number(record['invoice_price']-record['origin_unit_price_exclude_tax']), 6)
}