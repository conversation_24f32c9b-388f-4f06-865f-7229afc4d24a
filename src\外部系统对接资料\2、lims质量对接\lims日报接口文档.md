# lime_mes_api (1903003928551755776)  使用文档

## 概述

这份文档详细描述了如何使用API（lime_mes_api），包含了API的详细解释、调试示例、调用示例。

---

## API说明

#### API名称: lime_mes_api

**API ID:** 1903003928551755776

**API类型:** guide

**请求类型:** `POST`

**请求URL:** http://172.16.32.11/data_service/api/v2/api_service/query/1903003928551755776

**请求参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |

**返回参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| id | string | false | -- | -- |
| 榨季 | string | false | -- | -- |
| 公司代码 | string | false | -- | -- |
| 日期 | string | false | -- | -- |
| 班别代码 | string | false | -- | -- |
| 当日榨蔗量 | double | false | -- | -- |
| 当日甘蔗蔗糖分 | double | false | -- | -- |
| 当日榨季累计榨蔗量 | double | false | -- | -- |
| 当日榨季累计甘蔗蔗糖分 | double | false | -- | -- |
| 上榨季同期榨蔗量 | double | false | -- | -- |
| 上榨季同期甘蔗蔗糖分 | double | false | -- | -- |
| 当日设备生产安全率 | double | false | -- | -- |
| 当日榨季累计设备生产安全率 | double | false | -- | -- |
| 当日桔水（废蜜）产量 | double | false | -- | -- |
| 当日桔水重力纯度 | double | false | -- | -- |
| 当日桔水锤度 | double | false | -- | -- |
| 当日榨季累计桔水产量 | double | false | -- | -- |
| 蔗渣当日产量 | double | false | -- | -- |
| 当日蔗渣水分 | double | false | -- | -- |
| 蔗渣当日销售量 | double | false | -- | -- |
| 当日滤泥糖度 | double | false | -- | -- |
| 蔗渣糖度（明细） | double | false | -- | -- |
| 当日滤泥产量 | double | false | -- | -- |
| 当日蔗渣榨季累计产量 | double | false | -- | -- |
| 当日榨季累计蔗渣销售量 | double | false | -- | -- |
| 当日电厂供汽量 | double | false | -- | -- |
| 当日榨季累计电厂供汽量 | double | false | -- | -- |
| 总发电量 | double | false | -- | -- |
| 当日榨季累计总发电量 | double | false | -- | -- |
| 上榨季同期总发电量 | double | false | -- | -- |
| 01班当日原糖产量 | double | false | -- | -- |
| 02班当日原糖产量 | double | false | -- | -- |
| 03班当日原糖产量 | double | false | -- | -- |
| 当日原糖产量 | double | false | -- | -- |
| 当日榨季累计原糖产量 | double | false | -- | -- |
| 01班当日榨季累计原糖产量 | double | false | -- | -- |
| 02班当日榨季累计原糖产量 | double | false | -- | -- |
| 03班当日榨季累计原糖产量 | double | false | -- | -- |
| 小期检修时间 | double | false | -- | -- |
| 当日榨季累计小期检修时间 | double | false | -- | -- |
| 初压汁视纯度（明细） | double | false | -- | -- |
| 混合汁视纯度（明细） | double | false | -- | -- |
| 当日滤清汁浊度 | double | false | -- | -- |
| 当日浓缩糖浆锤度 | double | false | -- | -- |
| 当日浓缩糖浆色值 | double | false | -- | -- |
| 编号 | string | false | -- | -- |
| 白砂糖粒度 | double | false | -- | -- |
| 金砂糖粒度 | double | false | -- | -- |
| 白砂糖色值 | double | false | -- | -- |
| 金砂糖色值 | double | false | -- | -- |
| 初压汁简纯度（明细） | string | false | -- | -- |
| 当日初压汁简纯度 | string | false | -- | -- |
| 当日榨季累计初压汁简纯度 | string | false | -- | -- |
| 混合糖（含在制品）当日产量 | string | false | -- | -- |
| 混合糖（含在制品）当日产率 | string | false | -- | -- |
| 混合糖（含在制品）榨季累计产量 | string | false | -- | -- |
| 混合糖（含在制品）榨季累计产率 | string | false | -- | -- |
| 当日原糖部在制品折糖量 | string | false | -- | -- |
| 当日榨季累计洄溶糖产率 | string | false | -- | -- |
| 实时洄溶糖量 | string | false | -- | -- |
| 当日洄溶糖量 | string | false | -- | -- |
| 当日平均日洄溶糖量 | string | false | -- | -- |
| 当日榨季累计洄溶糖量 | string | false | -- | -- |
| 当日人均日洄溶糖量 | string | false | -- | -- |
| 当日人均累计洄溶糖量 | string | false | -- | -- |
| 当日混合糖产率 | string | false | -- | -- |
| 当日榨季累计可售蔗渣数量 | string | false | -- | -- |
| 当日榨季累计可售蔗渣产率 | string | false | -- | -- |
| 上榨季同期可售蔗渣数量 | string | false | -- | -- |
| 上榨季同期可售蔗渣产率 | string | false | -- | -- |
| 当日桔水产率 | string | false | -- | -- |
| 当日榨季累计桔水产率 | string | false | -- | -- |
| 当日榨季累计蔗渣产量对蔗比 | string | false | -- | -- |
| 当日榨季累计蔗渣销售量对蔗比 | string | false | -- | -- |
| 当日吨蔗综合用汽量 | string | false | -- | -- |
| 当日榨季累计吨蔗综合用汽量 | string | false | -- | -- |
| 当日单位产品用汽量 | string | false | -- | -- |
| 当日榨季累计单位产品用汽量 | string | false | -- | -- |
| 当日榨季累计百吨蔗耗标煤量 | string | false | -- | -- |
| 当日榨季累计生产总用电量 | string | false | -- | -- |
| 上榨季同期生产总用电量 | string | false | -- | -- |
| 当日吨蔗用电量 | string | false | -- | -- |
| 当日榨季累计吨蔗用电量 | string | false | -- | -- |
| 上榨季同期吨蔗用电量 | string | false | -- | -- |
| 当日吨蔗用水量 | string | false | -- | -- |
| 当日榨季累计吨蔗用水量 | string | false | -- | -- |
| 当日榨季累计吨糖用电量 | string | false | -- | -- |
| 上榨季同期吨糖用电量 | string | false | -- | -- |
| 当日榨季累计外售电量 | string | false | -- | -- |
| 当日售电量 | string | false | -- | -- |
| 当日榨季累计售电量 | string | false | -- | -- |
| 当日榨季累计炼糖总收回率 | string | false | -- | -- |
| 当日榨季累计总收回率 | string | false | -- | -- |
| 当日榨季累计蔗渣损失 | string | false | -- | -- |
| 当日榨季累计废蜜损失 | string | false | -- | -- |
| 当日榨季累计滤泥损失 | string | false | -- | -- |
| 当日榨季累计未测定损失 | string | false | -- | -- |
| 当日榨季累计压榨抽出率 | string | false | -- | -- |
| 当日榨季累计原糖产率（含在制品） | string | false | -- | -- |
| 当日榨季累计蔗渣剩余率 | string | false | -- | -- |
| 当日榨季累计等折白砂糖产率 | string | false | -- | -- |
| 当日榨季累计蔗渣阻碍损失时间 | string | false | -- | -- |
| 甲班当日抽出率 | string | false | -- | -- |
| 乙班当日抽出率 | string | false | -- | -- |
| 丙班当日抽出率 | string | false | -- | -- |
| 甲班当日a膏蜜纯度差 | string | false | -- | -- |
| 甲班当日b膏蜜纯度差 | string | false | -- | -- |
| 甲班当日c膏蜜纯度差 | string | false | -- | -- |
| 乙班当日a膏蜜纯度差 | string | false | -- | -- |
| 乙班当日b膏蜜纯度差 | string | false | -- | -- |
| 乙班当日c膏蜜纯度差 | string | false | -- | -- |
| 丙班当日a膏蜜纯度差 | string | false | -- | -- |
| 丙班当日b膏蜜纯度差 | string | false | -- | -- |
| 丙班当日c膏蜜纯度差 | string | false | -- | -- |
| 各编号金砂糖糖糊量 | string | false | -- | -- |
| 各编号金砂糖甲蜜量 | string | false | -- | -- |
| 各编号金砂糖水分 | string | false | -- | -- |
| 原糖水分（明细） | string | false | -- | -- |
| 桔水锤度（明细） | string | false | -- | -- |
| 桔水重力纯度（明细） | string | false | -- | -- |
| 当日饱充后糖浆ph值 | string | false | -- | -- |
| 当日榨季累计单位产品能耗 | string | false | -- | -- |
| 当日榨季累计产糖率 | string | false | -- | -- |
| 上榨季同期产糖率 | string | false | -- | -- |
| 当日精优极品率 | string | false | -- | -- |
| 当日r3蜜量 | string | false | -- | -- |
| 当日产糖率（不含r3蜜等折） | string | false | -- | -- |
| 当日产糖率（含在制品等折） | string | false | -- | -- |
| 当日榨季累计颗粒不合格产量 | string | false | -- | -- |
| 当日榨季累计全水分不合格产量 | string | false | -- | -- |
| 当日榨季累计黄黑点不合格产量 | string | false | -- | -- |
| 当日榨季累计其它不合格产量 | string | false | -- | -- |
| 当日合格产量 | string | false | -- | -- |
| 当日榨季累计吨洄溶糖耗汽量 | string | false | -- | -- |
| 当日榨季累计吨洄溶糖耗电量 | string | false | -- | -- |
| 上榨季同期吨洄溶糖耗汽量 | string | false | -- | -- |
| 上榨季同期吨洄溶糖耗电量 | string | false | -- | -- |
| 当日吨洄溶糖耗汽量 | string | false | -- | -- |
| 当日吨洄溶糖耗电量 | string | false | -- | -- |
| 当日洄溶糖浆色值 | string | false | -- | -- |
| 当日滤清糖浆色值 | string | false | -- | -- |
| 当周洄溶糖量 | string | false | -- | -- |
| 当日滤清糖浆浊度 | string | false | -- | -- |
| 甲班当日洄溶糖量 | string | false | -- | -- |
| 乙班当日洄溶糖量 | string | false | -- | -- |
| 丙班当日洄溶糖量 | string | false | -- | -- |
| 丁班当日洄溶糖量 | string | false | -- | -- |
| 当日脱色糖浆色值 | string | false | -- | -- |
| 饱充后糖浆ph值（明细） | string | false | -- | -- |
| 干滤泥糖度（明细） | string | false | -- | -- |
| 浓缩糖浆锤度（明细） | string | false | -- | -- |
| r1白砂糖筛前色值（明细） | string | false | -- | -- |
| r1白砂糖筛前颗粒度（明细） | string | false | -- | -- |
| 二级树脂糖浆色值（明细） | string | false | -- | -- |
| 二级树脂糖浆锤度（明细） | string | false | -- | -- |
| 浓缩糖浆色值（明细） | string | false | -- | -- |
| r1糖蜜色值（明细） | string | false | -- | -- |
| r1糖蜜锤度（明细） | string | false | -- | -- |
| r2糖蜜色值（明细） | string | false | -- | -- |
| r2糖蜜锤度（明细） | string | false | -- | -- |
| r3糖蜜色值（明细） | string | false | -- | -- |
| r3糖蜜锤度（明细） | string | false | -- | -- |
| 各编号白砂糖筛前变异系数cv | string | false | -- | -- |
| 当日产糖率 | string | false | -- | -- |
| s1白砂糖全水分（明细） | string | false | -- | -- |
| s2白砂糖全水分（明细） | string | false | -- | -- |
| r1白砂糖全水分（明细） | string | false | -- | -- |
| r2白砂糖全水分（明细） | string | false | -- | -- |
| 白砂糖粒度（明细） | string | false | -- | -- |
| 当日蔗渣剩余率 | string | false | -- | -- |
| 当日蔗渣剩余量 | string | false | -- | -- |
| 当日榨季累计蔗渣剩余量 | string | false | -- | -- |
| 滤清糖浆色值（明细） | string | false | -- | -- |
| 丙糖糊色值 | double | false | -- | -- |
| 丙糖糊锤度 | double | false | -- | -- |
| 丙糖膏色值 | double | false | -- | -- |
| 丙糖膏锤度 | double | false | -- | -- |
| 乙原蜜色值 | double | false | -- | -- |
| 乙原蜜锤度 | double | false | -- | -- |
| 乙糖糊色值 | double | false | -- | -- |
| 乙糖糊锤度 | double | false | -- | -- |
| 乙糖膏色值 | double | false | -- | -- |
| 乙糖膏锤度 | double | false | -- | -- |
| 回溶糖浆色值 | double | false | -- | -- |
| 回溶糖浆锤度 | double | false | -- | -- |
| 甲原蜜色值 | double | false | -- | -- |
| 甲原蜜锤度 | double | false | -- | -- |
| 甲洗蜜色值 | double | false | -- | -- |
| 甲洗蜜锤度 | double | false | -- | -- |
| 甲糖膏色值 | double | false | -- | -- |
| 甲糖膏锤度 | double | false | -- | -- |
| 01班榨蔗量合计 | double | false | -- | -- |
| 02班榨蔗量合计 | double | false | -- | -- |
| 03班榨蔗量合计 | double | false | -- | -- |
| 01班蒸汽产量 | double | false | -- | -- |
| 02班蒸汽产量 | double | false | -- | -- |
| 03班蒸汽产量 | double | false | -- | -- |
| 01班蔗渣水分 | double | false | -- | -- |
| 02班蔗渣水分 | double | false | -- | -- |
| 03班蔗渣水分 | double | false | -- | -- |
| 01班蔗渣燃烧量 | double | false | -- | -- |
| 02班蔗渣燃烧量 | double | false | -- | -- |
| 03班蔗渣燃烧量 | double | false | -- | -- |
| 01班蔗渣糖度 | double | false | -- | -- |
| 02班蔗渣糖度 | double | false | -- | -- |
| 03班蔗渣糖度 | double | false | -- | -- |
| 01班设备生产安全率 | double | false | -- | -- |
| 02班设备生产安全率 | double | false | -- | -- |
| 03班设备生产安全率 | double | false | -- | -- |
| 01班初压汁视纯度 | double | false | -- | -- |
| 01班混合汁视纯度 | double | false | -- | -- |
| 01班滤清汁视纯度 | double | false | -- | -- |
| 01班澄清汁视纯度 | double | false | -- | -- |
| 02班初压汁视纯度 | double | false | -- | -- |
| 02班混合汁视纯度 | double | false | -- | -- |
| 02班滤清汁视纯度 | double | false | -- | -- |
| 02班澄清汁视纯度 | double | false | -- | -- |
| 03班初压汁视纯度 | double | false | -- | -- |
| 03班混合汁视纯度 | double | false | -- | -- |
| 03班滤清汁视纯度 | double | false | -- | -- |
| 03班澄清汁视纯度 | double | false | -- | -- |
| 01班当日桔水产量 | double | false | -- | -- |
| 02班当日桔水产量 | double | false | -- | -- |
| 03班当日桔水产量 | double | false | -- | -- |
| 01班当日桔水重力纯度 | double | false | -- | -- |
| 02班当日桔水重力纯度 | double | false | -- | -- |
| 03班当日桔水重力纯度 | double | false | -- | -- |


**正常响应示例:**

```json
{
    "Status": {
        "Code": 0,
        "Message": ""
    },
    "Result": {
        "Meta": [
            {
                "Name": "id",
                "Type": "int"
            },
            {
                "Name": "name",
                "Type": "string"
            }
        ],
        "Data": [
            {
                "id": 123,
                "name": "test"
            }
        ]
    }
}
```

**错误响应示例:**

```json
{
    "Status": {
        "Code": 4,
        "Message": "[10001] api access denied"
    },
    "Result": {
        "Meta": [],
        "Data": []
    }
}
```

**错误代码:**

## ErrorCode 常见状态码相关：
|  |  |  |
| --- | --- | --- |
| 状态码 | 名称 | 含义 |
| 0 | QueryErrorType_OK | 查询成功 |
| 1 | QueryErrorType_PARSER_ERROR | 解析报错 |
| 2 | QueryErrorType_ILLGEAL_INPUT_ERROR | 非法参数报错 |
| 3 | QueryErrorType_RATE_LIMIT_ERROR | 限流报错 |
| 4 | QueryErrorType_AUTH_ERROR | 权限报错 |
| 5 | QueryErrorType_QUERY_TIMEOUT | 查询超时报错 |
| 6 | QueryErrorType_DS_TIMEOUT | 数据源超时报错 |
| 7 | QueryErrorType_INTERNAL_ERROR | 程序内部报错 |
| 8 | QueryErrorType_META_ERROR | 元信息报错 |
| 9 | QueryErrorType_DS_RATE_LIMIT_ERROR | 数据源限流报错 |
| 255 | QueryErrorType_UNKNOWN_ERROR | 未知错误 |
## One Service 常见错误码相关：
|  |  |  |
| --- | --- | --- |
| 错误码 | 名称 | 含义 |
| 10000 | UnknownQueryEngine | 未知查询引擎 |
| 10001 | NoPermission | 没有权限 |
| 10002 | MetaErr | 元信息错误 |
| 10003 | ParamsParseErr | 参数解析错误 |
| 10004 | ApiRateLimitExceed | Api的QPS超限额 |
| 10005 | ParseErr | 解析错误 |
| 10006 | ExecuteErr | 执行错误 |
| 10007 | UnknownQueryType | 未知查询类型 |
| 10008 | QueryRequestError | 查询请求错误 |
| 10009 | QueryEngineMismatchError | 查询引擎不匹配错误 |


**调用说明:**

###### HTTP调用示例
```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  -H 'dpstoken: $DPS_TOKEN' \
  http://172.16.32.11/data_service/api/v2/api_service/query/1903003928551755776 \
  -d '{}'
```
###### 如果使用动态密钥功能,可以使用如下接口动态获取token信息

```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  http://172.16.32.11/data_service/api/v2/api_service/token \
  -d '{"AppKey": "you_app_key","AppSecret": "you_app_secret"}'
```
###### 返回样例：
```
{
    "code": 0,
    "message": "Success",
    "data": {
        "AppKey": "you_app_key",
        "Token": "token_str"
    },
    "meta": {
        "text": "",
        "redirect": ""
    }
}
```
###### 解析其中的Token 调用的时候将$DPS_TOKEN替换为“该应用密钥”，动态token有一定有效期且会动态变化，请不要缓存动态token或者对动态token有任何假设，每次请求获取即可

**开启分页调用说明:**

向导式API分页调用说明
1、在API页面开启高级配置的分页，在API页面测试时系统会自动添加pageNum和pageSize参数。
2、调用API的时候需要将pageNum和pageSize填入请求参数中。
例如：{
  "pageNum": 1,
  "pageSize": 100
}

脚本式API分页调用说明
1、用SQL来进行分页操作，例如：SELECT * FROM table ORDER BY id LIMIT 10 OFFSET 0;
2、如果想要获取本次查询的total总数，请参考：https://bytedance.larkoffice.com/docx/HJKudzKHVoEAejxAmUncZ1LanIc

**API调用常见问题:**

1、api xxx meta not exists
答：一般是因为API没有在对应的环境发布，可在API页面版本信息中查看是否已发布到对应环境。

2、10001 api access denied
答：API调用时未输入DpsToken或者DpsToken与已对API授权的应用不一致。

---

## 使用 Postman 调试API说明

Postman是一个强大的HTTP API测试工具，可以帮助开发者快速开发和测试API。

### POST 请求示例:

1. 打开Postman应用程序。
2. 在请求方式空间中选择 `POST`。
3. 在请求URL空间中输入 http://172.16.32.11/data_service/api/v2/api_service/query/1903003928551755776，然后点击 `Body` 的选项卡，然后选择 `x-www-form-urlencoded`。
4. 在 `Key` 中输入参数名称，`Value` 中输入参数值，然后点击 `Send` 按钮。
5. 如果一切正常，则会在下方的 `Body` 中看到响应结果。

---

## 使用 Java 调用API说明

如果你在Java编程语言调用API，可以使用HttpClient等库。

### HttpClient库为例子，POST请求的代码示例

```java

// 导入需要的Apache HttpClient库包
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import java.io.IOException;

public class ApiTest {

    public static void main(String[] args) throws Exception {
        // 定义基础URL
        String baseUrl = "http://172.16.32.11/data_service/api/v2/api_service/query/";
        // 定义API ID
        String apiId = "1903003928551755776";
        // 定义API TOKEN
        String dpstoken = "$DPS_TOKEN";
        // 定义请求主体
        String body = "{}";
        // 调用performPostRequest函数发送POST请求并在控制台输出返回的结果
        System.out.println(performPostRequest(baseUrl, apiId, dpstoken, body));
    }

    // 定义函数performPostRequest发送POST请求
    private static String performPostRequest(String baseUrl, String apiId, String dpstoken, String body) throws IOException {
        // 创建一个默认的CloseableHttpClient实例
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建一个HttpPost实例，参数为目标URL（基础URL+API ID）
        HttpPost httpPost = new HttpPost(baseUrl + apiId);

        // 设置httpPost的header
        httpPost.setHeader("dpstoken", dpstoken);

        // 设置httpPost的body
        httpPost.setEntity(new StringEntity(body));

        // 使用httpclient执行httpPost，获取响应结果
        CloseableHttpResponse response = httpclient.execute(httpPost);
        try {
            // 从response中获取HttpEntity
            HttpEntity entity = response.getEntity();

            // 如果HttpEntity不为null，则将其转化为String类型，并返回
            return entity != null ? EntityUtils.toString(entity) : null;
            // 确保response在执行完毕后被关闭，避免资源泄漏
        } finally {
            response.close();
        }
    }
}
/*
需要在pom.xml中添加的dependency如下
<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpclient</artifactId>
    <version>4.5.13</version>
</dependency>

<!-- https://mvnrepository.com/artifact/org.json/json -->
<dependency>
    <groupId>org.json</groupId>
    <artifactId>json</artifactId>
    <version>20210307</version>
</dependency>
 */
```