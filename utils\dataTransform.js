class DataTransform {
    static groupByKey(array, key) {
        return array.reduce((acc, curr) => {
            (acc[curr[key]] = acc[curr[key]] || []).push(curr);
            return acc;
        }, {});
    }

    static sumByKey(array, key) {
        return array.reduce((sum, curr) => sum + (parseFloat(curr[key]) || 0), 0);
    }

    static compareData(source, target, key) {
        const sourceMap = new Map(source.map(item => [item[key], item]));
        const targetMap = new Map(target.map(item => [item[key], item]));
        
        const extra = source.filter(item => !targetMap.has(item[key]));
        const missing = target.filter(item => !sourceMap.has(item[key]));
        
        return { extra, missing };
    }
}

module.exports = DataTransform; 