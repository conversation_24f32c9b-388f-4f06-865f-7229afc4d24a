-- 获取今年和去年
select temp1.*, ifnull(temp2.cust_size,0) cust_size
from (WITH RECURSIVE
          months AS (SELECT 1 AS month
                     UNION ALL
                     SELECT month + 1
                     FROM months
                     WHERE month < 12),
          years AS (SELECT YEAR(NOW()) AS year
                    UNION ALL
                    SELECT YEAR(NOW()) - 1 AS year)
      SELECT y.year  `year`,
             m.month `month`
      FROM years y
               CROSS JOIN
           months m
      ORDER BY y.year, m.month) temp1
         left join
     (select date_format(create_time, '%Y') `year`, date_format(create_time, '%c') `month`, count(*) cust_size
      from crm_account_receivable_age
      where qkts > 120
        AND if(:admin,
               1,
               if(:cust_code_size > 0, cust_code in (:cust_code_arr), 1)
            )
      group by date_format(create_time, '%Y'), date_format(create_time, '%c')) temp2
     on temp1.`year` = temp2.`year` and temp1.`month` = temp2.`month`
order by temp1.`year`, temp1.`month`
;
