create table dwd_customer_risk_data
(
    id         int auto_increment
        primary key,
    cust_code  varchar(50)              null comment '客户编码',
    cust_name  varchar(100)             null comment '客户名称',
    amount     varchar(100) default '0' null comment '金额',
    days       varchar(100) default '0' null comment '天数',
    risk_score varchar(100) default '0' null comment '风险评分',
    risk_level varchar(20)              not null comment '风险等级',
    data_year  varchar(4)               not null comment '数据年份',
    data_month varchar(2)               not null comment '数据月份',
    type       varchar(2)               null comment '类型:1-库存2-欠款3-已发货未开票'
)
    comment '客户风险数据表';
-- 获取上个月库存数据
select * from cockpit.dwd_customer_risk_data t1
where t1.type=1 and  concat(t1.data_year,'-',t1.data_month) = DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')

-- 获取上个月所有类型的风险客户数据
SELECT 
    t1.cust_code,
    t1.cust_name,
    MAX(CASE WHEN t1.type = '1' THEN t1.amount END) as inventory_amount,
    MAX(CASE WHEN t1.type = '1' THEN t1.days END) as inventory_days,
    MAX(CASE WHEN t1.type = '2' THEN t1.amount END) as debt_amount,
    MAX(CASE WHEN t1.type = '2' THEN t1.days END) as debt_days,
    MAX(CASE WHEN t1.type = '3' THEN t1.amount END) as uninvoiced_amount,
    MAX(CASE WHEN t1.type = '3' THEN t1.days END) as uninvoiced_days,
    MAX(t1.risk_score) as max_risk_score,
    MAX(t1.risk_level) as highest_risk_level
FROM cockpit.dwd_customer_risk_data t1
WHERE concat(t1.data_year,'-',t1.data_month) = DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m')
GROUP BY t1.cust_code, t1.cust_name
ORDER BY MAX(t1.risk_score) DESC;