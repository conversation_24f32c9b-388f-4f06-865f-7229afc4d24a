-- ETL建表 创建已发货未开票报表表
drop table IF EXISTS dws_shipped_uninvoiced_orders;
CREATE TABLE dws_shipped_uninvoiced_orders (
                                           id INT AUTO_INCREMENT PRIMARY KEY,
                                           sale_company varchar(200) COMMENT '销售公司',
                                           deparment_code varchar(200) COMMENT '部门编码',
                                           deparment_name VARCHAR(100) COMMENT '部门名称',
                                           region varchar(200) COMMENT '区域',
                                           cust_code varchar(200) COMMENT '客户编码',
                                           cust_name VARCHAR(100) COMMENT '客户名称',
                                           cust_manager_code varchar(200) COMMENT '客户经理编码',
                                           cust_manager_name VARCHAR(100) COMMENT '客户经理名称',
                                           cust_type INT COMMENT '客户类型编码',
                                           cust_type_name varchar(200) COMMENT '客户类型名称',
                                           sales_order_code varchar(200) COMMENT '销售订单编码',
                                           status INT COMMENT '订单状态编码',
                                           status_desc varchar(200) COMMENT '订单状态描述',
                                           factory_assigned varchar(200) COMMENT '分配工厂',
                                           shipment_code text COMMENT '发货单号',
                                           shipment_date DATE COMMENT '发货日期',
                                           main_class varchar(200) COMMENT '主分类',
                                           sub_class varchar(200) COMMENT '子分类',
                                           material_code varchar(200) COMMENT '物料编码',
                                           material_name VARCHAR(100) COMMENT '物料名称',
                                           product_version varchar(200) COMMENT '产品版本',
                                           grade_name varchar(200) COMMENT '等级名称',
                                           contract_management_code varchar(200) COMMENT '合同管理编码',
                                           order_quantity_after_split DECIMAL(18) COMMENT '拆分后订单数量',
                                           shipped_quantity DECIMAL(18) COMMENT '已发货数量',
                                           unit_price_exclusive DECIMAL(18,6) COMMENT '不含税单价',
                                           amount_exclusive_tax DECIMAL(18,2) COMMENT '不含税金额',
                                           invoiced_quantity DECIMAL(18) COMMENT '已开票数量',
                                           uninvoiced_quantity DECIMAL(18) COMMENT '未开票数量',
                                           uninvoiced_amount DECIMAL(18,2) COMMENT '未开票金额',
                                           uninvoiced_age INT COMMENT '未开票账龄(天)',
                                           update_time varchar(20) comment '更新时间',
                                           INDEX idx_sales_order_code (sales_order_code),
                                           INDEX idx_cust_code (cust_code)
) COMMENT '已发货未开票报表';

-- ETL:CRM已发货未开票报表->dws_shipped_uninvoiced_orders
SELECT '西安环球'                               sale_company,
       t3.deparment_code,
       t3.deparment_name,
       t3.department_region region,
       t3.cust_code,
       t3.cust_name,
       t3.cust_manager_code,
       t3.cust_manager_name,
       t3.cust_type,
       CASE
           WHEN t3.cust_type = 0 THEN '新建客户'
           WHEN t3.cust_type = 1 THEN '公海客户'
           WHEN t3.cust_type = 2 THEN '合作客户'
           WHEN t3.cust_type = 3 THEN '开发中客户'
           WHEN t3.cust_type = 4 THEN '受限客户'
           END                                  cust_type_name,
       t1.sales_order_code,
       t1.status ,
       case
           when t1.status=10 then '已删除'
           when t1.status=8 then '已取消'
           when t1.status=7 then '已关闭'
           when t1.status=6 then '已开票'
           when (select count(*)
                 from invoice_application ia
                 where ia.status IN ('0', '3', '4')
                   and ia.flag_deleted = 0
                   and ia.split_order_no like concat('%', t1.sales_order_code, '%')) > 0
               then '已发货未开票'
           when t1.status=0 then '已拆分'
           when t1.status=1 then '已下达'
           when t1.status=2 then '已排产'
           when t1.status=3 then '已领料'
           when t1.status=4 then '生产中'
           when t1.status=5 then '已入库'
           else '其他'
           end status_desc,
       t1.factory_assigned,
       t4.shipment_code,
       t4.shipment_date,
       t2.main_class,
       t2.sub_class,
       t2.material_code,
       t2.material_name,
       t2.product_version,
       t2.grade_name,
       t2.contract_management_code,
       t2.order_quantity_after_split,
       t4.shipped_quantity,
       round(IF(t2.unit_price_exclusive NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.unit_price_exclusive ),
             4)                                 unit_price_exclusive,
       round(IF(t2.amount_exclusive_tax NOT REGEXP '^-?[0-9]+(\\.[0-9]+)?$', 0,
                t2.amount_exclusive_tax ),
             2)                                 amount_exclusive_tax,
       t4.invoiced_quantity                       invoiced_quantity,
       t4.shipped_quantity - t4.invoiced_quantity uninvoiced_quantity,
       round((t4.shipped_quantity - t4.invoiced_quantity) * t2.unit_price_exclusive ,
             2)                                 uninvoiced_amount,
       datediff(current_date, t4.shipment_date)    uninvoiced_age,
       date_format(current_date, '%Y-%m-%d') update_time
FROM crm_sales_order t1
         LEFT JOIN crm_sales_order_product t2 ON t1.sales_order_code = t2.sales_order_code
         LEFT JOIN crm_cust_basic t3 ON t3.cust_code = t1.cust_code
         join (
            select split_order_no sales_order_code,
                   product_code material_code,
                   split_order_line_no,
                   group_concat(outbound_no) shipment_code,
                   max(outbound_date) shipment_date,
                   sum(ship_quantity) shipped_quantity,
                   sum(ifnull(nnum,0))  invoiced_quantity
            from bip_outbound_order_detail bood
                     left join (select csrcid, csrcbid, sum(nnum) nnum
                                from crm_sales_invoice_details
                                where flag_deleted = 0
                                group by csrcid, csrcbid) csid
                               on bood.outbound_header = csid.csrcid and bood.outbound_line_id = csid.csrcbid
            where bood.flag_deleted=0
            group by sales_order_code,material_code,split_order_line_no
        ) t4 on t4.sales_order_code = t2.sales_order_code and t4.material_code = t2.material_code
                    and t4.split_order_line_no = t2.contract_product_line_number
WHERE t1.flag_deleted = 0
  AND t2.flag_deleted = 0
  AND t3.flag_deleted = 0
  and t3.cust_status=2 and t3.cust_type not in (0)
  and t4.shipped_quantity > t4.invoiced_quantity
  and t2.order_quantity_after_split>t4.invoiced_quantity
ORDER BY uninvoiced_age desc
;
select * from dws_shipped_uninvoiced_orders where update_time ='2025-04-30';



