select year(now()) update_year, '01'  AS update_month,concat(year(now()), '-01') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m')='01'
UNION ALL
select year(now()) update_year, '02'  AS update_month,concat(year(now()), '-02') AS update_time,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02')
UNION ALL
select year(now()) update_year, '03'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03')
UNION ALL
select year(now()) update_year, '04'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04')
UNION ALL
select year(now()) update_year, '05'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05')
UNION ALL
select year(now()) update_year, '06'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06')
UNION ALL
select year(now()) update_year, '07'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07')
UNION ALL
select year(now()) update_year, '08'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08')
UNION ALL
select year(now()) update_year, '09'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09')
UNION ALL
select year(now()) update_year, '10'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10')
UNION ALL
select year(now()) update_year, '11'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10','11')
UNION ALL
select year(now()) update_year, '12'  AS update_month,ifnull(sum(ifnull(profit_amount,0)),0) profit_amount from dws_profit_complete_metric dcpm
where DATE_FORMAT(create_time, '%Y') = DATE_FORMAT(NOW(), '%Y')
  and DATE_FORMAT(create_time,'%m') in ('01','02','03','04','05','06','07','08','09','10','11','12')
ORDER BY update_month;