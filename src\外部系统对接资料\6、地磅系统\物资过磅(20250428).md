# 物资过磅 (20250428)  使用文档

## 概述

这份文档详细描述了如何使用API（物资过磅），包含了API的详细解释、调试示例、调用示例。

---

## API说明

#### API名称: 物资过磅

**API ID:** 1904421526837399552

**API类型:** guide

**请求类型:** `POST`

**请求URL:** http://172.16.32.11/data_service/api/v2/api_service/query/1904421526837399552

**请求参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| gc | string | false | "cz" | 工厂 |
| in_date | datetime | false | "2025-03-14 00:00:00" | 进厂时间 |
| zjmc | string | false | "24/25" | 榨季 |
| pageSize | int | false | -- | 分页个数 |
| pageNum | int | false | -- | 分页页码 |

**返回参数:**

| 参数名称 | 字段类型 | 是否必需 | 示例值 | 描述 |
| :------- | :------- | :------- | :------- | :--------------------------- |
| gbxh | int | false | -- | 过磅序号 |
| thdh | string | false | -- | 提货单号 |
| rxh | string | false | -- | 日序号 |
| gxdw | string | false | -- | 购销单位 |
| shdw | string | false | -- | 销货单位 |
| xmm | string | false | -- | 项目码 |
| xmmc | string | false | -- | 项目名称 |
| cph | string | false | -- | 车牌号 |
| sjmc | string | false | -- | 司机名称 |
| mz | int | false | -- | 毛重 |
| pz | int | false | -- | 皮重 |
| kz | double | false | -- | 扣杂 |
| bzm | string | false | -- | 包装码 |
| bzmc | string | false | -- | 包装名称 |
| kdz | int | false | -- | 扣吨值 |
| bzz | int | false | -- | 包装重量 |
| jz | int | false | -- | 净重 |
| fhsl | double | false | -- | 发货数量 |
| in_date | datetime | false | -- | 进厂时间 |
| out_date | datetime | false | -- | 出厂时间 |
| czs | datetime | false | -- | 操作时间 |
| in_czr | string | false | -- | 进厂操作人 |
| out_czr | string | false | -- | 出厂操作人 |
| hwzt | string | false | -- | 货物状态 |
| kzt | string | false | -- | 扣杂态 |
| zt | string | false | -- | 状态 |
| fxhr | string | false | -- | 放行人员 |
| xgs | datetime | false | -- | 修改人 |
| xgr | string | false | -- | 修改日期 |
| bz | string | false | -- | 包装 |
| hthdzt | string | false | -- | 合同执行状态 |
| bb | string | false | -- | 班报标识 |
| zjmc | string | false | -- | 榨季名称 |
| fcxh | string | false | -- | 分厂线号 |
| fcxhdw | string | false | -- | 分厂线号单位 |
| dbh | string | false | -- | 调拨号  |
| gsm | string | false | -- | 公司码 |
| djlx | string | false | -- | 单据类型 |
| dbdh | string | false | -- | 磅单编号 |
| kzfs | string | false | -- | 扣杂方式 |
| sap_zt | string | false | -- | sap状态 |
| kzzl | int | false | -- | 扣杂重量 |
| gysdm | string | false | -- | 供应商代码 |
| jsbm | string | false | -- | 结算编码 |
| cldm | string | false | -- | 车辆代码 |
| gbfszt | string | false | -- | 过磅分销状态 |
| gc | string | false | -- | 工厂 |

```json
{
    "ApiID": "1904421526837399552",
    "Option": [
        {
            "Id": 100,
            "Val": 0,
            "Val_": "$DPS_TOKEN"
        }
    ],
    "Params": {
    "gc": "cz",
    "in_date": "2025-03-14 00:00:00",
    "zjmc": "24/25",
    "pageNum": null,
    "pageSize": null
}
}
```



**正常响应示例:**

```json
{
    "Status": {
        "Code": 0,
        "Message": ""
    },
    "Result": {
        "Meta": [
            {
                "Name": "id",
                "Type": "int"
            },
            {
                "Name": "name",
                "Type": "string"
            }
        ],
        "Data": [
            {
                "id": 123,
                "name": "test"
            }
        ]
    }
}
```

**错误响应示例:**

```json
{
    "Status": {
        "Code": 4,
        "Message": "[10001] api access denied"
    },
    "Result": {
        "Meta": [],
        "Data": []
    }
}
```

**错误代码:**

## ErrorCode 常见状态码相关：
|  |  |  |
| --- | --- | --- |
| 状态码 | 名称 | 含义 |
| 0 | QueryErrorType_OK | 查询成功 |
| 1 | QueryErrorType_PARSER_ERROR | 解析报错 |
| 2 | QueryErrorType_ILLGEAL_INPUT_ERROR | 非法参数报错 |
| 3 | QueryErrorType_RATE_LIMIT_ERROR | 限流报错 |
| 4 | QueryErrorType_AUTH_ERROR | 权限报错 |
| 5 | QueryErrorType_QUERY_TIMEOUT | 查询超时报错 |
| 6 | QueryErrorType_DS_TIMEOUT | 数据源超时报错 |
| 7 | QueryErrorType_INTERNAL_ERROR | 程序内部报错 |
| 8 | QueryErrorType_META_ERROR | 元信息报错 |
| 9 | QueryErrorType_DS_RATE_LIMIT_ERROR | 数据源限流报错 |
| 255 | QueryErrorType_UNKNOWN_ERROR | 未知错误 |
## One Service 常见错误码相关：
|  |  |  |
| --- | --- | --- |
| 错误码 | 名称 | 含义 |
| 10000 | UnknownQueryEngine | 未知查询引擎 |
| 10001 | NoPermission | 没有权限 |
| 10002 | MetaErr | 元信息错误 |
| 10003 | ParamsParseErr | 参数解析错误 |
| 10004 | ApiRateLimitExceed | Api的QPS超限额 |
| 10005 | ParseErr | 解析错误 |
| 10006 | ExecuteErr | 执行错误 |
| 10007 | UnknownQueryType | 未知查询类型 |
| 10008 | QueryRequestError | 查询请求错误 |
| 10009 | QueryEngineMismatchError | 查询引擎不匹配错误 |


**调用说明:**

###### HTTP调用示例
```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  -H 'dpstoken: $DPS_TOKEN' \
  http://172.16.32.11/data_service/api/v2/api_service/query/1904421526837399552 \
  -d '{"gc":"cz","in_date":"2025-03-14 00:00:00","zjmc":"24/25","pageSize":0,"pageNum":0}'
```
###### 如果使用动态密钥功能,可以使用如下接口动态获取token信息

```
curl -X POST \
  -H 'user:yushumeng' \
  -H 'Content-Type: application/json' \
  http://172.16.32.11/data_service/api/v2/api_service/token \
  -d '{"AppKey": "you_app_key","AppSecret": "you_app_secret"}'
```
###### 返回样例：
```
{
    "code": 0,
    "message": "Success",
    "data": {
        "AppKey": "you_app_key",
        "Token": "token_str"
    },
    "meta": {
        "text": "",
        "redirect": ""
    }
}
```
###### 解析其中的Token 调用的时候将$DPS_TOKEN替换为“该应用密钥”，动态token有一定有效期且会动态变化，请不要缓存动态token或者对动态token有任何假设，每次请求获取即可

**开启分页调用说明:**

向导式API分页调用说明
1、在API页面开启高级配置的分页，在API页面测试时系统会自动添加pageNum和pageSize参数。
2、调用API的时候需要将pageNum和pageSize填入请求参数中。
例如：{
  "pageNum": 1,
  "pageSize": 100
}

脚本式API分页调用说明
1、用SQL来进行分页操作，例如：SELECT * FROM table ORDER BY id LIMIT 10 OFFSET 0;
2、如果想要获取本次查询的total总数，请参考：https://bytedance.larkoffice.com/docx/HJKudzKHVoEAejxAmUncZ1LanIc

**API调用常见问题:**

1、api xxx meta not exists
答：一般是因为API没有在对应的环境发布，可在API页面版本信息中查看是否已发布到对应环境。

2、10001 api access denied
答：API调用时未输入DpsToken或者DpsToken与已对API授权的应用不一致。

---

## 使用 Postman 调试API说明

Postman是一个强大的HTTP API测试工具，可以帮助开发者快速开发和测试API。

### POST 请求示例:

1. 打开Postman应用程序。
2. 在请求方式空间中选择 `POST`。
3. 在请求URL空间中输入 http://172.16.32.11/data_service/api/v2/api_service/query/1904421526837399552，然后点击 `Body` 的选项卡，然后选择 `raw`。
4. 在 `Body` 中填写 `Json` 格式的请求参数，然后点击 `Send` 按钮。
5. 如果一切正常，则会在下方的 `Body` 中看到响应结果。

---

## 使用 Java 调用API说明

如果你在Java编程语言调用API，可以使用HttpClient等库。

### HttpClient库为例子，POST请求的代码示例

```java

// 导入需要的Apache HttpClient库包
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import java.io.IOException;

public class ApiTest {

    public static void main(String[] args) throws Exception {
        // 定义基础URL
        String baseUrl = "http://172.16.32.11/data_service/api/v2/api_service/query/";
        // 定义API ID
        String apiId = "1904421526837399552";
        // 定义API TOKEN
        String dpstoken = "$DPS_TOKEN";
        // 定义请求主体
        String body = "{\"gc\":\"cz\",\"in_date\":\"2025-03-14 00:00:00\",\"zjmc\":\"24/25\",\"pageSize\":0,\"pageNum\":0}";
        // 调用performPostRequest函数发送POST请求并在控制台输出返回的结果
        System.out.println(performPostRequest(baseUrl, apiId, dpstoken, body));
    }

    // 定义函数performPostRequest发送POST请求
    private static String performPostRequest(String baseUrl, String apiId, String dpstoken, String body) throws IOException {
        // 创建一个默认的CloseableHttpClient实例
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建一个HttpPost实例，参数为目标URL（基础URL+API ID）
        HttpPost httpPost = new HttpPost(baseUrl + apiId);

        // 设置httpPost的header
        httpPost.setHeader("dpstoken", dpstoken);

        // 设置httpPost的body
        httpPost.setEntity(new StringEntity(body));

        // 使用httpclient执行httpPost，获取响应结果
        CloseableHttpResponse response = httpclient.execute(httpPost);
        try {
            // 从response中获取HttpEntity
            HttpEntity entity = response.getEntity();

            // 如果HttpEntity不为null，则将其转化为String类型，并返回
            return entity != null ? EntityUtils.toString(entity) : null;
            // 确保response在执行完毕后被关闭，避免资源泄漏
        } finally {
            response.close();
        }
    }
}
/*
需要在pom.xml中添加的dependency如下
<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
<dependency>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpclient</artifactId>
    <version>4.5.13</version>
</dependency>

<!-- https://mvnrepository.com/artifact/org.json/json -->
<dependency>
    <groupId>org.json</groupId>
    <artifactId>json</artifactId>
    <version>20210307</version>
</dependency>
 */
```